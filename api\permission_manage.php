<?php
header('Content-Type: application/json');
require_once __DIR__.'/../config.php';

// 验证用户登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 'error', 'message' => '请先登录']);
    exit;
}

// 获取并过滤输入参数
$action = $_POST['action'] ?? '';
waf_check($action);

$application_id = intval($_POST['application_id'] ?? 0);
$user_id = intval($_POST['user_id'] ?? 0);
$permission_type = intval($_POST['permission_type'] ?? 0);

// 数据库连接
try {
    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception('数据库连接失败');
    }
  
    switch ($action) {
        case 'add':
            // 验证管理员权限
            if ($_SESSION['user_type'] != 'admin') {
                throw new Exception('无权限操作');
            }
            // ... existing code ...


            // 检查权限是否已存在
            $check_stmt = $conn->prepare("SELECT id FROM permission WHERE application_id = ? AND user_id = ?");
            $check_stmt->bind_param("ii", $application_id, $user_id);
            $check_stmt->execute();
            if ($check_stmt->get_result()->num_rows > 0) {
                throw new Exception('该用户在此应用下已有权限');
            }
            
            // 添加权限
            $stmt = $conn->prepare("INSERT INTO permission (application_id, user_id, permission_type) VALUES (?, ?, ?)");
            $stmt->bind_param("iii", $application_id, $user_id, $permission_type);
            $stmt->execute();
            
            // 记录日志
            logOperation($conn, $_SESSION['user_id'], $application_id, "添加权限: 应用ID={$application_id}, 用户ID={$user_id}, 权限类型={$permission_type}");
            
            echo json_encode(['status' => 'success', 'message' => '添加成功']);
            break;
            
        case 'edit':
            // 验证管理员权限
            if ($_SESSION['user_type'] != 'admin') {
                throw new Exception('无权限操作');
            }
            
            // 检查权限是否存在
            $check_stmt = $conn->prepare("SELECT id FROM permission WHERE application_id = ? AND user_id = ?");
            $check_stmt->bind_param("ii", $application_id, $user_id);
            $check_stmt->execute();
            if ($check_stmt->get_result()->num_rows == 0) {
                throw new Exception('要修改的权限不存在');
            }
            
            // 更新权限
            $stmt = $conn->prepare("UPDATE permission SET permission_type = ? WHERE application_id = ? AND user_id = ?");
            $stmt->bind_param("iii", $permission_type, $application_id, $user_id);
            $stmt->execute();
            
            // 记录日志
            logOperation($conn, $_SESSION['user_id'], $application_id, "修改权限: 应用ID={$application_id}, 用户ID={$user_id}, 权限类型={$permission_type}");
            
            echo json_encode(['status' => 'success', 'message' => '修改成功']);
            break;
            
        case 'delete':
            // 验证管理员权限
            if ($_SESSION['user_type'] != 'admin') {
                throw new Exception('无权限操作');
            }
            
            // 删除权限
            $stmt = $conn->prepare("DELETE FROM permission WHERE application_id = ? AND user_id = ?");
            $stmt->bind_param("ii", $application_id, $user_id);
            $stmt->execute();
            
            // 记录日志
            logOperation($conn, $_SESSION['user_id'], $application_id, "删除权限: 应用ID={$application_id}, 用户ID={$user_id}");
            
            echo json_encode(['status' => 'success', 'message' => '删除成功']);
            break;
            
        case 'list':
            // 查询权限列表
            $query = "SELECT p.*, a.application_name, u.name AS user_name 
                      FROM permission p 
                      JOIN application a ON p.application_id = a.id 
                      JOIN user u ON p.user_id = u.id";
            
            $params = [];
            if ($application_id > 0) {
                $query .= " WHERE p.application_id = ?";
                $params[] = $application_id;
            }
            
            $stmt = $conn->prepare($query);
            if (!empty($params)) {
                $stmt->bind_param(str_repeat('i', count($params)), ...$params);
            }
            
            $stmt->execute();
            $result = $stmt->get_result();
            $permissions = $result->fetch_all(MYSQLI_ASSOC);
            
            echo json_encode(['status' => 'success', 'data' => $permissions]);
            break;
            
        default:
            throw new Exception('无效操作');
    }
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
