<?php
/**
 * 测试文件上传IP记录功能
 * 创建时间: 2025-07-03
 */

require_once 'functions.php';

// 处理测试上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    $file = $_FILES['test_file'];
    $uploadIp = getClientRealIP();
    
    // 简单的文件验证
    if ($file['error'] === UPLOAD_ERR_OK && $file['size'] > 0) {
        // 生成测试文件信息
        $originalName = $file['name'];
        $storedName = 'test_' . time() . '_' . $originalName;
        $shareCode = generateShareCode();
        $fileType = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        
        try {
            // 测试数据库插入（不实际保存文件）
            $stmt = $pdo->prepare("
                INSERT INTO filecloud_info 
                (user_id, original_name, stored_name, file_size, file_type, share_code, is_public, file_path, uploadIp) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                999, // 测试用户ID
                $originalName,
                $storedName,
                $file['size'],
                $fileType,
                $shareCode,
                1,
                'test/' . $storedName,
                $uploadIp
            ]);
            
            $fileId = $pdo->lastInsertId();
            $success = "✅ 测试上传成功！文件ID: $fileId, 记录的IP: $uploadIp";
            
        } catch (PDOException $e) {
            $error = "❌ 数据库插入失败: " . $e->getMessage();
        }
    } else {
        $error = "❌ 文件上传失败";
    }
}

// 获取最近的测试记录
try {
    $stmt = $pdo->query("
        SELECT file_id, original_name, user_id, upload_time, uploadIp, file_size
        FROM filecloud_info 
        WHERE user_id = 999 
        ORDER BY upload_time DESC 
        LIMIT 5
    ");
    $testRecords = $stmt->fetchAll();
} catch (PDOException $e) {
    $testRecords = [];
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文件上传IP记录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #0d6efd;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0b5ed7;
        }
        .success {
            color: green;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #0c5460;
            background: #d1ecf1;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .current-ip {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 测试文件上传IP记录功能</h1>
        
        <div class="current-ip">
            <h3>📍 当前检测到的IP地址</h3>
            <p><strong>IP地址:</strong> <code><?= getClientRealIP() ?></code></p>
            <p><strong>REMOTE_ADDR:</strong> <code><?= $_SERVER['REMOTE_ADDR'] ?? '未设置' ?></code></p>
            <p><strong>HTTP_X_FORWARDED_FOR:</strong> <code><?= $_SERVER['HTTP_X_FORWARDED_FOR'] ?? '未设置' ?></code></p>
            <p><strong>HTTP_X_REAL_IP:</strong> <code><?= $_SERVER['HTTP_X_REAL_IP'] ?? '未设置' ?></code></p>
        </div>

        <?php if (isset($success)): ?>
            <div class="success"><?= $success ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error"><?= $error ?></div>
        <?php endif; ?>

        <h3>📤 测试文件上传</h3>
        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="test_file">选择测试文件:</label>
                <input type="file" id="test_file" name="test_file" required>
            </div>
            <button type="submit">测试上传并记录IP</button>
        </form>

        <div class="info">
            <strong>说明:</strong> 此测试会将文件信息保存到数据库（user_id=999），但不会实际保存文件到服务器。
        </div>

        <h3>📊 最近的测试记录</h3>
        <?php if (empty($testRecords)): ?>
            <p>暂无测试记录</p>
        <?php else: ?>
            <table>
                <tr>
                    <th>文件ID</th>
                    <th>文件名</th>
                    <th>文件大小</th>
                    <th>上传时间</th>
                    <th>记录的IP</th>
                </tr>
                <?php foreach ($testRecords as $record): ?>
                <tr>
                    <td><?= $record['file_id'] ?></td>
                    <td><?= htmlspecialchars($record['original_name']) ?></td>
                    <td><?= formatFileSize($record['file_size']) ?></td>
                    <td><?= $record['upload_time'] ?></td>
                    <td><code><?= $record['uploadIp'] ?? '未记录' ?></code></td>
                </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>

        <h3>🧹 清理测试数据</h3>
        <form method="POST" style="margin-top: 20px;">
            <input type="hidden" name="action" value="cleanup">
            <button type="submit" onclick="return confirm('确定要删除所有测试记录吗？')" 
                    style="background: #dc3545;">清理测试数据</button>
        </form>

        <h3>🔗 相关链接</h3>
        <ul>
            <li><a href="test_ip_recording.php" target="_blank">IP记录功能详细测试</a></li>
            <li><a href="upload.php" target="_blank">正式上传页面（登录用户）</a></li>
            <li><a href="anonymous_upload.php" target="_blank">匿名上传页面</a></li>
            <li><a href="update_database.php" target="_blank">数据库更新页面</a></li>
        </ul>
    </div>
</body>
</html>

<?php
// 处理清理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'cleanup') {
    try {
        $stmt = $pdo->prepare("DELETE FROM filecloud_info WHERE user_id = 999");
        $stmt->execute();
        $deletedCount = $stmt->rowCount();
        echo "<script>alert('已删除 $deletedCount 条测试记录'); window.location.reload();</script>";
    } catch (PDOException $e) {
        echo "<script>alert('删除失败: " . addslashes($e->getMessage()) . "');</script>";
    }
}
?>
