// 版本管理和缓存控制
const SYSTEM_VERSION = '1.0.9';
const BUILD_TIME = '2025-07-15 11:15:00';

// 检查版本更新
function checkVersion() {
    const lastVersion = localStorage.getItem('persimts_version');
    if (lastVersion !== SYSTEM_VERSION) {
        // 版本更新，清除相关缓存
        console.log('检测到版本更新，清除缓存...');
        
        // 清除localStorage中的缓存数据
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('persimts_')) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
        
        // 更新版本号
        localStorage.setItem('persimts_version', SYSTEM_VERSION);
        localStorage.setItem('persimts_build_time', BUILD_TIME);
        
        // 显示更新提示
        if (lastVersion) {
            console.log(`系统已从版本 ${lastVersion} 更新到 ${SYSTEM_VERSION}`);
        }
    }
}

// 强制刷新缓存
function forceRefresh() {
    // 清除所有缓存
    if ('caches' in window) {
        caches.keys().then(names => {
            names.forEach(name => {
                caches.delete(name);
            });
        });
    }
    
    // 重新加载页面
    window.location.reload(true);
}

// 获取带版本号的URL
function getVersionedUrl(url) {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}v=${SYSTEM_VERSION}&t=${Date.now()}`;
}

// 页面加载时检查版本
document.addEventListener('DOMContentLoaded', () => {
    checkVersion();
    
    // 在控制台显示版本信息
    console.log(`%c人员调动模拟系统 v${SYSTEM_VERSION}`, 'color: #3498db; font-weight: bold; font-size: 14px;');
    console.log(`构建时间: ${BUILD_TIME}`);
});

// 导出版本信息
window.PerSimTSVersion = {
    version: SYSTEM_VERSION,
    buildTime: BUILD_TIME,
    checkVersion: checkVersion,
    forceRefresh: forceRefresh,
    getVersionedUrl: getVersionedUrl
};
