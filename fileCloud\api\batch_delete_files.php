<?php
/**
 * 文件网盘系统 - 批量删除文件API
 * 创建时间: 2025-06-26
 */



require_once '../functions.php';

header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => '请先登录'], 401);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => '无效的请求方法'], 405);
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => '无效的请求'], 400);
}

$fileIds = $_POST['file_ids'] ?? '';
$currentUserId = $_SESSION['user_id'];

// 验证参数
if (empty($fileIds)) {
    jsonResponse(['success' => false, 'message' => '参数错误'], 400);
}

// 解析文件ID列表
$fileIdArray = array_filter(array_map('intval', explode(',', $fileIds)));
if (empty($fileIdArray)) {
    jsonResponse(['success' => false, 'message' => '无效的文件ID列表'], 400);
}

try {
    $pdo->beginTransaction();
    
    $deletedCount = 0;
    $errors = [];

    foreach ($fileIdArray as $fileId) {
        try {
            // 检查文件是否存在且属于当前用户
            $checkStmt = $pdo->prepare("
                SELECT file_id, original_name FROM filecloud_info 
                WHERE file_id = ? AND user_id = ? AND is_deleted = 0
            ");
            $checkStmt->execute([$fileId, $currentUserId]);
            $file = $checkStmt->fetch();
            
            if (!$file) {
                $errors[] = "文件 {$fileId} 不存在或无权限";
                continue;
            }
            
            // 软删除文件（设置is_deleted=1）
            $deleteStmt = $pdo->prepare("
                UPDATE filecloud_info 
                SET is_deleted = 1 
                WHERE file_id = ?
            ");
            
            if ($deleteStmt->execute([$fileId])) {
                $deletedCount++;
            } else {
                $errors[] = "删除文件 {$file['original_name']} 失败";
            }
            
        } catch (Exception $e) {
            $errors[] = "处理文件 {$fileId} 时出错: " . $e->getMessage();
        }
    }

    if ($deletedCount > 0) {
        $pdo->commit();
        
        $message = "成功删除 {$deletedCount} 个文件";
        
        if (!empty($errors)) {
            $message .= "，但有 " . count($errors) . " 个操作失败";
        }
        
        jsonResponse([
            'success' => true, 
            'message' => $message,
            'deleted_count' => $deletedCount,
            'errors' => $errors
        ]);
    } else {
        $pdo->rollback();
        $errorMessage = !empty($errors) ? implode('; ', $errors) : '没有文件被删除';
        jsonResponse(['success' => false, 'message' => $errorMessage], 400);
    }

} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }
    jsonResponse(['success' => false, 'message' => '操作失败: ' . $e->getMessage()], 500);
}
?>
