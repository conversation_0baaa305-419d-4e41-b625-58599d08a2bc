from aiohttp import web
import asyncio
import aiohttp
from aiohttp import web



async def sse_handler(request):
    response = web.StreamResponse(
        status=200,
        reason='OK',
        headers={
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type'
        }
    )
    await response.prepare(request)
    
    while True:
        try:
            # 发送API获取的数据
            data = await sseSend()
            await response.write(f"data: {data}\n\n".encode('utf-8'))
            await asyncio.sleep(30)  # 30秒间隔
        except ConnectionResetError:
            break
    
    return response

async def sseSend():
    async with aiohttp.ClientSession() as session:
        async with session.post(
            'http://192.168.108.168/DutySched/api/sched_manage.php',
            data={'controlCode': 'query1'}
        ) as response:
            return await response.text()

app = web.Application()
app.router.add_get('/sseServer', sse_handler)

if __name__ == '__main__':
    web.run_app(app, port=8080)