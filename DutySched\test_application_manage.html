<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用管理测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .operation-select {
            width: 100%;
            padding: 8px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        button:hover {
            background-color: #45a049;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .data-list {
            margin-top: 15px;
            border-collapse: collapse;
            width: 100%;
            display: none;
        }

        .data-list th, .data-list td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .data-list tr:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>应用管理测试页面</h2>
        
        <select class="operation-select" id="operationSelect">
            <option value="query">查询</option>
            <option value="add">新增</option>
            <option value="modify">修改</option>
            <option value="del">删除</option>
        </select>

        <div id="formContainer" style="display: none;">
            <!-- 新增和修改表单 -->
            <div id="addModifyForm">
                <div class="form-group">
                    <label for="application_name">应用名称:</label>
                    <input type="text" id="application_name" placeholder="请输入应用名称">
                    <label for="url">URL:</label>
                    <input type="text" id="url" placeholder="请输入应用URL">
                    <label for="public">公开级别:</label>
                    <select id="public">
                        <option value="0">公开</option>
                        <option value="1">非第三方人员</option>
                        <option value="2">民警</option>
                        <option value="3">授权用户</option>
                    </select>
                    <label for="roleList">角色列表(JSON):</label>
                    <input type="text" id="roleList" placeholder="请输入角色ID列表(JSON格式)" value="[]">
                </div>
            </div>

            <!-- 修改表单 -->
            <div id="modifyForm">
                <div class="form-group">
                    <label for="id">ID:</label>
                    <input type="text" id="id" placeholder="请输入ID">
                </div>
                <div class="form-group">
                    <label for="newApplicationName">新应用名称:</label>
                    <input type="text" id="newApplicationName" placeholder="请输入新应用名称">
                    <label for="newUrl">新URL:</label>
                    <input type="text" id="newUrl" placeholder="请输入新URL">
                    <label for="newPublic">新公开级别:</label>
                    <select id="newPublic">
                        <option value="0">公开</option>
                        <option value="1">非第三方人员</option>
                        <option value="2">民警</option>
                        <option value="3">授权用户</option>
                    </select>
                    <label for="newRoleList">新角色列表(JSON):</label>
                    <input type="text" id="newRoleList" placeholder="请输入新角色ID列表(JSON格式)" value="[]">
                </div>
            </div>

            <!-- 删除表单 -->
            <div id="deleteForm">
                <div class="form-group">
                    <label for="deleteId">ID:</label>
                    <input type="text" id="deleteId" placeholder="请输入ID">
                </div>
            </div>
        </div>

        <button onclick="executeOperation()">执行操作</button>

        <div class="result" id="resultArea">
            <!-- 查询结果将显示在这里 -->
        </div>

        <div id="dataList" class="data-list" style="display: none;">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>应用名称</th>
                    <th>URL</th>
                    <th>公开级别</th>
                </tr>
            </thead>
            <tbody id="dataBody"></tbody>
        </div>
    </div>

    <script>
        // 绑定下拉框的change事件，显示相应的表单
        document.getElementById('operationSelect').addEventListener('change', showFormBasedOnOperation);

        // 绑定按钮的click事件，负责执行操作
        const button = document.querySelector('button');
        if (!button.dataset.isBound) {
            button.addEventListener('click', executeOperation);
            button.dataset.isBound = 'true';
        }

        let isRequestInProgress = false;

        function showFormBasedOnOperation() {
            const operation = document.getElementById('operationSelect').value;
            const formContainer = document.getElementById('formContainer');
            const addModifyForm = document.getElementById('addModifyForm');
            const modifyForm = document.getElementById('modifyForm');
            const deleteForm = document.getElementById('deleteForm');

            // 初始化时隐藏所有表单
            formContainer.style.display = 'none';
            addModifyForm.style.display = 'none';
            modifyForm.style.display = 'none';
            deleteForm.style.display = 'none';

            // 根据操作类型显示相应的表单
            switch(operation) {
                case 'add':
                    formContainer.style.display = 'block';
                    addModifyForm.style.display = 'block';
                    break;
                case 'modify':
                    formContainer.style.display = 'block';
                    modifyForm.style.display = 'block';
                    break;
                case 'del':
                    formContainer.style.display = 'block';
                    deleteForm.style.display = 'block';
                    break;
                case 'query':
                    // 查询时不需要表单
                    formContainer.style.display = 'none';
                    break;
            }
        }

        function executeOperation() {
            const operationSelect = document.getElementById('operationSelect');
            const controlCode = operationSelect.value;
            //console.log('controlCode:', controlCode);
            if (!controlCode || ![ 'add', 'modify', 'del', 'query' ].includes(controlCode)) {
                alert('请选择有效的操作类型');
                return;
            }
            const formData = {
                controlCode: controlCode
            };

            // 根据操作类型获取参数
            switch(controlCode) {
                case 'add':
                    const application_name = document.getElementById('application_name');
                    const url = document.getElementById('url');
                    const public = document.getElementById('public');
                    const roleList = document.getElementById('roleList');
                    if (application_name && application_name.value) {
                        formData.application_name = application_name.value;
                        formData.url = url.value;
                        formData.public = public.value;
                        formData.roleList = roleList.value;
                    }
                    break;
                case 'modify':
                    const id = document.getElementById('id');
                    const newApplicationName = document.getElementById('newApplicationName'); 
                    const newUrl = document.getElementById('newUrl');
                    const newPublic = document.getElementById('newPublic');
                    const newRoleList = document.getElementById('newRoleList');
                    if (id && newApplicationName && id.value && newApplicationName.value) {
                        formData.id = id.value;
                        formData.application_name = newApplicationName.value;
                        formData.url = newUrl.value;
                        formData.public = newPublic.value;
                        formData.roleList = newRoleList.value;
                    }
                    break;
                case 'del':
                    const deleteId = document.getElementById('deleteId');
                    if (deleteId && deleteId.value) {
                        formData.id = deleteId.value;
                    }
                    break;
                case 'query':
                    // 查询时不需要额外参数
                    break;
            }

            if (isRequestInProgress) return;
            isRequestInProgress = true;

            // 发送请求
            fetch('../api/application_manage.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => {
                //console.log('Server response:', data);
                const resultArea = document.getElementById('resultArea');
                if (!data || !data.status || !data.message || !data.data.application || !Array.isArray(data.data.application)) {
                    resultArea.innerHTML = '<p style="color: red;">服务器返回数据格式错误</p>';
                    return;
                }
                resultArea.innerHTML = `
                    <p>${data.message}</p>
                    ${data.status === 1 ? '<p style="color: green;">成功</p>' : '<p style="color: red;">失败</p>'}
                `;

                if (data.status === 1 && data.data.application.length > 0) {
                    // 创建一个容器来显示数据
                    const dataContainer = document.createElement('div');
                    dataContainer.innerHTML = '<h3>查询结果:</h3>';
                    
                    // 创建无序列表
                    const ul = document.createElement('ul');
                    data.data.application.forEach(item => {
                        const li = document.createElement('li');
                        li.textContent = `ID: ${item.id}, 应用名称: ${item.application_name}, URL: ${item.url}, 公开级别: ${item.public}, 角色组ID：${item.roleList}`;
                        ul.appendChild(li);
                    });
                    dataContainer.appendChild(ul);
                    
                    resultArea.appendChild(dataContainer);
                }
            })
            .catch(error => {
                //console.error('Error:', error);
                const resultArea = document.getElementById('resultArea');
                resultArea.innerHTML = '<p style="color: red;">操作失败</p>';
                resultArea.innerHTML += `<p>详细错误信息: ${error.message}</p>`;
            })
            .finally(() => {
                isRequestInProgress = false;
            });
        }
    </script>
</body>
</html>