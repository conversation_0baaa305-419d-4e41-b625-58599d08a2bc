<?php
/**
 * 文件网盘系统 - 管理员文件管理（增强版）
 * 创建时间: 2025-06-27
 * 更新时间: 2025-07-08
 * 功能：支持软删除和硬删除的完整文件管理
 */

require_once 'functions.php';

// 检查用户是否已登录且为管理员
if (!isLoggedIn()) {
    redirect('login.php');
}

if (!isAdmin()) {
    die('访问被拒绝：您没有管理员权限');
}

$currentUsername = $_SESSION['username'] ?? '管理员';

// 获取筛选参数
$searchFileName = trim($_GET['search_file'] ?? '');
$searchUser = trim($_GET['search_user'] ?? '');
$showDeleted = isset($_GET['show_deleted']) && $_GET['show_deleted'] === '1';
$page = max(1, (int)($_GET['page'] ?? 1));
$perPage = 20;
$offset = ($page - 1) * $perPage;

// 构建查询条件
$whereConditions = [];
$params = [];

// 是否显示已删除文件
if (!$showDeleted) {
    $whereConditions[] = 'f.is_deleted = 0';
}

if (!empty($searchFileName)) {
    $whereConditions[] = 'f.original_name LIKE ?';
    $params[] = '%' . $searchFileName . '%';
}

if (!empty($searchUser)) {
    $whereConditions[] = 'u.name LIKE ?';
    $params[] = '%' . $searchUser . '%';
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 获取文件总数
try {
    $countSql = "
        SELECT COUNT(*)
        FROM filecloud_info f
        LEFT JOIN 3_user u ON f.user_id = u.id
        $whereClause
    ";
    $stmt = $pdo->prepare($countSql);
    $stmt->execute($params);
    $totalFiles = $stmt->fetchColumn();
    $totalPages = ceil($totalFiles / $perPage);
} catch (PDOException $e) {
    $totalFiles = 0;
    $totalPages = 0;
    $error = '获取文件数量失败：' . $e->getMessage();
}

// 获取文件列表
try {
    $sql = "
        SELECT f.file_id, f.original_name, f.file_size, f.file_type,
               f.upload_time, f.download_count, f.share_code, f.is_deleted, f.file_path,
               u.name as username, u.id as user_id
        FROM filecloud_info f
        LEFT JOIN 3_user u ON f.user_id = u.id
        $whereClause
        ORDER BY f.upload_time DESC
        LIMIT $perPage OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $files = $stmt->fetchAll();
} catch (PDOException $e) {
    $files = [];
    $error = '获取文件列表失败：' . $e->getMessage();
}

$csrfToken = generateCsrfToken();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 文件管理</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-files me-1"></i>我的文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>上传文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="shared.php">
                            <i class="bi bi-share me-1"></i>与我共享
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-gear me-1"></i>系统管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="admin.php">系统概览</a></li>
                            <li><a class="dropdown-item active" href="admin_files.php">文件管理</a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?= h($currentUsername) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid my-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col">
                <h2 class="mb-0">
                    <i class="bi bi-folder-open me-2"></i>文件管理
                </h2>
                <p class="text-muted mb-0">管理系统中的所有用户文件</p>
            </div>
        </div>

        <!-- 错误提示 -->
        <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?= h($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- 筛选和搜索 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="searchFile" class="form-label">文件名</label>
                        <input type="text" class="form-control" id="searchFile" name="search_file"
                               value="<?= h($searchFileName) ?>" placeholder="搜索文件名...">
                    </div>
                    <div class="col-md-3">
                        <label for="searchUser" class="form-label">上传用户</label>
                        <input type="text" class="form-control" id="searchUser" name="search_user"
                               value="<?= h($searchUser) ?>" placeholder="搜索用户名...">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">显示选项</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showDeleted"
                                   name="show_deleted" value="1" <?= $showDeleted ? 'checked' : '' ?>>
                            <label class="form-check-label" for="showDeleted">
                                包含已删除文件
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>搜索
                        </button>
                        <a href="admin_files.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>重置
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>文件列表
                    <span class="badge bg-primary ms-2"><?= number_format($totalFiles) ?></span>
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllBtn" onclick="toggleSelectAll()">
                        <i class="bi bi-check-square me-1"></i>全选
                    </button>
                    <span class="text-muted small me-2">已选择 <span id="selectedCount">0</span> 个文件</span>
                    <button type="button" class="btn btn-warning btn-sm me-1" id="batchSoftDeleteBtn" onclick="batchDeleteFiles('soft')" disabled>
                        <i class="bi bi-archive me-1"></i>批量软删除
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="batchHardDeleteBtn" onclick="batchDeleteFiles('hard')" disabled>
                        <i class="bi bi-trash me-1"></i>批量硬删除
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($files)): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="filesTable">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 40px;">
                                    <!-- 工具栏复选框，不在表头 -->
                                </th>
                                <th>文件名</th>
                                <th style="width: 100px;">类型</th>
                                <th style="width: 100px;">大小</th>
                                <th style="width: 120px;">上传用户</th>
                                <th style="width: 140px;">上传时间</th>
                                <th style="width: 80px;">下载量</th>
                                <th style="width: 80px;">状态</th>
                                <th style="width: 80px;">文件存在</th>
                                <th style="width: 140px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($files as $file): ?>
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input file-checkbox" type="checkbox" 
                                               value="<?= $file['file_id'] ?>" 
                                               data-file-name="<?= h($file['original_name']) ?>"
                                               onchange="updateBatchDeleteButton()"
                                               aria-label="选择文件 <?= h($file['original_name']) ?>">
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark me-2 text-primary"></i>
                                        <span class="text-truncate" style="max-width: 250px;" title="<?= h($file['original_name']) ?>">
                                            <?= h($file['original_name']) ?>
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark"><?= h($file['file_type'] ?? 'unknown') ?></span>
                                </td>
                                <td><?= formatFileSize($file['file_size']) ?></td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 100px;" title="<?= h($file['username'] ?? '未知用户') ?>">
                                        <?= h($file['username'] ?? '未知用户') ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted"><?= date('Y-m-d H:i', strtotime($file['upload_time'])) ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= number_format($file['download_count']) ?></span>
                                </td>
                                <td>
                                    <?php if ($file['is_deleted']): ?>
                                        <span class="badge bg-danger">已删除</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">正常</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (file_exists($file['file_path'])): ?>
                                        <span class="badge bg-success">存在</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">缺失</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <?php if (!$file['is_deleted']): ?>
                                            <button type="button" class="btn btn-outline-warning"
                                                    onclick="singleDelete(<?= $file['file_id'] ?>, '<?= h($file['original_name']) ?>', 'soft')"
                                                    title="软删除">
                                                <i class="bi bi-archive"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="singleDelete(<?= $file['file_id'] ?>, '<?= h($file['original_name']) ?>', 'hard')"
                                                title="硬删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <h5 class="text-muted mt-3">暂无文件</h5>
                    <p class="text-muted">没有找到符合条件的文件</p>
                </div>
                <?php endif; ?>
            </div>

            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
            <div class="card-footer">
                <nav aria-label="文件列表分页">
                    <ul class="pagination justify-content-center mb-0">
                        <!-- 上一页 -->
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?>&search_file=<?= urlencode($searchFileName) ?>&search_user=<?= urlencode($searchUser) ?>&show_deleted=<?= $showDeleted ? '1' : '0' ?>" aria-label="上一页">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <span aria-hidden="true">&laquo;</span>
                            </span>
                        </li>
                        <?php endif; ?>

                        <!-- 页码 -->
                        <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);

                        if ($startPage > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=1&search_file=<?= urlencode($searchFileName) ?>&search_user=<?= urlencode($searchUser) ?>&show_deleted=<?= $showDeleted ? '1' : '0' ?>">1</a>
                        </li>
                        <?php if ($startPage > 2): ?>
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>&search_file=<?= urlencode($searchFileName) ?>&search_user=<?= urlencode($searchUser) ?>&show_deleted=<?= $showDeleted ? '1' : '0' ?>"><?= $i ?></a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($endPage < $totalPages): ?>
                        <?php if ($endPage < $totalPages - 1): ?>
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $totalPages ?>&search_file=<?= urlencode($searchFileName) ?>&search_user=<?= urlencode($searchUser) ?>&show_deleted=<?= $showDeleted ? '1' : '0' ?>"><?= $totalPages ?></a>
                        </li>
                        <?php endif; ?>

                        <!-- 下一页 -->
                        <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?>&search_file=<?= urlencode($searchFileName) ?>&search_user=<?= urlencode($searchUser) ?>&show_deleted=<?= $showDeleted ? '1' : '0' ?>" aria-label="下一页">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <span aria-hidden="true">&raquo;</span>
                            </span>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <div class="text-center mt-2">
                    <small class="text-muted">
                        显示第 <?= number_format(($page - 1) * $perPage + 1) ?> - <?= number_format(min($page * $perPage, $totalFiles)) ?> 条，
                        共 <?= number_format($totalFiles) ?> 条记录，第 <?= $page ?> / <?= $totalPages ?> 页
                    </small>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                        <span id="deleteModalTitle">确认删除</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="deleteModalContent"></div>

                    <div class="delete-type-selector" id="deleteTypeSelector" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <h6>选择删除类型：</h6>
                        <div class="delete-option" data-type="soft" onclick="selectDeleteType('soft')" style="margin: 10px 0; padding: 10px; border: 2px solid #dee2e6; border-radius: 5px; cursor: pointer;">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-archive text-warning me-3"></i>
                                <div>
                                    <strong>软删除</strong>
                                    <p class="mb-0 text-muted small">标记为已删除，可以恢复，物理文件保留</p>
                                </div>
                            </div>
                        </div>
                        <div class="delete-option hard" data-type="hard" onclick="selectDeleteType('hard')" style="margin: 10px 0; padding: 10px; border: 2px solid #dc3545; border-radius: 5px; cursor: pointer;">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-trash text-danger me-3"></i>
                                <div>
                                    <strong>硬删除</strong>
                                    <p class="mb-0 text-muted small">永久删除，无法恢复，清理物理文件和数据库记录</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-check mt-3" id="confirmCheckContainer">
                        <input class="form-check-input" type="checkbox" id="confirmCheck">
                        <label class="form-check-label" for="confirmCheck" id="confirmLabel">
                            我确认要执行此删除操作
                        </label>
                    </div>

                    <div class="form-check mt-2" id="hardConfirmContainer" style="display: none;">
                        <input class="form-check-input" type="checkbox" id="hardConfirmCheck">
                        <label class="form-check-label text-danger" for="hardConfirmCheck">
                            我理解硬删除将永久删除文件，无法恢复
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" onclick="executeDelete()" disabled>
                        <i class="bi bi-trash me-1"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>



    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentDeleteData = null;
        let selectedDeleteType = 'soft';

        // 选择删除类型
        function selectDeleteType(type) {
            selectedDeleteType = type;

            // 更新UI
            document.querySelectorAll('.delete-option').forEach(option => {
                option.classList.remove('selected');
                option.style.borderColor = option.classList.contains('hard') ? '#dc3545' : '#dee2e6';
                option.style.backgroundColor = 'transparent';
            });

            const selectedOption = document.querySelector(`[data-type="${type}"]`);
            selectedOption.classList.add('selected');
            if (type === 'hard') {
                selectedOption.style.borderColor = '#dc3545';
                selectedOption.style.backgroundColor = '#f8d7da';
            } else {
                selectedOption.style.borderColor = '#007bff';
                selectedOption.style.backgroundColor = '#e7f3ff';
            }

            // 更新确认标签
            const confirmLabel = document.getElementById('confirmLabel');
            const hardConfirmContainer = document.getElementById('hardConfirmContainer');

            if (type === 'hard') {
                confirmLabel.textContent = '我确认要执行硬删除操作';
                hardConfirmContainer.style.display = 'block';
            } else {
                confirmLabel.textContent = '我确认要执行软删除操作';
                hardConfirmContainer.style.display = 'none';
            }

            updateConfirmButton();
        }

        // 更新确认按钮状态
        function updateConfirmButton() {
            const confirmCheck = document.getElementById('confirmCheck').checked;
            const hardConfirmCheck = document.getElementById('hardConfirmCheck').checked;
            const confirmBtn = document.getElementById('confirmDeleteBtn');

            const canConfirm = confirmCheck && (selectedDeleteType !== 'hard' || hardConfirmCheck);
            confirmBtn.disabled = !canConfirm;

            // 更新按钮样式
            if (selectedDeleteType === 'hard') {
                confirmBtn.className = 'btn btn-danger';
                confirmBtn.innerHTML = '<i class="bi bi-trash me-1"></i>确认硬删除';
            } else {
                confirmBtn.className = 'btn btn-warning';
                confirmBtn.innerHTML = '<i class="bi bi-archive me-1"></i>确认软删除';
            }
        }

        // 监听确认框变化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('confirmCheck').addEventListener('change', updateConfirmButton);
            document.getElementById('hardConfirmCheck').addEventListener('change', updateConfirmButton);
        });

        // 更新批量删除按钮状态
        function updateBatchDeleteButton() {
            const checkboxes = document.querySelectorAll('.file-checkbox:checked');
            const count = checkboxes.length;
            const batchSoftDeleteBtn = document.getElementById('batchSoftDeleteBtn');
            const batchHardDeleteBtn = document.getElementById('batchHardDeleteBtn');
            const selectedCountSpan = document.getElementById('selectedCount');

            selectedCountSpan.textContent = count;
            batchSoftDeleteBtn.disabled = count === 0;
            batchHardDeleteBtn.disabled = count === 0;

            // 更新全选按钮状态
            const allCheckboxes = document.querySelectorAll('.file-checkbox');
            const selectAllBtn = document.getElementById('selectAllBtn');

            if (count === 0) {
                selectAllBtn.innerHTML = '<i class="bi bi-check-square me-1"></i>全选';
            } else if (count === allCheckboxes.length) {
                selectAllBtn.innerHTML = '<i class="bi bi-check-square-fill me-1"></i>取消全选';
            } else {
                selectAllBtn.innerHTML = '<i class="bi bi-dash-square me-1"></i>部分选中';
            }
        }

        // 切换全选状态
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('.file-checkbox');
            const checkedCount = document.querySelectorAll('.file-checkbox:checked').length;
            const shouldCheck = checkedCount === 0;

            checkboxes.forEach(checkbox => {
                checkbox.checked = shouldCheck;
            });

            updateBatchDeleteButton();
        }

        // 单个文件删除
        function singleDelete(fileId, fileName, defaultType = 'soft') {
            currentDeleteData = {
                type: 'single',
                fileIds: [fileId],
                fileNames: [fileName]
            };

            document.getElementById('deleteModalTitle').textContent = '删除文件';
            document.getElementById('deleteModalContent').innerHTML = `
                <p>您要删除以下文件：</p>
                <div class="alert alert-info">
                    <i class="bi bi-file-earmark me-2"></i><strong>${fileName}</strong>
                </div>
            `;

            // 重置选择
            selectDeleteType(defaultType);
            document.getElementById('confirmCheck').checked = false;
            document.getElementById('hardConfirmCheck').checked = false;
            updateConfirmButton();

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // 批量删除
        function batchDeleteFiles(defaultType = 'soft') {
            const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
            if (checkedBoxes.length === 0) {
                showToast('请选择要删除的文件', 'warning');
                return;
            }

            const fileIds = Array.from(checkedBoxes).map(cb => cb.value);
            const fileNames = Array.from(checkedBoxes).map(cb => cb.dataset.fileName);

            currentDeleteData = {
                type: 'batch',
                fileIds: fileIds,
                fileNames: fileNames
            };

            document.getElementById('deleteModalTitle').textContent = `批量删除 ${fileNames.length} 个文件`;
            document.getElementById('deleteModalContent').innerHTML = `
                <p>您要删除以下 ${fileNames.length} 个文件：</p>
                <div class="alert alert-info" style="max-height: 200px; overflow-y: auto;">
                    ${fileNames.map(name => `<div><i class="bi bi-file-earmark me-2"></i>${name}</div>`).join('')}
                </div>
            `;

            // 重置选择
            selectDeleteType(defaultType);
            document.getElementById('confirmCheck').checked = false;
            document.getElementById('hardConfirmCheck').checked = false;
            updateConfirmButton();

            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // 执行删除
        function executeDelete() {
            if (!currentDeleteData) return;

            const confirmBtn = document.getElementById('confirmDeleteBtn');
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>删除中...';

            const formData = new FormData();
            formData.append('file_ids', currentDeleteData.fileIds.join(','));
            formData.append('csrf_token', '<?= $csrfToken ?>');
            formData.append('delete_type', selectedDeleteType);
            formData.append('confirmed', 'true');

            if (selectedDeleteType === 'hard') {
                formData.append('hard_confirmed', 'true');
            }

            fetch('api/admin_batch_delete_files.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');

                    // 移除表格行或刷新页面
                    currentDeleteData.fileIds.forEach(fileId => {
                        const row = document.querySelector(`input[value="${fileId}"]`).closest('tr');
                        if (row) {
                            row.remove();
                        }
                    });

                    // 如果当前页没有文件了，刷新页面
                    if (document.querySelectorAll('#filesTable tbody tr').length === 0) {
                        setTimeout(() => location.reload(), 1000);
                    }

                    updateBatchDeleteButton();
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                showToast('删除失败：网络错误', 'error');
            })
            .finally(() => {
                bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                confirmBtn.disabled = false;
                updateConfirmButton();
                currentDeleteData = null;
            });
        }

        // 显示Toast消息
        function showToast(message, type = 'info') {
            const toastContainer = document.querySelector('.toast-container');
            const toastId = 'toast-' + Date.now();

            const bgClass = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            }[type] || 'bg-info';

            const toastHtml = `
                <div class="toast ${bgClass} text-white" id="${toastId}" role="alert">
                    <div class="toast-body">
                        ${message}
                        <button type="button" class="btn-close btn-close-white float-end" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            toastContainer.insertAdjacentHTML('beforeend', toastHtml);

            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
            toast.show();

            // 自动移除
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateBatchDeleteButton();
            selectDeleteType('soft'); // 默认选择软删除

            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'a') {
                    e.preventDefault();
                    toggleSelectAll();
                }
                if (e.key === 'Delete') {
                    const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
                    if (checkedBoxes.length > 0) {
                        batchDeleteFiles('soft');
                    }
                }
            });
        });
    </script>
</body>
</html>
