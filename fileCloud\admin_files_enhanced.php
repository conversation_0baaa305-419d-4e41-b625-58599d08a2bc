<?php
/**
 * 文件网盘系统 - 增强版管理员文件管理页面
 * 创建时间: 2025-07-08
 * 功能：支持软删除和硬删除的文件管理界面
 */

require_once 'functions.php';

// 检查管理员权限
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

$csrfToken = generateCsrfToken();

// 分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 搜索参数
$search = trim($_GET['search'] ?? '');
$showDeleted = isset($_GET['show_deleted']) && $_GET['show_deleted'] === '1';

// 构建查询条件
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(f.original_name LIKE ? OR u.name LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

if (!$showDeleted) {
    $whereConditions[] = "f.is_deleted = 0";
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 获取文件列表
try {
    $sql = "
        SELECT 
            f.file_id,
            f.original_name,
            f.file_size,
            f.file_type,
            f.upload_time,
            f.download_count,
            f.is_deleted,
            f.file_path,
            u.name as username
        FROM filecloud_info f 
        LEFT JOIN 3_user u ON f.user_id = u.id
        {$whereClause}
        ORDER BY f.upload_time DESC
        LIMIT {$limit} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $files = $stmt->fetchAll();
    
    // 获取总数
    $countSql = "
        SELECT COUNT(*) 
        FROM filecloud_info f 
        LEFT JOIN 3_user u ON f.user_id = u.id
        {$whereClause}
    ";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalFiles = $countStmt->fetchColumn();
    
    $totalPages = ceil($totalFiles / $limit);
    
} catch (PDOException $e) {
    $files = [];
    $totalFiles = 0;
    $totalPages = 0;
    $error = "数据库查询失败：" . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版文件管理 - 管理员面板</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <style>
        .file-checkbox { transform: scale(1.2); }
        .table-responsive { max-height: 70vh; overflow-y: auto; }
        .btn-group-sm .btn { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
        .toast-container { position: fixed; top: 20px; right: 20px; z-index: 1055; }
        .delete-type-selector { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
        .delete-option { margin: 10px 0; padding: 10px; border: 2px solid #dee2e6; border-radius: 5px; cursor: pointer; }
        .delete-option.selected { border-color: #007bff; background-color: #e7f3ff; }
        .delete-option.hard { border-color: #dc3545; }
        .delete-option.hard.selected { border-color: #dc3545; background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="bi bi-files me-2"></i>增强版文件管理</h2>
                    <div>
                        <a href="admin_files.php" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left me-1"></i>返回标准版
                        </a>
                        <a href="admin.php" class="btn btn-outline-primary">
                            <i class="bi bi-house me-1"></i>管理首页
                        </a>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">搜索文件或用户</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?= h($search) ?>" placeholder="输入文件名或用户名">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">显示选项</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showDeleted" 
                                           name="show_deleted" value="1" <?= $showDeleted ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="showDeleted">
                                        包含已删除文件
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                                <a href="?" class="btn btn-outline-secondary">重置</a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 批量操作 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllBtn" onclick="toggleSelectAll()">
                                    <i class="bi bi-check-square me-1"></i>全选
                                </button>
                                <span class="text-muted ms-2">已选择 <span id="selectedCount">0</span> 个文件</span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-warning btn-sm me-2" id="batchSoftDeleteBtn" 
                                        onclick="batchDeleteFiles('soft')" disabled>
                                    <i class="bi bi-archive me-1"></i>批量软删除
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" id="batchHardDeleteBtn" 
                                        onclick="batchDeleteFiles('hard')" disabled>
                                    <i class="bi bi-trash me-1"></i>批量硬删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list me-2"></i>文件列表 
                            <span class="badge bg-secondary"><?= $totalFiles ?></span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger m-3"><?= h($error) ?></div>
                        <?php elseif (empty($files)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-inbox display-1 text-muted"></i>
                                <p class="text-muted mt-3">没有找到文件</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="filesTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="50"><input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()"></th>
                                            <th>文件名</th>
                                            <th>上传者</th>
                                            <th>大小</th>
                                            <th>类型</th>
                                            <th>上传时间</th>
                                            <th>下载次数</th>
                                            <th>状态</th>
                                            <th>文件存在</th>
                                            <th width="120">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($files as $file): ?>
                                            <tr>
                                                <td>
                                                    <input type="checkbox" class="file-checkbox" 
                                                           value="<?= $file['file_id'] ?>" 
                                                           data-file-name="<?= h($file['original_name']) ?>"
                                                           onchange="updateBatchButtons()">
                                                </td>
                                                <td>
                                                    <i class="bi bi-file-earmark me-2 text-primary"></i>
                                                    <?= h($file['original_name']) ?>
                                                </td>
                                                <td><?= h($file['username'] ?: '匿名用户') ?></td>
                                                <td><?= formatFileSize($file['file_size']) ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?= strtoupper($file['file_type']) ?></span>
                                                </td>
                                                <td><?= date('Y-m-d H:i', strtotime($file['upload_time'])) ?></td>
                                                <td><?= $file['download_count'] ?></td>
                                                <td>
                                                    <?php if ($file['is_deleted']): ?>
                                                        <span class="badge bg-danger">已删除</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">正常</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (file_exists($file['file_path'])): ?>
                                                        <span class="badge bg-success">存在</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">缺失</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if (!$file['is_deleted']): ?>
                                                            <button type="button" class="btn btn-outline-warning" 
                                                                    onclick="singleDelete(<?= $file['file_id'] ?>, '<?= h($file['original_name']) ?>', 'soft')"
                                                                    title="软删除">
                                                                <i class="bi bi-archive"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <button type="button" class="btn btn-outline-danger" 
                                                                onclick="singleDelete(<?= $file['file_id'] ?>, '<?= h($file['original_name']) ?>', 'hard')"
                                                                title="硬删除">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                    <nav class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&show_deleted=<?= $showDeleted ? '1' : '0' ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                        <span id="deleteModalTitle">确认删除</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="deleteModalContent"></div>
                    
                    <div class="delete-type-selector" id="deleteTypeSelector">
                        <h6>选择删除类型：</h6>
                        <div class="delete-option" data-type="soft" onclick="selectDeleteType('soft')">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-archive text-warning me-3"></i>
                                <div>
                                    <strong>软删除</strong>
                                    <p class="mb-0 text-muted small">标记为已删除，可以恢复，物理文件保留</p>
                                </div>
                            </div>
                        </div>
                        <div class="delete-option hard" data-type="hard" onclick="selectDeleteType('hard')">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-trash text-danger me-3"></i>
                                <div>
                                    <strong>硬删除</strong>
                                    <p class="mb-0 text-muted small">永久删除，无法恢复，清理物理文件和数据库记录</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check mt-3" id="confirmCheckContainer">
                        <input class="form-check-input" type="checkbox" id="confirmCheck">
                        <label class="form-check-label" for="confirmCheck" id="confirmLabel">
                            我确认要执行此删除操作
                        </label>
                    </div>
                    
                    <div class="form-check mt-2" id="hardConfirmContainer" style="display: none;">
                        <input class="form-check-input" type="checkbox" id="hardConfirmCheck">
                        <label class="form-check-label text-danger" for="hardConfirmCheck">
                            我理解硬删除将永久删除文件，无法恢复
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" onclick="executeDelete()" disabled>
                        <i class="bi bi-trash me-1"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div class="toast-container"></div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentDeleteData = null;
        let selectedDeleteType = 'soft';

        // 选择删除类型
        function selectDeleteType(type) {
            selectedDeleteType = type;
            
            // 更新UI
            document.querySelectorAll('.delete-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('selected');
            
            // 更新确认标签
            const confirmLabel = document.getElementById('confirmLabel');
            const hardConfirmContainer = document.getElementById('hardConfirmContainer');
            
            if (type === 'hard') {
                confirmLabel.textContent = '我确认要执行硬删除操作';
                hardConfirmContainer.style.display = 'block';
            } else {
                confirmLabel.textContent = '我确认要执行软删除操作';
                hardConfirmContainer.style.display = 'none';
            }
            
            updateConfirmButton();
        }

        // 更新确认按钮状态
        function updateConfirmButton() {
            const confirmCheck = document.getElementById('confirmCheck').checked;
            const hardConfirmCheck = document.getElementById('hardConfirmCheck').checked;
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            
            const canConfirm = confirmCheck && (selectedDeleteType !== 'hard' || hardConfirmCheck);
            confirmBtn.disabled = !canConfirm;
            
            // 更新按钮样式
            if (selectedDeleteType === 'hard') {
                confirmBtn.className = 'btn btn-danger';
                confirmBtn.innerHTML = '<i class="bi bi-trash me-1"></i>确认硬删除';
            } else {
                confirmBtn.className = 'btn btn-warning';
                confirmBtn.innerHTML = '<i class="bi bi-archive me-1"></i>确认软删除';
            }
        }

        // 监听确认框变化
        document.getElementById('confirmCheck').addEventListener('change', updateConfirmButton);
        document.getElementById('hardConfirmCheck').addEventListener('change', updateConfirmButton);

        // 单个文件删除
        function singleDelete(fileId, fileName, defaultType = 'soft') {
            currentDeleteData = {
                type: 'single',
                fileIds: [fileId],
                fileNames: [fileName]
            };
            
            document.getElementById('deleteModalTitle').textContent = '删除文件';
            document.getElementById('deleteModalContent').innerHTML = `
                <p>您要删除以下文件：</p>
                <div class="alert alert-info">
                    <i class="bi bi-file-earmark me-2"></i><strong>${fileName}</strong>
                </div>
            `;
            
            // 重置选择
            selectDeleteType(defaultType);
            document.getElementById('confirmCheck').checked = false;
            document.getElementById('hardConfirmCheck').checked = false;
            updateConfirmButton();
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // 批量删除
        function batchDeleteFiles(defaultType = 'soft') {
            const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
            if (checkedBoxes.length === 0) {
                showToast('请选择要删除的文件', 'warning');
                return;
            }
            
            const fileIds = Array.from(checkedBoxes).map(cb => cb.value);
            const fileNames = Array.from(checkedBoxes).map(cb => cb.dataset.fileName);
            
            currentDeleteData = {
                type: 'batch',
                fileIds: fileIds,
                fileNames: fileNames
            };
            
            document.getElementById('deleteModalTitle').textContent = `批量删除 ${fileNames.length} 个文件`;
            document.getElementById('deleteModalContent').innerHTML = `
                <p>您要删除以下 ${fileNames.length} 个文件：</p>
                <div class="alert alert-info" style="max-height: 200px; overflow-y: auto;">
                    ${fileNames.map(name => `<div><i class="bi bi-file-earmark me-2"></i>${name}</div>`).join('')}
                </div>
            `;
            
            // 重置选择
            selectDeleteType(defaultType);
            document.getElementById('confirmCheck').checked = false;
            document.getElementById('hardConfirmCheck').checked = false;
            updateConfirmButton();
            
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // 执行删除
        function executeDelete() {
            if (!currentDeleteData) return;
            
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>删除中...';
            
            const formData = new FormData();
            formData.append('file_ids', currentDeleteData.fileIds.join(','));
            formData.append('csrf_token', '<?= $csrfToken ?>');
            formData.append('delete_type', selectedDeleteType);
            formData.append('confirmed', 'true');
            
            if (selectedDeleteType === 'hard') {
                formData.append('hard_confirmed', 'true');
            }
            
            fetch('api/admin_batch_delete_files.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    
                    // 移除表格行或刷新页面
                    currentDeleteData.fileIds.forEach(fileId => {
                        const row = document.querySelector(`input[value="${fileId}"]`).closest('tr');
                        if (row) {
                            row.remove();
                        }
                    });
                    
                    // 如果当前页没有文件了，刷新页面
                    if (document.querySelectorAll('#filesTable tbody tr').length === 0) {
                        setTimeout(() => location.reload(), 1000);
                    }
                    
                    updateBatchButtons();
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                showToast('删除失败：网络错误', 'error');
            })
            .finally(() => {
                bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                confirmBtn.disabled = false;
                updateConfirmButton();
                currentDeleteData = null;
            });
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const fileCheckboxes = document.querySelectorAll('.file-checkbox');
            
            fileCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            
            updateBatchButtons();
        }

        // 更新批量操作按钮状态
        function updateBatchButtons() {
            const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
            const count = checkedBoxes.length;
            
            document.getElementById('selectedCount').textContent = count;
            document.getElementById('batchSoftDeleteBtn').disabled = count === 0;
            document.getElementById('batchHardDeleteBtn').disabled = count === 0;
            
            // 更新全选按钮状态
            const allCheckboxes = document.querySelectorAll('.file-checkbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const selectAllBtn = document.getElementById('selectAllBtn');
            
            if (count === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
                selectAllBtn.innerHTML = '<i class="bi bi-check-square me-1"></i>全选';
            } else if (count === allCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
                selectAllBtn.innerHTML = '<i class="bi bi-square me-1"></i>取消全选';
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllBtn.innerHTML = '<i class="bi bi-dash-square me-1"></i>部分选中';
            }
        }

        // 显示Toast消息
        function showToast(message, type = 'info') {
            const toastContainer = document.querySelector('.toast-container');
            const toastId = 'toast-' + Date.now();
            
            const bgClass = {
                'success': 'bg-success',
                'error': 'bg-danger',
                'warning': 'bg-warning',
                'info': 'bg-info'
            }[type] || 'bg-info';
            
            const toastHtml = `
                <div class="toast ${bgClass} text-white" id="${toastId}" role="alert">
                    <div class="toast-body">
                        ${message}
                        <button type="button" class="btn-close btn-close-white float-end" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
            toast.show();
            
            // 自动移除
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateBatchButtons();
            selectDeleteType('soft'); // 默认选择软删除
        });
    </script>
</body>
</html>
