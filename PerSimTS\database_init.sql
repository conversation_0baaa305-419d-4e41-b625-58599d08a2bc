-- 人员调动模拟系统数据库初始化脚本
-- 创建时间: 2025-07-14

USE application;

-- 1. 模拟方案表
CREATE TABLE IF NOT EXISTS sim_scenarios (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '方案ID',
  scenario_name VARCHAR(100) NOT NULL COMMENT '方案名称',
  description TEXT COMMENT '方案描述',
  created_by INT NOT NULL COMMENT '创建者用户ID',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
  data_source_date DATE COMMENT '数据源日期',
  INDEX idx_created_by (created_by),
  INDEX idx_active (is_active),
  FOREIGN KEY (created_by) REFERENCES 3_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模拟方案表';

-- 2. 模拟单位表
CREATE TABLE IF NOT EXISTS sim_units (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模拟单位ID',
  scenario_id INT NOT NULL COMMENT '所属方案ID',
  original_unit_id INT COMMENT '原始单位ID（来源于2_unit）',
  unit_name VARCHAR(255) NOT NULL COMMENT '单位名称',
  parent_id INT DEFAULT NULL COMMENT '上级单位ID',
  sort_order INT NOT NULL DEFAULT 0 COMMENT '排序字段',
  code VARCHAR(255) COMMENT '组织机构代码',
  unit_level INT DEFAULT 1 COMMENT '单位层级',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除',
  INDEX idx_scenario (scenario_id),
  INDEX idx_parent (parent_id),
  INDEX idx_original (original_unit_id),
  FOREIGN KEY (scenario_id) REFERENCES sim_scenarios(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模拟单位表';

-- 3. 模拟人员表
CREATE TABLE IF NOT EXISTS sim_personnel (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模拟人员ID',
  scenario_id INT NOT NULL COMMENT '所属方案ID',
  original_user_id INT NOT NULL COMMENT '原始用户ID（来源于3_user）',
  name VARCHAR(50) NOT NULL COMMENT '姓名',
  id_number CHAR(18) COMMENT '身份证号',
  phone VARCHAR(11) COMMENT '手机号码',
  personnel_type VARCHAR(100) COMMENT '人员身份',
  organization_unit INT COMMENT '当前所属单位ID（关联sim_units.id）',
  police_number VARCHAR(20) COMMENT '警号/辅警号',
  position VARCHAR(100) COMMENT '职务',
  job_rank VARCHAR(50) COMMENT '职级',
  transfer_count INT DEFAULT 0 COMMENT '调动次数',
  last_transfer_time DATETIME COMMENT '最后调动时间',
  is_active TINYINT(1) DEFAULT 1 COMMENT '是否在职',
  INDEX idx_scenario (scenario_id),
  INDEX idx_original_user (original_user_id),
  INDEX idx_organization (organization_unit),
  INDEX idx_id_number (id_number),
  FOREIGN KEY (scenario_id) REFERENCES sim_scenarios(id) ON DELETE CASCADE,
  FOREIGN KEY (organization_unit) REFERENCES sim_units(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模拟人员表';

-- 4. 调动日志表
CREATE TABLE IF NOT EXISTS sim_transfer_logs (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
  scenario_id INT NOT NULL COMMENT '所属方案ID',
  personnel_id INT NOT NULL COMMENT '人员ID（关联sim_personnel.id）',
  from_unit_id INT COMMENT '源单位ID',
  to_unit_id INT COMMENT '目标单位ID',
  from_unit_name VARCHAR(255) COMMENT '源单位名称',
  to_unit_name VARCHAR(255) COMMENT '目标单位名称',
  transfer_type ENUM('manual', 'batch', 'import') DEFAULT 'manual' COMMENT '调动类型',
  transfer_reason VARCHAR(500) COMMENT '调动原因',
  operator_id INT NOT NULL COMMENT '操作者ID',
  operator_name VARCHAR(50) COMMENT '操作者姓名',
  transfer_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '调动时间',
  remarks TEXT COMMENT '备注',
  INDEX idx_scenario (scenario_id),
  INDEX idx_personnel (personnel_id),
  INDEX idx_transfer_time (transfer_time),
  INDEX idx_operator (operator_id),
  FOREIGN KEY (scenario_id) REFERENCES sim_scenarios(id) ON DELETE CASCADE,
  FOREIGN KEY (personnel_id) REFERENCES sim_personnel(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调动日志表';

-- 5. 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
  user_id INT NOT NULL COMMENT '用户ID',
  scenario_id INT DEFAULT NULL COMMENT '方案ID',
  operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
  operation_content TEXT NOT NULL COMMENT '操作内容',
  status ENUM('success','failed') NOT NULL DEFAULT 'success' COMMENT '操作状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  INDEX idx_user_id (user_id),
  INDEX idx_scenario_id (scenario_id),
  INDEX idx_created_at (created_at),
  INDEX idx_operation_type (operation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 插入测试数据（可选）
-- INSERT INTO sim_scenarios (scenario_name, description, created_by) VALUES
-- ('测试方案', '用于系统测试的模拟方案', 1);

SELECT 'PerSimTS数据库表创建完成！' as status;
