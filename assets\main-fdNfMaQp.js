import{_ as F,c as i,r,o as a,w as n,a as _,b as e,d as u,F as f,e as g,u as m,n as y,t as c,E as I,f as N,i as P,g as V}from"./index-CUCRdtpH.js";import{_ as $}from"./logo-LbKsNqzq.js";/* empty css              */const L={class:"category-container"},S={class:"app-category"},j={class:"card-grid"},z=["onClick"],D={class:"app-info"},O={class:"app-category"},T={class:"card-grid"},q=["onClick"],G={class:"app-info"},H={class:"app-category"},J={class:"card-grid"},K=["onClick"],M={class:"app-info"},Q={__name:"Front",setup(v){const l=[{id:1,name:"通讯录",path:"/contact",desc:"通讯录管理",color:"grey",category:"public"},{id:3,name:"资产管理",path:"/asset",desc:"岳池县公安局资产管理",color:"grey",category:"public"},{id:2,name:"系统管理",path:"/system",desc:"组织机构与部门管理",color:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)",category:"system"},{id:8,name:"数据大屏",path:"/dashboard",desc:"数据可视化分析",color:"linear-gradient(135deg, #2f54eb 0%, #5c70ff 100%)",category:"private"},{id:9,name:"文件快递柜",path:"fileCloud",desc:"文件上传与下载",color:"linear-gradient(135deg, #fa541c 0%, #ff8968 100%)",category:"private"},{id:10,name:"学生信息查询系统",path:"schInfo",desc:"学生信息查询",color:"linear-gradient(135deg, #fa541c 0%, #ff8968 100%)",category:"private"}],b=l.filter(t=>t.category==="public"),C=l.filter(t=>t.category==="private"),w=l.filter(t=>t.category==="system"),d=t=>{if(t==="/contact"||t==="/asset"){I({title:"提示",message:"应用开发中！",type:"info",duration:3e3});return}t==="schInfo"||t==="fileCloud"?window.location.href=t:window.location.href="back.html"};return(t,s)=>{const x=r("el-header"),p=r("el-card"),A=r("el-main"),B=r("el-footer"),E=r("el-container");return a(),i(E,{class:"portal-container"},{default:n(()=>[_(x,{class:"header"},{default:n(()=>s[0]||(s[0]=[e("div",{class:"header-left"},[e("img",{src:$,alt:"Logo",class:"header-logo"}),e("span",{class:"logo"},"CloudPivot")],-1)])),_:1,__:[0]}),_(A,null,{default:n(()=>[e("div",L,[e("div",S,[s[1]||(s[1]=e("h2",{class:"category-title"},"公用应用",-1)),e("div",j,[(a(!0),u(f,null,g(m(b),o=>(a(),i(p,{key:o.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:n(()=>[e("div",{class:"card-content",onClick:k=>d(o.path)},[e("div",{class:"app-icon",style:y({background:o.color})},null,4),e("div",D,[e("h3",null,c(o.name),1),e("p",null,c(o.desc),1)])],8,z)]),_:2},1024))),128))])]),e("div",O,[s[2]||(s[2]=e("h2",{class:"category-title"},"专用应用",-1)),e("div",T,[(a(!0),u(f,null,g(m(C),o=>(a(),i(p,{key:o.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:n(()=>[e("div",{class:"card-content",onClick:k=>d(o.path)},[e("div",{class:"app-icon",style:y({background:o.color})},null,4),e("div",G,[e("h3",null,c(o.name),1),e("p",null,c(o.desc),1)])],8,q)]),_:2},1024))),128))])]),e("div",H,[s[3]||(s[3]=e("h2",{class:"category-title"},"系统应用",-1)),e("div",J,[(a(!0),u(f,null,g(m(w),o=>(a(),i(p,{key:o.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:n(()=>[e("div",{class:"card-content",onClick:k=>d(o.path)},[e("div",{class:"app-icon",style:y({background:o.color})},null,4),e("div",M,[e("h3",null,c(o.name),1),e("p",null,c(o.desc),1)])],8,K)]),_:2},1024))),128))])])])]),_:1}),_(B,{class:"portal-footer"},{default:n(()=>s[4]||(s[4]=[e("p",null,"Powered by ：科技通信中队",-1)])),_:1,__:[4]})]),_:1})}}},R=F(Q,[["__scopeId","data-v-21f87401"]]),h=N(R);h.use(P);for(const[v,l]of Object.entries(V))h.component(v,l);h.mount("#app");
