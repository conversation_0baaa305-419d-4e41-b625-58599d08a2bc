# 人员调动模拟系统 (PerSimTS) 开发指南

## 项目概述

人员调动模拟系统是一个基于现有数据库架构的独立模拟平台，允许用户在不影响生产数据的情况下进行人员调动的模拟操作，支持统计分析和数据导出功能。

## 技术架构

### 技术栈
- **后端**: PHP + MySQL
- **前端**: HTML5 + JavaScript ES6 + CSS3
- **数据库**: 基于现有application数据库扩展
- **API设计**: RESTful风格，遵循现有命名规范

### 系统依赖
- 现有conn_waf.php连接配置

## 数据库设计

### 核心表结构

#### 1. sim_scenarios (模拟方案表)
```sql
CREATE TABLE sim_scenarios (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '方案ID',
  scenario_name VARCHAR(100) NOT NULL COMMENT '方案名称',
  description TEXT COMMENT '方案描述',
  created_by INT NOT NULL COMMENT '创建者用户ID',
  created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
  data_source_date DATE COMMENT '数据源日期',
  INDEX idx_created_by (created_by),
  INDEX idx_active (is_active),
  FOREIGN KEY (created_by) REFERENCES 3_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模拟方案表';
```

#### 2. sim_units (模拟单位表)
```sql
CREATE TABLE sim_units (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模拟单位ID',
  scenario_id INT NOT NULL COMMENT '所属方案ID',
  original_unit_id INT COMMENT '原始单位ID（来源于2_unit）',
  unit_name VARCHAR(255) NOT NULL COMMENT '单位名称',
  parent_id INT DEFAULT NULL COMMENT '上级单位ID',
  sort_order INT NOT NULL DEFAULT 0 COMMENT '排序字段',
  code VARCHAR(255) COMMENT '组织机构代码',
  unit_level INT DEFAULT 1 COMMENT '单位层级',
  is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除',
  INDEX idx_scenario (scenario_id),
  INDEX idx_parent (parent_id),
  INDEX idx_original (original_unit_id),
  FOREIGN KEY (scenario_id) REFERENCES sim_scenarios(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模拟单位表';
```

#### 3. sim_personnel (模拟人员表)
```sql
CREATE TABLE sim_personnel (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '模拟人员ID',
  scenario_id INT NOT NULL COMMENT '所属方案ID',
  original_user_id INT NOT NULL COMMENT '原始用户ID（来源于3_user）',
  name VARCHAR(50) NOT NULL COMMENT '姓名',
  id_number CHAR(18) NOT NULL COMMENT '身份证号',
  phone VARCHAR(11) COMMENT '手机号码',
  personnel_type VARCHAR(100) COMMENT '人员身份',
  organization_unit INT COMMENT '当前所属单位ID（关联sim_units.id）',
  police_number VARCHAR(20) COMMENT '警号/辅警号',
  position VARCHAR(100) COMMENT '职务',
  job_rank VARCHAR(50) COMMENT '职级',
  transfer_count INT DEFAULT 0 COMMENT '调动次数',
  last_transfer_time DATETIME COMMENT '最后调动时间',
  is_active TINYINT(1) DEFAULT 1 COMMENT '是否在职',
  INDEX idx_scenario (scenario_id),
  INDEX idx_original_user (original_user_id),
  INDEX idx_organization (organization_unit),
  INDEX idx_id_number (id_number),
  FOREIGN KEY (scenario_id) REFERENCES sim_scenarios(id) ON DELETE CASCADE,
  FOREIGN KEY (organization_unit) REFERENCES sim_units(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模拟人员表';
```

#### 4. sim_transfer_logs (调动日志表)
```sql
CREATE TABLE sim_transfer_logs (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
  scenario_id INT NOT NULL COMMENT '所属方案ID',
  personnel_id INT NOT NULL COMMENT '人员ID（关联sim_personnel.id）',
  from_unit_id INT COMMENT '源单位ID',
  to_unit_id INT COMMENT '目标单位ID',
  from_unit_name VARCHAR(255) COMMENT '源单位名称',
  to_unit_name VARCHAR(255) COMMENT '目标单位名称',
  transfer_type ENUM('manual', 'batch', 'import') DEFAULT 'manual' COMMENT '调动类型',
  transfer_reason VARCHAR(500) COMMENT '调动原因',
  operator_id INT NOT NULL COMMENT '操作者ID',
  operator_name VARCHAR(50) COMMENT '操作者姓名',
  transfer_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '调动时间',
  remarks TEXT COMMENT '备注',
  INDEX idx_scenario (scenario_id),
  INDEX idx_personnel (personnel_id),
  INDEX idx_transfer_time (transfer_time),
  INDEX idx_operator (operator_id),
  FOREIGN KEY (scenario_id) REFERENCES sim_scenarios(id) ON DELETE CASCADE,
  FOREIGN KEY (personnel_id) REFERENCES sim_personnel(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调动日志表';
```

## API接口设计

### 1. 方案管理接口 (personnel_simulation_manage.php)

#### 创建模拟方案
```php
POST /api/personnel_simulation_manage.php?operation=createScenario
参数: {
  "scenario_name": "2024年人员调动方案",
  "description": "基于当前组织架构的调动模拟",
  "copy_data": true
}
返回: {
  "status": 1,
  "message": "方案创建成功",
  "data": {
    "scenario_id": 1,
    "units_count": 25,
    "personnel_count": 150
  }
}
```

#### 获取方案列表
```php
POST /api/personnel_simulation_manage.php?operation=getScenarios
返回: {
  "status": 1,
  "data": [
    {
      "id": 1,
      "scenario_name": "2024年人员调动方案",
      "description": "基于当前组织架构的调动模拟",
      "created_time": "2024-07-14 10:00:00",
      "personnel_count": 150,
      "units_count": 25,
      "is_active": 1
    }
  ]
}
```

#### 人员调动操作
```php
POST /api/personnel_simulation_manage.php?operation=transferPersonnel
参数: {
  "scenario_id": 1,
  "personnel_ids": [1, 2, 3],
  "target_unit_id": 5,
  "transfer_reason": "工作需要",
  "remarks": "批量调动"
}
返回: {
  "status": 1,
  "message": "调动成功",
  "data": {
    "transferred_count": 3,
    "failed_count": 0,
    "transfer_log_ids": [101, 102, 103]
  }
}
```

### 2. 查询统计接口

#### 获取单位统计信息
```php
POST /api/personnel_simulation_manage.php?operation=getUnitStatistics
参数: {
  "scenario_id": 1,
  "unit_id": null  // null表示所有单位
}
返回: {
  "status": 1,
  "data": [
    {
      "unit_id": 1,
      "unit_name": "九龙所",
      "parent_unit_name": "某某分局",
      "police_list": [
        {"name": "张三", "police_number": "001234", "position": "所长"},
        {"name": "李四", "police_number": "001235", "position": "副所长"}
      ],
      "police_count": 2,
      "auxiliary_list": [
        {"name": "王五", "personnel_type": "辅警"},
        {"name": "赵六", "personnel_type": "辅警"}
      ],
      "auxiliary_count": 2,
      "civilian_list": [
        {"name": "孙七", "personnel_type": "文职"}
      ],
      "civilian_count": 1,
      "total_count": 5,
      "transfer_in_count": 2,
      "transfer_out_count": 1
    }
  ]
}
```

#### 获取人员列表
```php
POST /api/personnel_simulation_manage.php?operation=getPersonnelList
参数: {
  "scenario_id": 1,
  "unit_id": 1,
  "page": 1,
  "pagesize": 20,
  "search_keyword": ""
}
```

### 3. 数据导出接口 (simulation_export.php)

#### Excel导出
```php
GET /api/simulation_export.php?type=personnel&scenario_id=1&unit_id=1
GET /api/simulation_export.php?type=statistics&scenario_id=1
GET /api/simulation_export.php?type=transfer_logs&scenario_id=1
```

## 前端界面设计

### 1. 主界面布局 (index.html)
```html
<!DOCTYPE html>
<html>
<head>
    <title>人员调动模拟系统</title>
    <link rel="stylesheet" href="simulation.css">
</head>
<body>
    <div class="container">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <select id="scenarioSelect">
                <option value="">选择模拟方案</option>
            </select>
            <button id="createScenarioBtn">新建方案</button>
            <button id="exportBtn">导出数据</button>
        </div>
        
        <!-- 主要内容区 -->
        <div class="main-content">
            <!-- 左侧单位树 -->
            <div class="left-panel">
                <div class="unit-tree" id="unitTree"></div>
            </div>
            
            <!-- 右侧人员列表 -->
            <div class="right-panel">
                <div class="personnel-header">
                    <h3 id="currentUnitName">请选择单位</h3>
                    <div class="actions">
                        <button id="batchTransferBtn">批量调动</button>
                        <button id="statisticsBtn">统计信息</button>
                    </div>
                </div>
                <div class="personnel-list" id="personnelList"></div>
            </div>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div id="transferModal" class="modal">
        <div class="modal-content">
            <h3>人员调动</h3>
            <div class="transfer-form">
                <!-- 调动表单内容 -->
            </div>
        </div>
    </div>
    
    <script src="simulation.js"></script>
</body>
</html>
```

### 2. 样式设计要点 (simulation.css)
- 采用现代扁平化设计风格
- 响应式布局，支持不同屏幕尺寸
- 清晰的视觉层次和交互反馈
- 统一的色彩方案和字体规范

### 3. 交互逻辑 (simulation.js)
- 单位树的展开/折叠
- 人员的多选操作
- 拖拽调动功能
- 实时数据更新
- 操作确认和错误处理

## 开发实施计划

### 阶段1: 基础架构搭建 (预计2天)
1. 创建数据库表结构
2. 搭建基础API框架
3. 实现数据初始化功能
4. 基础权限验证集成

### 阶段2: 核心功能开发 (预计3天)
1. 方案管理功能
2. 人员调动核心逻辑
3. 日志记录系统
4. 基础查询接口

### 阶段3: 统计分析功能 (预计2天)
1. 单位统计报表
2. 调动历史分析
3. 数据对比功能
4. 图表展示组件

### 阶段4: 数据导出功能 (预计1天)
1. Excel导出实现
2. 多格式支持
3. 自定义导出字段
4. 大数据量处理优化

### 阶段5: 前端界面开发 (预计3天)
1. 主界面布局实现
2. 单位树组件开发
3. 人员列表组件
4. 调动操作界面

### 阶段6: 用户体验优化 (预计2天)
1. 交互动效实现
2. 响应式适配
3. 操作流程优化
4. 错误处理完善

### 阶段7: 测试和部署 (预计2天)
1. 功能测试
2. 性能测试
3. 安全性测试
4. 部署和文档完善

## 质量保证

### 代码规范
- 遵循PSR-4自动加载规范
- 统一的命名约定
- 完整的注释文档
- 错误处理机制

### 安全措施
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证
- 输入数据验证

### 性能优化
- 数据库索引优化
- 查询语句优化
- 前端资源压缩
- 缓存策略实施

## 部署说明

### 环境要求
- PHP 8.0+
- MySQL 5.7+
- Web服务器 (Apache/Nginx)
- 现有系统权限集成
- 如有引用到网络文件如“图片，CSS，JS”等，需下载到本地进行引用（因为项目需隔离互联网）

### 安装步骤
1. 创建PerSimTS目录结构
2. 执行数据库初始化脚本
3. 配置API接口权限
4. 部署前端资源文件
5. 测试系统功能完整性

此开发指南将作为整个项目的技术蓝图，确保开发过程的规范性和系统的可维护性。
