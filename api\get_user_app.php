<?php
header('Content-Type: application/json');
require_once '../config.php';



// 获取当前用户信息
$userId = $_SESSION['user_id'];
$personnelType = $_SESSION['personnel_type'] ?? 0;
$isAdminUser = isAdmin();

// 数据库连接
$conn = $GLOBALS['conn'];
if (!$conn) {
    echo json_encode([
        'status' => 0,
        'message' => '数据库连接失败',
        'data' => []
    ]);
    exit;
}

// 获取用户有权访问的应用ID
function getUserAccessibleAppIds($userId, $personnelType, $isAdminUser, $conn) {
    $appIds = [];

    // 系统管理员拥有所有权限
    if ($isAdminUser) {
        $sql = "SELECT id FROM 5_application";
        $result = mysqli_query($conn, $sql);
        while ($row = mysqli_fetch_assoc($result)) {
            $appIds[] = $row['id'];
        }
        return $appIds;
    }

    // 获取用户作为应用管理员的应用
    $sql = "SELECT id FROM 5_application";
    $result = mysqli_query($conn, $sql);
    $hasAppAdminRole = false;
    while ($row = mysqli_fetch_assoc($result)) {
        $appId = $row['id'];
        if (isAppAdmin($appId)) {
            $appIds[] = $appId;
            $hasAppAdminRole = true;
        }
    }

    // 如果用户是应用管理员，添加"授权管理"应用
    if ($hasAppAdminRole) {
        // 查询"授权管理"应用的ID（假设应用名称为"授权管理"）
        $authSql = "SELECT id FROM 5_application WHERE application_name = '授权管理'";
        $authResult = mysqli_query($conn, $authSql);
        if ($authResult && mysqli_num_rows($authResult) > 0) {
            $authRow = mysqli_fetch_assoc($authResult);
            $appIds[] = $authRow['id'];
        }
    }

    // 获取公共应用和人员类型匹配的应用
    $sql = "SELECT id FROM 5_application WHERE public = 0 OR public = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $personnelType);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    while ($row = mysqli_fetch_assoc($result)) {
        $appIds[] = $row['id'];
    }

    // 获取基于角色授权的应用
    $sql = "SELECT DISTINCT a.id FROM 5_application a
            JOIN 3_user_Role ur ON a.id = ur.appId
            WHERE ur.userId = ? AND JSON_CONTAINS(a.roleList, CAST(ur.roleId AS JSON))";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    while ($row = mysqli_fetch_assoc($result)) {
        $appIds[] = $row['id'];
    }

    // 去重并返回
    return array_unique($appIds);
}

// 获取有权访问的应用ID列表
$accessibleAppIds = getUserAccessibleAppIds($userId, $personnelType, $isAdminUser, $conn);

if (empty($accessibleAppIds)) {
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => []
    ]);
    exit;
}

// 获取应用列表并构建层次结构
function buildAppTree($appIds, $conn) {
    // 获取所有有权访问的应用
    $placeholders = implode(',', array_fill(0, count($appIds), '?'));
    $sql = "SELECT * FROM 5_application WHERE id IN ($placeholders) ORDER BY sort_order";
    $stmt = mysqli_prepare($conn, $sql);
    $types = str_repeat('i', count($appIds));
    mysqli_stmt_bind_param($stmt, $types, ...$appIds);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $apps = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // 处理roleList字段（JSON字符串转数组）
        $row['roleList'] = json_decode($row['roleList'] ?? '[]', true) ?? [];
        $apps[] = $row;
    }

    // 构建树形结构
    $appMap = [];
    $tree = [];

    // 创建应用ID到应用的映射
    foreach ($apps as $app) {
        $app['children'] = [];
        $appMap[$app['id']] = $app;
    }

    // 构建父子关系
    foreach ($apps as $app) {
        $parentId = $app['parent_id'];
        if ($parentId == 0) {
            $tree[] = &$appMap[$app['id']];
        } elseif (isset($appMap[$parentId])) {
            $appMap[$parentId]['children'][] = &$appMap[$app['id']];
        }
    }

    return $tree;
}

// 构建应用树
$appTree = buildAppTree($accessibleAppIds, $conn);

// 返回结果
echo json_encode([
    'status' => 1,
    'message' => '查询成功',
    'data' => $appTree
]);

// 关闭数据库连接
mysqli_close($conn);
?>