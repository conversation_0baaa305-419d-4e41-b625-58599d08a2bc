<?php
require_once '../config.php';

function AddOperation() {
    global $application_name, $url, $public, $roleList,$conn;
    $parent_id = $_POST['parent_id'] ?? 0;
    $sort_order = $_POST['sort_order'] ?? 0;
    if (empty($application_name)) {
        throw new Exception('应用名称不能为空');
    }
    $sql = "INSERT INTO 5_application (application_name, url, public, roleList, parent_id, sort_order) VALUES (?, ?, ?, ?, ?, ?)";
    $params = [$application_name, $url, $public, $roleList, $parent_id, $sort_order];
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'ssisii', $params[0], $params[1], $params[2], $params[3], $params[4], $params[5]);
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('插入失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('插入失败: 没有影响任何行');
    }

    return ['status' => 1, 'message' => '插入成功', 'data' => []];
}

function UpdateOperation() {
    global $id, $application_name, $url, $public, $roleList, $conn;
    $parent_id = $_POST['parent_id'] ?? null;
    $sort_order = $_POST['sort_order'] ?? null;
    if (empty($id)) {
        throw new Exception('缺少必要参数');
    }
    
    // 构建动态更新字段和参数
    $setClauses = ['application_name = ?', 'url = ?', 'public = ?', 'roleList = ?'];
    $params = [$application_name, $url, $public, $roleList];
    $types = 'ssis';
    
    // 条件添加parent_id
    if ($parent_id !== null && $parent_id !== '') {
        $setClauses[] = 'parent_id = ?';
        $params[] = $parent_id;
        $types .= 'i';
    }
    
    // 条件添加sort_order
    if ($sort_order !== null && $sort_order !== '') {
        $setClauses[] = 'sort_order = ?';
        $params[] = $sort_order;
        $types .= 'i';
    }
    
    // 添加WHERE条件参数
    $params[] = $id;
    $types .= 'i';
    
    $sql = "UPDATE 5_application SET " . implode(', ', $setClauses) . " WHERE id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('修改失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('修改失败: 没有影响任何行');
    }
    
    return ['status' => 1, 'message' => '修改成功', 'data' => [
        'id' => $id,
        'parent_id' => $parent_id,
        'sort_order' => $sort_order,
        'application_name' => $application_name,
        'url' => $url,
        'public' => $public,
        'roleList' => $roleList
    ]];
}

function DeleteOperation() {
    global $id, $conn;
    
    if (empty($id)) {
        throw new Exception('缺少必要参数');
    }
    
    $sql = "DELETE FROM 5_application WHERE id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $id);
    $executeResult = mysqli_stmt_execute($stmt);
    if ($executeResult === false) {
        throw new Exception('删除失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('删除失败: 没有影响任何行');
    }
    
    return ['status' => 1, 'message' => '删除成功', 'data' => []];
}

function QueryOperation() {
    global $id, $conn;
    
    // 应用查询 - 查询所有应用记录以构建树形结构
    $appSql = "SELECT * FROM 5_application WHERE application_name != 'system'";
    $resultQuery = mysqli_query($conn, $appSql);

    $applicationData = [];
    if (mysqli_num_rows($resultQuery) > 0) {
        while ($row = mysqli_fetch_assoc($resultQuery)) {
            $applicationData[] = $row;
        }
    }

    // 构建树形结构
    $nodes = [];
    $tree = [];

    // 首先将所有节点存入映射，并初始化children数组
    foreach ($applicationData as $item) {
        $item['children'] = [];
        // 将ID转换为整数确保类型一致
        $nodeId = (int)$item['id'];
        $nodes[$nodeId] = $item;
    }

    // 遍历节点，将子节点添加到父节点的children中（使用引用传递）
    foreach ($nodes as &$node) {
        $parentId = (int)$node['parent_id'];
        if ($parentId != 0 && isset($nodes[$parentId])) {
            $nodes[$parentId]['children'][] = &$node;
        }
    }
    unset($node); // 解除引用

    // 收集所有根节点（parent_id=0）
    foreach ($nodes as $node) {
        if ((int)$node['parent_id'] == 0) {
            $tree[] = $node;
        }
    }

    // 递归排序函数 - 按sort_order升序排列
    function sortChildren(&$nodes) {
        // 先排序当前层级
        usort($nodes, function($a, $b) {
            return (int)$a['sort_order'] - (int)$b['sort_order'];
        });
        // 递归排序子节点
        foreach ($nodes as &$node) {
            if (!empty($node['children'])) {
                sortChildren($node['children']);
            }
        }
        unset($node); // 解除引用
    }

    // 对根节点进行排序
    sortChildren($tree);

    // 如果提供了id参数，筛选特定节点及其子树
    if (!empty($id)) {
        $targetId = (int)$id;
        $filteredTree = [];
        
        // 递归查找节点
        function findNode($node, $targetId) {
            if ((int)$node['id'] == $targetId) {
                return $node;
            }
            foreach ($node['children'] as $child) {
                $found = findNode($child, $targetId);
                if ($found) {
                    return $found;
                }
            }
            return null;
        }
        
        // 在树形结构中查找目标节点
        foreach ($tree as $node) {
            $foundNode = findNode($node, $targetId);
            if ($foundNode) {
                $filteredTree[] = $foundNode;
                break;
            }
        }
        
        $applicationData = $filteredTree;
    } else {
        $applicationData = $tree;
    }

    // 收集所有应用的角色ID
    $roleIds = [];
    foreach ($applicationData as $app) {
        $appRoleIds = json_decode($app['roleList'], true) ?? [];
        $roleIds = array_merge($roleIds, $appRoleIds);
    }
    $roleIds = array_unique($roleIds);
    
    // 查询所有角色并建立完整映射
    $roleSql = "SELECT id, roleName FROM 4_Role_List";
    $roleResult = mysqli_query($conn, $roleSql);
    
    // 检查查询错误
    if (!$roleResult) {
        throw new Exception('角色查询失败: ' . mysqli_error($conn));
    }
    $roleListData = [];
    
    if (mysqli_num_rows($roleResult) > 0) {
        while ($row = mysqli_fetch_assoc($roleResult)) {
            $roleListData[] = $row;
        }
    }
    
    // 创建角色ID到名称的映射
    $roleMap = [];
    foreach ($roleListData as $role) {
        $roleMap[$role['id']] = $role['roleName'] ?? '未知角色';
    }
    
    // 递归为所有节点添加roleList_name字段
    function addRoleListNames(&$nodes, $roleMap) {
        foreach ($nodes as &$node) {
            // 转换字段类型为整数
            $node['id'] = (int)$node['id'];
            $node['parent_id'] = (int)$node['parent_id'];
            $node['sort_order'] = (int)$node['sort_order'];
            $node['public'] = (int)$node['public'];
            
            // 将roleList从JSON字符串转换为整数数组
            $roleIds = json_decode($node['roleList'], true) ?? [];
            $node['roleList'] = array_map('intval', $roleIds);
            
            // 生成roleList_name
            $roleNames = [];
            foreach ($node['roleList'] as $id) {
                if (isset($roleMap[$id])) {
                    $roleNames[] = $roleMap[$id];
                }
            }
            $node['roleList_name'] = implode(', ', $roleNames);
            
            // 递归处理子节点
            if (!empty($node['children'])) {
                addRoleListNames($node['children'], $roleMap);
            }
        }
        unset($node); // 解除引用
    }
    
    // 处理所有根节点及其子节点
    addRoleListNames($applicationData, $roleMap);
    unset($app); // 解除引用
    
    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $applicationData
    ];
}

try {
    // 获取参数
    $controlCode = $_POST['controlCode'] ?? '';
    $id = $_POST['id'] ?? '';
    $application_name = $_POST['application_name'] ?? '';
    $url = $_POST['url'] ?? '';
    $public = $_POST['public'] ?? 0;
    $roleList = $_POST['roleList'] ?? '[]';
    switch ($controlCode) {
        case 'add': // 插入操作
            $result = AddOperation();
            break;

        case 'modify': // 修改操作
            $result = UpdateOperation();
            break;

        case 'del': // 删除操作
            $result = DeleteOperation();
            break;

        case 'query': // 查询操作
            $result = QueryOperation();
            break;

        default:
            throw new Exception('无效的控制码');
    }

    echo json_encode($result);
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['status' => 0, 'message' => $e->getMessage(), 'data' => []]);
} finally {
    $conn->close();
}