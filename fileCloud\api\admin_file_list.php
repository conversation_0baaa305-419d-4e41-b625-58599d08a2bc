<?php
/**
 * 管理员文件列表API
 * 创建时间: 2025-07-08
 */

require_once '../functions.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => '仅支持POST请求'], 405);
}

// 检查用户是否已登录且为管理员
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => '请先登录'], 401);
}

if (!isAdmin()) {
    jsonResponse(['success' => false, 'message' => '访问被拒绝：您没有管理员权限'], 403);
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => '无效的请求令牌'], 400);
}

try {
    // 获取文件列表（包括已删除的文件）
    $limit = intval($_POST['limit'] ?? 50);
    $offset = intval($_POST['offset'] ?? 0);
    
    if ($limit > 100) $limit = 100; // 限制最大数量
    
    $stmt = $pdo->prepare("
        SELECT 
            f.file_id,
            f.original_name,
            f.file_size,
            f.file_type,
            f.upload_time,
            f.is_deleted,
            f.file_path,
            u.name as username
        FROM filecloud_info f 
        LEFT JOIN 3_user u ON f.user_id = u.id
        ORDER BY f.upload_time DESC
        LIMIT ? OFFSET ?
    ");
    
    $stmt->execute([$limit, $offset]);
    $files = $stmt->fetchAll();
    
    // 格式化文件信息
    $formattedFiles = array_map(function($file) {
        return [
            'file_id' => $file['file_id'],
            'original_name' => $file['original_name'],
            'file_size' => $file['file_size'],
            'file_size_formatted' => formatFileSize($file['file_size']),
            'file_type' => $file['file_type'],
            'upload_time' => $file['upload_time'],
            'is_deleted' => $file['is_deleted'],
            'username' => $file['username'],
            'file_exists' => file_exists($file['file_path'])
        ];
    }, $files);
    
    // 获取总数
    $countStmt = $pdo->query("SELECT COUNT(*) FROM filecloud_info");
    $totalCount = $countStmt->fetchColumn();
    
    jsonResponse([
        'success' => true,
        'files' => $formattedFiles,
        'total_count' => $totalCount,
        'limit' => $limit,
        'offset' => $offset
    ]);
    
} catch (Exception $e) {
    error_log("Admin file list error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => '获取文件列表失败'], 500);
}
?>
