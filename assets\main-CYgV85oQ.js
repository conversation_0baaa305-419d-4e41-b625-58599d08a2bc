import{s as Fl,d as Eo,u as ue,a as Ll,c as ke,p as Rn,r as O,w as st,h as Vo,n as on,i as ct,b as Ue,_ as gt,o as Qe,e as ne,f as se,g as X,j as p,k as _,l as F,m as ql,F as Se,q as j,t as Yl,v as Ee,x as ee,y as Ve,z as be,A as Bl,B as Ro,C as kr,D as Sr,E as q,G as Hl,H as Pt,I as ur,J as Po,K as wn,L as xn,M as $o,N as Io,O as Mt,P as Xl,Q as Gl,R as Kl,S as Wl,T as Ql,U as Zl}from"./index-tTBIG6iu.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const lt=typeof document<"u";function Mo(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Jl(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Mo(e.default)}const ie=Object.assign;function Pn(e,t){const n={};for(const r in t){const o=t[r];n[r]=Te(o)?o.map(e):e(o)}return n}const $t=()=>{},Te=Array.isArray,Do=/#/g,jl=/&/g,ea=/\//g,ta=/=/g,na=/\?/g,Ao=/\+/g,ra=/%5B/g,oa=/%5D/g,No=/%5E/g,la=/%60/g,Uo=/%7B/g,aa=/%7C/g,To=/%7D/g,ia=/%20/g;function cr(e){return encodeURI(""+e).replace(aa,"|").replace(ra,"[").replace(oa,"]")}function sa(e){return cr(e).replace(Uo,"{").replace(To,"}").replace(No,"^")}function zn(e){return cr(e).replace(Ao,"%2B").replace(ia,"+").replace(Do,"%23").replace(jl,"%26").replace(la,"`").replace(Uo,"{").replace(To,"}").replace(No,"^")}function ua(e){return zn(e).replace(ta,"%3D")}function ca(e){return cr(e).replace(Do,"%23").replace(na,"%3F")}function da(e){return e==null?"":ca(e).replace(ea,"%2F")}function Dt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const fa=/\/$/,pa=e=>e.replace(fa,"");function $n(e,t,n="/"){let r,o={},l="",a="";const s=t.indexOf("#");let c=t.indexOf("?");return s<c&&s>=0&&(c=-1),c>-1&&(r=t.slice(0,c),l=t.slice(c+1,s>-1?s:t.length),o=e(l)),s>-1&&(r=r||t.slice(0,s),a=t.slice(s,t.length)),r=ga(r??t,n),{fullPath:r+(l&&"?")+l+a,path:r,query:o,hash:Dt(a)}}function ha(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Cr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function ma(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&dt(t.matched[r],n.matched[o])&&zo(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function dt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function zo(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!_a(e[n],t[n]))return!1;return!0}function _a(e,t){return Te(e)?Er(e,t):Te(t)?Er(t,e):e===t}function Er(e,t){return Te(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function ga(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let l=n.length-1,a,s;for(a=0;a<r.length;a++)if(s=r[a],s!==".")if(s==="..")l>1&&l--;else break;return n.slice(0,l).join("/")+"/"+r.slice(a).join("/")}const Ge={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var At;(function(e){e.pop="pop",e.push="push"})(At||(At={}));var It;(function(e){e.back="back",e.forward="forward",e.unknown=""})(It||(It={}));function va(e){if(!e)if(lt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),pa(e)}const ya=/^[^#]+#/;function wa(e,t){return e.replace(ya,"#")+t}function xa(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const bn=()=>({left:window.scrollX,top:window.scrollY});function ba(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=xa(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Vr(e,t){return(history.state?history.state.position-t:-1)+e}const On=new Map;function ka(e,t){On.set(e,t)}function Sa(e){const t=On.get(e);return On.delete(e),t}let Ca=()=>location.protocol+"//"+location.host;function Oo(e,t){const{pathname:n,search:r,hash:o}=t,l=e.indexOf("#");if(l>-1){let s=o.includes(e.slice(l))?e.slice(l).length:1,c=o.slice(s);return c[0]!=="/"&&(c="/"+c),Cr(c,"")}return Cr(n,e)+r+o}function Ea(e,t,n,r){let o=[],l=[],a=null;const s=({state:d})=>{const m=Oo(e,location),w=n.value,P=t.value;let v=0;if(d){if(n.value=m,t.value=d,a&&a===w){a=null;return}v=P?d.position-P.position:0}else r(m);o.forEach(E=>{E(n.value,w,{delta:v,type:At.pop,direction:v?v>0?It.forward:It.back:It.unknown})})};function c(){a=n.value}function f(d){o.push(d);const m=()=>{const w=o.indexOf(d);w>-1&&o.splice(w,1)};return l.push(m),m}function i(){const{history:d}=window;d.state&&d.replaceState(ie({},d.state,{scroll:bn()}),"")}function u(){for(const d of l)d();l=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",i)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",i,{passive:!0}),{pauseListeners:c,listen:f,destroy:u}}function Rr(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?bn():null}}function Va(e){const{history:t,location:n}=window,r={value:Oo(e,n)},o={value:t.state};o.value||l(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function l(c,f,i){const u=e.indexOf("#"),d=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+c:Ca()+e+c;try{t[i?"replaceState":"pushState"](f,"",d),o.value=f}catch(m){console.error(m),n[i?"replace":"assign"](d)}}function a(c,f){const i=ie({},t.state,Rr(o.value.back,c,o.value.forward,!0),f,{position:o.value.position});l(c,i,!0),r.value=c}function s(c,f){const i=ie({},o.value,t.state,{forward:c,scroll:bn()});l(i.current,i,!0);const u=ie({},Rr(r.value,c,null),{position:i.position+1},f);l(c,u,!1),r.value=c}return{location:r,state:o,push:s,replace:a}}function Ra(e){e=va(e);const t=Va(e),n=Ea(e,t.state,t.location,t.replace);function r(l,a=!0){a||n.pauseListeners(),history.go(l)}const o=ie({location:"",base:e,go:r,createHref:wa.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Pa(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Ra(e)}function $a(e){return typeof e=="string"||e&&typeof e=="object"}function Fo(e){return typeof e=="string"||typeof e=="symbol"}const Lo=Symbol("");var Pr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Pr||(Pr={}));function ft(e,t){return ie(new Error,{type:e,[Lo]:!0},t)}function Ye(e,t){return e instanceof Error&&Lo in e&&(t==null||!!(e.type&t))}const $r="[^/]+?",Ia={sensitive:!1,strict:!1,start:!0,end:!0},Ma=/[.+*?^${}()[\]/\\]/g;function Da(e,t){const n=ie({},Ia,t),r=[];let o=n.start?"^":"";const l=[];for(const f of e){const i=f.length?[]:[90];n.strict&&!f.length&&(o+="/");for(let u=0;u<f.length;u++){const d=f[u];let m=40+(n.sensitive?.25:0);if(d.type===0)u||(o+="/"),o+=d.value.replace(Ma,"\\$&"),m+=40;else if(d.type===1){const{value:w,repeatable:P,optional:v,regexp:E}=d;l.push({name:w,repeatable:P,optional:v});const C=E||$r;if(C!==$r){m+=10;try{new RegExp(`(${C})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${w}" (${C}): `+T.message)}}let N=P?`((?:${C})(?:/(?:${C}))*)`:`(${C})`;u||(N=v&&f.length<2?`(?:/${N})`:"/"+N),v&&(N+="?"),o+=N,m+=20,v&&(m+=-8),P&&(m+=-20),C===".*"&&(m+=-50)}i.push(m)}r.push(i)}if(n.strict&&n.end){const f=r.length-1;r[f][r[f].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const a=new RegExp(o,n.sensitive?"":"i");function s(f){const i=f.match(a),u={};if(!i)return null;for(let d=1;d<i.length;d++){const m=i[d]||"",w=l[d-1];u[w.name]=m&&w.repeatable?m.split("/"):m}return u}function c(f){let i="",u=!1;for(const d of e){(!u||!i.endsWith("/"))&&(i+="/"),u=!1;for(const m of d)if(m.type===0)i+=m.value;else if(m.type===1){const{value:w,repeatable:P,optional:v}=m,E=w in f?f[w]:"";if(Te(E)&&!P)throw new Error(`Provided param "${w}" is an array but it is not repeatable (* or + modifiers)`);const C=Te(E)?E.join("/"):E;if(!C)if(v)d.length<2&&(i.endsWith("/")?i=i.slice(0,-1):u=!0);else throw new Error(`Missing required param "${w}"`);i+=C}}return i||"/"}return{re:a,score:r,keys:l,parse:s,stringify:c}}function Aa(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function qo(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const l=Aa(r[n],o[n]);if(l)return l;n++}if(Math.abs(o.length-r.length)===1){if(Ir(r))return 1;if(Ir(o))return-1}return o.length-r.length}function Ir(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Na={type:0,value:""},Ua=/[a-zA-Z0-9_]/;function Ta(e){if(!e)return[[]];if(e==="/")return[[Na]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${f}": ${m}`)}let n=0,r=n;const o=[];let l;function a(){l&&o.push(l),l=[]}let s=0,c,f="",i="";function u(){f&&(n===0?l.push({type:0,value:f}):n===1||n===2||n===3?(l.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),l.push({type:1,value:f,regexp:i,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),f="")}function d(){f+=c}for(;s<e.length;){if(c=e[s++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(f&&u(),a()):c===":"?(u(),n=1):d();break;case 4:d(),n=r;break;case 1:c==="("?n=2:Ua.test(c)?d():(u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--);break;case 2:c===")"?i[i.length-1]=="\\"?i=i.slice(0,-1)+c:n=3:i+=c;break;case 3:u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--,i="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),u(),a(),o}function za(e,t,n){const r=Da(Ta(e.path),n),o=ie(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Oa(e,t){const n=[],r=new Map;t=Nr({strict:!1,end:!0,sensitive:!1},t);function o(u){return r.get(u)}function l(u,d,m){const w=!m,P=Dr(u);P.aliasOf=m&&m.record;const v=Nr(t,u),E=[P];if("alias"in u){const T=typeof u.alias=="string"?[u.alias]:u.alias;for(const U of T)E.push(Dr(ie({},P,{components:m?m.record.components:P.components,path:U,aliasOf:m?m.record:P})))}let C,N;for(const T of E){const{path:U}=T;if(d&&U[0]!=="/"){const k=d.record.path,R=k[k.length-1]==="/"?"":"/";T.path=d.record.path+(U&&R+U)}if(C=za(T,d,v),m?m.alias.push(C):(N=N||C,N!==C&&N.alias.push(C),w&&u.name&&!Ar(C)&&a(u.name)),Yo(C)&&c(C),P.children){const k=P.children;for(let R=0;R<k.length;R++)l(k[R],C,m&&m.children[R])}m=m||C}return N?()=>{a(N)}:$t}function a(u){if(Fo(u)){const d=r.get(u);d&&(r.delete(u),n.splice(n.indexOf(d),1),d.children.forEach(a),d.alias.forEach(a))}else{const d=n.indexOf(u);d>-1&&(n.splice(d,1),u.record.name&&r.delete(u.record.name),u.children.forEach(a),u.alias.forEach(a))}}function s(){return n}function c(u){const d=qa(u,n);n.splice(d,0,u),u.record.name&&!Ar(u)&&r.set(u.record.name,u)}function f(u,d){let m,w={},P,v;if("name"in u&&u.name){if(m=r.get(u.name),!m)throw ft(1,{location:u});v=m.record.name,w=ie(Mr(d.params,m.keys.filter(N=>!N.optional).concat(m.parent?m.parent.keys.filter(N=>N.optional):[]).map(N=>N.name)),u.params&&Mr(u.params,m.keys.map(N=>N.name))),P=m.stringify(w)}else if(u.path!=null)P=u.path,m=n.find(N=>N.re.test(P)),m&&(w=m.parse(P),v=m.record.name);else{if(m=d.name?r.get(d.name):n.find(N=>N.re.test(d.path)),!m)throw ft(1,{location:u,currentLocation:d});v=m.record.name,w=ie({},d.params,u.params),P=m.stringify(w)}const E=[];let C=m;for(;C;)E.unshift(C.record),C=C.parent;return{name:v,path:P,params:w,matched:E,meta:La(E)}}e.forEach(u=>l(u));function i(){n.length=0,r.clear()}return{addRoute:l,resolve:f,removeRoute:a,clearRoutes:i,getRoutes:s,getRecordMatcher:o}}function Mr(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Dr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Fa(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Fa(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Ar(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function La(e){return e.reduce((t,n)=>ie(t,n.meta),{})}function Nr(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function qa(e,t){let n=0,r=t.length;for(;n!==r;){const l=n+r>>1;qo(e,t[l])<0?r=l:n=l+1}const o=Ya(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function Ya(e){let t=e;for(;t=t.parent;)if(Yo(t)&&qo(e,t)===0)return t}function Yo({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Ba(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const l=r[o].replace(Ao," "),a=l.indexOf("="),s=Dt(a<0?l:l.slice(0,a)),c=a<0?null:Dt(l.slice(a+1));if(s in t){let f=t[s];Te(f)||(f=t[s]=[f]),f.push(c)}else t[s]=c}return t}function Ur(e){let t="";for(let n in e){const r=e[n];if(n=ua(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Te(r)?r.map(l=>l&&zn(l)):[r&&zn(r)]).forEach(l=>{l!==void 0&&(t+=(t.length?"&":"")+n,l!=null&&(t+="="+l))})}return t}function Ha(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Te(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const Xa=Symbol(""),Tr=Symbol(""),kn=Symbol(""),Bo=Symbol(""),Fn=Symbol("");function wt(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ke(e,t,n,r,o,l=a=>a()){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((s,c)=>{const f=d=>{d===!1?c(ft(4,{from:n,to:t})):d instanceof Error?c(d):$a(d)?c(ft(2,{from:t,to:d})):(a&&r.enterCallbacks[o]===a&&typeof d=="function"&&a.push(d),s())},i=l(()=>e.call(r&&r.instances[o],t,n,f));let u=Promise.resolve(i);e.length<3&&(u=u.then(f)),u.catch(d=>c(d))})}function In(e,t,n,r,o=l=>l()){const l=[];for(const a of e)for(const s in a.components){let c=a.components[s];if(!(t!=="beforeRouteEnter"&&!a.instances[s]))if(Mo(c)){const i=(c.__vccOpts||c)[t];i&&l.push(Ke(i,n,r,a,s,o))}else{let f=c();l.push(()=>f.then(i=>{if(!i)throw new Error(`Couldn't resolve component "${s}" at "${a.path}"`);const u=Jl(i)?i.default:i;a.mods[s]=i,a.components[s]=u;const m=(u.__vccOpts||u)[t];return m&&Ke(m,n,r,a,s,o)()}))}}return l}function zr(e){const t=ct(kn),n=ct(Bo),r=ke(()=>{const c=ue(e.to);return t.resolve(c)}),o=ke(()=>{const{matched:c}=r.value,{length:f}=c,i=c[f-1],u=n.matched;if(!i||!u.length)return-1;const d=u.findIndex(dt.bind(null,i));if(d>-1)return d;const m=Or(c[f-2]);return f>1&&Or(i)===m&&u[u.length-1].path!==m?u.findIndex(dt.bind(null,c[f-2])):d}),l=ke(()=>o.value>-1&&Za(n.params,r.value.params)),a=ke(()=>o.value>-1&&o.value===n.matched.length-1&&zo(n.params,r.value.params));function s(c={}){if(Qa(c)){const f=t[ue(e.replace)?"replace":"push"](ue(e.to)).catch($t);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:r,href:ke(()=>r.value.href),isActive:l,isExactActive:a,navigate:s}}function Ga(e){return e.length===1?e[0]:e}const Ka=Eo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:zr,setup(e,{slots:t}){const n=Ue(zr(e)),{options:r}=ct(kn),o=ke(()=>({[Fr(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Fr(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const l=t.default&&Ga(t.default(n));return e.custom?l:Vo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},l)}}}),Wa=Ka;function Qa(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Za(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!Te(o)||o.length!==r.length||r.some((l,a)=>l!==o[a]))return!1}return!0}function Or(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Fr=(e,t,n)=>e??t??n,Ja=Eo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=ct(Fn),o=ke(()=>e.route||r.value),l=ct(Tr,0),a=ke(()=>{let f=ue(l);const{matched:i}=o.value;let u;for(;(u=i[f])&&!u.components;)f++;return f}),s=ke(()=>o.value.matched[a.value]);Rn(Tr,ke(()=>a.value+1)),Rn(Xa,s),Rn(Fn,o);const c=O();return st(()=>[c.value,s.value,e.name],([f,i,u],[d,m,w])=>{i&&(i.instances[u]=f,m&&m!==i&&f&&f===d&&(i.leaveGuards.size||(i.leaveGuards=m.leaveGuards),i.updateGuards.size||(i.updateGuards=m.updateGuards))),f&&i&&(!m||!dt(i,m)||!d)&&(i.enterCallbacks[u]||[]).forEach(P=>P(f))},{flush:"post"}),()=>{const f=o.value,i=e.name,u=s.value,d=u&&u.components[i];if(!d)return Lr(n.default,{Component:d,route:f});const m=u.props[i],w=m?m===!0?f.params:typeof m=="function"?m(f):m:null,v=Vo(d,ie({},w,t,{onVnodeUnmounted:E=>{E.component.isUnmounted&&(u.instances[i]=null)},ref:c}));return Lr(n.default,{Component:v,route:f})||v}}});function Lr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ja=Ja;function ei(e){const t=Oa(e.routes,e),n=e.parseQuery||Ba,r=e.stringifyQuery||Ur,o=e.history,l=wt(),a=wt(),s=wt(),c=Fl(Ge);let f=Ge;lt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const i=Pn.bind(null,$=>""+$),u=Pn.bind(null,da),d=Pn.bind(null,Dt);function m($,G){let L,z;return Fo($)?(L=t.getRecordMatcher($),z=G):z=$,t.addRoute(z,L)}function w($){const G=t.getRecordMatcher($);G&&t.removeRoute(G)}function P(){return t.getRoutes().map($=>$.record)}function v($){return!!t.getRecordMatcher($)}function E($,G){if(G=ie({},G||c.value),typeof $=="string"){const te=$n(n,$,G.path),g=t.resolve({path:te.path},G),b=o.createHref(te.fullPath);return ie(te,g,{params:d(g.params),hash:Dt(te.hash),redirectedFrom:void 0,href:b})}let L;if($.path!=null)L=ie({},$,{path:$n(n,$.path,G.path).path});else{const te=ie({},$.params);for(const g in te)te[g]==null&&delete te[g];L=ie({},$,{params:u(te)}),G.params=u(G.params)}const z=t.resolve(L,G),ae=$.hash||"";z.params=i(d(z.params));const me=ha(r,ie({},$,{hash:sa(ae),path:z.path})),re=o.createHref(me);return ie({fullPath:me,hash:ae,query:r===Ur?Ha($.query):$.query||{}},z,{redirectedFrom:void 0,href:re})}function C($){return typeof $=="string"?$n(n,$,c.value.path):ie({},$)}function N($,G){if(f!==$)return ft(8,{from:G,to:$})}function T($){return R($)}function U($){return T(ie(C($),{replace:!0}))}function k($){const G=$.matched[$.matched.length-1];if(G&&G.redirect){const{redirect:L}=G;let z=typeof L=="function"?L($):L;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=C(z):{path:z},z.params={}),ie({query:$.query,hash:$.hash,params:z.path!=null?{}:$.params},z)}}function R($,G){const L=f=E($),z=c.value,ae=$.state,me=$.force,re=$.replace===!0,te=k(L);if(te)return R(ie(C(te),{state:typeof te=="object"?ie({},ae,te.state):ae,force:me,replace:re}),G||L);const g=L;g.redirectedFrom=G;let b;return!me&&ma(r,z,L)&&(b=ft(16,{to:g,from:z}),W(z,z,!0,!1)),(b?Promise.resolve(b):h(g,z)).catch(K=>Ye(K)?Ye(K,2)?K:J(K):x(K,g,z)).then(K=>{if(K){if(Ye(K,2))return R(ie({replace:re},C(K.to),{state:typeof K.to=="object"?ie({},ae,K.to.state):ae,force:me}),G||g)}else K=A(g,z,!0,re,ae);return y(g,z,K),K})}function I($,G){const L=N($,G);return L?Promise.reject(L):Promise.resolve()}function M($){const G=we.values().next().value;return G&&typeof G.runWithContext=="function"?G.runWithContext($):$()}function h($,G){let L;const[z,ae,me]=ti($,G);L=In(z.reverse(),"beforeRouteLeave",$,G);for(const te of z)te.leaveGuards.forEach(g=>{L.push(Ke(g,$,G))});const re=I.bind(null,$,G);return L.push(re),xe(L).then(()=>{L=[];for(const te of l.list())L.push(Ke(te,$,G));return L.push(re),xe(L)}).then(()=>{L=In(ae,"beforeRouteUpdate",$,G);for(const te of ae)te.updateGuards.forEach(g=>{L.push(Ke(g,$,G))});return L.push(re),xe(L)}).then(()=>{L=[];for(const te of me)if(te.beforeEnter)if(Te(te.beforeEnter))for(const g of te.beforeEnter)L.push(Ke(g,$,G));else L.push(Ke(te.beforeEnter,$,G));return L.push(re),xe(L)}).then(()=>($.matched.forEach(te=>te.enterCallbacks={}),L=In(me,"beforeRouteEnter",$,G,M),L.push(re),xe(L))).then(()=>{L=[];for(const te of a.list())L.push(Ke(te,$,G));return L.push(re),xe(L)}).catch(te=>Ye(te,8)?te:Promise.reject(te))}function y($,G,L){s.list().forEach(z=>M(()=>z($,G,L)))}function A($,G,L,z,ae){const me=N($,G);if(me)return me;const re=G===Ge,te=lt?history.state:{};L&&(z||re?o.replace($.fullPath,ie({scroll:re&&te&&te.scroll},ae)):o.push($.fullPath,ae)),c.value=$,W($,G,L,re),J()}let H;function B(){H||(H=o.listen(($,G,L)=>{if(!Q.listening)return;const z=E($),ae=k(z);if(ae){R(ie(ae,{replace:!0,force:!0}),z).catch($t);return}f=z;const me=c.value;lt&&ka(Vr(me.fullPath,L.delta),bn()),h(z,me).catch(re=>Ye(re,12)?re:Ye(re,2)?(R(ie(C(re.to),{force:!0}),z).then(te=>{Ye(te,20)&&!L.delta&&L.type===At.pop&&o.go(-1,!1)}).catch($t),Promise.reject()):(L.delta&&o.go(-L.delta,!1),x(re,z,me))).then(re=>{re=re||A(z,me,!1),re&&(L.delta&&!Ye(re,8)?o.go(-L.delta,!1):L.type===At.pop&&Ye(re,20)&&o.go(-1,!1)),y(z,me,re)}).catch($t)}))}let S=wt(),Y=wt(),V;function x($,G,L){J($);const z=Y.list();return z.length?z.forEach(ae=>ae($,G,L)):console.error($),Promise.reject($)}function Z(){return V&&c.value!==Ge?Promise.resolve():new Promise(($,G)=>{S.add([$,G])})}function J($){return V||(V=!$,B(),S.list().forEach(([G,L])=>$?L($):G()),S.reset()),$}function W($,G,L,z){const{scrollBehavior:ae}=e;if(!lt||!ae)return Promise.resolve();const me=!L&&Sa(Vr($.fullPath,0))||(z||!L)&&history.state&&history.state.scroll||null;return on().then(()=>ae($,G,me)).then(re=>re&&ba(re)).catch(re=>x(re,$,G))}const oe=$=>o.go($);let fe;const we=new Set,Q={currentRoute:c,listening:!0,addRoute:m,removeRoute:w,clearRoutes:t.clearRoutes,hasRoute:v,getRoutes:P,resolve:E,options:e,push:T,replace:U,go:oe,back:()=>oe(-1),forward:()=>oe(1),beforeEach:l.add,beforeResolve:a.add,afterEach:s.add,onError:Y.add,isReady:Z,install($){const G=this;$.component("RouterLink",Wa),$.component("RouterView",ja),$.config.globalProperties.$router=G,Object.defineProperty($.config.globalProperties,"$route",{enumerable:!0,get:()=>ue(c)}),lt&&!fe&&c.value===Ge&&(fe=!0,T(o.location).catch(ae=>{}));const L={};for(const ae in Ge)Object.defineProperty(L,ae,{get:()=>c.value[ae],enumerable:!0});$.provide(kn,G),$.provide(Bo,Ll(L)),$.provide(Fn,c);const z=$.unmount;we.add($),$.unmount=function(){we.delete($),we.size<1&&(f=Ge,H&&H(),H=null,c.value=Ge,fe=!1,V=!1),z()}}};function xe($){return $.reduce((G,L)=>G.then(()=>M(L)),Promise.resolve())}return Q}function ti(e,t){const n=[],r=[],o=[],l=Math.max(t.matched.length,e.matched.length);for(let a=0;a<l;a++){const s=t.matched[a];s&&(e.matched.find(f=>dt(f,s))?r.push(s):n.push(s));const c=e.matched[a];c&&(t.matched.find(f=>dt(f,c))||o.push(c))}return[n,r,o]}function ni(){return ct(kn)}const ri={class:"app-container"},oi={class:"grid-container"},li={class:"header"},ai={class:"header-left"},ii={class:"user-info"},si={class:"el-dropdown-link"},ui={class:"user-name"},ci={class:"sidebar"},di={class:"content",ref:"contentRef"},fi={__name:"App",setup(e){const t=ni(),n=O(""),r=O([]),o=O(!1),l=O(""),a=O(null);Qe(async()=>{await s(),await c(),t.push("/unit-management")});const s=async()=>{const k=await ne.post("/api/get_user_info.php");n.value=k.user.name},c=async()=>{const k=await ne.post("/api/get_user_app.php");r.value=k.data},f=k=>{console.log("点击的菜单对应的路由是:",k),t.push(k)},i=()=>{o.value=!o.value},u=O(!1),d=O({oldPassword:"",newPassword:"",confirmPassword:""}),m=O(null),w=O(!1),P=O(!1),v=()=>{w.value=!w.value},E=()=>{P.value=!P.value},C=async k=>{k==="logout"?(await ne.get("/api/logout.php"),window.location.href="login.html"):k==="changePassword"&&(u.value=!0)},N=async()=>{m.value&&await m.value.validate(k=>{if(k){if(d.value.newPassword!==d.value.confirmPassword){q.error("两次输入的密码不一致");return}const R=new FormData;R.append("old_password",d.value.oldPassword),R.append("new_password",d.value.newPassword),ne.post("/api/change_password.php",R).then(()=>{q.success("密码修改成功，请重新登录"),u.value=!1,d.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{q.error("密码修改失败")})}})},T=Ue({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(k,R,I)=>{R!==d.value.newPassword?I(new Error("两次输入的密码不一致")):I()},trigger:"blur"}]}),U=()=>{const k=a.value;k&&(document.fullscreenElement?document.exitFullscreen():k.requestFullscreen().catch(R=>{console.error("全屏失败:",R),q.error("全屏功能不支持")}))};return(k,R)=>{const I=F("el-button"),M=F("el-icon"),h=F("el-avatar"),y=F("el-dropdown-item"),A=F("el-dropdown-menu"),H=F("el-dropdown"),B=F("el-menu-item"),S=F("el-menu"),Y=F("router-view"),V=F("el-input"),x=F("el-form-item"),Z=F("el-form"),J=F("el-dialog");return j(),se(Se,null,[X("div",ri,[X("div",oi,[X("div",li,[X("div",ai,[p(I,{onClick:i,type:"text",class:"menu-btn"},{default:_(()=>R[5]||(R[5]=[X("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),R[6]||(R[6]=X("img",{src:ql,alt:"Logo",class:"header-logo"},null,-1)),R[7]||(R[7]=X("span",{class:"logo"},"CloudPivot",-1))])]),X("div",ii,[p(H,{onCommand:C},{dropdown:_(()=>[p(A,null,{default:_(()=>[p(y,{command:"profile"},{default:_(()=>R[8]||(R[8]=[ee("个人信息")])),_:1,__:[8]}),p(y,{command:"changePassword"},{default:_(()=>R[9]||(R[9]=[ee("修改密码")])),_:1,__:[9]}),p(y,{command:"logout"},{default:_(()=>R[10]||(R[10]=[ee("退出登录")])),_:1,__:[10]})]),_:1})]),default:_(()=>[X("span",si,[p(h,{size:"small"},{default:_(()=>[p(M,null,{default:_(()=>[p(ue(Yl),{style:{color:"#409EFF"}})]),_:1})]),_:1}),X("span",ui,Ee(n.value),1)])]),_:1})]),X("div",ci,[p(S,{"default-active":l.value,class:"el-menu-vertical",mode:"vertical",collapse:o.value,onOpen:k.handleOpen,onClose:k.handleClose},{default:_(()=>[(j(!0),se(Se,null,Ve(r.value,W=>(j(),be(B,{key:W.id,index:W.id.toString(),router:W.url,onClick:oe=>f(W.url)},{title:_(()=>[X("span",null,Ee(W.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),X("div",di,[p(I,{onClick:U,type:"text",class:"fullscreen-btn"},{default:_(()=>[p(M,null,{default:_(()=>[p(ue(Bl))]),_:1})]),_:1}),X("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:a},[p(Y,{ref:"routerViewRef"},null,512)],512)],512)])]),p(J,{modelValue:u.value,"onUpdate:modelValue":R[4]||(R[4]=W=>u.value=W),width:"400px"},{default:_(()=>[p(Z,{model:d.value,ref_key:"passwordFormRef",ref:m,rules:T,"label-width":"120px",onSubmit:Ro(N,["prevent"])},{default:_(()=>[p(x,{label:"旧密码",prop:"oldPassword",required:""},{default:_(()=>[p(V,{modelValue:d.value.oldPassword,"onUpdate:modelValue":R[0]||(R[0]=W=>d.value.oldPassword=W),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),p(x,{label:"新密码",prop:"newPassword",required:""},{default:_(()=>[p(V,{modelValue:d.value.newPassword,"onUpdate:modelValue":R[1]||(R[1]=W=>d.value.newPassword=W),type:w.value?"text":"password",placeholder:"请输入新密码"},{suffix:_(()=>[p(I,{icon:w.value?ue(kr):ue(Sr),onClick:v,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),p(x,{label:"确认新密码",prop:"confirmPassword",required:""},{default:_(()=>[p(V,{modelValue:d.value.confirmPassword,"onUpdate:modelValue":R[2]||(R[2]=W=>d.value.confirmPassword=W),type:P.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:_(()=>[p(I,{icon:P.value?ue(kr):ue(Sr),onClick:E,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),p(x,null,{default:_(()=>[p(I,{type:"primary","native-type":"submit"},{default:_(()=>R[11]||(R[11]=[ee("确定")])),_:1,__:[11]}),p(I,{onClick:R[3]||(R[3]=W=>u.value=!1)},{default:_(()=>R[12]||(R[12]=[ee("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},pi=gt(fi,[["__scopeId","data-v-1e64b65d"]]),hi={class:"unit-management"},mi={style:{"text-align":"right",margin:"10px"}},_i={__name:"UnitManagement",setup(e){const t=O(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=h=>{if(h&&h.length>0){const y=h[h.length-1],A=l(y);A&&(o.parentId=y,u.value=A.children||[])}else o.parentId=null,u.value=[]},o=Ue({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:0});st(()=>o.parentId,(h,y)=>{if(console.log("parentId 发生变化，旧值: ",y,"新值: ",h),h){const A=l(h);A&&(u.value=A.children||[])}else u.value=[]},{immediate:!1});const l=(h,y=a.value)=>{for(let A=0;A<y.length;A++){if(y[A].id===h)return y[A];if(y[A].children){const H=l(h,y[A].children);if(H)return H}}return null},a=O([]),s=O(new Set),c=ke(()=>{const h=[],y=(A,H=0,B=null)=>{A.forEach(S=>{h.push({...S,level:H,parentId:B,expanded:s.value.has(S.id)}),S.children&&y(S.children,H+1,S.id)})};return y(a.value),h}),f=ke(()=>{const h=[],y=A=>{if(A.level===0)return!0;let H=c.value.find(B=>B.id===A.parentId);for(;H;){if(!H.expanded)return!1;H=c.value.find(B=>B.id===H.parentId)}return!0};return c.value.forEach(A=>{y(A)&&h.push(A)}),h}),i=O(!1),u=O([]),d=O(null);Qe(async()=>{await C()});const m=O(!1),w=h=>{const y=[];function A(H,B){for(const S of H){const Y=[...B,S.id];if(S.id===h)return y.push(...Y),!0;if(S.children&&S.children.length>0&&A(S.children,Y))return!0}return!1}return A(a.value,[]),y},P=h=>{if(d.value&&d.value.resetFields(),t.value=!1,o.id=null,o.name="",o.code="",o.parentId=null,o.parentIdPath=[],o.sort_order=0,m.value=!!h,h){const y=l(h);y&&(o.parentId=h,console.log("parentId位置一:",h),o.parentIdPath=w(h),u.value=y.children||[])}else o.parentId=null,o.parentIdPath=[],u.value=[];i.value=!0,console.log("dialogVisible 已设为 true")},v=h=>{d.value&&d.value.resetFields(),t.value=!0,o.id=h.id;let y={currentUnit:h.unit_name,selectedSortOrder:null,selectedUnit:null};o.name=h.unit_name,o.code=h.code,o.parentId=h.parent_id,o.parentIdPath=w(h.parent_id);const A=l(h.parent_id);if(A){u.value=(A.children||[]).filter(S=>S.id!==h.id);const H=A.children||[],B=H.findIndex(S=>S.id===h.id);if(B>0){const S=H[B-1];o.sort_order=S.sort_order,y.selectedUnit=S.unit_name}else o.sort_order=0,y.selectedUnit="最前";y.selectedSortOrder=o.sort_order}else{u.value=a.value.filter(B=>B.id!==h.id);const H=a.value.findIndex(B=>B.id===h.id);if(H>0){const B=a.value[H-1];o.sort_order=B.sort_order,y.selectedUnit=B.unit_name}else o.sort_order=0,y.selectedUnit="最前";y.selectedSortOrder=o.sort_order}console.log("排序选项:",u.value),console.log("自动选择结果:",y),i.value=!0},E=()=>{d.value.validate(async h=>{if(h)try{const y=new FormData;o.id?(y.append("action","edit"),y.append("id",o.id)):y.append("action","add"),y.append("sort_order",o.sort_order+1),y.append("unit_name",o.name),y.append("code",o.code),y.append("parent_id",o.parentId||null);const A=await ne.post("api/unit_manage.php",y,{headers:{"Content-Type":"multipart/form-data"}});A.status===1?(await C(),i.value=!1,q.success(o.id?"编辑成功":"新增成功")):q.error(A.message)}catch(y){console.error("保存单位失败:",y),q.error("保存单位失败，请稍后重试")}})},C=async()=>{const h=await ne.post("api/get_unit_info.php");a.value=[h.data],console.log("获取单位数据成功:",a.value);const y=A=>{A.forEach(H=>{H.children&&H.children.length>0&&(s.value.add(H.id),y(H.children))})};y(a.value)},N=h=>{Mt.confirm(`确定要删除 ${h.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const y=new FormData;y.append("action","del"),y.append("id",h.id),await ne.post("api/unit_manage.php",y),q.success("删除成功"),await C()})},T=h=>{const y=new Set(s.value);y.has(h.id)?y.delete(h.id):y.add(h.id),s.value=y},U=h=>((h.parentId?l(h.parentId):{children:a.value}).children||[]).findIndex(B=>B.id===h.id)===0,k=h=>{const A=(h.parentId?l(h.parentId):{children:a.value}).children||[];return A.findIndex(B=>B.id===h.id)===A.length-1},R=async h=>{const y=new FormData;y.append("action","edit"),y.append("id",h.id),y.append("sort_order",h.sort_order-1),y.append("unit_name",h.unit_name),y.append("code",h.code),y.append("parent_id",h.parentId),await ne.post("api/unit_manage.php",y),await C()},I=async h=>{const y=new FormData;y.append("action","edit"),y.append("id",h.id),y.append("sort_order",h.sort_order+1),y.append("unit_name",h.unit_name),y.append("code",h.code),y.append("parent_id",h.parentId),await ne.post("api/unit_manage.php",y),await C()},M=ke(()=>u.value.filter(h=>h.id!==o.id));return(h,y)=>{const A=F("el-button"),H=F("el-col"),B=F("el-row"),S=F("el-table-column"),Y=F("el-icon"),V=F("el-button-group"),x=F("el-table"),Z=F("el-input"),J=F("el-form-item"),W=F("el-cascader"),oe=F("el-option"),fe=F("el-select"),we=F("el-form");return j(),se("div",hi,[p(B,null,{default:_(()=>[p(H,{span:24},{default:_(()=>[X("div",mi,[p(A,{type:"primary",round:"",onClick:y[0]||(y[0]=Q=>P(null))},{default:_(()=>y[7]||(y[7]=[ee("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),p(x,{data:f.value,style:{width:"100%",height:"calc(100vh - 200px)","overflow-y":"auto"},border:""},{default:_(()=>[p(S,{label:"操作",width:"60",align:"center"},{default:_(Q=>[Q.row.children&&Q.row.children.length?(j(),be(A,{key:0,size:"mini",type:"text",onClick:xe=>T(Q.row)},{default:_(()=>[ee(Ee(Q.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Pt("",!0)]),_:1}),p(S,{prop:"unit_name",label:"单位名称"},{default:_(Q=>[X("span",{style:ur({paddingLeft:`${Q.row.level*20}px`})},Ee(Q.row.unit_name),5)]),_:1}),p(S,{prop:"code",label:"组织机构代码",width:"250"}),p(S,{label:"操作",width:"350"},{default:_(Q=>[p(V,null,{default:_(()=>[p(A,{size:"mini",type:"primary",onClick:xe=>P(Q.row.id)},{default:_(()=>[p(Y,null,{default:_(()=>[p(ue(Po))]),_:1})]),_:2},1032,["onClick"]),p(A,{size:"mini",type:"warning",onClick:xe=>v(Q.row)},{default:_(()=>[p(Y,null,{default:_(()=>[p(ue(wn))]),_:1})]),_:2},1032,["onClick"]),p(A,{size:"mini",type:"danger",onClick:xe=>N(Q.row)},{default:_(()=>[p(Y,null,{default:_(()=>[p(ue(xn))]),_:1})]),_:2},1032,["onClick"]),p(A,{size:"mini",type:"info",onClick:xe=>R(Q.row),disabled:U(Q.row)},{default:_(()=>[p(Y,null,{default:_(()=>[p(ue($o))]),_:1})]),_:2},1032,["onClick","disabled"]),p(A,{size:"mini",type:"info",onClick:xe=>I(Q.row),disabled:k(Q.row)},{default:_(()=>[p(Y,null,{default:_(()=>[p(ue(Io))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),p(ue(Hl),{modelValue:i.value,"onUpdate:modelValue":y[6]||(y[6]=Q=>i.value=Q),title:"",width:"450px","close-on-click-modal":!1},{footer:_(()=>[p(A,{onClick:y[5]||(y[5]=Q=>i.value=!1)},{default:_(()=>y[8]||(y[8]=[ee("取消")])),_:1,__:[8]}),p(A,{type:"primary",onClick:E},{default:_(()=>y[9]||(y[9]=[ee("确定")])),_:1,__:[9]})]),default:_(()=>[p(we,{model:o,ref_key:"formRef",ref:d,"label-width":"130px"},{default:_(()=>[p(J,{label:"单位名称",prop:"name",required:""},{default:_(()=>[p(Z,{modelValue:o.name,"onUpdate:modelValue":y[1]||(y[1]=Q=>o.name=Q),required:""},null,8,["modelValue"])]),_:1}),p(J,{label:"组织机构代码",prop:"code"},{default:_(()=>[p(Z,{modelValue:o.code,"onUpdate:modelValue":y[2]||(y[2]=Q=>o.code=Q)},null,8,["modelValue"])]),_:1}),p(J,{label:"上级单位",prop:"parentId"},{default:_(()=>[p(W,{modelValue:o.parentIdPath,"onUpdate:modelValue":y[3]||(y[3]=Q=>o.parentIdPath=Q),options:a.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),p(J,{label:"排序",prop:"sort",required:""},{default:_(()=>[p(fe,{modelValue:o.sort_order,"onUpdate:modelValue":y[4]||(y[4]=Q=>o.sort_order=Q),placeholder:"请选择排序位置"},{default:_(()=>[p(oe,{label:"置于 最前",value:0}),(j(!0),se(Se,null,Ve(M.value,Q=>(j(),be(oe,{key:Q.id,label:`置于 ${Q.unit_name} 之后`,value:Q.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},gi={class:"user-management-container"},vi={class:"left-panel"},yi=["onClick"],wi={class:"right-panel"},xi={class:"action-buttons",style:{display:"flex","align-items":"center"}},bi={class:"left-buttons",style:{display:"flex","align-items":"center",gap:"8px"}},ki={class:"right-switches",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},Si={class:"form-row"},Ci={class:"form-row"},Ei={class:"form-row"},Vi={class:"form-row"},Ri={class:"form-row"},Pi={class:"form-row"},$i={class:"form-row"},Ii={class:"form-row"},Mi={class:"dialog-footer"},Di={__name:"UserManagement",setup(e){const t=O([]),n=O([]),r=O([]),o=O(null);O("");const l=O([]),a=O([]),s=ke(()=>{if(!o.value)return"";const g=d.value.find(b=>b.id===o.value);return console.log("当前选中的部门ID:",o.value),console.log("当前选中的部门:",g.unit_name),g?g.unit_name:""});O(null);const c=O(1),f=O(10),i=O(0),u=O([]),d=O([]),m=O(""),w=O([]),P=O([]),v=O([]),E=O([]),C=O(!1),N=O(""),T=O(!1),U=O({});O(!1);const k=O(!1),R=O(null),I=()=>{T.value?C.value=!1:G()},M=ke(()=>m.value?d.value.filter(g=>g.show&&g.unit_name.toLowerCase().includes(m.value.toLowerCase())):d.value.filter(g=>g.show)),h=Ue({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sort_order:null,desc:""}),y={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},A=O(null),H=async()=>{try{const g=new FormData;g.append("type","identity");const b=await ne({url:"/api/person_info_api.php",method:"post",data:g});b.status===1?t.value=b.data.map(K=>({label:K,value:K})):q.error("获取人员身份数据失败："+b.message)}catch{q.error("网络请求失败")}},B=async()=>{try{const g=new FormData;g.append("type","status");const b=await ne({url:"/api/person_info_api.php",method:"post",data:g});b.status===1?n.value=b.data.map(K=>({label:K,value:K})):q.error("获取人员状态数据失败："+b.message)}catch{q.error("网络请求失败")}},S=async()=>{try{const g=new FormData;g.append("type","rank");const b=await ne({url:"/api/person_info_api.php",method:"post",data:g});b.status===1?r.value=b.data.map(K=>({label:K,value:K})):q.error("获取职级数据失败："+b.message)}catch{q.error("网络请求失败")}};Qe(async()=>{try{const g=await ne.post("api/get_unit_info.php");u.value=[g.data],V(g.data),w.value=u.value,P.value=u.value,console.log("获取部门数据成功:",u.value),await Y(),H(),B(),S()}catch(g){console.error("获取数据失败:",g),q.error("获取数据失败，请稍后重试")}});const Y=async()=>{console.log("开始获取用户数据...");try{const g=new FormData;g.append("controlCode","query"),g.append("page",c.value),g.append("pagesize",f.value),o.value&&g.append("organization_unit",o.value),J.value?g.append("isShowInPersonnel","1"):g.append("isShowInPersonnel","0"),Z.value?g.append("isShowOutPersonnel","1"):g.append("isShowOutPersonnel","0");const b=await ne.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});v.value=b.data,console.log("获取用户数据成功:",v.value),i.value=b.total}catch(g){console.error("获取用户数据失败:",g),q.error("获取用户数据失败，请稍后重试")}},V=(g,b=0,K=null)=>{const pe={...g,level:b,expanded:g.children&&g.children.length>0,parent:K,indent:b*20,show:!0};d.value.push(pe),g.children&&g.children.length>0&&g.children.forEach(_e=>{V(_e,b+1,pe)})};console.log("扁平化后的部门列表:",d.value);const x=g=>{g.expanded=!g.expanded;const b=d.value.indexOf(g)+1;let K=g.level+1;for(let pe=b;pe<d.value.length;pe++){const _e=d.value[pe];if(_e.level<=g.level)break;_e.level===K?_e.show=g.expanded:_e.level>K&&(_e.show=g.expanded&&d.value[pe-1].show)}},Z=O(!0),J=O(!0),W=()=>{C.value=!0,A.value.resetFields()},oe=async()=>{if(E.value.length===0){q.warning("请先选择要删除的用户");return}try{await Mt.confirm(`确定要删除选中的 ${E.value.length} 个用户吗？删除后数据不可恢复`,"批量删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const g=new FormData;g.append("controlCode","del");const b=E.value.map(K=>K.id);g.append("id",b.join(",")),await ne.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}}),q.success(`成功删除 ${E.value.length} 个用户`),await Y(),E.value=[]}catch(g){g!=="cancel"&&q.error(`删除失败：${g.message||"未知错误"}`)}},fe=async g=>{A.value&&A.value.resetFields(),k.value=!0,R.value=g.id,C.value=!0,N.value="编辑用户信息",T.value=!1,h.name=g.name,h.id_number=g.id_number,h.phone=g.phone,h.archive_birthdate=g.archive_birthdate,h.gender=g.gender,console.log("性别值:",g.gender),h.short_code=g.short_code,h.alt_phone_1=g.alt_phone_1,h.alt_phone_2=g.alt_phone_2,h.landline=g.landline,h.organization_unit=g.organization_unit,h.work_unit=g.work_unit,h.employment_date=g.employment_date,h.political_status=g.political_status,h.party_join_date=g.party_join_date,h.personnel_type=g.personnel_type,h.police_number=g.police_number,h.is_assisting_officer=g.is_assisting_officer,h.employment_status=g.employment_status,h.job_rank=g.job_rank,h.current_rank_date=g.current_rank_date,h.position=g.position,h.current_position_date=g.current_position_date,await re(g.organization_unit),h.sort_order=g.sort_order,console.log("排序值:",g.sort_order),h.desc=g.desc},we=g=>{N.value="查看用户详情",U.value={...g},T.value=!0,C.value=!0},Q=async g=>{try{await Mt.confirm(`确定要删除用户 ${g.name} 吗？删除后数据不可恢复`,"删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const b=new FormData;b.append("controlCode","del"),b.append("id",g.id),await ne.post("api/user_manage.php",b,{headers:{"Content-Type":"multipart/form-data"}}),q.success("用户删除成功"),await Y()}catch(b){b!=="cancel"&&q.error(`删除失败：${b.message||"未知错误"}`)}},xe=g=>{E.value=g},$=async g=>{console.log("点击的部门名称:",g),o.value=g.id,await Y()},G=async()=>{try{await A.value.validate();const g=new FormData;k.value?(g.append("controlCode","modify"),g.append("id",R.value)):g.append("controlCode","add"),g.append("name",h.name),g.append("id_number",h.id_number||""),g.append("phone",h.phone),g.append("archive_birthdate",h.archive_birthdate||""),g.append("gender",h.gender||""),g.append("short_code",h.short_code||""),g.append("alt_phone_1",h.alt_phone_1||""),g.append("alt_phone_2",h.alt_phone_2||""),g.append("landline",h.landline||"");const b=Array.isArray(h.organization_unit)?h.organization_unit[h.organization_unit.length-1]:h.organization_unit;g.append("organization_unit",b||"");const K=Array.isArray(h.work_unit)?h.work_unit[h.work_unit.length-1]:h.work_unit;g.append("work_unit",K||b),g.append("employment_date",h.employment_date||""),g.append("political_status",h.political_status||""),g.append("party_join_date",h.party_join_date||""),g.append("personnel_type",h.personnel_type||""),g.append("police_number",h.police_number||""),g.append("assisting_officer",h.is_assisting_officer||""),g.append("employment_status",h.employment_status||""),g.append("job_rank",h.job_rank||""),g.append("current_rank_date",h.current_rank_date||""),g.append("position",h.position||""),g.append("current_position_date",h.current_position_date||""),g.append("sort_order",h.sort_order+1),g.append("desc",h.desc||""),await ne.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});const pe=k.value?"编辑":"添加";q.success(`用户${pe}成功`),C.value=!1,k.value=!1,R.value=null,await Y(),A.value.resetFields()}catch(g){q.error("提交失败："+(g.message||"未知错误"))}},L=ke(()=>{const g=new Map;return d.value.forEach(b=>{g.set(b.id,b.unit_name)}),g}),z=g=>(console.log("单位ID:",g),console.log("组织映射:",L.value),console.log("单位名称:",L.value.get(g)),L.value.get(g)||"未知单位"),ae=async g=>{try{const b=new FormData;b.append("controlCode","modify"),b.append("id",g.id);const K=g.sort_order-1;b.append("sort_order",K),await ne.post("api/user_manage.php",b),await Y(),q.success("上移成功")}catch{q.error("上移失败，请稍后重试")}},me=async g=>{try{const b=new FormData;b.append("controlCode","modify"),b.append("id",g.id);const K=g.sort_order+1;b.append("sort_order",K),await ne.post("api/user_manage.php",b),await Y(),q.success("下移成功")}catch{q.error("下移失败，请稍后重试")}};st(J,async g=>{c.value=1,await Y()}),st(Z,async g=>{c.value=1,await Y()});const re=async g=>{if(!g){a.value=[];return}try{const b=new FormData;b.append("controlCode","getSortOptions"),b.append("organization_unit",g);const K=await ne.post("/api/user_manage.php",b,{headers:{"Content-Type":"multipart/form-data"}});a.value=K.data,console.log("获取排序选项成功:",a.value)}catch(b){console.error("获取排序选项失败:",b),q.error("获取排序选项失败，请稍后重试"),a.value=[]}},te=async g=>{if(!g){l.value=[];return}try{const b=new FormData;b.append("controlCode","getPspInfo"),b.append("organization_unit",g);const K=await ne({url:"/api/user_manage.php",method:"post",data:b});K.status===1?l.value=K.data.map(pe=>({label:pe.name,value:pe.id})):q.error("获取带辅民警数据失败")}catch{q.error("网络请求失败")}};return st(()=>h.organization_unit,async g=>{const b=Array.isArray(g)?g[g.length-1]:g;await re(b),await te(b)}),(g,b)=>{const K=F("el-input"),pe=F("el-button"),_e=F("el-table-column"),wr=F("el-table"),Nl=F("el-tag"),xr=F("el-switch"),vt=F("el-icon"),Ul=F("el-button-group"),Tl=F("el-pagination"),de=F("el-form-item"),yt=F("el-date-picker"),Ce=F("el-option"),Ze=F("el-select"),br=F("el-cascader"),zl=F("el-form"),Ol=F("el-dialog");return j(),se("div",gi,[X("div",vi,[p(K,{modelValue:m.value,"onUpdate:modelValue":b[0]||(b[0]=D=>m.value=D),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),p(wr,{data:M.value,border:"",class:"department-table"},{default:_(()=>[p(_e,{label:"操作",width:"55"},{default:_(({row:D})=>[D.children&&D.children.length>0?(j(),be(pe,{key:0,type:"text",size:"small",onClick:Ro(ot=>x(D),["stop"])},{default:_(()=>[ee(Ee(D.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Pt("",!0)]),_:1}),p(_e,{prop:"unit_name",label:"单位名称"},{default:_(({row:D})=>[X("span",{class:"indent",style:ur({width:`${D.indent}px`})},null,4),X("span",{onClick:ot=>$(D),style:{cursor:"pointer"}},Ee(D.unit_name),9,yi)]),_:1})]),_:1},8,["data"])]),X("div",wi,[X("div",xi,[X("div",bi,[p(pe,{type:"primary",onClick:W},{default:_(()=>b[31]||(b[31]=[ee("新增")])),_:1,__:[31]}),p(pe,{type:"danger",onClick:oe},{default:_(()=>b[32]||(b[32]=[ee("删除")])),_:1,__:[32]})]),X("div",ki,[s.value?(j(),be(Nl,{key:0,type:"info",style:{"margin-left":"10px"}},{default:_(()=>[ee(" 当前单位："+Ee(s.value),1)]),_:1})):Pt("",!0),p(xr,{modelValue:Z.value,"onUpdate:modelValue":b[1]||(b[1]=D=>Z.value=D),"inline-prompt":"","active-text":"显示抽出人员","inactive-text":"不显示抽出人员",style:{"margin-left":"10px"}},null,8,["modelValue"]),p(xr,{modelValue:J.value,"onUpdate:modelValue":b[2]||(b[2]=D=>J.value=D),"active-text":"显示抽入人员","inactive-text":"显示抽入人员","inline-prompt":"",style:{"margin-left":"10px"}},null,8,["modelValue"])])]),p(wr,{data:v.value,border:"",class:"user-table",onSelectionChange:xe},{default:_(()=>[p(_e,{type:"selection",width:"55"}),p(_e,{prop:"name",label:"姓名",width:"70"}),p(_e,{prop:"id_number",label:"身份证号",width:"100"}),p(_e,{prop:"phone",label:"手机号码",width:"115"}),p(_e,{label:"性别",width:"55"},{default:_(D=>[ee(Ee(D.row.gender===1?"男":D.row.gender===2?"女":"未知"),1)]),_:1}),p(_e,{label:"编制单位"},{default:_(D=>[ee(Ee(z(D.row.organization_unit)),1)]),_:1}),p(_e,{label:"工作单位"},{default:_(D=>[ee(Ee(z(D.row.work_unit)),1)]),_:1}),p(_e,{prop:"personnel_type",label:"人员身份"}),p(_e,{prop:"employment_status",label:"人员状态"}),p(_e,{prop:"desc",label:"备注"}),p(_e,{label:"操作",width:"260",align:"center"},{default:_(({row:D})=>[p(Ul,null,{default:_(()=>[p(pe,{type:"warning",size:"mini",onClick:ot=>fe(D)},{default:_(()=>[p(vt,null,{default:_(()=>[p(ue(wn))]),_:1})]),_:2},1032,["onClick"]),p(pe,{type:"success",size:"mini",onClick:ot=>we(D)},{default:_(()=>[p(vt,null,{default:_(()=>[p(ue(Xl))]),_:1})]),_:2},1032,["onClick"]),p(pe,{type:"danger",size:"mini",onClick:ot=>Q(D)},{default:_(()=>[p(vt,null,{default:_(()=>[p(ue(xn))]),_:1})]),_:2},1032,["onClick"]),p(pe,{size:"mini",type:"info",onClick:ot=>ae(D),disabled:D.sort_order<=1},{default:_(()=>[p(vt,null,{default:_(()=>[p(ue($o))]),_:1})]),_:2},1032,["onClick","disabled"]),p(pe,{size:"mini",type:"info",onClick:ot=>me(D),disabled:D.sort_order>=D.sortMax},{default:_(()=>[p(vt,null,{default:_(()=>[p(ue(Io))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),p(Tl,{"current-page":c.value,"page-size":f.value,total:i.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:b[3]||(b[3]=D=>{c.value=D,Y()}),onSizeChange:b[4]||(b[4]=D=>{f.value=D,c.value=1,Y()})},null,8,["current-page","page-size","total"])]),p(Ol,{modelValue:C.value,"onUpdate:modelValue":b[30]||(b[30]=D=>C.value=D),title:N.value,width:"1000px"},{footer:_(()=>[X("span",Mi,[T.value?Pt("",!0):(j(),be(pe,{key:0,onClick:b[29]||(b[29]=D=>C.value=!1)},{default:_(()=>b[34]||(b[34]=[ee("取消")])),_:1,__:[34]})),p(pe,{type:"primary",onClick:I},{default:_(()=>b[35]||(b[35]=[ee("确定")])),_:1,__:[35]})])]),default:_(()=>[p(zl,{model:h,rules:y,ref_key:"newUserFormRef",ref:A,"label-width":"120px",inline:!1,class:"user-form",disabled:T.value},{default:_(()=>[X("div",Si,[p(de,{label:"姓名",prop:"name",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.name,"onUpdate:modelValue":b[5]||(b[5]=D=>h.name=D)},null,8,["modelValue"])]),_:1}),p(de,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.id_number,"onUpdate:modelValue":b[6]||(b[6]=D=>h.id_number=D),maxlength:"18"},null,8,["modelValue"])]),_:1}),p(de,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.phone,"onUpdate:modelValue":b[7]||(b[7]=D=>h.phone=D),maxlength:"11"},null,8,["modelValue"])]),_:1})]),X("div",Ci,[p(de,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:_(()=>[p(yt,{modelValue:h.archive_birthdate,"onUpdate:modelValue":b[8]||(b[8]=D=>h.archive_birthdate=D),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(de,{label:"性别",prop:"gender",style:{flex:"1"}},{default:_(()=>[p(Ze,{modelValue:h.gender,"onUpdate:modelValue":b[9]||(b[9]=D=>h.gender=D)},{default:_(()=>[p(Ce,{label:"未知",value:0}),p(Ce,{label:"男",value:1}),p(Ce,{label:"女",value:2})]),_:1},8,["modelValue"])]),_:1}),p(de,{label:"备注",prop:"desc",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.desc,"onUpdate:modelValue":b[10]||(b[10]=D=>h.desc=D)},null,8,["modelValue"])]),_:1})]),X("div",Ei,[p(de,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.short_code,"onUpdate:modelValue":b[11]||(b[11]=D=>h.short_code=D)},null,8,["modelValue"]),b[33]||(b[33]=X("div",{class:""},null,-1))]),_:1,__:[33]}),p(de,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.alt_phone_1,"onUpdate:modelValue":b[12]||(b[12]=D=>h.alt_phone_1=D)},null,8,["modelValue"])]),_:1}),p(de,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.alt_phone_2,"onUpdate:modelValue":b[13]||(b[13]=D=>h.alt_phone_2=D)},null,8,["modelValue"])]),_:1})]),X("div",Vi,[p(de,{label:"座机",prop:"landline",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.landline,"onUpdate:modelValue":b[14]||(b[14]=D=>h.landline=D)},null,8,["modelValue"])]),_:1}),p(de,{label:"编制单位",prop:"organization_unit",required:""},{default:_(()=>[p(br,{modelValue:h.organization_unit,"onUpdate:modelValue":b[15]||(b[15]=D=>h.organization_unit=D),options:w.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),p(de,{label:"工作单位",prop:"work_unit"},{default:_(()=>[p(br,{modelValue:h.work_unit,"onUpdate:modelValue":b[16]||(b[16]=D=>h.work_unit=D),options:P.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),X("div",Ri,[p(de,{label:"参工日期",prop:"employment_date",style:{flex:"1"}},{default:_(()=>[p(yt,{modelValue:h.employment_date,"onUpdate:modelValue":b[17]||(b[17]=D=>h.employment_date=D),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(de,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:_(()=>[p(Ze,{modelValue:h.political_status,"onUpdate:modelValue":b[18]||(b[18]=D=>h.political_status=D),placeholder:"请选择政治面貌"},{default:_(()=>[p(Ce,{label:"中共党员",value:"中共党员"}),p(Ce,{label:"中共预备党员",value:"中共预备党员"}),p(Ce,{label:"共青团员",value:"共青团员"}),p(Ce,{label:"民主党派",value:"民主党派"}),p(Ce,{label:"无党派人士",value:"无党派人士"}),p(Ce,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),p(de,{label:"加入组织日期",prop:"party_join_date",style:{flex:"1"}},{default:_(()=>[p(yt,{modelValue:h.party_join_date,"onUpdate:modelValue":b[19]||(b[19]=D=>h.party_join_date=D),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),X("div",Pi,[p(de,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:_(()=>[p(Ze,{modelValue:h.personnel_type,"onUpdate:modelValue":b[20]||(b[20]=D=>h.personnel_type=D),placeholder:"请选择人员身份"},{default:_(()=>[(j(!0),se(Se,null,Ve(t.value,D=>(j(),be(Ce,{key:D.value,label:D.label,value:D.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),p(de,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.police_number,"onUpdate:modelValue":b[21]||(b[21]=D=>h.police_number=D)},null,8,["modelValue"])]),_:1}),p(de,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:_(()=>[p(Ze,{modelValue:h.is_assisting_officer,"onUpdate:modelValue":b[22]||(b[22]=D=>h.is_assisting_officer=D),placeholder:"请选择带辅民警",clearable:""},{default:_(()=>[(j(!0),se(Se,null,Ve(l.value,D=>(j(),be(Ce,{key:D.value,label:D.label,value:D.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),X("div",$i,[p(de,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:_(()=>[p(Ze,{modelValue:h.employment_status,"onUpdate:modelValue":b[23]||(b[23]=D=>h.employment_status=D)},{default:_(()=>[(j(!0),se(Se,null,Ve(n.value,D=>(j(),be(Ce,{key:D.value,label:D.label,value:D.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),p(de,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:_(()=>[p(Ze,{modelValue:h.job_rank,"onUpdate:modelValue":b[24]||(b[24]=D=>h.job_rank=D)},{default:_(()=>[(j(!0),se(Se,null,Ve(r.value,D=>(j(),be(Ce,{key:D.value,label:D.label,value:D.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),p(de,{label:"任现职级日期",prop:"current_rank_date",style:{flex:"1"}},{default:_(()=>[p(yt,{modelValue:h.current_rank_date,"onUpdate:modelValue":b[25]||(b[25]=D=>h.current_rank_date=D),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),X("div",Ii,[p(de,{label:"职务",prop:"position",style:{flex:"1"}},{default:_(()=>[p(K,{modelValue:h.position,"onUpdate:modelValue":b[26]||(b[26]=D=>h.position=D)},null,8,["modelValue"])]),_:1}),p(de,{label:"任现职务日期",prop:"current_position_date",style:{flex:"1"}},{default:_(()=>[p(yt,{modelValue:h.current_position_date,"onUpdate:modelValue":b[27]||(b[27]=D=>h.current_position_date=D),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(de,{label:"人员排序",prop:"sort_order",style:{flex:"1"}},{default:_(()=>[p(Ze,{modelValue:h.sort_order,"onUpdate:modelValue":b[28]||(b[28]=D=>h.sort_order=D),placeholder:"请选择人员排序"},{default:_(()=>[p(Ce,{label:"置于 最前",value:"0"}),(j(!0),se(Se,null,Ve(a.value,D=>(j(),be(Ce,{key:D.id,label:`置于 ${D.name} 之后`,value:D.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","disabled"])]),_:1},8,["modelValue","title"])])}}},Ai=gt(Di,[["__scopeId","data-v-c51400a5"]]);class et{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let o=0;o<this._n&&o<32;o++){const l=n[o],a=t+l,s=Math.abs(t)<Math.abs(l)?t-(a-l):l-(a-t);s&&(n[r++]=s),t=a}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,o,l,a=0;if(n>0){for(a=t[--n];n>0&&(r=a,o=t[--n],a=r+o,l=o-(a-r),!l););n>0&&(l<0&&t[n-1]<0||l>0&&t[n-1]>0)&&(o=l*2,r=a+o,o==r-a&&(a=r))}return a}}function*Ni(e){for(const t of e)yield*t}function Ho(e){return Array.from(Ni(e))}var Ui={value:()=>{}};function Xo(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new en(n)}function en(e){this._=e}function Ti(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}en.prototype=Xo.prototype={constructor:en,on:function(e,t){var n=this._,r=Ti(e+"",n),o,l=-1,a=r.length;if(arguments.length<2){for(;++l<a;)if((o=(e=r[l]).type)&&(o=zi(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++l<a;)if(o=(e=r[l]).type)n[o]=qr(n[o],e.name,t);else if(t==null)for(o in n)n[o]=qr(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new en(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,l;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(l=this._[e],r=0,o=l.length;r<o;++r)l[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,l=r.length;o<l;++o)r[o].value.apply(t,n)}};function zi(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function qr(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=Ui,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var Ln="http://www.w3.org/1999/xhtml";const Yr={svg:"http://www.w3.org/2000/svg",xhtml:Ln,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Sn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Yr.hasOwnProperty(t)?{space:Yr[t],local:e}:e}function Oi(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Ln&&t.documentElement.namespaceURI===Ln?t.createElement(e):t.createElementNS(n,e)}}function Fi(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Go(e){var t=Sn(e);return(t.local?Fi:Oi)(t)}function Li(){}function dr(e){return e==null?Li:function(){return this.querySelector(e)}}function qi(e){typeof e!="function"&&(e=dr(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var l=t[o],a=l.length,s=r[o]=new Array(a),c,f,i=0;i<a;++i)(c=l[i])&&(f=e.call(c,c.__data__,i,l))&&("__data__"in c&&(f.__data__=c.__data__),s[i]=f);return new Ie(r,this._parents)}function Yi(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Bi(){return[]}function Ko(e){return e==null?Bi:function(){return this.querySelectorAll(e)}}function Hi(e){return function(){return Yi(e.apply(this,arguments))}}function Xi(e){typeof e=="function"?e=Hi(e):e=Ko(e);for(var t=this._groups,n=t.length,r=[],o=[],l=0;l<n;++l)for(var a=t[l],s=a.length,c,f=0;f<s;++f)(c=a[f])&&(r.push(e.call(c,c.__data__,f,a)),o.push(c));return new Ie(r,o)}function Wo(e){return function(){return this.matches(e)}}function Qo(e){return function(t){return t.matches(e)}}var Gi=Array.prototype.find;function Ki(e){return function(){return Gi.call(this.children,e)}}function Wi(){return this.firstElementChild}function Qi(e){return this.select(e==null?Wi:Ki(typeof e=="function"?e:Qo(e)))}var Zi=Array.prototype.filter;function Ji(){return Array.from(this.children)}function ji(e){return function(){return Zi.call(this.children,e)}}function es(e){return this.selectAll(e==null?Ji:ji(typeof e=="function"?e:Qo(e)))}function ts(e){typeof e!="function"&&(e=Wo(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var l=t[o],a=l.length,s=r[o]=[],c,f=0;f<a;++f)(c=l[f])&&e.call(c,c.__data__,f,l)&&s.push(c);return new Ie(r,this._parents)}function Zo(e){return new Array(e.length)}function ns(){return new Ie(this._enter||this._groups.map(Zo),this._parents)}function ln(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}ln.prototype={constructor:ln,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function rs(e){return function(){return e}}function os(e,t,n,r,o,l){for(var a=0,s,c=t.length,f=l.length;a<f;++a)(s=t[a])?(s.__data__=l[a],r[a]=s):n[a]=new ln(e,l[a]);for(;a<c;++a)(s=t[a])&&(o[a]=s)}function ls(e,t,n,r,o,l,a){var s,c,f=new Map,i=t.length,u=l.length,d=new Array(i),m;for(s=0;s<i;++s)(c=t[s])&&(d[s]=m=a.call(c,c.__data__,s,t)+"",f.has(m)?o[s]=c:f.set(m,c));for(s=0;s<u;++s)m=a.call(e,l[s],s,l)+"",(c=f.get(m))?(r[s]=c,c.__data__=l[s],f.delete(m)):n[s]=new ln(e,l[s]);for(s=0;s<i;++s)(c=t[s])&&f.get(d[s])===c&&(o[s]=c)}function as(e){return e.__data__}function is(e,t){if(!arguments.length)return Array.from(this,as);var n=t?ls:os,r=this._parents,o=this._groups;typeof e!="function"&&(e=rs(e));for(var l=o.length,a=new Array(l),s=new Array(l),c=new Array(l),f=0;f<l;++f){var i=r[f],u=o[f],d=u.length,m=ss(e.call(i,i&&i.__data__,f,r)),w=m.length,P=s[f]=new Array(w),v=a[f]=new Array(w),E=c[f]=new Array(d);n(i,u,P,v,E,m,t);for(var C=0,N=0,T,U;C<w;++C)if(T=P[C]){for(C>=N&&(N=C+1);!(U=v[N])&&++N<w;);T._next=U||null}}return a=new Ie(a,r),a._enter=s,a._exit=c,a}function ss(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function us(){return new Ie(this._exit||this._groups.map(Zo),this._parents)}function cs(e,t,n){var r=this.enter(),o=this,l=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?l.remove():n(l),r&&o?r.merge(o).order():o}function ds(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,l=r.length,a=Math.min(o,l),s=new Array(o),c=0;c<a;++c)for(var f=n[c],i=r[c],u=f.length,d=s[c]=new Array(u),m,w=0;w<u;++w)(m=f[w]||i[w])&&(d[w]=m);for(;c<o;++c)s[c]=n[c];return new Ie(s,this._parents)}function fs(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,l=r[o],a;--o>=0;)(a=r[o])&&(l&&a.compareDocumentPosition(l)^4&&l.parentNode.insertBefore(a,l),l=a);return this}function ps(e){e||(e=hs);function t(u,d){return u&&d?e(u.__data__,d.__data__):!u-!d}for(var n=this._groups,r=n.length,o=new Array(r),l=0;l<r;++l){for(var a=n[l],s=a.length,c=o[l]=new Array(s),f,i=0;i<s;++i)(f=a[i])&&(c[i]=f);c.sort(t)}return new Ie(o,this._parents).order()}function hs(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function ms(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function _s(){return Array.from(this)}function gs(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,l=r.length;o<l;++o){var a=r[o];if(a)return a}return null}function vs(){let e=0;for(const t of this)++e;return e}function ys(){return!this.node()}function ws(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],l=0,a=o.length,s;l<a;++l)(s=o[l])&&e.call(s,s.__data__,l,o);return this}function xs(e){return function(){this.removeAttribute(e)}}function bs(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ks(e,t){return function(){this.setAttribute(e,t)}}function Ss(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function Cs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function Es(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function Vs(e,t){var n=Sn(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?bs:xs:typeof t=="function"?n.local?Es:Cs:n.local?Ss:ks)(n,t))}function Jo(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function Rs(e){return function(){this.style.removeProperty(e)}}function Ps(e,t,n){return function(){this.style.setProperty(e,t,n)}}function $s(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function Is(e,t,n){return arguments.length>1?this.each((t==null?Rs:typeof t=="function"?$s:Ps)(e,t,n??"")):pt(this.node(),e)}function pt(e,t){return e.style.getPropertyValue(t)||Jo(e).getComputedStyle(e,null).getPropertyValue(t)}function Ms(e){return function(){delete this[e]}}function Ds(e,t){return function(){this[e]=t}}function As(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function Ns(e,t){return arguments.length>1?this.each((t==null?Ms:typeof t=="function"?As:Ds)(e,t)):this.node()[e]}function jo(e){return e.trim().split(/^|\s+/)}function fr(e){return e.classList||new el(e)}function el(e){this._node=e,this._names=jo(e.getAttribute("class")||"")}el.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function tl(e,t){for(var n=fr(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function nl(e,t){for(var n=fr(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function Us(e){return function(){tl(this,e)}}function Ts(e){return function(){nl(this,e)}}function zs(e,t){return function(){(t.apply(this,arguments)?tl:nl)(this,e)}}function Os(e,t){var n=jo(e+"");if(arguments.length<2){for(var r=fr(this.node()),o=-1,l=n.length;++o<l;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?zs:t?Us:Ts)(n,t))}function Fs(){this.textContent=""}function Ls(e){return function(){this.textContent=e}}function qs(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Ys(e){return arguments.length?this.each(e==null?Fs:(typeof e=="function"?qs:Ls)(e)):this.node().textContent}function Bs(){this.innerHTML=""}function Hs(e){return function(){this.innerHTML=e}}function Xs(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Gs(e){return arguments.length?this.each(e==null?Bs:(typeof e=="function"?Xs:Hs)(e)):this.node().innerHTML}function Ks(){this.nextSibling&&this.parentNode.appendChild(this)}function Ws(){return this.each(Ks)}function Qs(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Zs(){return this.each(Qs)}function Js(e){var t=typeof e=="function"?e:Go(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function js(){return null}function eu(e,t){var n=typeof e=="function"?e:Go(e),r=t==null?js:typeof t=="function"?t:dr(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function tu(){var e=this.parentNode;e&&e.removeChild(this)}function nu(){return this.each(tu)}function ru(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ou(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function lu(e){return this.select(e?ou:ru)}function au(e){return arguments.length?this.property("__data__",e):this.node().__data__}function iu(e){return function(t){e.call(this,t,this.__data__)}}function su(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function uu(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,l;n<o;++n)l=t[n],(!e.type||l.type===e.type)&&l.name===e.name?this.removeEventListener(l.type,l.listener,l.options):t[++r]=l;++r?t.length=r:delete this.__on}}}function cu(e,t,n){return function(){var r=this.__on,o,l=iu(t);if(r){for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=l,o.options=n),o.value=t;return}}this.addEventListener(e.type,l,n),o={type:e.type,name:e.name,value:t,listener:l,options:n},r?r.push(o):this.__on=[o]}}function du(e,t,n){var r=su(e+""),o,l=r.length,a;if(arguments.length<2){var s=this.node().__on;if(s){for(var c=0,f=s.length,i;c<f;++c)for(o=0,i=s[c];o<l;++o)if((a=r[o]).type===i.type&&a.name===i.name)return i.value}return}for(s=t?cu:uu,o=0;o<l;++o)this.each(s(r[o],t,n));return this}function rl(e,t,n){var r=Jo(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function fu(e,t){return function(){return rl(this,e,t)}}function pu(e,t){return function(){return rl(this,e,t.apply(this,arguments))}}function hu(e,t){return this.each((typeof t=="function"?pu:fu)(e,t))}function*mu(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,l=r.length,a;o<l;++o)(a=r[o])&&(yield a)}var ol=[null];function Ie(e,t){this._groups=e,this._parents=t}function Yt(){return new Ie([[document.documentElement]],ol)}function _u(){return this}Ie.prototype=Yt.prototype={constructor:Ie,select:qi,selectAll:Xi,selectChild:Qi,selectChildren:es,filter:ts,data:is,enter:ns,exit:us,join:cs,merge:ds,selection:_u,order:fs,sort:ps,call:ms,nodes:_s,node:gs,size:vs,empty:ys,each:ws,attr:Vs,style:Is,property:Ns,classed:Os,text:Ys,html:Gs,raise:Ws,lower:Zs,append:Js,insert:eu,remove:nu,clone:lu,datum:au,on:du,dispatch:hu,[Symbol.iterator]:mu};function Ht(e){return typeof e=="string"?new Ie([[document.querySelector(e)]],[document.documentElement]):new Ie([[e]],ol)}function pr(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function ll(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Bt(){}var Nt=.7,an=1/Nt,ut="\\s*([+-]?\\d+)\\s*",Ut="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Le="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",gu=/^#([0-9a-f]{3,8})$/,vu=new RegExp(`^rgb\\(${ut},${ut},${ut}\\)$`),yu=new RegExp(`^rgb\\(${Le},${Le},${Le}\\)$`),wu=new RegExp(`^rgba\\(${ut},${ut},${ut},${Ut}\\)$`),xu=new RegExp(`^rgba\\(${Le},${Le},${Le},${Ut}\\)$`),bu=new RegExp(`^hsl\\(${Ut},${Le},${Le}\\)$`),ku=new RegExp(`^hsla\\(${Ut},${Le},${Le},${Ut}\\)$`),Br={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};pr(Bt,Tt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Hr,formatHex:Hr,formatHex8:Su,formatHsl:Cu,formatRgb:Xr,toString:Xr});function Hr(){return this.rgb().formatHex()}function Su(){return this.rgb().formatHex8()}function Cu(){return al(this).formatHsl()}function Xr(){return this.rgb().formatRgb()}function Tt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=gu.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Gr(t):n===3?new Re(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Xt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Xt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=vu.exec(e))?new Re(t[1],t[2],t[3],1):(t=yu.exec(e))?new Re(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=wu.exec(e))?Xt(t[1],t[2],t[3],t[4]):(t=xu.exec(e))?Xt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=bu.exec(e))?Qr(t[1],t[2]/100,t[3]/100,1):(t=ku.exec(e))?Qr(t[1],t[2]/100,t[3]/100,t[4]):Br.hasOwnProperty(e)?Gr(Br[e]):e==="transparent"?new Re(NaN,NaN,NaN,0):null}function Gr(e){return new Re(e>>16&255,e>>8&255,e&255,1)}function Xt(e,t,n,r){return r<=0&&(e=t=n=NaN),new Re(e,t,n,r)}function Eu(e){return e instanceof Bt||(e=Tt(e)),e?(e=e.rgb(),new Re(e.r,e.g,e.b,e.opacity)):new Re}function qn(e,t,n,r){return arguments.length===1?Eu(e):new Re(e,t,n,r??1)}function Re(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}pr(Re,qn,ll(Bt,{brighter(e){return e=e==null?an:Math.pow(an,e),new Re(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Nt:Math.pow(Nt,e),new Re(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Re(je(this.r),je(this.g),je(this.b),sn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Kr,formatHex:Kr,formatHex8:Vu,formatRgb:Wr,toString:Wr}));function Kr(){return`#${Je(this.r)}${Je(this.g)}${Je(this.b)}`}function Vu(){return`#${Je(this.r)}${Je(this.g)}${Je(this.b)}${Je((isNaN(this.opacity)?1:this.opacity)*255)}`}function Wr(){const e=sn(this.opacity);return`${e===1?"rgb(":"rgba("}${je(this.r)}, ${je(this.g)}, ${je(this.b)}${e===1?")":`, ${e})`}`}function sn(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function je(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Je(e){return e=je(e),(e<16?"0":"")+e.toString(16)}function Qr(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Ne(e,t,n,r)}function al(e){if(e instanceof Ne)return new Ne(e.h,e.s,e.l,e.opacity);if(e instanceof Bt||(e=Tt(e)),!e)return new Ne;if(e instanceof Ne)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),l=Math.max(t,n,r),a=NaN,s=l-o,c=(l+o)/2;return s?(t===l?a=(n-r)/s+(n<r)*6:n===l?a=(r-t)/s+2:a=(t-n)/s+4,s/=c<.5?l+o:2-l-o,a*=60):s=c>0&&c<1?0:a,new Ne(a,s,c,e.opacity)}function Ru(e,t,n,r){return arguments.length===1?al(e):new Ne(e,t,n,r??1)}function Ne(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}pr(Ne,Ru,ll(Bt,{brighter(e){return e=e==null?an:Math.pow(an,e),new Ne(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Nt:Math.pow(Nt,e),new Ne(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new Re(Mn(e>=240?e-240:e+120,o,r),Mn(e,o,r),Mn(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new Ne(Zr(this.h),Gt(this.s),Gt(this.l),sn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=sn(this.opacity);return`${e===1?"hsl(":"hsla("}${Zr(this.h)}, ${Gt(this.s)*100}%, ${Gt(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Zr(e){return e=(e||0)%360,e<0?e+360:e}function Gt(e){return Math.max(0,Math.min(1,e||0))}function Mn(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const il=e=>()=>e;function Pu(e,t){return function(n){return e+n*t}}function $u(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function Iu(e){return(e=+e)==1?sl:function(t,n){return n-t?$u(t,n,e):il(isNaN(t)?n:t)}}function sl(e,t){var n=t-e;return n?Pu(e,n):il(isNaN(e)?t:e)}const Jr=function e(t){var n=Iu(t);function r(o,l){var a=n((o=qn(o)).r,(l=qn(l)).r),s=n(o.g,l.g),c=n(o.b,l.b),f=sl(o.opacity,l.opacity);return function(i){return o.r=a(i),o.g=s(i),o.b=c(i),o.opacity=f(i),o+""}}return r.gamma=e,r}(1);function We(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Yn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Dn=new RegExp(Yn.source,"g");function Mu(e){return function(){return e}}function Du(e){return function(t){return e(t)+""}}function Au(e,t){var n=Yn.lastIndex=Dn.lastIndex=0,r,o,l,a=-1,s=[],c=[];for(e=e+"",t=t+"";(r=Yn.exec(e))&&(o=Dn.exec(t));)(l=o.index)>n&&(l=t.slice(n,l),s[a]?s[a]+=l:s[++a]=l),(r=r[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,c.push({i:a,x:We(r,o)})),n=Dn.lastIndex;return n<t.length&&(l=t.slice(n),s[a]?s[a]+=l:s[++a]=l),s.length<2?c[0]?Du(c[0].x):Mu(t):(t=c.length,function(f){for(var i=0,u;i<t;++i)s[(u=c[i]).i]=u.x(f);return s.join("")})}var jr=180/Math.PI,Bn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function ul(e,t,n,r,o,l){var a,s,c;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,c/=s),e*r<t*n&&(e=-e,t=-t,c=-c,a=-a),{translateX:o,translateY:l,rotate:Math.atan2(t,e)*jr,skewX:Math.atan(c)*jr,scaleX:a,scaleY:s}}var Kt;function Nu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Bn:ul(t.a,t.b,t.c,t.d,t.e,t.f)}function Uu(e){return e==null||(Kt||(Kt=document.createElementNS("http://www.w3.org/2000/svg","g")),Kt.setAttribute("transform",e),!(e=Kt.transform.baseVal.consolidate()))?Bn:(e=e.matrix,ul(e.a,e.b,e.c,e.d,e.e,e.f))}function cl(e,t,n,r){function o(f){return f.length?f.pop()+" ":""}function l(f,i,u,d,m,w){if(f!==u||i!==d){var P=m.push("translate(",null,t,null,n);w.push({i:P-4,x:We(f,u)},{i:P-2,x:We(i,d)})}else(u||d)&&m.push("translate("+u+t+d+n)}function a(f,i,u,d){f!==i?(f-i>180?i+=360:i-f>180&&(f+=360),d.push({i:u.push(o(u)+"rotate(",null,r)-2,x:We(f,i)})):i&&u.push(o(u)+"rotate("+i+r)}function s(f,i,u,d){f!==i?d.push({i:u.push(o(u)+"skewX(",null,r)-2,x:We(f,i)}):i&&u.push(o(u)+"skewX("+i+r)}function c(f,i,u,d,m,w){if(f!==u||i!==d){var P=m.push(o(m)+"scale(",null,",",null,")");w.push({i:P-4,x:We(f,u)},{i:P-2,x:We(i,d)})}else(u!==1||d!==1)&&m.push(o(m)+"scale("+u+","+d+")")}return function(f,i){var u=[],d=[];return f=e(f),i=e(i),l(f.translateX,f.translateY,i.translateX,i.translateY,u,d),a(f.rotate,i.rotate,u,d),s(f.skewX,i.skewX,u,d),c(f.scaleX,f.scaleY,i.scaleX,i.scaleY,u,d),f=i=null,function(m){for(var w=-1,P=d.length,v;++w<P;)u[(v=d[w]).i]=v.x(m);return u.join("")}}}var Tu=cl(Nu,"px, ","px)","deg)"),zu=cl(Uu,", ",")",")"),ht=0,bt=0,xt=0,dl=1e3,un,kt,cn=0,tt=0,Cn=0,zt=typeof performance=="object"&&performance.now?performance:Date,fl=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function hr(){return tt||(fl(Ou),tt=zt.now()+Cn)}function Ou(){tt=0}function dn(){this._call=this._time=this._next=null}dn.prototype=pl.prototype={constructor:dn,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?hr():+n)+(t==null?0:+t),!this._next&&kt!==this&&(kt?kt._next=this:un=this,kt=this),this._call=e,this._time=n,Hn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Hn())}};function pl(e,t,n){var r=new dn;return r.restart(e,t,n),r}function Fu(){hr(),++ht;for(var e=un,t;e;)(t=tt-e._time)>=0&&e._call.call(void 0,t),e=e._next;--ht}function eo(){tt=(cn=zt.now())+Cn,ht=bt=0;try{Fu()}finally{ht=0,qu(),tt=0}}function Lu(){var e=zt.now(),t=e-cn;t>dl&&(Cn-=t,cn=e)}function qu(){for(var e,t=un,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:un=n);kt=e,Hn(r)}function Hn(e){if(!ht){bt&&(bt=clearTimeout(bt));var t=e-tt;t>24?(e<1/0&&(bt=setTimeout(eo,e-zt.now()-Cn)),xt&&(xt=clearInterval(xt))):(xt||(cn=zt.now(),xt=setInterval(Lu,dl)),ht=1,fl(eo))}}function to(e,t,n){var r=new dn;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var Yu=Xo("start","end","cancel","interrupt"),Bu=[],hl=0,no=1,Xn=2,tn=3,ro=4,Gn=5,nn=6;function En(e,t,n,r,o,l){var a=e.__transition;if(!a)e.__transition={};else if(n in a)return;Hu(e,n,{name:t,index:r,group:o,on:Yu,tween:Bu,time:l.time,delay:l.delay,duration:l.duration,ease:l.ease,timer:null,state:hl})}function mr(e,t){var n=ze(e,t);if(n.state>hl)throw new Error("too late; already scheduled");return n}function qe(e,t){var n=ze(e,t);if(n.state>tn)throw new Error("too late; already running");return n}function ze(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Hu(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=pl(l,0,n.time);function l(f){n.state=no,n.timer.restart(a,n.delay,n.time),n.delay<=f&&a(f-n.delay)}function a(f){var i,u,d,m;if(n.state!==no)return c();for(i in r)if(m=r[i],m.name===n.name){if(m.state===tn)return to(a);m.state===ro?(m.state=nn,m.timer.stop(),m.on.call("interrupt",e,e.__data__,m.index,m.group),delete r[i]):+i<t&&(m.state=nn,m.timer.stop(),m.on.call("cancel",e,e.__data__,m.index,m.group),delete r[i])}if(to(function(){n.state===tn&&(n.state=ro,n.timer.restart(s,n.delay,n.time),s(f))}),n.state=Xn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Xn){for(n.state=tn,o=new Array(d=n.tween.length),i=0,u=-1;i<d;++i)(m=n.tween[i].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=m);o.length=u+1}}function s(f){for(var i=f<n.duration?n.ease.call(null,f/n.duration):(n.timer.restart(c),n.state=Gn,1),u=-1,d=o.length;++u<d;)o[u].call(e,i);n.state===Gn&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){n.state=nn,n.timer.stop(),delete r[t];for(var f in r)return;delete e.__transition}}function Xu(e,t){var n=e.__transition,r,o,l=!0,a;if(n){t=t==null?null:t+"";for(a in n){if((r=n[a]).name!==t){l=!1;continue}o=r.state>Xn&&r.state<Gn,r.state=nn,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[a]}l&&delete e.__transition}}function Gu(e){return this.each(function(){Xu(this,e)})}function Ku(e,t){var n,r;return function(){var o=qe(this,e),l=o.tween;if(l!==n){r=n=l;for(var a=0,s=r.length;a<s;++a)if(r[a].name===t){r=r.slice(),r.splice(a,1);break}}o.tween=r}}function Wu(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var l=qe(this,e),a=l.tween;if(a!==r){o=(r=a).slice();for(var s={name:t,value:n},c=0,f=o.length;c<f;++c)if(o[c].name===t){o[c]=s;break}c===f&&o.push(s)}l.tween=o}}function Qu(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=ze(this.node(),n).tween,o=0,l=r.length,a;o<l;++o)if((a=r[o]).name===e)return a.value;return null}return this.each((t==null?Ku:Wu)(n,e,t))}function _r(e,t,n){var r=e._id;return e.each(function(){var o=qe(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return ze(o,r).value[t]}}function ml(e,t){var n;return(typeof t=="number"?We:t instanceof Tt?Jr:(n=Tt(t))?(t=n,Jr):Au)(e,t)}function Zu(e){return function(){this.removeAttribute(e)}}function Ju(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ju(e,t,n){var r,o=n+"",l;return function(){var a=this.getAttribute(e);return a===o?null:a===r?l:l=t(r=a,n)}}function ec(e,t,n){var r,o=n+"",l;return function(){var a=this.getAttributeNS(e.space,e.local);return a===o?null:a===r?l:l=t(r=a,n)}}function tc(e,t,n){var r,o,l;return function(){var a,s=n(this),c;return s==null?void this.removeAttribute(e):(a=this.getAttribute(e),c=s+"",a===c?null:a===r&&c===o?l:(o=c,l=t(r=a,s)))}}function nc(e,t,n){var r,o,l;return function(){var a,s=n(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local),c=s+"",a===c?null:a===r&&c===o?l:(o=c,l=t(r=a,s)))}}function rc(e,t){var n=Sn(e),r=n==="transform"?zu:ml;return this.attrTween(e,typeof t=="function"?(n.local?nc:tc)(n,r,_r(this,"attr."+e,t)):t==null?(n.local?Ju:Zu)(n):(n.local?ec:ju)(n,r,t))}function oc(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function lc(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function ac(e,t){var n,r;function o(){var l=t.apply(this,arguments);return l!==r&&(n=(r=l)&&lc(e,l)),n}return o._value=t,o}function ic(e,t){var n,r;function o(){var l=t.apply(this,arguments);return l!==r&&(n=(r=l)&&oc(e,l)),n}return o._value=t,o}function sc(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=Sn(e);return this.tween(n,(r.local?ac:ic)(r,t))}function uc(e,t){return function(){mr(this,e).delay=+t.apply(this,arguments)}}function cc(e,t){return t=+t,function(){mr(this,e).delay=t}}function dc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?uc:cc)(t,e)):ze(this.node(),t).delay}function fc(e,t){return function(){qe(this,e).duration=+t.apply(this,arguments)}}function pc(e,t){return t=+t,function(){qe(this,e).duration=t}}function hc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?fc:pc)(t,e)):ze(this.node(),t).duration}function mc(e,t){if(typeof t!="function")throw new Error;return function(){qe(this,e).ease=t}}function _c(e){var t=this._id;return arguments.length?this.each(mc(t,e)):ze(this.node(),t).ease}function gc(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;qe(this,e).ease=n}}function vc(e){if(typeof e!="function")throw new Error;return this.each(gc(this._id,e))}function yc(e){typeof e!="function"&&(e=Wo(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var l=t[o],a=l.length,s=r[o]=[],c,f=0;f<a;++f)(c=l[f])&&e.call(c,c.__data__,f,l)&&s.push(c);return new Xe(r,this._parents,this._name,this._id)}function wc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,l=Math.min(r,o),a=new Array(r),s=0;s<l;++s)for(var c=t[s],f=n[s],i=c.length,u=a[s]=new Array(i),d,m=0;m<i;++m)(d=c[m]||f[m])&&(u[m]=d);for(;s<r;++s)a[s]=t[s];return new Xe(a,this._parents,this._name,this._id)}function xc(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function bc(e,t,n){var r,o,l=xc(t)?mr:qe;return function(){var a=l(this,e),s=a.on;s!==r&&(o=(r=s).copy()).on(t,n),a.on=o}}function kc(e,t){var n=this._id;return arguments.length<2?ze(this.node(),n).on.on(e):this.each(bc(n,e,t))}function Sc(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function Cc(){return this.on("end.remove",Sc(this._id))}function Ec(e){var t=this._name,n=this._id;typeof e!="function"&&(e=dr(e));for(var r=this._groups,o=r.length,l=new Array(o),a=0;a<o;++a)for(var s=r[a],c=s.length,f=l[a]=new Array(c),i,u,d=0;d<c;++d)(i=s[d])&&(u=e.call(i,i.__data__,d,s))&&("__data__"in i&&(u.__data__=i.__data__),f[d]=u,En(f[d],t,n,d,f,ze(i,n)));return new Xe(l,this._parents,t,n)}function Vc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Ko(e));for(var r=this._groups,o=r.length,l=[],a=[],s=0;s<o;++s)for(var c=r[s],f=c.length,i,u=0;u<f;++u)if(i=c[u]){for(var d=e.call(i,i.__data__,u,c),m,w=ze(i,n),P=0,v=d.length;P<v;++P)(m=d[P])&&En(m,t,n,P,d,w);l.push(d),a.push(i)}return new Xe(l,a,t,n)}var Rc=Yt.prototype.constructor;function Pc(){return new Rc(this._groups,this._parents)}function $c(e,t){var n,r,o;return function(){var l=pt(this,e),a=(this.style.removeProperty(e),pt(this,e));return l===a?null:l===n&&a===r?o:o=t(n=l,r=a)}}function _l(e){return function(){this.style.removeProperty(e)}}function Ic(e,t,n){var r,o=n+"",l;return function(){var a=pt(this,e);return a===o?null:a===r?l:l=t(r=a,n)}}function Mc(e,t,n){var r,o,l;return function(){var a=pt(this,e),s=n(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),pt(this,e))),a===c?null:a===r&&c===o?l:(o=c,l=t(r=a,s))}}function Dc(e,t){var n,r,o,l="style."+t,a="end."+l,s;return function(){var c=qe(this,e),f=c.on,i=c.value[l]==null?s||(s=_l(t)):void 0;(f!==n||o!==i)&&(r=(n=f).copy()).on(a,o=i),c.on=r}}function Ac(e,t,n){var r=(e+="")=="transform"?Tu:ml;return t==null?this.styleTween(e,$c(e,r)).on("end.style."+e,_l(e)):typeof t=="function"?this.styleTween(e,Mc(e,r,_r(this,"style."+e,t))).each(Dc(this._id,e)):this.styleTween(e,Ic(e,r,t),n).on("end.style."+e,null)}function Nc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function Uc(e,t,n){var r,o;function l(){var a=t.apply(this,arguments);return a!==o&&(r=(o=a)&&Nc(e,a,n)),r}return l._value=t,l}function Tc(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,Uc(e,t,n??""))}function zc(e){return function(){this.textContent=e}}function Oc(e){return function(){var t=e(this);this.textContent=t??""}}function Fc(e){return this.tween("text",typeof e=="function"?Oc(_r(this,"text",e)):zc(e==null?"":e+""))}function Lc(e){return function(t){this.textContent=e.call(this,t)}}function qc(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&Lc(o)),t}return r._value=e,r}function Yc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,qc(e))}function Bc(){for(var e=this._name,t=this._id,n=gl(),r=this._groups,o=r.length,l=0;l<o;++l)for(var a=r[l],s=a.length,c,f=0;f<s;++f)if(c=a[f]){var i=ze(c,t);En(c,e,n,f,a,{time:i.time+i.delay+i.duration,delay:0,duration:i.duration,ease:i.ease})}return new Xe(r,this._parents,e,n)}function Hc(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(l,a){var s={value:a},c={value:function(){--o===0&&l()}};n.each(function(){var f=qe(this,r),i=f.on;i!==e&&(t=(e=i).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),f.on=t}),o===0&&l()})}var Xc=0;function Xe(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function gl(){return++Xc}var Be=Yt.prototype;Xe.prototype={constructor:Xe,select:Ec,selectAll:Vc,selectChild:Be.selectChild,selectChildren:Be.selectChildren,filter:yc,merge:wc,selection:Pc,transition:Bc,call:Be.call,nodes:Be.nodes,node:Be.node,size:Be.size,empty:Be.empty,each:Be.each,on:kc,attr:rc,attrTween:sc,style:Ac,styleTween:Tc,text:Fc,textTween:Yc,remove:Cc,tween:Qu,delay:dc,duration:hc,ease:_c,easeVarying:vc,end:Hc,[Symbol.iterator]:Be[Symbol.iterator]};function Gc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Kc={time:null,delay:0,duration:250,ease:Gc};function Wc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Qc(e){var t,n;e instanceof Xe?(t=e._id,e=e._name):(t=gl(),(n=Kc).time=hr(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,l=0;l<o;++l)for(var a=r[l],s=a.length,c,f=0;f<s;++f)(c=a[f])&&En(c,e,t,f,a,n||Wc(c,t));return new Xe(r,this._parents,e,t)}Yt.prototype.interrupt=Gu;Yt.prototype.transition=Qc;function Zc(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function Jc(e,t){return fetch(e,t).then(Zc)}var ce=1e-6,le=Math.PI,Pe=le/2,oo=le/4,Me=le*2,$e=180/le,ye=le/180,he=Math.abs,vl=Math.atan,Ot=Math.atan2,ge=Math.cos,jc=Math.exp,ed=Math.log,ve=Math.sin,td=Math.sign||function(e){return e>0?1:e<0?-1:0},rt=Math.sqrt,nd=Math.tan;function rd(e){return e>1?0:e<-1?le:Math.acos(e)}function Ft(e){return e>1?Pe:e<-1?-Pe:Math.asin(e)}function Ae(){}function fn(e,t){e&&ao.hasOwnProperty(e.type)&&ao[e.type](e,t)}var lo={Feature:function(e,t){fn(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,o=n.length;++r<o;)fn(n[r].geometry,t)}},ao={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){Kn(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)Kn(n[r],t,0)},Polygon:function(e,t){io(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)io(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,o=n.length;++r<o;)fn(n[r],t)}};function Kn(e,t,n){var r=-1,o=e.length-n,l;for(t.lineStart();++r<o;)l=e[r],t.point(l[0],l[1],l[2]);t.lineEnd()}function io(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)Kn(e[n],t,1);t.polygonEnd()}function at(e,t){e&&lo.hasOwnProperty(e.type)?lo[e.type](e,t):fn(e,t)}function Wn(e){return[Ot(e[1],e[0]),Ft(e[2])]}function mt(e){var t=e[0],n=e[1],r=ge(n);return[r*ge(t),r*ve(t),ve(n)]}function Wt(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function pn(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function An(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function Qt(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function Qn(e){var t=rt(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function Zn(e,t){function n(r,o){return r=e(r,o),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,o){return r=t.invert(r,o),r&&e.invert(r[0],r[1])}),n}function Jn(e,t){return he(e)>le&&(e-=Math.round(e/Me)*Me),[e,t]}Jn.invert=Jn;function yl(e,t,n){return(e%=Me)?t||n?Zn(uo(e),co(t,n)):uo(e):t||n?co(t,n):Jn}function so(e){return function(t,n){return t+=e,he(t)>le&&(t-=Math.round(t/Me)*Me),[t,n]}}function uo(e){var t=so(e);return t.invert=so(-e),t}function co(e,t){var n=ge(e),r=ve(e),o=ge(t),l=ve(t);function a(s,c){var f=ge(c),i=ge(s)*f,u=ve(s)*f,d=ve(c),m=d*n+i*r;return[Ot(u*o-m*l,i*n-d*r),Ft(m*o+u*l)]}return a.invert=function(s,c){var f=ge(c),i=ge(s)*f,u=ve(s)*f,d=ve(c),m=d*o-u*l;return[Ot(u*o+d*l,i*n+m*r),Ft(m*n-i*r)]},a}function od(e){e=yl(e[0]*ye,e[1]*ye,e.length>2?e[2]*ye:0);function t(n){return n=e(n[0]*ye,n[1]*ye),n[0]*=$e,n[1]*=$e,n}return t.invert=function(n){return n=e.invert(n[0]*ye,n[1]*ye),n[0]*=$e,n[1]*=$e,n},t}function ld(e,t,n,r,o,l){if(n){var a=ge(t),s=ve(t),c=r*n;o==null?(o=t+r*Me,l=t-c/2):(o=fo(a,o),l=fo(a,l),(r>0?o<l:o>l)&&(o+=r*Me));for(var f,i=o;r>0?i>l:i<l;i-=c)f=Wn([a,-s*ge(i),-s*ve(i)]),e.point(f[0],f[1])}}function fo(e,t){t=mt(t),t[0]-=e,Qn(t);var n=rd(-t[1]);return((-t[2]<0?-n:n)+Me-ce)%Me}function wl(){var e=[],t;return{point:function(n,r,o){t.push([n,r,o])},lineStart:function(){e.push(t=[])},lineEnd:Ae,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function rn(e,t){return he(e[0]-t[0])<ce&&he(e[1]-t[1])<ce}function Zt(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function xl(e,t,n,r,o){var l=[],a=[],s,c;if(e.forEach(function(w){if(!((P=w.length-1)<=0)){var P,v=w[0],E=w[P],C;if(rn(v,E)){if(!v[2]&&!E[2]){for(o.lineStart(),s=0;s<P;++s)o.point((v=w[s])[0],v[1]);o.lineEnd();return}E[0]+=2*ce}l.push(C=new Zt(v,w,null,!0)),a.push(C.o=new Zt(v,null,C,!1)),l.push(C=new Zt(E,w,null,!1)),a.push(C.o=new Zt(E,null,C,!0))}}),!!l.length){for(a.sort(t),po(l),po(a),s=0,c=a.length;s<c;++s)a[s].e=n=!n;for(var f=l[0],i,u;;){for(var d=f,m=!0;d.v;)if((d=d.n)===f)return;i=d.z,o.lineStart();do{if(d.v=d.o.v=!0,d.e){if(m)for(s=0,c=i.length;s<c;++s)o.point((u=i[s])[0],u[1]);else r(d.x,d.n.x,1,o);d=d.n}else{if(m)for(i=d.p.z,s=i.length-1;s>=0;--s)o.point((u=i[s])[0],u[1]);else r(d.x,d.p.x,-1,o);d=d.p}d=d.o,i=d.z,m=!m}while(!d.v);o.lineEnd()}}}function po(e){if(t=e.length){for(var t,n=0,r=e[0],o;++n<t;)r.n=o=e[n],o.p=r,r=o;r.n=o=e[0],o.p=r}}function Nn(e){return he(e[0])<=le?e[0]:td(e[0])*((he(e[0])+le)%Me-le)}function ad(e,t){var n=Nn(t),r=t[1],o=ve(r),l=[ve(n),-ge(n),0],a=0,s=0,c=new et;o===1?r=Pe+ce:o===-1&&(r=-Pe-ce);for(var f=0,i=e.length;f<i;++f)if(d=(u=e[f]).length)for(var u,d,m=u[d-1],w=Nn(m),P=m[1]/2+oo,v=ve(P),E=ge(P),C=0;C<d;++C,w=T,v=k,E=R,m=N){var N=u[C],T=Nn(N),U=N[1]/2+oo,k=ve(U),R=ge(U),I=T-w,M=I>=0?1:-1,h=M*I,y=h>le,A=v*k;if(c.add(Ot(A*M*ve(h),E*R+A*ge(h))),a+=y?I+M*Me:I,y^w>=n^T>=n){var H=pn(mt(m),mt(N));Qn(H);var B=pn(l,H);Qn(B);var S=(y^I>=0?-1:1)*Ft(B[2]);(r>S||r===S&&(H[0]||H[1]))&&(s+=y^I>=0?1:-1)}}return(a<-1e-6||a<ce&&c<-1e-12)^s&1}function bl(e,t,n,r){return function(o){var l=t(o),a=wl(),s=t(a),c=!1,f,i,u,d={point:m,lineStart:P,lineEnd:v,polygonStart:function(){d.point=E,d.lineStart=C,d.lineEnd=N,i=[],f=[]},polygonEnd:function(){d.point=m,d.lineStart=P,d.lineEnd=v,i=Ho(i);var T=ad(f,r);i.length?(c||(o.polygonStart(),c=!0),xl(i,sd,T,n,o)):T&&(c||(o.polygonStart(),c=!0),o.lineStart(),n(null,null,1,o),o.lineEnd()),c&&(o.polygonEnd(),c=!1),i=f=null},sphere:function(){o.polygonStart(),o.lineStart(),n(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function m(T,U){e(T,U)&&o.point(T,U)}function w(T,U){l.point(T,U)}function P(){d.point=w,l.lineStart()}function v(){d.point=m,l.lineEnd()}function E(T,U){u.push([T,U]),s.point(T,U)}function C(){s.lineStart(),u=[]}function N(){E(u[0][0],u[0][1]),s.lineEnd();var T=s.clean(),U=a.result(),k,R=U.length,I,M,h;if(u.pop(),f.push(u),u=null,!!R){if(T&1){if(M=U[0],(I=M.length-1)>0){for(c||(o.polygonStart(),c=!0),o.lineStart(),k=0;k<I;++k)o.point((h=M[k])[0],h[1]);o.lineEnd()}return}R>1&&T&2&&U.push(U.pop().concat(U.shift())),i.push(U.filter(id))}}return d}}function id(e){return e.length>1}function sd(e,t){return((e=e.x)[0]<0?e[1]-Pe-ce:Pe-e[1])-((t=t.x)[0]<0?t[1]-Pe-ce:Pe-t[1])}const ho=bl(function(){return!0},ud,dd,[-le,-Pe]);function ud(e){var t=NaN,n=NaN,r=NaN,o;return{lineStart:function(){e.lineStart(),o=1},point:function(l,a){var s=l>0?le:-le,c=he(l-t);he(c-le)<ce?(e.point(t,n=(n+a)/2>0?Pe:-Pe),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),e.point(l,n),o=0):r!==s&&c>=le&&(he(t-r)<ce&&(t-=r*ce),he(l-s)<ce&&(l-=s*ce),n=cd(t,n,l,a),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),o=0),e.point(t=l,n=a),r=s},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-o}}}function cd(e,t,n,r){var o,l,a=ve(e-n);return he(a)>ce?vl((ve(t)*(l=ge(r))*ve(n)-ve(r)*(o=ge(t))*ve(e))/(o*l*a)):(t+r)/2}function dd(e,t,n,r){var o;if(e==null)o=n*Pe,r.point(-le,o),r.point(0,o),r.point(le,o),r.point(le,0),r.point(le,-o),r.point(0,-o),r.point(-le,-o),r.point(-le,0),r.point(-le,o);else if(he(e[0]-t[0])>ce){var l=e[0]<t[0]?le:-le;o=n*l/2,r.point(-l,o),r.point(0,o),r.point(l,o)}else r.point(t[0],t[1])}function fd(e){var t=ge(e),n=2*ye,r=t>0,o=he(t)>ce;function l(i,u,d,m){ld(m,e,n,d,i,u)}function a(i,u){return ge(i)*ge(u)>t}function s(i){var u,d,m,w,P;return{lineStart:function(){w=m=!1,P=1},point:function(v,E){var C=[v,E],N,T=a(v,E),U=r?T?0:f(v,E):T?f(v+(v<0?le:-le),E):0;if(!u&&(w=m=T)&&i.lineStart(),T!==m&&(N=c(u,C),(!N||rn(u,N)||rn(C,N))&&(C[2]=1)),T!==m)P=0,T?(i.lineStart(),N=c(C,u),i.point(N[0],N[1])):(N=c(u,C),i.point(N[0],N[1],2),i.lineEnd()),u=N;else if(o&&u&&r^T){var k;!(U&d)&&(k=c(C,u,!0))&&(P=0,r?(i.lineStart(),i.point(k[0][0],k[0][1]),i.point(k[1][0],k[1][1]),i.lineEnd()):(i.point(k[1][0],k[1][1]),i.lineEnd(),i.lineStart(),i.point(k[0][0],k[0][1],3)))}T&&(!u||!rn(u,C))&&i.point(C[0],C[1]),u=C,m=T,d=U},lineEnd:function(){m&&i.lineEnd(),u=null},clean:function(){return P|(w&&m)<<1}}}function c(i,u,d){var m=mt(i),w=mt(u),P=[1,0,0],v=pn(m,w),E=Wt(v,v),C=v[0],N=E-C*C;if(!N)return!d&&i;var T=t*E/N,U=-t*C/N,k=pn(P,v),R=Qt(P,T),I=Qt(v,U);An(R,I);var M=k,h=Wt(R,M),y=Wt(M,M),A=h*h-y*(Wt(R,R)-1);if(!(A<0)){var H=rt(A),B=Qt(M,(-h-H)/y);if(An(B,R),B=Wn(B),!d)return B;var S=i[0],Y=u[0],V=i[1],x=u[1],Z;Y<S&&(Z=S,S=Y,Y=Z);var J=Y-S,W=he(J-le)<ce,oe=W||J<ce;if(!W&&x<V&&(Z=V,V=x,x=Z),oe?W?V+x>0^B[1]<(he(B[0]-S)<ce?V:x):V<=B[1]&&B[1]<=x:J>le^(S<=B[0]&&B[0]<=Y)){var fe=Qt(M,(-h+H)/y);return An(fe,R),[B,Wn(fe)]}}}function f(i,u){var d=r?e:le-e,m=0;return i<-d?m|=1:i>d&&(m|=2),u<-d?m|=4:u>d&&(m|=8),m}return bl(a,s,l,r?[0,-e]:[-le,e-le])}function pd(e,t,n,r,o,l){var a=e[0],s=e[1],c=t[0],f=t[1],i=0,u=1,d=c-a,m=f-s,w;if(w=n-a,!(!d&&w>0)){if(w/=d,d<0){if(w<i)return;w<u&&(u=w)}else if(d>0){if(w>u)return;w>i&&(i=w)}if(w=o-a,!(!d&&w<0)){if(w/=d,d<0){if(w>u)return;w>i&&(i=w)}else if(d>0){if(w<i)return;w<u&&(u=w)}if(w=r-s,!(!m&&w>0)){if(w/=m,m<0){if(w<i)return;w<u&&(u=w)}else if(m>0){if(w>u)return;w>i&&(i=w)}if(w=l-s,!(!m&&w<0)){if(w/=m,m<0){if(w>u)return;w>i&&(i=w)}else if(m>0){if(w<i)return;w<u&&(u=w)}return i>0&&(e[0]=a+i*d,e[1]=s+i*m),u<1&&(t[0]=a+u*d,t[1]=s+u*m),!0}}}}}var Jt=1e9,jt=-1e9;function hd(e,t,n,r){function o(f,i){return e<=f&&f<=n&&t<=i&&i<=r}function l(f,i,u,d){var m=0,w=0;if(f==null||(m=a(f,u))!==(w=a(i,u))||c(f,i)<0^u>0)do d.point(m===0||m===3?e:n,m>1?r:t);while((m=(m+u+4)%4)!==w);else d.point(i[0],i[1])}function a(f,i){return he(f[0]-e)<ce?i>0?0:3:he(f[0]-n)<ce?i>0?2:1:he(f[1]-t)<ce?i>0?1:0:i>0?3:2}function s(f,i){return c(f.x,i.x)}function c(f,i){var u=a(f,1),d=a(i,1);return u!==d?u-d:u===0?i[1]-f[1]:u===1?f[0]-i[0]:u===2?f[1]-i[1]:i[0]-f[0]}return function(f){var i=f,u=wl(),d,m,w,P,v,E,C,N,T,U,k,R={point:I,lineStart:A,lineEnd:H,polygonStart:h,polygonEnd:y};function I(S,Y){o(S,Y)&&i.point(S,Y)}function M(){for(var S=0,Y=0,V=m.length;Y<V;++Y)for(var x=m[Y],Z=1,J=x.length,W=x[0],oe,fe,we=W[0],Q=W[1];Z<J;++Z)oe=we,fe=Q,W=x[Z],we=W[0],Q=W[1],fe<=r?Q>r&&(we-oe)*(r-fe)>(Q-fe)*(e-oe)&&++S:Q<=r&&(we-oe)*(r-fe)<(Q-fe)*(e-oe)&&--S;return S}function h(){i=u,d=[],m=[],k=!0}function y(){var S=M(),Y=k&&S,V=(d=Ho(d)).length;(Y||V)&&(f.polygonStart(),Y&&(f.lineStart(),l(null,null,1,f),f.lineEnd()),V&&xl(d,s,S,l,f),f.polygonEnd()),i=f,d=m=w=null}function A(){R.point=B,m&&m.push(w=[]),U=!0,T=!1,C=N=NaN}function H(){d&&(B(P,v),E&&T&&u.rejoin(),d.push(u.result())),R.point=I,T&&i.lineEnd()}function B(S,Y){var V=o(S,Y);if(m&&w.push([S,Y]),U)P=S,v=Y,E=V,U=!1,V&&(i.lineStart(),i.point(S,Y));else if(V&&T)i.point(S,Y);else{var x=[C=Math.max(jt,Math.min(Jt,C)),N=Math.max(jt,Math.min(Jt,N))],Z=[S=Math.max(jt,Math.min(Jt,S)),Y=Math.max(jt,Math.min(Jt,Y))];pd(x,Z,e,t,n,r)?(T||(i.lineStart(),i.point(x[0],x[1])),i.point(Z[0],Z[1]),V||i.lineEnd(),k=!1):V&&(i.lineStart(),i.point(S,Y),k=!1)}C=S,N=Y,T=V}return R}}const jn=e=>e;var Un=new et,er=new et,kl,Sl,tr,nr,He={point:Ae,lineStart:Ae,lineEnd:Ae,polygonStart:function(){He.lineStart=md,He.lineEnd=gd},polygonEnd:function(){He.lineStart=He.lineEnd=He.point=Ae,Un.add(he(er)),er=new et},result:function(){var e=Un/2;return Un=new et,e}};function md(){He.point=_d}function _d(e,t){He.point=Cl,kl=tr=e,Sl=nr=t}function Cl(e,t){er.add(nr*e-tr*t),tr=e,nr=t}function gd(){Cl(kl,Sl)}var _t=1/0,hn=_t,Lt=-_t,mn=Lt,_n={point:vd,lineStart:Ae,lineEnd:Ae,polygonStart:Ae,polygonEnd:Ae,result:function(){var e=[[_t,hn],[Lt,mn]];return Lt=mn=-(hn=_t=1/0),e}};function vd(e,t){e<_t&&(_t=e),e>Lt&&(Lt=e),t<hn&&(hn=t),t>mn&&(mn=t)}var rr=0,or=0,St=0,gn=0,vn=0,it=0,lr=0,ar=0,Ct=0,El,Vl,Oe,Fe,De={point:nt,lineStart:mo,lineEnd:_o,polygonStart:function(){De.lineStart=xd,De.lineEnd=bd},polygonEnd:function(){De.point=nt,De.lineStart=mo,De.lineEnd=_o},result:function(){var e=Ct?[lr/Ct,ar/Ct]:it?[gn/it,vn/it]:St?[rr/St,or/St]:[NaN,NaN];return rr=or=St=gn=vn=it=lr=ar=Ct=0,e}};function nt(e,t){rr+=e,or+=t,++St}function mo(){De.point=yd}function yd(e,t){De.point=wd,nt(Oe=e,Fe=t)}function wd(e,t){var n=e-Oe,r=t-Fe,o=rt(n*n+r*r);gn+=o*(Oe+e)/2,vn+=o*(Fe+t)/2,it+=o,nt(Oe=e,Fe=t)}function _o(){De.point=nt}function xd(){De.point=kd}function bd(){Rl(El,Vl)}function kd(e,t){De.point=Rl,nt(El=Oe=e,Vl=Fe=t)}function Rl(e,t){var n=e-Oe,r=t-Fe,o=rt(n*n+r*r);gn+=o*(Oe+e)/2,vn+=o*(Fe+t)/2,it+=o,o=Fe*e-Oe*t,lr+=o*(Oe+e),ar+=o*(Fe+t),Ct+=o*3,nt(Oe=e,Fe=t)}function Pl(e){this._context=e}Pl.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,Me);break}}},result:Ae};var ir=new et,Tn,$l,Il,Et,Vt,qt={point:Ae,lineStart:function(){qt.point=Sd},lineEnd:function(){Tn&&Ml($l,Il),qt.point=Ae},polygonStart:function(){Tn=!0},polygonEnd:function(){Tn=null},result:function(){var e=+ir;return ir=new et,e}};function Sd(e,t){qt.point=Ml,$l=Et=e,Il=Vt=t}function Ml(e,t){Et-=e,Vt-=t,ir.add(rt(Et*Et+Vt*Vt)),Et=e,Vt=t}let go,yn,vo,yo;class wo{constructor(t){this._append=t==null?Dl:Cd(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==vo||this._append!==yn){const r=this._radius,o=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,vo=r,yn=this._append,yo=this._,this._=o}this._+=yo;break}}}result(){const t=this._;return this._="",t.length?t:null}}function Dl(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function Cd(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return Dl;if(t!==go){const n=10**t;go=t,yn=function(o){let l=1;this._+=o[0];for(const a=o.length;l<a;++l)this._+=Math.round(arguments[l]*n)/n+o[l]}}return yn}function xo(e,t){let n=3,r=4.5,o,l;function a(s){return s&&(typeof r=="function"&&l.pointRadius(+r.apply(this,arguments)),at(s,o(l))),l.result()}return a.area=function(s){return at(s,o(He)),He.result()},a.measure=function(s){return at(s,o(qt)),qt.result()},a.bounds=function(s){return at(s,o(_n)),_n.result()},a.centroid=function(s){return at(s,o(De)),De.result()},a.projection=function(s){return arguments.length?(o=s==null?(e=null,jn):(e=s).stream,a):e},a.context=function(s){return arguments.length?(l=s==null?(t=null,new wo(n)):new Pl(t=s),typeof r!="function"&&l.pointRadius(r),a):t},a.pointRadius=function(s){return arguments.length?(r=typeof s=="function"?s:(l.pointRadius(+s),+s),a):r},a.digits=function(s){if(!arguments.length)return n;if(s==null)n=null;else{const c=Math.floor(s);if(!(c>=0))throw new RangeError(`invalid digits: ${s}`);n=c}return t===null&&(l=new wo(n)),a},a.projection(e).digits(n).context(t)}function gr(e){return function(t){var n=new sr;for(var r in e)n[r]=e[r];return n.stream=t,n}}function sr(){}sr.prototype={constructor:sr,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function vr(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),at(n,e.stream(_n)),t(_n.result()),r!=null&&e.clipExtent(r),e}function Al(e,t,n){return vr(e,function(r){var o=t[1][0]-t[0][0],l=t[1][1]-t[0][1],a=Math.min(o/(r[1][0]-r[0][0]),l/(r[1][1]-r[0][1])),s=+t[0][0]+(o-a*(r[1][0]+r[0][0]))/2,c=+t[0][1]+(l-a*(r[1][1]+r[0][1]))/2;e.scale(150*a).translate([s,c])},n)}function Ed(e,t,n){return Al(e,[[0,0],t],n)}function Vd(e,t,n){return vr(e,function(r){var o=+t,l=o/(r[1][0]-r[0][0]),a=(o-l*(r[1][0]+r[0][0]))/2,s=-l*r[0][1];e.scale(150*l).translate([a,s])},n)}function Rd(e,t,n){return vr(e,function(r){var o=+t,l=o/(r[1][1]-r[0][1]),a=-l*r[0][0],s=(o-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([a,s])},n)}var bo=16,Pd=ge(30*ye);function ko(e,t){return+t?Id(e,t):$d(e)}function $d(e){return gr({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function Id(e,t){function n(r,o,l,a,s,c,f,i,u,d,m,w,P,v){var E=f-r,C=i-o,N=E*E+C*C;if(N>4*t&&P--){var T=a+d,U=s+m,k=c+w,R=rt(T*T+U*U+k*k),I=Ft(k/=R),M=he(he(k)-1)<ce||he(l-u)<ce?(l+u)/2:Ot(U,T),h=e(M,I),y=h[0],A=h[1],H=y-r,B=A-o,S=C*H-E*B;(S*S/N>t||he((E*H+C*B)/N-.5)>.3||a*d+s*m+c*w<Pd)&&(n(r,o,l,a,s,c,y,A,M,T/=R,U/=R,k,P,v),v.point(y,A),n(y,A,M,T,U,k,f,i,u,d,m,w,P,v))}}return function(r){var o,l,a,s,c,f,i,u,d,m,w,P,v={point:E,lineStart:C,lineEnd:T,polygonStart:function(){r.polygonStart(),v.lineStart=U},polygonEnd:function(){r.polygonEnd(),v.lineStart=C}};function E(I,M){I=e(I,M),r.point(I[0],I[1])}function C(){u=NaN,v.point=N,r.lineStart()}function N(I,M){var h=mt([I,M]),y=e(I,M);n(u,d,i,m,w,P,u=y[0],d=y[1],i=I,m=h[0],w=h[1],P=h[2],bo,r),r.point(u,d)}function T(){v.point=E,r.lineEnd()}function U(){C(),v.point=k,v.lineEnd=R}function k(I,M){N(o=I,M),l=u,a=d,s=m,c=w,f=P,v.point=N}function R(){n(u,d,i,m,w,P,l,a,o,s,c,f,bo,r),v.lineEnd=T,T()}return v}}var Md=gr({point:function(e,t){this.stream.point(e*ye,t*ye)}});function Dd(e){return gr({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function Ad(e,t,n,r,o){function l(a,s){return a*=r,s*=o,[t+e*a,n-e*s]}return l.invert=function(a,s){return[(a-t)/e*r,(n-s)/e*o]},l}function So(e,t,n,r,o,l){if(!l)return Ad(e,t,n,r,o);var a=ge(l),s=ve(l),c=a*e,f=s*e,i=a/e,u=s/e,d=(s*n-a*t)/e,m=(s*t+a*n)/e;function w(P,v){return P*=r,v*=o,[c*P-f*v+t,n-f*P-c*v]}return w.invert=function(P,v){return[r*(i*P-u*v+d),o*(m-u*P-i*v)]},w}function Nd(e){return Ud(function(){return e})()}function Ud(e){var t,n=150,r=480,o=250,l=0,a=0,s=0,c=0,f=0,i,u=0,d=1,m=1,w=null,P=ho,v=null,E,C,N,T=jn,U=.5,k,R,I,M,h;function y(S){return I(S[0]*ye,S[1]*ye)}function A(S){return S=I.invert(S[0],S[1]),S&&[S[0]*$e,S[1]*$e]}y.stream=function(S){return M&&h===S?M:M=Md(Dd(i)(P(k(T(h=S)))))},y.preclip=function(S){return arguments.length?(P=S,w=void 0,B()):P},y.postclip=function(S){return arguments.length?(T=S,v=E=C=N=null,B()):T},y.clipAngle=function(S){return arguments.length?(P=+S?fd(w=S*ye):(w=null,ho),B()):w*$e},y.clipExtent=function(S){return arguments.length?(T=S==null?(v=E=C=N=null,jn):hd(v=+S[0][0],E=+S[0][1],C=+S[1][0],N=+S[1][1]),B()):v==null?null:[[v,E],[C,N]]},y.scale=function(S){return arguments.length?(n=+S,H()):n},y.translate=function(S){return arguments.length?(r=+S[0],o=+S[1],H()):[r,o]},y.center=function(S){return arguments.length?(l=S[0]%360*ye,a=S[1]%360*ye,H()):[l*$e,a*$e]},y.rotate=function(S){return arguments.length?(s=S[0]%360*ye,c=S[1]%360*ye,f=S.length>2?S[2]%360*ye:0,H()):[s*$e,c*$e,f*$e]},y.angle=function(S){return arguments.length?(u=S%360*ye,H()):u*$e},y.reflectX=function(S){return arguments.length?(d=S?-1:1,H()):d<0},y.reflectY=function(S){return arguments.length?(m=S?-1:1,H()):m<0},y.precision=function(S){return arguments.length?(k=ko(R,U=S*S),B()):rt(U)},y.fitExtent=function(S,Y){return Al(y,S,Y)},y.fitSize=function(S,Y){return Ed(y,S,Y)},y.fitWidth=function(S,Y){return Vd(y,S,Y)},y.fitHeight=function(S,Y){return Rd(y,S,Y)};function H(){var S=So(n,0,0,d,m,u).apply(null,t(l,a)),Y=So(n,r-S[0],o-S[1],d,m,u);return i=yl(s,c,f),R=Zn(t,Y),I=Zn(i,R),k=ko(R,U),B()}function B(){return M=h=null,y}return function(){return t=e.apply(this,arguments),y.invert=t.invert&&A,H()}}function yr(e,t){return[e,ed(nd((Pe+t)/2))]}yr.invert=function(e,t){return[e,2*vl(jc(t))-Pe]};function Td(){return zd(yr).scale(961/Me)}function zd(e){var t=Nd(e),n=t.center,r=t.scale,o=t.translate,l=t.clipExtent,a=null,s,c,f;t.scale=function(u){return arguments.length?(r(u),i()):r()},t.translate=function(u){return arguments.length?(o(u),i()):o()},t.center=function(u){return arguments.length?(n(u),i()):n()},t.clipExtent=function(u){return arguments.length?(u==null?a=s=c=f=null:(a=+u[0][0],s=+u[0][1],c=+u[1][0],f=+u[1][1]),i()):a==null?null:[[a,s],[c,f]]};function i(){var u=le*r(),d=t(od(t.rotate()).invert([0,0]));return l(a==null?[[d[0]-u,d[1]-u],[d[0]+u,d[1]+u]]:e===yr?[[Math.max(d[0]-u,a),s],[Math.min(d[0]+u,c),f]]:[[a,Math.max(d[1]-u,s)],[c,Math.min(d[1]+u,f)]])}return i()}function Rt(e,t,n){this.k=e,this.x=t,this.y=n}Rt.prototype={constructor:Rt,scale:function(e){return e===1?this:new Rt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new Rt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};Rt.prototype;const Od={class:"clock-container"},Fd={class:"holographic-clock",viewBox:"0 0 100 100"},Ld={class:"clock-markers"},qd=["x1","y1","x2","y2"],Yd={class:"clock-hands"},Bd=["x2","y2"],Hd=["x2","y2"],Xd=["x2","y2"],Gd=["id"],Kd=["id"],Wd={__name:"VisualScreen",setup(e){const t=O(null),n=O(null),r=O(0),o=O(!1),l=new Map,a=O({x:0,y:0}),s=O(0),c=O(0),f=O(0),i=O(0);let u;const d=O([{id:"china",url:"/public/geo/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"/public/geo/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"/public/geo/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"/public/geo/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),m={longitude:106.43,latitude:30.55},w=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},P=()=>{const I=new Date;s.value=I.getHours()%12,c.value=I.getMinutes(),f.value=I.getSeconds(),i.value=I.getMilliseconds()},v=I=>{if(l.has(I.id))return;const M=w(),h=Ht(`#${I.id}-map`).attr("viewBox",`0 0 ${M.width} ${M.height}`).attr("preserveAspectRatio","xMidYMid meet");Jc(I.url).then(y=>{const A=Td().center(I.center).scale(Math.min(M.width,M.height)*I.scaleFactor).translate([M.width/2,M.height/2]);l.set(I.id,{projection:A,data:y}),h.selectAll(".boundary").data(y.features).enter().append("path").attr("class","boundary").attr("d",xo().projection(A)),E(I.id)}).catch(y=>console.error("地图加载失败:",y))},E=I=>{const{projection:M}=l.get(I)||{};if(!M)return;const[h,y]=M([m.longitude,m.latitude]);a.value={x:h,y}},C=()=>{const I=w();d.value.forEach(M=>{const{projection:h,data:y}=l.get(M.id)||{};!h||!y||(h.scale(Math.min(I.width,I.height)*M.scaleFactor).translate([I.width/2,I.height/2]),Ht(`#${M.id}-map`).attr("viewBox",`0 0 ${I.width} ${I.height}`).selectAll(".boundary").attr("d",xo().projection(h)),M.id===d.value[r.value].id&&E(M.id))})},N=I=>{if(o.value)return;o.value=!0;const M=d.value.length,h=(r.value+I+M)%M,y=d.value[r.value],A=d.value[h];Ht(`#${y.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{Ht(`#${A.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=h,o.value=!1,E(A.id)})})},T=()=>{const{width:M,height:h}=w();for(let y=0;y<100;y++){const A=document.createElement("div");A.className="particle";const H=Math.random()*M,B=Math.random()*h,S=Math.random()*10,Y=Math.random()*2+1;A.style.left=`${H}px`,A.style.top=`${B}px`,A.style.animationDelay=`${S}s`,A.style.width=`${Y}px`,A.style.height=`${Y}px`,n.value.appendChild(A)}};let U;const k=()=>{U=setInterval(()=>{N(1)},d.value[0].duration+1500)};Qe(()=>{P(),u=setInterval(P,50),d.value.forEach(v),T(),k(),document.addEventListener("fullscreenchange",R)}),Gl(()=>{clearInterval(u),clearInterval(U),l.clear(),document.removeEventListener("fullscreenchange",R)});const R=()=>{on(()=>{C(),n.value&&(n.value.innerHTML=""),T()})};return st([()=>window.innerWidth,()=>window.innerHeight,()=>{var I;return(I=t.value)==null?void 0:I.clientWidth},()=>{var I;return(I=t.value)==null?void 0:I.clientHeight}],()=>{on(C)}),(I,M)=>(j(),se("div",{class:"holographic-container",ref_key:"container",ref:t},[M[3]||(M[3]=X("div",{class:"slogan-container"},[X("div",null,"对党忠诚  服务人民"),X("div",null,"执法公正  纪律严明")],-1)),M[4]||(M[4]=X("div",{class:"title-container"},[X("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),X("div",Od,[(j(),se("svg",Fd,[M[0]||(M[0]=X("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),M[1]||(M[1]=X("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),X("g",Ld,[(j(),se(Se,null,Ve(12,h=>X("line",{key:h,x1:50+38*Math.cos((h*30-90)*Math.PI/180),y1:50+38*Math.sin((h*30-90)*Math.PI/180),x2:50+42*Math.cos((h*30-90)*Math.PI/180),y2:50+42*Math.sin((h*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,qd)),64))]),X("g",Yd,[X("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((s.value*30+c.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((s.value*30+c.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,Bd),X("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((c.value*6+f.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((c.value*6+f.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Hd),X("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((f.value*6+i.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((f.value*6+i.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,Xd)]),M[2]||(M[2]=X("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),X("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),M[5]||(M[5]=X("div",{class:"hologram-grid"},null,-1)),M[6]||(M[6]=X("div",{class:"scan-line-vertical"},null,-1)),M[7]||(M[7]=X("div",{class:"scan-line-horizontal"},null,-1)),M[8]||(M[8]=X("div",{class:"hologram-frame"},[X("div")],-1)),(j(!0),se(Se,null,Ve(d.value,(h,y)=>(j(),se("div",{key:h.id,id:`${h.id}-container`,class:Kl(["map-container",{"map-visible":r.value===y}])},[(j(),se("svg",{id:`${h.id}-map`,class:"map-svg"},null,8,Kd)),r.value===y?(j(),se("div",{key:0,class:"location-marker",style:ur({left:a.value.x+"px",top:a.value.y+"px"})},null,4)):Pt("",!0)],10,Gd))),128))],512))}},Qd={class:"app-management-container"},Zd={class:"clearfix"},Jd={__name:"AppManagement",setup(e){const t=O([]),n=O([]),r=O(0),o=O(!1);O(!1),O("");const l=O(null),a=Ue({id:null,name:"",url:"",isPublic:"",roles:[]}),s=Ue({name:[{required:!0,message:"请输入应用名称",trigger:"blur"}],url:[{required:!0,message:"请输入应用URL",trigger:"blur"}],isPublic:[{required:!0,message:"请选择可见范围",trigger:"change"}],roles:[{type:"array",required:!0,validator:(v,E,C)=>{E&&E.length>0?C():C(new Error("至少选择一个角色组"))},trigger:"change"}]}),c=v=>{try{return JSON.parse(v).map(C=>{const N=n.value.find(T=>String(T.value)===String(C));return N?N.label:C})}catch(E){return console.error("解析 roleList 失败:",E),[]}},f=async()=>{try{const v=new FormData;v.append("controlCode","query");const E=await ne.post("/api/application_manage.php",v);E.status===1&&(t.value=E.data.application,n.value=E.data.rolelist.map(C=>({value:C.id,label:C.roleName})),r.value=E.data.application.length)}catch(v){console.error("获取应用列表失败:",v),q.error("获取应用列表失败")}};Qe(()=>{f()});const i=v=>{switch(v){case"0":return"公开";case"1":return"非第三方人员";case"2":return"民警";case"3":return"授权用户";default:return"未知"}},u=()=>{m(),o.value=!0},d=v=>{if(m(),n.value.length===0){console.error("角色选项未加载完成"),q.error("角色数据加载中，请稍后再试");return}a.id=v.id,a.name=v.application_name,a.url=v.url,a.isPublic=v.public;try{const E=JSON.parse(v.roleList);a.roles=E.map(C=>String(C)),on(()=>{a.roles=[...a.roles]})}catch(E){console.error("解析角色列表失败:",E),a.roles=[]}o.value=!0},m=()=>{l.value&&l.value.resetFields(),a.id=null,a.name="",a.url="",a.isPublic="",a.roles=[]},w=v=>{Mt.confirm(`确定要删除应用 "${v.application_name}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const E=new FormData;E.append("controlCode","del"),E.append("id",v.id);const C=await ne.post("/api/application_manage.php",E);C.status===1?(q.success("删除成功"),f()):q.error("删除失败: "+(C.message||"未知错误"))}catch(E){console.error("删除应用失败:",E),q.error("删除应用失败: "+E.message)}}).catch(()=>{q.info("已取消删除")})},P=async()=>{try{await l.value.validate()}catch(v){console.error("表单验证失败:",v);return}try{const v=new FormData;v.append("controlCode",a.id?"modify":"add"),v.append("id",a.id),v.append("application_name",a.name),v.append("url",a.url),v.append("public",a.isPublic),v.append("roleList",JSON.stringify(a.roles));const E=await ne.post("/api/application_manage.php",v);E.status===1?(q.success("更新成功"),o.value=!1,f()):q.error(E.message||"更新失败")}catch(v){console.error("更新应用失败:",v),q.error("更新应用失败: "+v.message)}};return(v,E)=>{const C=F("el-button"),N=F("el-table-column"),T=F("el-tag"),U=F("el-icon"),k=F("el-button-group"),R=F("el-table"),I=F("el-card"),M=F("el-input"),h=F("el-form-item"),y=F("el-option"),A=F("el-select"),H=F("el-checkbox"),B=F("el-checkbox-group"),S=F("el-form"),Y=F("el-dialog");return j(),se("div",Qd,[p(I,{class:"box-card"},{header:_(()=>[X("div",Zd,[p(C,{style:{float:"right"},round:"",type:"primary",onClick:u},{default:_(()=>E[6]||(E[6]=[ee(" 添加应用 ")])),_:1,__:[6]})])]),default:_(()=>[p(R,{data:t.value,stripe:"",border:"",fit:"","highlight-current-row":"",onRowDblclick:d,style:{width:"100%"}},{default:_(()=>[p(N,{label:"序号",width:"80",align:"center"},{default:_(V=>[ee(Ee(V.$index+1),1)]),_:1}),p(N,{prop:"application_name",label:"应用名称","min-width":"120"}),p(N,{prop:"url",label:"URL","min-width":"150"}),p(N,{prop:"public",label:"是否公开",width:"150",align:"center"},{default:_(V=>[ee(Ee(i(V.row.public)),1)]),_:1}),p(N,{prop:"roles",label:"授权角色组","min-width":"180"},{default:_(V=>[(j(!0),se(Se,null,Ve(c(V.row.roleList),x=>(j(),be(T,{key:x,size:"small",type:"info"},{default:_(()=>[ee(Ee(x),1)]),_:2},1024))),128))]),_:1}),p(N,{label:"操作",width:"160",align:"center"},{default:_(V=>[p(k,null,{default:_(()=>[p(C,{size:"mini",type:"warning",onClick:x=>d(V.row)},{default:_(()=>[p(U,null,{default:_(()=>[p(ue(wn))]),_:1})]),_:2},1032,["onClick"]),p(C,{size:"mini",type:"danger",onClick:x=>w(V.row)},{default:_(()=>[p(U,null,{default:_(()=>[p(ue(xn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1}),p(Y,{modelValue:o.value,"onUpdate:modelValue":E[5]||(E[5]=V=>o.value=V),title:a.id?"编辑应用":"添加应用",width:"50%"},{footer:_(()=>[p(C,{onClick:E[4]||(E[4]=V=>o.value=!1)},{default:_(()=>E[7]||(E[7]=[ee("取消")])),_:1,__:[7]}),p(C,{type:"primary",onClick:P},{default:_(()=>E[8]||(E[8]=[ee("保存")])),_:1,__:[8]})]),default:_(()=>[p(S,{model:a,rules:s,ref_key:"formRef",ref:l,"label-width":"100px"},{default:_(()=>[p(h,{label:"应用名称",prop:"name"},{default:_(()=>[p(M,{modelValue:a.name,"onUpdate:modelValue":E[0]||(E[0]=V=>a.name=V),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1}),p(h,{label:"URL",prop:"url"},{default:_(()=>[p(M,{modelValue:a.url,"onUpdate:modelValue":E[1]||(E[1]=V=>a.url=V),placeholder:"请输入应用URL"},null,8,["modelValue"])]),_:1}),p(h,{label:"是否公开",prop:"isPublic"},{default:_(()=>[p(A,{modelValue:a.isPublic,"onUpdate:modelValue":E[2]||(E[2]=V=>a.isPublic=V),placeholder:"请选择是否公开"},{default:_(()=>[p(y,{value:"0",label:"公开"}),p(y,{value:"1",label:"非第三方人员"}),p(y,{value:"2",label:"民警"}),p(y,{value:"3",label:"授权用户"})]),_:1},8,["modelValue"])]),_:1}),p(h,{label:"授权角色组",prop:"roles"},{default:_(()=>[p(B,{modelValue:a.roles,"onUpdate:modelValue":E[3]||(E[3]=V=>a.roles=V)},{default:_(()=>[(j(!0),se(Se,null,Ve(n.value,V=>(j(),be(H,{key:V.value,label:V.value},{default:_(()=>[ee(Ee(V.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}},jd=gt(Jd,[["__scopeId","data-v-2a67e87b"]]),ef={class:"container"},tf={class:"left-section"},nf={class:"query-bar"},rf={class:"user-table"},of={class:"pagination"},lf={class:"right-section"},af={class:"query-bar"},sf={class:"authorization-table"},Co=10,uf={__name:"PowerManagement",setup(e){const t=O([]);O("");const n=O(""),r=O(""),o=O(1),l=O(0),a=O([]),s=O([]),c=O([]),f=O([]),i=O([]),u=O(!1),d=O(!1),m=O("add"),w=O(null),P=Ue({unitId:null,unitIdPath:[]}),v=Ue({id:null,userId:null,userName:"",appId:null,roleId:null,unitIdPath:[],unitId:null});ke(()=>v.appId?f.value.filter(V=>V.applicationId==v.appId):[]);const E={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"};Qe(()=>{C(),N(),T()});const C=async()=>{try{const V=new FormData;V.append("controlCode","query");const x=await ne.post("/api/application_manage.php",V);x.status===1&&(c.value=x.data.application)}catch(V){console.error("获取应用列表失败:",V),q.error("获取应用列表失败")}},N=async()=>{try{const V=await ne.post("api/get_unit_info.php");s.value=V.data?[V.data]:[]}catch(V){console.error("获取单位数据失败:",V),q.error("获取单位数据失败")}},T=async()=>{try{const V=new FormData;V.append("controlCode","query"),V.append("page",o.value),V.append("pagesize",Co),r.value&&V.append("search_keyword",r.value),P.unitId&&V.append("organization_unit",P.unitId);const x=await ne.post("/api/user_manage.php",V);x.status===1?(l.value=x.total,a.value=x.data.map(Z=>{const J=S(s.value,Z.organization_unit);return{...Z,unit_name:J?J.unit_name:"未知单位"}})):q.error("获取用户数据失败")}catch(V){console.error("查询用户失败:",V),q.error("查询用户失败")}},U=V=>{V&&V.length>0?P.unitId=V[V.length-1]:P.unitId=null,console.log("Selected Unit ID:",P.unitId),T()},k=V=>{o.value=V,T()},R=async()=>{if(!n.value){q.warning("请先选择应用");return}const V=new FormData;V.append("controlCode","getAppUser"),V.append("appId",n.value);try{const x=await ne.post("/api/user_Role_manage.php",V);console.log("响应",x),x.status===1?a.value=x.data||[]:q.error("查询授权信息失败")}catch(x){console.error("查询授权失败:",x),q.error("查询授权失败")}},I=async V=>{const x=new FormData;x.append("controlCode","query"),x.append("userId",V);try{const Z=await ne.post("/api/user_Role_manage.php",x);Z.status===1?i.value=Z.data.map(J=>{const W=c.value.find(we=>we.id==J.appId),oe=S(s.value,J.unitId),fe=f.value.find(we=>we.id==J.roleId);return{id:J.id,appName:W?W.application_name:"未知应用",unitName:oe?oe.unit_name:"未知单位",roleName:fe?fe.roleName:"未知角色",...J}}):q.error("获取用户授权失败")}catch(Z){console.error("获取用户授权失败:",Z),q.error("获取用户授权失败")}},M=V=>{if(m.value="add",v.id=null,v.userId=V.id,v.userName=V.name,v.appId=null,v.roleId=null,v.unitIdPath=[],v.unitId=null,V.organization_unit){const x=Y(s.value,V.organization_unit);v.unitIdPath=x,v.unitId=V.organization_unit}u.value=!0},h=V=>{m.value="edit",v.id=V.id,v.userId=V.userId,v.userName=V.userName||"",v.appId=String(V.appId),y(),v.roleId=V.roleId;const x=Y(s.value,V.unitId);v.unitIdPath=x,v.unitId=V.unitId,u.value=!0},y=async()=>{const V=v.appId;if(!V){t.value=[];return}try{const x=new FormData;x.append("controlCode","query"),x.append("id",V);const Z=await ne.post("/api/application_manage.php",x);Z.status===1?t.value=Z.data.rolelist.map(J=>({id:J.id,roleName:J.roleName})):(q.error("获取角色列表失败"),t.value=[])}catch(x){console.error("加载角色失败:",x),q.error("加载角色失败"),t.value=[]}},A=async()=>{if(v.unitIdPath&&v.unitIdPath.length>0)v.unitId=v.unitIdPath[v.unitIdPath.length-1];else{q.warning("请选择单位");return}if(!v.roleId){q.warning("请选择角色");return}try{const V=m.value==="add"?"add":"modify",x=new FormData;x.append("controlCode",V),x.append("userId",v.userId),x.append("appId",v.appId),x.append("unitId",v.unitId),x.append("roleId",v.roleId),m.value==="edit"&&x.append("id",v.id),(await ne.post("/api/user_Role_manage.php",x)).status===1?(q.success("授权保存成功"),u.value=!1,m.value==="add"?I(v.userId):R()):q.error("授权保存失败")}catch(V){console.error("保存授权失败:",V),q.error("保存授权失败")}},H=V=>{w.value=V,d.value=!0},B=async()=>{try{const V=new FormData;V.append("controlCode","del"),V.append("id",w.value),(await ne.post("/api/user_Role_manage.php",V)).status===1?(q.success("授权删除成功"),d.value=!1,I()):q.error("授权删除失败")}catch(V){console.error("删除授权失败:",V),q.error("删除授权失败")}},S=(V,x)=>{for(const Z of V){if(Z.id==x)return Z;if(Z.children){const J=S(Z.children,x);if(J)return J}}return null},Y=(V,x,Z=[])=>{for(const J of V){const W=[...Z,J.id];if(J.id==x)return W;if(J.children&&J.children.length>0){const oe=Y(J.children,x,W);if(oe)return oe}}return null};return(V,x)=>{const Z=F("el-cascader"),J=F("el-input"),W=F("el-button"),oe=F("el-table-column"),fe=F("el-table"),we=F("el-pagination"),Q=F("el-option"),xe=F("el-select"),$=F("el-form-item"),G=F("el-form"),L=F("el-dialog");return j(),se("div",ef,[X("div",tf,[X("div",nf,[p(Z,{modelValue:P.unitIdPath,"onUpdate:modelValue":x[0]||(x[0]=z=>P.unitIdPath=z),options:s.value,props:E,onChange:U,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择单位"},null,8,["modelValue","options"]),p(J,{modelValue:r.value,"onUpdate:modelValue":x[1]||(x[1]=z=>r.value=z),placeholder:"输入姓名、警号或身份证号",class:"input-user"},null,8,["modelValue"]),p(W,{type:"primary",onClick:T},{default:_(()=>x[11]||(x[11]=[ee("查询用户")])),_:1,__:[11]})]),X("div",rf,[x[14]||(x[14]=X("h4",null,"用户列表",-1)),p(fe,{data:a.value,"empty-text":"No Data",class:"table"},{default:_(()=>[p(oe,{prop:"unit_name",label:"单位"}),p(oe,{prop:"name",label:"姓名"}),p(oe,{prop:"police_number",label:"警号"}),p(oe,{prop:"id_number",label:"身份证号"}),p(oe,{prop:"personnel_type",label:"身份"}),p(oe,{label:"操作",width:"180"},{default:_(({row:z})=>[p(W,{size:"small",onClick:ae=>M(z)},{default:_(()=>x[12]||(x[12]=[ee("添加权限")])),_:2,__:[12]},1032,["onClick"]),p(W,{size:"small",onClick:ae=>I(z.id),type:"success"},{default:_(()=>x[13]||(x[13]=[ee("查看权限")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),X("div",of,[p(we,{layout:"prev, pager, next","current-page":o.value,"page-size":Co,total:l.value,onCurrentChange:k},null,8,["current-page","total"])])])]),X("div",lf,[X("div",af,[p(xe,{modelValue:n.value,"onUpdate:modelValue":x[2]||(x[2]=z=>n.value=z),placeholder:"选择应用",class:"select-app"},{default:_(()=>[p(Q,{label:"选择应用",value:""}),(j(!0),se(Se,null,Ve(c.value,z=>(j(),be(Q,{key:z.id,label:z.application_name,value:z.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),p(W,{type:"primary",onClick:R},{default:_(()=>x[15]||(x[15]=[ee("查询授权人员")])),_:1,__:[15]})]),X("div",sf,[x[18]||(x[18]=X("h4",null,"授权信息",-1)),p(fe,{data:i.value,"empty-text":"No Data",class:"table"},{default:_(()=>[p(oe,{prop:"appName",label:"授权应用"}),p(oe,{prop:"unitName",label:"授权单位"}),p(oe,{prop:"roleName",label:"授权角色"}),p(oe,{label:"操作",width:"150"},{default:_(({row:z})=>[p(W,{size:"small",onClick:ae=>h(z)},{default:_(()=>x[16]||(x[16]=[ee("修改")])),_:2,__:[16]},1032,["onClick"]),p(W,{size:"small",onClick:ae=>H(z.id),type:"danger"},{default:_(()=>x[17]||(x[17]=[ee("删除")])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),p(L,{modelValue:u.value,"onUpdate:modelValue":x[8]||(x[8]=z=>u.value=z),title:m.value==="add"?"添加授权":"修改授权"},{footer:_(()=>[p(W,{onClick:x[7]||(x[7]=z=>u.value=!1)},{default:_(()=>x[19]||(x[19]=[ee("取消")])),_:1,__:[19]}),p(W,{type:"primary",onClick:A},{default:_(()=>x[20]||(x[20]=[ee("确认")])),_:1,__:[20]})]),default:_(()=>[p(G,{model:v,"label-width":"120px"},{default:_(()=>[p($,{label:"用户姓名",prop:"userName"},{default:_(()=>[p(J,{modelValue:v.userName,"onUpdate:modelValue":x[3]||(x[3]=z=>v.userName=z),disabled:""},null,8,["modelValue"])]),_:1}),p($,{label:"应用选择",prop:"appId",required:""},{default:_(()=>[p(xe,{modelValue:v.appId,"onUpdate:modelValue":x[4]||(x[4]=z=>v.appId=z),onChange:y,placeholder:"请选择应用"},{default:_(()=>[(j(!0),se(Se,null,Ve(c.value,z=>(j(),be(Q,{key:z.id,label:z.application_name,value:z.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),p($,{label:"单位选择",prop:"unitId",required:""},{default:_(()=>[p(Z,{modelValue:v.unitIdPath,"onUpdate:modelValue":x[5]||(x[5]=z=>v.unitIdPath=z),options:s.value,props:E,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择单位"},null,8,["modelValue","options"])]),_:1}),p($,{label:"角色选择",prop:"roleId",required:""},{default:_(()=>[p(xe,{modelValue:v.roleId,"onUpdate:modelValue":x[6]||(x[6]=z=>v.roleId=z),placeholder:"请选择角色"},{default:_(()=>[(j(!0),se(Se,null,Ve(t.value,z=>(j(),be(Q,{key:z.id,label:z.roleName,value:z.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),p(L,{modelValue:d.value,"onUpdate:modelValue":x[10]||(x[10]=z=>d.value=z),title:"确认删除",width:"30%"},{footer:_(()=>[p(W,{onClick:x[9]||(x[9]=z=>d.value=!1)},{default:_(()=>x[21]||(x[21]=[ee("取消")])),_:1,__:[21]}),p(W,{type:"danger",onClick:B},{default:_(()=>x[22]||(x[22]=[ee("确认删除")])),_:1,__:[22]})]),default:_(()=>[x[23]||(x[23]=X("p",null,"确定要删除此授权信息吗？",-1))]),_:1,__:[23]},8,["modelValue"])])}}},cf=gt(uf,[["__scopeId","data-v-1ed7837f"]]),df={class:"role-management-container"},ff={class:"toolbar flex justify-end items-center mb-4"},pf={__name:"RoleManagement",setup(e){const t=O([]),n=O(null),r=async()=>{try{const i=new FormData;i.append("controlCode","query");const u=await ne.post("/api/role_List_manage.php",i);u.status===1?t.value=u.data.map(d=>({id:d.id,name:d.roleName,desc:d.roleDesc})):q.error(u.message||"获取角色列表失败")}catch(i){console.error("获取角色列表失败:",i),q.error("获取角色列表失败")}};Qe(()=>{r()});const o=O(!1),l=Ue({id:null,name:"",desc:""}),a=()=>{n.value&&n.value.resetFields(),l.id=null,l.name="",l.desc="",o.value=!0},s=i=>{n.value&&n.value.resetFields(),l.id=i.id,l.name=i.name,l.desc=i.desc,o.value=!0},c=i=>{Mt.confirm(`确定要删除角色 "${i.name}" 吗？`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const u=new FormData;u.append("controlCode","del"),u.append("id",i.id);const d=await ne.post("/api/role_List_manage.php",u);d.status===1?(q.success("删除成功"),r()):q.error(d.message||"删除失败")}catch(u){console.error("删除角色失败:",u),q.error("删除角色失败: "+u.message)}}).catch(()=>{q.info("已取消删除")})},f=async()=>{if(!l.name){q.error("角色名称不能为空");return}try{const i=new FormData;i.append("controlCode",l.id?"modify":"add"),i.append("roleName",l.name),i.append("roleDesc",l.desc),l.id&&i.append("id",l.id);const u=await ne.post("/api/role_List_manage.php",i);u.status===1?(q.success(l.id?"更新成功":"添加成功"),o.value=!1,r()):q.error(u.message||(l.id?"更新失败":"添加失败"))}catch(i){console.error("操作失败:",i),q.error("操作失败: "+i.message)}};return(i,u)=>{const d=F("el-icon"),m=F("el-button"),w=F("el-table-column"),P=F("el-button-group"),v=F("el-table"),E=F("el-input"),C=F("el-form-item"),N=F("el-form"),T=F("el-dialog");return j(),se("div",df,[X("div",ff,[p(m,{type:"primary",onClick:a},{default:_(()=>[p(d,null,{default:_(()=>[p(ue(Po))]),_:1}),u[4]||(u[4]=ee(" 添加角色 "))]),_:1,__:[4]})]),p(v,{data:t.value,style:{width:"100%"},border:""},{default:_(()=>[p(w,{label:"序号",width:"80",align:"center"},{default:_(U=>[ee(Ee(U.$index+1),1)]),_:1}),p(w,{prop:"name",label:"角色名称"}),p(w,{prop:"desc",label:"角色描述"}),p(w,{label:"操作"},{default:_(U=>[p(P,null,{default:_(()=>[p(m,{size:"mini",type:"warning",onClick:k=>s(U.row)},{default:_(()=>[p(d,null,{default:_(()=>[p(ue(wn))]),_:1})]),_:2},1032,["onClick"]),p(m,{size:"mini",type:"danger",onClick:k=>c(U.row)},{default:_(()=>[p(d,null,{default:_(()=>[p(ue(xn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),p(T,{modelValue:o.value,"onUpdate:modelValue":u[3]||(u[3]=U=>o.value=U),width:"20%"},{default:_(()=>[p(N,{model:l,"label-width":"100px",ref_key:"formRef",ref:n},{default:_(()=>[p(C,{label:"角色名称",prop:"name"},{default:_(()=>[p(E,{modelValue:l.name,"onUpdate:modelValue":u[0]||(u[0]=U=>l.name=U)},null,8,["modelValue"])]),_:1}),p(C,{label:"角色描述",prop:"desc"},{default:_(()=>[p(E,{modelValue:l.desc,"onUpdate:modelValue":u[1]||(u[1]=U=>l.desc=U),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),footer:_(()=>[p(m,{onClick:u[2]||(u[2]=U=>o.value=!1)},{default:_(()=>u[5]||(u[5]=[ee("取消")])),_:1,__:[5]}),p(m,{type:"primary",onClick:f},{default:_(()=>u[6]||(u[6]=[ee("确定")])),_:1,__:[6]})]),_:1},8,["modelValue"])])}}},hf=gt(pf,[["__scopeId","data-v-2a4abc11"]]),mf={class:"duty-management"},_f={__name:"DutyManagement",setup(e){const t=O([]),n=O(!1),r=O([]),o=O(!1),l=O(1),a=O(10),s=O(0),c=Ue({time:"",aLeader:"",bLeader:"",aCommander:"",bCommander:"",dutyLeader:"",dutyStaff:"",techRoom:"",innerSecurity:[]}),f=O(!1),i=O(""),u=Ue([]),d=O({}),m=async U=>{if(!U){t.value=[];return}n.value=!0;try{const k=new FormData;k.append("controlCode","query"),k.append("search_keyword",U);const R=await ne.post("/api/user_manage.php",k);R.status===1?t.value=R.data:t.value=[]}catch{t.value=[]}finally{n.value=!1}},w=async()=>{const U=new FormData;U.append("controlCode","query"),U.append("page",l),U.append("pagesize",a);try{const k=await ne.post("/DutySched/api/sched_manage.php",U);if(k.status===1){s.value=k.totalCount;const R=new Map(k.userInfo.map(M=>[M.userId,M.userName])),I=k.data.map(M=>({id:M.id,schedDate:M.sched_date,aDir:R.get(M.a_dir)||"未知用户",bDir:R.get(M.b_dir)||"未知用户",aCom:R.get(M.a_com)||"未知用户",bCom:R.get(M.b_com)||"未知用户",sub:R.get(M.sub)||"未知用户",op:M.op.map(h=>R.get(h)||"未知用户").join("、"),tech:R.get(M.tech)||"未知用户",sec:M.sec.map(h=>R.get(h)||"未知用户").join("、")}));u.length=0,u.push(...I)}else q.error(k.message||"获取值班记录失败")}catch{q.error("网络请求失败")}},P=U=>{l.value=U,w()},v=U=>{a.value=U,w()},E=async()=>{const U=new FormData;U.append("controlCode","getRole");const k=await ne.post("/DutySched/api/sched_manage.php",U);if(k.status===1){const R={};k.data.forEach(I=>{R[I.roleName]=I.id}),d.value=R}},C=U=>{f.value=!0,i.value="新增值班记录";const k=Object.values(d.value).find(R=>R.name==="局领导");k&&T(k.id)};Qe(()=>{w(),E()});const N=async U=>{if(await q.confirm("确定删除该值班记录？","提示",{type:"warning"})==="confirm")try{const R=new FormData;R.append("controlCode","delete"),R.append("id",U.id);const I=await ne.post("/DutySched/api/sched_manage.php",R);if(I.status===1){const M=u.findIndex(h=>h.id===U.id);M!==-1&&(u.splice(M,1),q.success("删除成功"))}else q.error("删除失败："+I.msg)}catch{q.error("删除请求失败")}},T=async U=>{o.value=!0;try{const k=new FormData;k.append("controlCode","getRoleUser"),k.append("roleId",U);const R=await axios.post("/DutySched/api/sched_manage.php",k);R.data.status===1&&(r.value=R.data.data.map(I=>({value:I.userId,label:I.name})))}catch(k){console.error("获取角色用户失败:",k)}finally{o.value=!1}};return(U,k)=>{const R=F("el-button"),I=F("el-table-column"),M=F("el-table"),h=F("el-pagination"),y=F("el-date-picker"),A=F("el-form-item"),H=F("el-select"),B=F("el-input"),S=F("el-option"),Y=F("el-form"),V=F("el-dialog");return j(),se("div",mf,[k[16]||(k[16]=X("h2",null,"值班管理",-1)),p(R,{type:"primary",onClick:k[0]||(k[0]=x=>C())},{default:_(()=>k[12]||(k[12]=[ee("新增记录")])),_:1,__:[12]}),p(M,{data:u,style:{width:"100%"},border:""},{default:_(()=>[p(I,{prop:"schedDate",label:"值班日期",width:"120"}),p(I,{prop:"aDir",label:"A岗领导",width:"100"}),p(I,{prop:"bDir",label:"B岗领导",width:"100"}),p(I,{prop:"aCom",label:"A岗指挥长",width:"120"}),p(I,{prop:"bCom",label:"B岗指挥长",width:"120"}),p(I,{prop:"sub",label:"值班员",width:"100"}),p(I,{prop:"op",label:"操作人",width:"180"}),p(I,{prop:"tech",label:"技术室",width:"100"}),p(I,{prop:"sec",label:"内部安全值班"}),p(I,{label:"操作",width:"100"},{default:_(x=>[p(R,{type:"danger",size:"small",onClick:Z=>N(x.row)},{default:_(()=>k[13]||(k[13]=[ee(" 删除 ")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),p(h,{"current-page":l.value,"page-size":a.value,total:s.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:P,onSizeChange:v},null,8,["current-page","page-size","total"]),p(V,{modelValue:f.value,"onUpdate:modelValue":k[11]||(k[11]=x=>f.value=x),title:i.value,width:"60%"},{footer:_(()=>[p(R,{onClick:k[10]||(k[10]=x=>f.value=!1)},{default:_(()=>k[14]||(k[14]=[ee("取消")])),_:1,__:[14]}),p(R,{type:"primary",onClick:U.saveRecord},{default:_(()=>k[15]||(k[15]=[ee("保存")])),_:1,__:[15]},8,["onClick"])]),default:_(()=>[p(Y,{model:c,"label-width":"120px"},{default:_(()=>[p(A,{label:"时间"},{default:_(()=>[p(y,{modelValue:c.time,"onUpdate:modelValue":k[1]||(k[1]=x=>c.time=x),type:"date",placeholder:"选择时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(A,{label:"局领导"},{default:_(()=>[p(H,{modelValue:c.aLeader,"onUpdate:modelValue":k[2]||(k[2]=x=>c.aLeader=x),placeholder:"请选择局领导",loading:o.value,options:r.value},null,8,["modelValue","loading","options"])]),_:1}),p(A,{label:"B岗局领导"},{default:_(()=>[p(B,{modelValue:c.bLeader,"onUpdate:modelValue":k[3]||(k[3]=x=>c.bLeader=x),placeholder:"请输入人员"},null,8,["modelValue"])]),_:1}),p(A,{label:"A岗指挥长"},{default:_(()=>[p(B,{modelValue:c.aCommander,"onUpdate:modelValue":k[4]||(k[4]=x=>c.aCommander=x),placeholder:"请输入人员"},null,8,["modelValue"])]),_:1}),p(A,{label:"B岗指挥长"},{default:_(()=>[p(B,{modelValue:c.bCommander,"onUpdate:modelValue":k[5]||(k[5]=x=>c.bCommander=x),placeholder:"请输入人员"},null,8,["modelValue"])]),_:1}),p(A,{label:"值班长"},{default:_(()=>[p(B,{modelValue:c.dutyLeader,"onUpdate:modelValue":k[6]||(k[6]=x=>c.dutyLeader=x),placeholder:"请输入人员"},null,8,["modelValue"])]),_:1}),p(A,{label:"值班员"},{default:_(()=>[p(B,{modelValue:c.dutyStaff,"onUpdate:modelValue":k[7]||(k[7]=x=>c.dutyStaff=x),placeholder:"请输入人员（多个用空格分隔）"},null,8,["modelValue"])]),_:1}),p(A,{label:"技术室"},{default:_(()=>[p(B,{modelValue:c.techRoom,"onUpdate:modelValue":k[8]||(k[8]=x=>c.techRoom=x),placeholder:"请输入人员"},null,8,["modelValue"])]),_:1}),p(A,{label:"内部安全值班"},{default:_(()=>[p(H,{modelValue:c.innerSecurity,"onUpdate:modelValue":k[9]||(k[9]=x=>c.innerSecurity=x),multiple:"",filterable:"",remote:"","remote-method":m,loading:n.value,placeholder:"请选择/搜索人员",style:{width:"300px"}},{default:_(()=>[(j(!0),se(Se,null,Ve(t.value,x=>(j(),be(S,{key:x.id,label:x.name,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},gf=gt(_f,[["__scopeId","data-v-ac154a51"]]),vf=[{path:"/unit-management",name:"UnitManagement",component:_i},{path:"/user-management",name:"UserManagement",component:Ai},{path:"/visual-screen",name:"VisualScreen",component:Wd},{path:"/app-management",name:"AppManagement",component:jd},{path:"/role-management",name:"RoleManagement",component:hf},{path:"/power-management",name:"PowerManagement",component:cf},{path:"/duty-management",name:"DutyManagement",component:gf}],yf=ei({history:Pa(),routes:vf}),Vn=Wl(pi);Vn.use(Ql);for(const[e,t]of Object.entries(Zl))Vn.component(e,t);Vn.use(yf);Vn.mount("#app");
