<?php
/**
 * 文件网盘系统 - 公共函数库
 * 创建时间: 2025-06-23
 */

require_once 'config.php';

/**
 * 获取客户端真实IP地址
 * 考虑代理服务器和负载均衡器的情况
 * @return string 客户端IP地址
 */
function getClientRealIP() {
    // 检查是否通过代理
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // 可能包含多个IP，取第一个
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $ip = trim($ips[0]);
    } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
        // Nginx代理常用的头
        $ip = $_SERVER['HTTP_X_REAL_IP'];
    } elseif (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        // 某些代理服务器使用的头
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else {
        // 直接连接的IP
        $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    }

    // 验证IP地址格式
    $ip = trim($ip);
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
        return $ip;
    } elseif (filter_var($ip, FILTER_VALIDATE_IP)) {
        // 允许私有IP（内网环境）
        return $ip;
    }

    // 如果都无效，返回REMOTE_ADDR
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * 检查用户是否已登录
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * 取用户信息
 * @param string $keyword 搜索关键词（用户姓名或组织单位名称）
 * @return array 返回匹配的用户记录数组，包含name和组织单位名称字段
 */
function getUserInfo($keyword) {
    global $pdo;

    // 处理空关键词的边界情况
    if (empty($keyword) || trim($keyword) === '') {
        return [];
    }

    try {
        // 使用JOIN查询关联3_user表和2_unit表
        // 搜索用户姓名和组织单位名称
        $stmt = $pdo->prepare("
            SELECT u.id, u.name, u.organization_unit, ut.unit_name as organization_unitName
            FROM 3_user u
            LEFT JOIN 2_unit ut ON u.organization_unit = ut.id
            WHERE u.name LIKE ? OR ut.unit_name LIKE ?
            ORDER BY u.name ASC
        ");

        // 使用通配符进行模糊匹配
        $searchPattern = '%' . trim($keyword) . '%';
        $stmt->execute([$searchPattern, $searchPattern]);

        // 返回所有匹配记录
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $results;

    } catch (PDOException $e) {
        // 数据库连接错误等异常情况，返回空数组
        error_log('getUserInfo数据库查询错误: ' . $e->getMessage());
        return [];
    }
}



/**
 * 生成随机分享码
 */
function generateShareCode($length = 6) {
    global $pdo;
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    
    do {
        $code = '';
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        // 检查分享码是否已存在
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM filecloud_info WHERE share_code = ?");
        $stmt->execute([$code]);
        $exists = $stmt->fetchColumn() > 0;
    } while ($exists);
    
    return $code;
}

/**
 * 格式化文件大小
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return round($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return round($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return round($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

/**
 * 验证文件类型
 */
function isValidFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, ALLOWED_EXTENSIONS);
}

/**
 * 生成安全的文件名
 */
function generateSecureFilename($originalName) {
    return uniqid() . '_' . time();
}

/**
 * 创建目录结构
 */
function createDirectoryStructure($path) {
    if (!file_exists($path)) {
        return mkdir($path, 0755, true);
    }
    return true;
}

/**
 * 获取文件上传路径
 */
function getUploadPath() {
    $date = date('Y/m/d');
    $fullPath = UPLOAD_PATH . $date . '/';
    createDirectoryStructure($fullPath);
    return $fullPath;
}

/**
 * 安全地输出HTML内容
 */
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 重定向函数
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * 返回JSON响应
 */
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 获取系统配置
 */
function getConfig($key, $default = null) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT config_value FROM filecloud_config WHERE config_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetchColumn();
        return $result !== false ? $result : $default;
    } catch (PDOException $e) {
        return $default;
    }
}

/**
 * 设置系统配置
 */
function setConfig($key, $value, $description = '') {
    global $pdo;
    try {
        $stmt = $pdo->prepare("
            INSERT INTO filecloud_config (config_key, config_value, config_desc) 
            VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            config_value = VALUES(config_value), 
            config_desc = VALUES(config_desc),
            updated_time = CURRENT_TIMESTAMP
        ");
        return $stmt->execute([$key, $value, $description]);
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * 记录下载次数
 */
function incrementDownloadCount($fileId) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE filecloud_info SET download_count = download_count + 1 WHERE file_id = ?");
        return $stmt->execute([$fileId]);
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * 检查分享码是否过期
 */
function isShareCodeExpired($expirationTime) {
    if (empty($expirationTime)) {
        return false; // 永不过期
    }
    return strtotime($expirationTime) < time();
}

/**
 * 获取文件MIME类型
 */
function getMimeType($filePath) {
    $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
    $mimeTypes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'ppt' => 'application/vnd.ms-powerpoint',
        'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'txt' => 'text/plain',
        'zip' => 'application/zip',
        'rar' => 'application/x-rar-compressed'
    ];
    
    return isset($mimeTypes[$extension]) ? $mimeTypes[$extension] : 'application/octet-stream';
}

/**
 * 验证CSRF令牌
 */
function validateCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * 生成CSRF令牌
 */
function generateCsrfToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * 记录管理员操作日志
 * @param int $userId 用户ID
 * @param string $operation 操作类型
 * @param array $details 操作详情
 * @param string $ip IP地址（可选）
 * @return bool 是否记录成功
 */
function logAdminAction($userId, $operation, $details = [], $ip = null) {
    global $pdo;

    try {
        // 如果没有提供IP，尝试获取当前IP
        if ($ip === null) {
            $ip = getClientRealIP();
        }

        // 准备日志数据
        $logData = [
            'user_id' => $userId,
            'operation' => $operation,
            'details' => json_encode($details, JSON_UNESCAPED_UNICODE),
            'ip_address' => $ip,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        // 使用现有的log表记录（application_id设为0表示文件系统操作）
        $stmt = $pdo->prepare("
            INSERT INTO log (user_id, application_id, operation, ip, timestamp)
            VALUES (?, 0, ?, ?, NOW())
        ");

        $operationText = $operation . ': ' . json_encode($details, JSON_UNESCAPED_UNICODE);

        return $stmt->execute([$userId, $operationText, $ip]);

    } catch (PDOException $e) {
        // 记录到错误日志，但不影响主要操作
        error_log("Admin action log failed: " . $e->getMessage());
        return false;
    }
}

/**
 * 安全删除物理文件
 * @param string $filePath 文件路径
 * @return array 包含成功状态和消息的数组
 */
function safeDeleteFile($filePath) {
    $result = ['success' => false, 'message' => ''];

    try {
        // 检查文件是否存在
        if (!file_exists($filePath)) {
            $result['success'] = true;
            $result['message'] = '文件不存在，视为删除成功';
            return $result;
        }

        // 检查文件是否可写（可删除）
        if (!is_writable($filePath)) {
            $result['message'] = '文件无法删除：权限不足';
            return $result;
        }

        // 尝试删除文件
        if (unlink($filePath)) {
            $result['success'] = true;
            $result['message'] = '文件删除成功';
        } else {
            $result['message'] = '文件删除失败：系统错误';
        }

    } catch (Exception $e) {
        $result['message'] = '文件删除异常：' . $e->getMessage();
    }

    return $result;
}

/**
 * 清理文件相关的所有数据
 * @param PDO $pdo 数据库连接
 * @param int $fileId 文件ID
 * @return array 包含成功状态和消息的数组
 */
function cleanupFileData($pdo, $fileId) {
    $result = ['success' => false, 'message' => '', 'cleaned_tables' => []];

    try {
        // 删除文件分享记录
        $shareStmt = $pdo->prepare("DELETE FROM filecloud_share WHERE file_id = ?");
        if ($shareStmt->execute([$fileId])) {
            $deletedShares = $shareStmt->rowCount();
            if ($deletedShares > 0) {
                $result['cleaned_tables'][] = "filecloud_share ({$deletedShares} 条记录)";
            }
        }

        // 删除主文件记录
        $fileStmt = $pdo->prepare("DELETE FROM filecloud_info WHERE file_id = ?");
        if ($fileStmt->execute([$fileId])) {
            $deletedFiles = $fileStmt->rowCount();
            if ($deletedFiles > 0) {
                $result['cleaned_tables'][] = "filecloud_info ({$deletedFiles} 条记录)";
                $result['success'] = true;
                $result['message'] = '数据清理成功';
            } else {
                $result['message'] = '文件记录不存在或已被删除';
            }
        } else {
            $result['message'] = '删除文件记录失败';
        }

    } catch (PDOException $e) {
        $result['message'] = '数据清理失败：' . $e->getMessage();
    }

    return $result;
}
?>
