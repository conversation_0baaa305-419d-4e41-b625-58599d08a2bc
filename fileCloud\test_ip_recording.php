<?php
/**
 * IP记录功能测试页面
 * 创建时间: 2025-07-03
 */

require_once 'functions.php';

// 测试IP获取函数
function testIPFunction() {
    echo "<h3>测试IP获取函数</h3>";
    
    $currentIP = getClientRealIP();
    echo "<p><strong>当前检测到的IP地址：</strong> <code>$currentIP</code></p>";
    
    // 显示所有相关的服务器变量
    echo "<h4>服务器环境变量：</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>变量名</th><th>值</th></tr>";
    
    $ipHeaders = [
        'REMOTE_ADDR',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_REAL_IP',
        'HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED'
    ];
    
    foreach ($ipHeaders as $header) {
        $value = $_SERVER[$header] ?? '未设置';
        echo "<tr><td>$header</td><td><code>$value</code></td></tr>";
    }
    echo "</table>";
    
    // IP验证测试
    echo "<h4>IP地址验证测试：</h4>";
    $testIPs = [
        '***********',      // 私有IP
        '********',         // 私有IP
        '**********',       // 私有IP
        '*******',          // 公网IP
        '127.0.0.1',        // 本地回环
        '::1',              // IPv6本地回环
        '2001:db8::1',      // IPv6地址
        'invalid.ip',       // 无效IP
        ''                  // 空值
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>测试IP</th><th>filter_var验证</th><th>类型</th></tr>";
    
    foreach ($testIPs as $testIP) {
        $isValid = filter_var($testIP, FILTER_VALIDATE_IP);
        $isPublic = filter_var($testIP, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
        
        $type = '';
        if ($isValid) {
            if ($isPublic) {
                $type = '公网IP';
            } else {
                $type = '私有/保留IP';
            }
        } else {
            $type = '无效IP';
        }
        
        $validText = $isValid ? '✓ 有效' : '✗ 无效';
        echo "<tr><td><code>$testIP</code></td><td>$validText</td><td>$type</td></tr>";
    }
    echo "</table>";
}

// 测试数据库字段
function testDatabaseField() {
    global $pdo;
    echo "<h3>测试数据库uploadIp字段</h3>";
    
    try {
        // 检查字段是否存在
        $stmt = $pdo->query("
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'application' 
            AND TABLE_NAME = 'filecloud_info' 
            AND COLUMN_NAME = 'uploadIp'
        ");
        $fieldInfo = $stmt->fetch();
        
        if ($fieldInfo) {
            echo "<p style='color: green;'>✓ uploadIp字段已存在</p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>属性</th><th>值</th></tr>";
            echo "<tr><td>字段名</td><td>{$fieldInfo['COLUMN_NAME']}</td></tr>";
            echo "<tr><td>数据类型</td><td>{$fieldInfo['DATA_TYPE']}</td></tr>";
            echo "<tr><td>允许NULL</td><td>{$fieldInfo['IS_NULLABLE']}</td></tr>";
            echo "<tr><td>默认值</td><td>" . ($fieldInfo['COLUMN_DEFAULT'] ?? 'NULL') . "</td></tr>";
            echo "<tr><td>注释</td><td>{$fieldInfo['COLUMN_COMMENT']}</td></tr>";
            echo "</table>";
        } else {
            echo "<p style='color: red;'>✗ uploadIp字段不存在，请运行数据库更新脚本</p>";
            echo "<p><strong>执行以下SQL语句添加字段：</strong></p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px;'>";
            echo "ALTER TABLE filecloud_info ADD COLUMN uploadIp VARCHAR(45) NULL COMMENT '上传者IP地址' AFTER file_path;";
            echo "</pre>";
        }
        
        // 检查索引
        $stmt = $pdo->query("
            SELECT INDEX_NAME, COLUMN_NAME 
            FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = 'application' 
            AND TABLE_NAME = 'filecloud_info' 
            AND INDEX_NAME = 'idx_upload_ip'
        ");
        $indexInfo = $stmt->fetch();
        
        if ($indexInfo) {
            echo "<p style='color: green;'>✓ uploadIp索引已存在</p>";
        } else {
            echo "<p style='color: orange;'>⚠ uploadIp索引不存在（可选）</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ 数据库查询失败: " . $e->getMessage() . "</p>";
    }
}

// 测试最近上传的文件IP记录
function testRecentUploads() {
    global $pdo;
    echo "<h3>测试最近上传文件的IP记录</h3>";
    
    try {
        $stmt = $pdo->query("
            SELECT file_id, original_name, user_id, upload_time, uploadIp
            FROM filecloud_info 
            WHERE is_deleted = 0 
            ORDER BY upload_time DESC 
            LIMIT 10
        ");
        $files = $stmt->fetchAll();
        
        if (empty($files)) {
            echo "<p>暂无上传文件记录</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>文件ID</th><th>文件名</th><th>用户ID</th><th>上传时间</th><th>上传IP</th></tr>";
            
            foreach ($files as $file) {
                $userId = $file['user_id'] == 0 ? '匿名用户' : $file['user_id'];
                $uploadIp = $file['uploadIp'] ?? '<span style="color: red;">未记录</span>';
                echo "<tr>";
                echo "<td>{$file['file_id']}</td>";
                echo "<td>{$file['original_name']}</td>";
                echo "<td>$userId</td>";
                echo "<td>{$file['upload_time']}</td>";
                echo "<td>$uploadIp</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ 查询失败: " . $e->getMessage() . "</p>";
    }
}

// 模拟不同IP环境的测试
function testIPEnvironments() {
    echo "<h3>模拟不同IP环境测试</h3>";
    
    $testCases = [
        [
            'name' => '直接连接',
            'env' => ['REMOTE_ADDR' => '***********00'],
            'expected' => '***********00'
        ],
        [
            'name' => '单层代理',
            'env' => [
                'REMOTE_ADDR' => '***********',
                'HTTP_X_FORWARDED_FOR' => '***********'
            ],
            'expected' => '***********'
        ],
        [
            'name' => '多层代理',
            'env' => [
                'REMOTE_ADDR' => '***********',
                'HTTP_X_FORWARDED_FOR' => '***********, ************, ***********'
            ],
            'expected' => '***********'
        ],
        [
            'name' => 'Nginx代理',
            'env' => [
                'REMOTE_ADDR' => '127.0.0.1',
                'HTTP_X_REAL_IP' => '***********'
            ],
            'expected' => '***********'
        ]
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>测试场景</th><th>模拟环境</th><th>预期结果</th><th>实际结果</th><th>状态</th></tr>";
    
    foreach ($testCases as $case) {
        // 备份原始环境变量
        $originalEnv = [];
        foreach ($case['env'] as $key => $value) {
            $originalEnv[$key] = $_SERVER[$key] ?? null;
            $_SERVER[$key] = $value;
        }
        
        // 测试IP获取
        $result = getClientRealIP();
        
        // 恢复环境变量
        foreach ($originalEnv as $key => $value) {
            if ($value === null) {
                unset($_SERVER[$key]);
            } else {
                $_SERVER[$key] = $value;
            }
        }
        
        $status = ($result === $case['expected']) ? '✓ 通过' : '✗ 失败';
        $statusColor = ($result === $case['expected']) ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>{$case['name']}</td>";
        echo "<td><pre>" . json_encode($case['env'], JSON_PRETTY_PRINT) . "</pre></td>";
        echo "<td><code>{$case['expected']}</code></td>";
        echo "<td><code>$result</code></td>";
        echo "<td style='color: $statusColor;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP记录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h3 {
            color: #0d6efd;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        h4 {
            color: #6c757d;
            margin-top: 20px;
        }
        table {
            width: 100%;
            margin: 15px 0;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #dee2e6;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9em;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 IP记录功能测试页面</h1>
        
        <div class="alert alert-info">
            <h4>📋 测试说明</h4>
            <ul>
                <li>此页面用于测试文件上传时IP地址记录功能</li>
                <li>验证getClientRealIP()函数是否正确获取客户端IP</li>
                <li>检查数据库uploadIp字段是否正确配置</li>
                <li>测试不同网络环境下的IP获取逻辑</li>
            </ul>
        </div>

        <div class="test-section">
            <?php testIPFunction(); ?>
        </div>

        <div class="test-section">
            <?php testDatabaseField(); ?>
        </div>

        <div class="test-section">
            <?php testRecentUploads(); ?>
        </div>

        <div class="test-section">
            <?php testIPEnvironments(); ?>
        </div>

        <div class="test-section">
            <h3>🔧 实施步骤</h3>
            <ol>
                <li><strong>数据库更新：</strong>运行 <code>database_update_uploadip.sql</code> 脚本添加uploadIp字段</li>
                <li><strong>功能测试：</strong>
                    <ul>
                        <li>访问 <a href="upload.php" target="_blank">登录用户上传页面</a> 测试IP记录</li>
                        <li>访问 <a href="anonymous_upload.php" target="_blank">匿名上传页面</a> 测试IP记录</li>
                    </ul>
                </li>
                <li><strong>验证结果：</strong>刷新此页面查看最近上传文件的IP记录</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>⚠️ 注意事项</h3>
            <div class="alert alert-warning">
                <ul>
                    <li><strong>代理环境：</strong>在使用代理服务器的环境中，确保代理正确设置了X-Forwarded-For或X-Real-IP头</li>
                    <li><strong>IPv6支持：</strong>uploadIp字段使用VARCHAR(45)以支持IPv6地址</li>
                    <li><strong>隐私考虑：</strong>IP地址属于敏感信息，请确保符合相关隐私法规</li>
                    <li><strong>数据清理：</strong>考虑定期清理或匿名化历史IP记录</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
