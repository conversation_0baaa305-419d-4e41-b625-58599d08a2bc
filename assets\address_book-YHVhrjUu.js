import{_ as Ie,h as s,x as G,j as Oe,k as Te,m as p,A as H,r as m,d as Y,o as f,b as _,a as n,w as o,c as h,$ as K,J as Ae,l as w,t as F,n as Be,u as S,a1 as Ne,N as Pe,a2 as Ee,a3 as qe,D as Le,F as I,e as O,a5 as ie,f as Re,i as Je,g as Xe}from"./index-D8fAg56F.js";import{s as v}from"./requests-B1avZrnq.js";/* empty css              */const Ge={class:"user-management-container"},He={class:"left-panel"},Ke=["onClick"],Qe={class:"right-panel"},We={class:"action-buttons",style:{display:"flex","align-items":"center"}},Ze={class:"left-buttons",style:{display:"flex","align-items":"center",gap:"3px"}},el={class:"right-switches",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},ll={class:"form-row"},al={class:"form-row"},tl={class:"form-row"},nl={class:"form-row"},ol={class:"form-row"},rl={class:"form-row"},sl={class:"form-row"},dl={class:"form-row"},ul={class:"dialog-footer"},il={__name:"AddressBook",setup(W){const T=s([]),Z=s([]),ee=s([]),D=s(null);s("");const L=s([]),z=s([]),le=G(()=>{if(!D.value)return"";const e=y.value.find(l=>l.id===D.value);return console.log("当前选中的部门ID:",D.value),console.log("当前选中的部门:",e.unit_name),e?e.unit_name:""});s(null);const V=s(1),A=s(10),ae=s(0),B=s([]),y=s([]),N=s(""),te=s([]),ne=s([]),R=s([]),k=s([]),b=s(!1),J=s(""),M=s(!1),pe=s({});s(!1);const P=s(!1),X=s(null),_e=()=>{M.value?b.value=!1:Ce()},me=G(()=>N.value?y.value.filter(e=>e.show&&e.unit_name.toLowerCase().includes(N.value.toLowerCase())):y.value.filter(e=>e.show)),a=Oe({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sort_order:null,desc:""}),ce={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},C=s(null),fe=async()=>{try{const e=new FormData;e.append("type","identity");const l=await v({url:"/api/person_info_api.php",method:"post",data:e});l.status===1?T.value=l.data.map(r=>({label:r,value:r})):p.error("获取人员身份数据失败："+l.message)}catch{p.error("网络请求失败")}},ve=async()=>{try{const e=new FormData;e.append("type","status");const l=await v({url:"/api/person_info_api.php",method:"post",data:e});l.status===1?Z.value=l.data.map(r=>({label:r,value:r})):p.error("获取人员状态数据失败："+l.message)}catch{p.error("网络请求失败")}},ge=async()=>{try{const e=new FormData;e.append("type","rank");const l=await v({url:"/api/person_info_api.php",method:"post",data:e});l.status===1?ee.value=l.data.map(r=>({label:r,value:r})):p.error("获取职级数据失败："+l.message)}catch{p.error("网络请求失败")}};Te(async()=>{try{const e=await v.post("api/get_unit_info.php");B.value=[e.data],oe(e.data),te.value=B.value,ne.value=B.value,console.log("获取部门数据成功:",B.value),await g(),fe(),ve(),ge()}catch(e){console.error("获取数据失败:",e),p.error("获取数据失败，请稍后重试")}});const g=async()=>{console.log("开始获取用户数据...");try{const e=new FormData;e.append("controlCode","query"),e.append("page",V.value),e.append("pagesize",A.value),D.value&&e.append("organization_unit",D.value),q.value?e.append("isShowInPersonnel","1"):e.append("isShowInPersonnel","0"),E.value?e.append("isShowOutPersonnel","1"):e.append("isShowOutPersonnel","0");const l=await v.post("api/user_manage.php",e,{headers:{"Content-Type":"multipart/form-data"}});R.value=l.data,console.log("获取用户数据成功:",R.value),ae.value=l.total}catch(e){console.error("获取用户数据失败:",e),p.error("获取用户数据失败，请稍后重试")}},oe=(e,l=0,r=null)=>{const u={...e,level:l,expanded:e.children&&e.children.length>0,parent:r,indent:l*20,show:!0};y.value.push(u),e.children&&e.children.length>0&&e.children.forEach(i=>{oe(i,l+1,u)})};console.log("扁平化后的部门列表:",y.value);const ye=e=>{e.expanded=!e.expanded;const l=y.value.indexOf(e)+1;let r=e.level+1;for(let u=l;u<y.value.length;u++){const i=y.value[u];if(i.level<=e.level)break;i.level===r?i.show=e.expanded:i.level>r&&(i.show=e.expanded&&y.value[u-1].show)}},E=s(!0),q=s(!0),be=()=>{b.value=!0,C.value.resetFields()},he=async()=>{if(k.value.length===0){p.warning("请先选择要删除的用户");return}try{await ie.confirm(`确定要删除选中的 ${k.value.length} 个用户吗？删除后数据不可恢复`,"批量删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const e=new FormData;e.append("controlCode","del");const l=k.value.map(r=>r.id);e.append("id",l.join(",")),await v.post("api/user_manage.php",e,{headers:{"Content-Type":"multipart/form-data"}}),p.success(`成功删除 ${k.value.length} 个用户`),await g(),k.value=[]}catch(e){e!=="cancel"&&p.error(`删除失败：${e.message||"未知错误"}`)}},Ve=async e=>{C.value&&C.value.resetFields(),P.value=!0,X.value=e.id,b.value=!0,J.value="编辑用户信息",M.value=!1,a.name=e.name,a.id_number=e.id_number,a.phone=e.phone,a.archive_birthdate=e.archive_birthdate,a.gender=e.gender,console.log("性别值:",e.gender),a.short_code=e.short_code,a.alt_phone_1=e.alt_phone_1,a.alt_phone_2=e.alt_phone_2,a.landline=e.landline,a.organization_unit=e.organization_unit,a.work_unit=e.work_unit,a.employment_date=e.employment_date,a.political_status=e.political_status,a.party_join_date=e.party_join_date,a.personnel_type=e.personnel_type,a.police_number=e.police_number,a.is_assisting_officer=e.is_assisting_officer,a.employment_status=e.employment_status,a.job_rank=e.job_rank,a.current_rank_date=e.current_rank_date,a.position=e.position,a.current_position_date=e.current_position_date,await re(e.organization_unit),a.sort_order=e.sort_order,console.log("排序值:",e.sort_order),a.desc=e.desc},ke=e=>{J.value="查看用户详情",pe.value={...e},M.value=!0,b.value=!0},xe=async e=>{try{await ie.confirm(`确定要删除用户 ${e.name} 吗？删除后数据不可恢复`,"删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const l=new FormData;l.append("controlCode","del"),l.append("id",e.id),await v.post("api/user_manage.php",l,{headers:{"Content-Type":"multipart/form-data"}}),p.success("用户删除成功"),await g()}catch(l){l!=="cancel"&&p.error(`删除失败：${l.message||"未知错误"}`)}},we=e=>{k.value=e},De=async e=>{console.log("点击的部门名称:",e),D.value=e.id,await g()},Ce=async()=>{try{await C.value.validate();const e=new FormData;P.value?(e.append("controlCode","modify"),e.append("id",X.value)):e.append("controlCode","add"),e.append("name",a.name),e.append("id_number",a.id_number||""),e.append("phone",a.phone),e.append("archive_birthdate",a.archive_birthdate||""),e.append("gender",a.gender||""),e.append("short_code",a.short_code||""),e.append("alt_phone_1",a.alt_phone_1||""),e.append("alt_phone_2",a.alt_phone_2||""),e.append("landline",a.landline||"");const l=Array.isArray(a.organization_unit)?a.organization_unit[a.organization_unit.length-1]:a.organization_unit;e.append("organization_unit",l||"");const r=Array.isArray(a.work_unit)?a.work_unit[a.work_unit.length-1]:a.work_unit;e.append("work_unit",r||l),e.append("employment_date",a.employment_date||""),e.append("political_status",a.political_status||""),e.append("party_join_date",a.party_join_date||""),e.append("personnel_type",a.personnel_type||""),e.append("police_number",a.police_number||""),e.append("assisting_officer",a.is_assisting_officer||""),e.append("employment_status",a.employment_status||""),e.append("job_rank",a.job_rank||""),e.append("current_rank_date",a.current_rank_date||""),e.append("position",a.position||""),e.append("current_position_date",a.current_position_date||""),e.append("sort_order",a.sort_order+1),e.append("desc",a.desc||""),await v.post("api/user_manage.php",e,{headers:{"Content-Type":"multipart/form-data"}});const u=P.value?"编辑":"添加";p.success(`用户${u}成功`),b.value=!1,P.value=!1,X.value=null,await g(),C.value.resetFields()}catch(e){p.error("提交失败："+(e.message||"未知错误"))}};G(()=>{const e=new Map;return y.value.forEach(l=>{e.set(l.id,l.unit_name)}),e});const Ue=async e=>{try{const l=new FormData;l.append("controlCode","modify"),l.append("id",e.id);const r=e.sort_order-1;l.append("sort_order",r),await v.post("api/user_manage.php",l),await g(),p.success("上移成功")}catch{p.error("上移失败，请稍后重试")}},Ye=async e=>{try{const l=new FormData;l.append("controlCode","modify"),l.append("id",e.id);const r=e.sort_order+1;l.append("sort_order",r),await v.post("api/user_manage.php",l),await g(),p.success("下移成功")}catch{p.error("下移失败，请稍后重试")}};H(q,async e=>{V.value=1,await g()}),H(E,async e=>{V.value=1,await g()});const re=async e=>{if(!e){z.value=[];return}try{const l=new FormData;l.append("controlCode","getSortOptions"),l.append("organization_unit",e);const r=await v.post("/api/user_manage.php",l,{headers:{"Content-Type":"multipart/form-data"}});z.value=r.data,console.log("获取排序选项成功:",z.value)}catch(l){console.error("获取排序选项失败:",l),p.error("获取排序选项失败，请稍后重试"),z.value=[]}},ze=async e=>{if(!e){L.value=[];return}try{const l=new FormData;l.append("controlCode","getPspInfo"),l.append("organization_unit",e);const r=await v({url:"/api/user_manage.php",method:"post",data:l});r.status===1?L.value=r.data.map(u=>({label:u.name,value:u.id})):p.error("获取带辅民警数据失败")}catch{p.error("网络请求失败")}};return H(()=>a.organization_unit,async e=>{const l=Array.isArray(e)?e[e.length-1]:e;await re(l),await ze(l)}),(e,l)=>{const r=m("el-input"),u=m("el-button"),i=m("el-table-column"),se=m("el-table"),Me=m("el-tag"),de=m("el-switch"),$=m("el-icon"),$e=m("el-button-group"),je=m("el-pagination"),d=m("el-form-item"),j=m("el-date-picker"),c=m("el-option"),x=m("el-select"),ue=m("el-cascader"),Fe=m("el-form"),Se=m("el-dialog");return f(),Y("div",Ge,[_("div",He,[n(r,{modelValue:N.value,"onUpdate:modelValue":l[0]||(l[0]=t=>N.value=t),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),n(se,{data:me.value,border:"",stripe:"",fit:"",class:"department-table"},{default:o(()=>[n(i,{label:"操作",width:"55"},{default:o(({row:t})=>[t.children&&t.children.length>0?(f(),h(u,{key:0,type:"text",size:"small",onClick:Ae(U=>ye(t),["stop"])},{default:o(()=>[w(F(t.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):K("",!0)]),_:1}),n(i,{prop:"unit_name",label:"单位名称"},{default:o(({row:t})=>[_("span",{class:"indent",style:Be({width:`${t.indent}px`})},null,4),_("span",{onClick:U=>De(t),style:{cursor:"pointer"}},F(t.unit_name),9,Ke)]),_:1})]),_:1},8,["data"])]),_("div",Qe,[_("div",We,[_("div",Ze,[n(u,{type:"primary",round:"",onClick:be},{default:o(()=>l[31]||(l[31]=[w("添加用户")])),_:1,__:[31]}),n(u,{type:"danger",round:"",onClick:he},{default:o(()=>[w("批量删除 ( "+F(k.value.length)+" )",1)]),_:1})]),_("div",el,[le.value?(f(),h(Me,{key:0,type:"info",style:{"margin-left":"10px"}},{default:o(()=>[w(" 当前单位："+F(le.value),1)]),_:1})):K("",!0),n(de,{modelValue:E.value,"onUpdate:modelValue":l[1]||(l[1]=t=>E.value=t),"inline-prompt":"","active-text":"显示抽出人员","inactive-text":"不显示抽出人员",style:{"margin-left":"10px"}},null,8,["modelValue"]),n(de,{modelValue:q.value,"onUpdate:modelValue":l[2]||(l[2]=t=>q.value=t),"active-text":"显示抽入人员","inactive-text":"显示抽入人员","inline-prompt":"",style:{"margin-left":"10px"}},null,8,["modelValue"])])]),n(se,{data:R.value,border:"",stripe:"",fit:"",class:"user-table",onSelectionChange:we},{default:o(()=>[n(i,{type:"selection",width:"55"}),n(i,{type:"index",label:"序号",width:"60",align:"center",index:t=>(V.value-1)*A.value+t+1},null,8,["index"]),n(i,{prop:"name",label:"姓名",width:"70"}),n(i,{prop:"id_number",label:"身份证号",width:"100"}),n(i,{prop:"phone",label:"手机号码",width:"115"}),n(i,{label:"性别",width:"55"},{default:o(t=>[w(F(t.row.gender===1?"男":t.row.gender===2?"女":"未知"),1)]),_:1}),n(i,{label:"编制单位",prop:"organization_unitName"}),n(i,{label:"工作单位",prop:"work_unitName"}),n(i,{prop:"personnel_type",label:"人员身份"}),n(i,{prop:"employment_status",label:"人员状态"}),n(i,{prop:"desc",label:"备注"}),n(i,{label:"操作",width:"260",align:"center"},{default:o(({row:t})=>[n($e,null,{default:o(()=>[n(u,{type:"warning",size:"mini",onClick:U=>Ve(t)},{default:o(()=>[n($,null,{default:o(()=>[n(S(Ne))]),_:1})]),_:2},1032,["onClick"]),n(u,{type:"success",size:"mini",onClick:U=>ke(t)},{default:o(()=>[n($,null,{default:o(()=>[n(S(Pe))]),_:1})]),_:2},1032,["onClick"]),n(u,{type:"danger",size:"mini",onClick:U=>xe(t)},{default:o(()=>[n($,null,{default:o(()=>[n(S(Ee))]),_:1})]),_:2},1032,["onClick"]),n(u,{size:"mini",type:"info",onClick:U=>Ue(t),disabled:t.sort_order<=1},{default:o(()=>[n($,null,{default:o(()=>[n(S(qe))]),_:1})]),_:2},1032,["onClick","disabled"]),n(u,{size:"mini",type:"info",onClick:U=>Ye(t),disabled:t.sort_order>=t.sortMax},{default:o(()=>[n($,null,{default:o(()=>[n(S(Le))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),n(je,{"current-page":V.value,"page-size":A.value,total:ae.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:l[3]||(l[3]=t=>{V.value=t,g()}),onSizeChange:l[4]||(l[4]=t=>{A.value=t,V.value=1,g()})},null,8,["current-page","page-size","total"])]),n(Se,{modelValue:b.value,"onUpdate:modelValue":l[30]||(l[30]=t=>b.value=t),title:J.value,width:"1000px"},{footer:o(()=>[_("span",ul,[M.value?K("",!0):(f(),h(u,{key:0,onClick:l[29]||(l[29]=t=>b.value=!1)},{default:o(()=>l[33]||(l[33]=[w("取消")])),_:1,__:[33]})),n(u,{type:"primary",onClick:_e},{default:o(()=>l[34]||(l[34]=[w("确定")])),_:1,__:[34]})])]),default:o(()=>[n(Fe,{model:a,rules:ce,ref_key:"newUserFormRef",ref:C,"label-width":"120px",inline:!1,class:"user-form",disabled:M.value},{default:o(()=>[_("div",ll,[n(d,{label:"姓名",prop:"name",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.name,"onUpdate:modelValue":l[5]||(l[5]=t=>a.name=t)},null,8,["modelValue"])]),_:1}),n(d,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.id_number,"onUpdate:modelValue":l[6]||(l[6]=t=>a.id_number=t),maxlength:"18"},null,8,["modelValue"])]),_:1}),n(d,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.phone,"onUpdate:modelValue":l[7]||(l[7]=t=>a.phone=t),maxlength:"11"},null,8,["modelValue"])]),_:1})]),_("div",al,[n(d,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:o(()=>[n(j,{modelValue:a.archive_birthdate,"onUpdate:modelValue":l[8]||(l[8]=t=>a.archive_birthdate=t),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(d,{label:"性别",prop:"gender",style:{flex:"1"}},{default:o(()=>[n(x,{modelValue:a.gender,"onUpdate:modelValue":l[9]||(l[9]=t=>a.gender=t)},{default:o(()=>[n(c,{label:"未知",value:0}),n(c,{label:"男",value:1}),n(c,{label:"女",value:2})]),_:1},8,["modelValue"])]),_:1}),n(d,{label:"备注",prop:"desc",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.desc,"onUpdate:modelValue":l[10]||(l[10]=t=>a.desc=t)},null,8,["modelValue"])]),_:1})]),_("div",tl,[n(d,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.short_code,"onUpdate:modelValue":l[11]||(l[11]=t=>a.short_code=t)},null,8,["modelValue"]),l[32]||(l[32]=_("div",{class:""},null,-1))]),_:1,__:[32]}),n(d,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.alt_phone_1,"onUpdate:modelValue":l[12]||(l[12]=t=>a.alt_phone_1=t)},null,8,["modelValue"])]),_:1}),n(d,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.alt_phone_2,"onUpdate:modelValue":l[13]||(l[13]=t=>a.alt_phone_2=t)},null,8,["modelValue"])]),_:1})]),_("div",nl,[n(d,{label:"座机",prop:"landline",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.landline,"onUpdate:modelValue":l[14]||(l[14]=t=>a.landline=t)},null,8,["modelValue"])]),_:1}),n(d,{label:"编制单位",prop:"organization_unit",required:""},{default:o(()=>[n(ue,{modelValue:a.organization_unit,"onUpdate:modelValue":l[15]||(l[15]=t=>a.organization_unit=t),options:te.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),n(d,{label:"工作单位",prop:"work_unit"},{default:o(()=>[n(ue,{modelValue:a.work_unit,"onUpdate:modelValue":l[16]||(l[16]=t=>a.work_unit=t),options:ne.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),_("div",ol,[n(d,{label:"参工日期",prop:"employment_date",style:{flex:"1"}},{default:o(()=>[n(j,{modelValue:a.employment_date,"onUpdate:modelValue":l[17]||(l[17]=t=>a.employment_date=t),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(d,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:o(()=>[n(x,{modelValue:a.political_status,"onUpdate:modelValue":l[18]||(l[18]=t=>a.political_status=t),placeholder:"请选择政治面貌"},{default:o(()=>[n(c,{label:"中共党员",value:"中共党员"}),n(c,{label:"中共预备党员",value:"中共预备党员"}),n(c,{label:"共青团员",value:"共青团员"}),n(c,{label:"民主党派",value:"民主党派"}),n(c,{label:"无党派人士",value:"无党派人士"}),n(c,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),n(d,{label:"加入组织日期",prop:"party_join_date",style:{flex:"1"}},{default:o(()=>[n(j,{modelValue:a.party_join_date,"onUpdate:modelValue":l[19]||(l[19]=t=>a.party_join_date=t),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_("div",rl,[n(d,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:o(()=>[n(x,{modelValue:a.personnel_type,"onUpdate:modelValue":l[20]||(l[20]=t=>a.personnel_type=t),placeholder:"请选择人员身份"},{default:o(()=>[(f(!0),Y(I,null,O(T.value,t=>(f(),h(c,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(d,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.police_number,"onUpdate:modelValue":l[21]||(l[21]=t=>a.police_number=t)},null,8,["modelValue"])]),_:1}),n(d,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:o(()=>[n(x,{modelValue:a.is_assisting_officer,"onUpdate:modelValue":l[22]||(l[22]=t=>a.is_assisting_officer=t),placeholder:"请选择带辅民警",clearable:""},{default:o(()=>[(f(!0),Y(I,null,O(L.value,t=>(f(),h(c,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_("div",sl,[n(d,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:o(()=>[n(x,{modelValue:a.employment_status,"onUpdate:modelValue":l[23]||(l[23]=t=>a.employment_status=t)},{default:o(()=>[(f(!0),Y(I,null,O(Z.value,t=>(f(),h(c,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(d,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:o(()=>[n(x,{modelValue:a.job_rank,"onUpdate:modelValue":l[24]||(l[24]=t=>a.job_rank=t)},{default:o(()=>[(f(!0),Y(I,null,O(ee.value,t=>(f(),h(c,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(d,{label:"任现职级日期",prop:"current_rank_date",style:{flex:"1"}},{default:o(()=>[n(j,{modelValue:a.current_rank_date,"onUpdate:modelValue":l[25]||(l[25]=t=>a.current_rank_date=t),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),_("div",dl,[n(d,{label:"职务",prop:"position",style:{flex:"1"}},{default:o(()=>[n(r,{modelValue:a.position,"onUpdate:modelValue":l[26]||(l[26]=t=>a.position=t)},null,8,["modelValue"])]),_:1}),n(d,{label:"任现职务日期",prop:"current_position_date",style:{flex:"1"}},{default:o(()=>[n(j,{modelValue:a.current_position_date,"onUpdate:modelValue":l[27]||(l[27]=t=>a.current_position_date=t),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),n(d,{label:"人员排序",prop:"sort_order",style:{flex:"1"}},{default:o(()=>[n(x,{modelValue:a.sort_order,"onUpdate:modelValue":l[28]||(l[28]=t=>a.sort_order=t),placeholder:"请选择人员排序"},{default:o(()=>[n(c,{label:"置于 最前",value:"0"}),(f(!0),Y(I,null,O(z.value,t=>(f(),h(c,{key:t.id,label:`置于 ${t.name} 之后`,value:t.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","disabled"])]),_:1},8,["modelValue","title"])])}}},pl=Ie(il,[["__scopeId","data-v-3ba7f067"]]),Q=Re(pl);Q.use(Je);for(const[W,T]of Object.entries(Xe))Q.component(W,T);Q.mount("#app");
