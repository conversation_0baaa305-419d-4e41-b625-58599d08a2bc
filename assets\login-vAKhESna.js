import{_ as N,g as p,h as v,j as P,r,d as S,o as L,b as n,a as l,w as t,k as w,l as $,f as E,i as h,E as B}from"./index-QCnABT6_.js";import{_ as U}from"./logo-LbKsNqzq.js";import{s as j}from"./requests-Chsm6HQA.js";const q="/assets/login-box-bg-CL6i7T2F.svg",D={class:"login-container"},R={class:"login-content"},T={class:"login-right"},z={__name:"Login",setup(g){const m=p(null),o=v({id_number:"",password:"",rememberMe:!1}),V=v({id_number:[{required:!0,message:"",trigger:"blur"}],password:[{required:!0,message:"",trigger:"blur"}]}),u=p(!1),d=p(!1),I=()=>{d.value=!d.value};P(()=>{const s=localStorage.getItem("savedIdNumber"),e=localStorage.getItem("savedPassword");s&&(o.id_number=s),e&&(o.password=e,o.rememberMe=!0)});const k=s=>{console.log("表单被点击:",s.target)},_=s=>{console.log(`${s} 输入框获得焦点`),console.log(`${s} 当前值:`,o[s])},x=s=>{var e;console.log("输入内容变化:",(e=s.target)==null?void 0:e.value)},y=async()=>{try{u.value=!0;const s=new FormData;s.append("id_number",o.id_number),s.append("password",o.password);const e=await j.post("/api/login.php",s);o.rememberMe?(localStorage.setItem("savedIdNumber",o.id_number),localStorage.setItem("savedPassword",o.password)):(localStorage.removeItem("savedIdNumber"),localStorage.removeItem("savedPassword")),$.success(`${e.user.name} 欢迎回来！`),console.log("登录成功:",e),window.location.href="index.html"}finally{u.value=!1}};return(s,e)=>{const f=r("el-input"),i=r("el-form-item"),b=r("el-button"),C=r("el-checkbox"),F=r("el-form"),M=r("el-card");return L(),S("div",D,[e[9]||(e[9]=n("div",{class:"header-logo"},[n("img",{src:U,class:"logo"}),n("h2",null,"CloudPivot")],-1)),n("div",R,[e[8]||(e[8]=n("div",{class:"login-left"},[n("img",{src:q,class:"login-img"})],-1)),n("div",T,[l(M,{class:"login-card"},{default:t(()=>[e[7]||(e[7]=n("h2",{class:"login-title"},"登录",-1)),l(F,{ref_key:"loginFormRef",ref:m,model:o,rules:V,"label-width":"80px",onClick:k},{default:t(()=>[l(i,{label:"身份证号",prop:"id_number"},{default:t(()=>[l(f,{modelValue:o.id_number,"onUpdate:modelValue":e[0]||(e[0]=a=>o.id_number=a),placeholder:"请输入身份证号",onFocus:e[1]||(e[1]=a=>_("id_number")),onInput:x},null,8,["modelValue"])]),_:1}),l(i,{label:"密码",prop:"password"},{default:t(()=>[l(f,{modelValue:o.password,"onUpdate:modelValue":e[2]||(e[2]=a=>o.password=a),type:d.value?"text":"password",placeholder:"请输入密码",onFocus:e[3]||(e[3]=a=>_("password"))},{suffix:t(()=>[l(b,{icon:d.value?"View":"Hide",onClick:I,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),l(i,{class:"form-item"},{default:t(()=>[l(C,{modelValue:o.rememberMe,"onUpdate:modelValue":e[4]||(e[4]=a=>o.rememberMe=a)},{default:t(()=>e[5]||(e[5]=[w("记住我")])),_:1,__:[5]},8,["modelValue"])]),_:1}),l(i,null,{default:t(()=>[l(b,{type:"primary",round:"",loading:u.value,onClick:y,class:"login-button"},{default:t(()=>e[6]||(e[6]=[w(" 登录 ")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1},8,["model","rules"])]),_:1,__:[7]})])])])}}},A=N(z,[["__scopeId","data-v-2c18ceb4"]]),c=E(A);c.use(h);for(const[g,m]of Object.entries(B))c.component(g,m);c.mount("#app");
