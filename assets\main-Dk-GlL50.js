import{s as zl,d as Eo,u as de,a as Fl,c as Ce,p as Vn,r as M,w as ut,h as Ro,n as on,i as dt,b as Ue,_ as rt,o as Qe,e as te,f as le,g as W,j as f,k as v,l as z,m as ql,F as _e,q as J,t as Yl,v as Re,x as ne,y as ye,z as he,A as Bl,B as Vo,C as Sr,D as kr,E as H,G as Hl,H as $t,I as ur,J as $o,K as wn,L as bn,M as Po,N as Io,O as Dt,P as Xl,Q as Gl,R as Kl,S as Wl,T as Ql,U as Zl}from"./index-tTBIG6iu.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const at=typeof document<"u";function Do(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Jl(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Do(e.default)}const ue=Object.assign;function $n(e,t){const n={};for(const r in t){const o=t[r];n[r]=Te(o)?o.map(e):e(o)}return n}const Pt=()=>{},Te=Array.isArray,Mo=/#/g,jl=/&/g,ea=/\//g,ta=/=/g,na=/\?/g,No=/\+/g,ra=/%5B/g,oa=/%5D/g,Ao=/%5E/g,la=/%60/g,Uo=/%7B/g,aa=/%7C/g,To=/%7D/g,ia=/%20/g;function cr(e){return encodeURI(""+e).replace(aa,"|").replace(ra,"[").replace(oa,"]")}function sa(e){return cr(e).replace(Uo,"{").replace(To,"}").replace(Ao,"^")}function On(e){return cr(e).replace(No,"%2B").replace(ia,"+").replace(Mo,"%23").replace(jl,"%26").replace(la,"`").replace(Uo,"{").replace(To,"}").replace(Ao,"^")}function ua(e){return On(e).replace(ta,"%3D")}function ca(e){return cr(e).replace(Mo,"%23").replace(na,"%3F")}function da(e){return e==null?"":ca(e).replace(ea,"%2F")}function Mt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const fa=/\/$/,pa=e=>e.replace(fa,"");function Pn(e,t,n="/"){let r,o={},l="",a="";const s=t.indexOf("#");let c=t.indexOf("?");return s<c&&s>=0&&(c=-1),c>-1&&(r=t.slice(0,c),l=t.slice(c+1,s>-1?s:t.length),o=e(l)),s>-1&&(r=r||t.slice(0,s),a=t.slice(s,t.length)),r=va(r??t,n),{fullPath:r+(l&&"?")+l+a,path:r,query:o,hash:Mt(a)}}function ha(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Cr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function ma(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&ft(t.matched[r],n.matched[o])&&Oo(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function ft(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Oo(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!_a(e[n],t[n]))return!1;return!0}function _a(e,t){return Te(e)?Er(e,t):Te(t)?Er(t,e):e===t}function Er(e,t){return Te(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function va(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let l=n.length-1,a,s;for(a=0;a<r.length;a++)if(s=r[a],s!==".")if(s==="..")l>1&&l--;else break;return n.slice(0,l).join("/")+"/"+r.slice(a).join("/")}const Ge={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Nt;(function(e){e.pop="pop",e.push="push"})(Nt||(Nt={}));var It;(function(e){e.back="back",e.forward="forward",e.unknown=""})(It||(It={}));function ga(e){if(!e)if(at){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),pa(e)}const ya=/^[^#]+#/;function wa(e,t){return e.replace(ya,"#")+t}function ba(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const xn=()=>({left:window.scrollX,top:window.scrollY});function xa(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=ba(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Rr(e,t){return(history.state?history.state.position-t:-1)+e}const Ln=new Map;function Sa(e,t){Ln.set(e,t)}function ka(e){const t=Ln.get(e);return Ln.delete(e),t}let Ca=()=>location.protocol+"//"+location.host;function Lo(e,t){const{pathname:n,search:r,hash:o}=t,l=e.indexOf("#");if(l>-1){let s=o.includes(e.slice(l))?e.slice(l).length:1,c=o.slice(s);return c[0]!=="/"&&(c="/"+c),Cr(c,"")}return Cr(n,e)+r+o}function Ea(e,t,n,r){let o=[],l=[],a=null;const s=({state:d})=>{const h=Lo(e,location),b=n.value,P=t.value;let _=0;if(d){if(n.value=h,t.value=d,a&&a===b){a=null;return}_=P?d.position-P.position:0}else r(h);o.forEach(E=>{E(n.value,b,{delta:_,type:Nt.pop,direction:_?_>0?It.forward:It.back:It.unknown})})};function c(){a=n.value}function p(d){o.push(d);const h=()=>{const b=o.indexOf(d);b>-1&&o.splice(b,1)};return l.push(h),h}function i(){const{history:d}=window;d.state&&d.replaceState(ue({},d.state,{scroll:xn()}),"")}function u(){for(const d of l)d();l=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",i)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",i,{passive:!0}),{pauseListeners:c,listen:p,destroy:u}}function Vr(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?xn():null}}function Ra(e){const{history:t,location:n}=window,r={value:Lo(e,n)},o={value:t.state};o.value||l(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function l(c,p,i){const u=e.indexOf("#"),d=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+c:Ca()+e+c;try{t[i?"replaceState":"pushState"](p,"",d),o.value=p}catch(h){console.error(h),n[i?"replace":"assign"](d)}}function a(c,p){const i=ue({},t.state,Vr(o.value.back,c,o.value.forward,!0),p,{position:o.value.position});l(c,i,!0),r.value=c}function s(c,p){const i=ue({},o.value,t.state,{forward:c,scroll:xn()});l(i.current,i,!0);const u=ue({},Vr(r.value,c,null),{position:i.position+1},p);l(c,u,!1),r.value=c}return{location:r,state:o,push:s,replace:a}}function Va(e){e=ga(e);const t=Ra(e),n=Ea(e,t.state,t.location,t.replace);function r(l,a=!0){a||n.pauseListeners(),history.go(l)}const o=ue({location:"",base:e,go:r,createHref:wa.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function $a(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Va(e)}function Pa(e){return typeof e=="string"||e&&typeof e=="object"}function zo(e){return typeof e=="string"||typeof e=="symbol"}const Fo=Symbol("");var $r;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})($r||($r={}));function pt(e,t){return ue(new Error,{type:e,[Fo]:!0},t)}function Ye(e,t){return e instanceof Error&&Fo in e&&(t==null||!!(e.type&t))}const Pr="[^/]+?",Ia={sensitive:!1,strict:!1,start:!0,end:!0},Da=/[.+*?^${}()[\]/\\]/g;function Ma(e,t){const n=ue({},Ia,t),r=[];let o=n.start?"^":"";const l=[];for(const p of e){const i=p.length?[]:[90];n.strict&&!p.length&&(o+="/");for(let u=0;u<p.length;u++){const d=p[u];let h=40+(n.sensitive?.25:0);if(d.type===0)u||(o+="/"),o+=d.value.replace(Da,"\\$&"),h+=40;else if(d.type===1){const{value:b,repeatable:P,optional:_,regexp:E}=d;l.push({name:b,repeatable:P,optional:_});const k=E||Pr;if(k!==Pr){h+=10;try{new RegExp(`(${k})`)}catch(N){throw new Error(`Invalid custom RegExp for param "${b}" (${k}): `+N.message)}}let D=P?`((?:${k})(?:/(?:${k}))*)`:`(${k})`;u||(D=_&&p.length<2?`(?:/${D})`:"/"+D),_&&(D+="?"),o+=D,h+=20,_&&(h+=-8),P&&(h+=-20),k===".*"&&(h+=-50)}i.push(h)}r.push(i)}if(n.strict&&n.end){const p=r.length-1;r[p][r[p].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const a=new RegExp(o,n.sensitive?"":"i");function s(p){const i=p.match(a),u={};if(!i)return null;for(let d=1;d<i.length;d++){const h=i[d]||"",b=l[d-1];u[b.name]=h&&b.repeatable?h.split("/"):h}return u}function c(p){let i="",u=!1;for(const d of e){(!u||!i.endsWith("/"))&&(i+="/"),u=!1;for(const h of d)if(h.type===0)i+=h.value;else if(h.type===1){const{value:b,repeatable:P,optional:_}=h,E=b in p?p[b]:"";if(Te(E)&&!P)throw new Error(`Provided param "${b}" is an array but it is not repeatable (* or + modifiers)`);const k=Te(E)?E.join("/"):E;if(!k)if(_)d.length<2&&(i.endsWith("/")?i=i.slice(0,-1):u=!0);else throw new Error(`Missing required param "${b}"`);i+=k}}return i||"/"}return{re:a,score:r,keys:l,parse:s,stringify:c}}function Na(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function qo(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const l=Na(r[n],o[n]);if(l)return l;n++}if(Math.abs(o.length-r.length)===1){if(Ir(r))return 1;if(Ir(o))return-1}return o.length-r.length}function Ir(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Aa={type:0,value:""},Ua=/[a-zA-Z0-9_]/;function Ta(e){if(!e)return[[]];if(e==="/")return[[Aa]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${n})/"${p}": ${h}`)}let n=0,r=n;const o=[];let l;function a(){l&&o.push(l),l=[]}let s=0,c,p="",i="";function u(){p&&(n===0?l.push({type:0,value:p}):n===1||n===2||n===3?(l.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${p}) must be alone in its segment. eg: '/:ids+.`),l.push({type:1,value:p,regexp:i,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),p="")}function d(){p+=c}for(;s<e.length;){if(c=e[s++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(p&&u(),a()):c===":"?(u(),n=1):d();break;case 4:d(),n=r;break;case 1:c==="("?n=2:Ua.test(c)?d():(u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--);break;case 2:c===")"?i[i.length-1]=="\\"?i=i.slice(0,-1)+c:n=3:i+=c;break;case 3:u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--,i="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${p}"`),u(),a(),o}function Oa(e,t,n){const r=Ma(Ta(e.path),n),o=ue(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function La(e,t){const n=[],r=new Map;t=Ar({strict:!1,end:!0,sensitive:!1},t);function o(u){return r.get(u)}function l(u,d,h){const b=!h,P=Mr(u);P.aliasOf=h&&h.record;const _=Ar(t,u),E=[P];if("alias"in u){const N=typeof u.alias=="string"?[u.alias]:u.alias;for(const B of N)E.push(Mr(ue({},P,{components:h?h.record.components:P.components,path:B,aliasOf:h?h.record:P})))}let k,D;for(const N of E){const{path:B}=N;if(d&&B[0]!=="/"){const F=d.record.path,U=F[F.length-1]==="/"?"":"/";N.path=d.record.path+(B&&U+B)}if(k=Oa(N,d,_),h?h.alias.push(k):(D=D||k,D!==k&&D.alias.push(k),b&&u.name&&!Nr(k)&&a(u.name)),Yo(k)&&c(k),P.children){const F=P.children;for(let U=0;U<F.length;U++)l(F[U],k,h&&h.children[U])}h=h||k}return D?()=>{a(D)}:Pt}function a(u){if(zo(u)){const d=r.get(u);d&&(r.delete(u),n.splice(n.indexOf(d),1),d.children.forEach(a),d.alias.forEach(a))}else{const d=n.indexOf(u);d>-1&&(n.splice(d,1),u.record.name&&r.delete(u.record.name),u.children.forEach(a),u.alias.forEach(a))}}function s(){return n}function c(u){const d=qa(u,n);n.splice(d,0,u),u.record.name&&!Nr(u)&&r.set(u.record.name,u)}function p(u,d){let h,b={},P,_;if("name"in u&&u.name){if(h=r.get(u.name),!h)throw pt(1,{location:u});_=h.record.name,b=ue(Dr(d.params,h.keys.filter(D=>!D.optional).concat(h.parent?h.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),u.params&&Dr(u.params,h.keys.map(D=>D.name))),P=h.stringify(b)}else if(u.path!=null)P=u.path,h=n.find(D=>D.re.test(P)),h&&(b=h.parse(P),_=h.record.name);else{if(h=d.name?r.get(d.name):n.find(D=>D.re.test(d.path)),!h)throw pt(1,{location:u,currentLocation:d});_=h.record.name,b=ue({},d.params,u.params),P=h.stringify(b)}const E=[];let k=h;for(;k;)E.unshift(k.record),k=k.parent;return{name:_,path:P,params:b,matched:E,meta:Fa(E)}}e.forEach(u=>l(u));function i(){n.length=0,r.clear()}return{addRoute:l,resolve:p,removeRoute:a,clearRoutes:i,getRoutes:s,getRecordMatcher:o}}function Dr(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Mr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:za(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function za(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Nr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Fa(e){return e.reduce((t,n)=>ue(t,n.meta),{})}function Ar(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function qa(e,t){let n=0,r=t.length;for(;n!==r;){const l=n+r>>1;qo(e,t[l])<0?r=l:n=l+1}const o=Ya(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function Ya(e){let t=e;for(;t=t.parent;)if(Yo(t)&&qo(e,t)===0)return t}function Yo({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Ba(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const l=r[o].replace(No," "),a=l.indexOf("="),s=Mt(a<0?l:l.slice(0,a)),c=a<0?null:Mt(l.slice(a+1));if(s in t){let p=t[s];Te(p)||(p=t[s]=[p]),p.push(c)}else t[s]=c}return t}function Ur(e){let t="";for(let n in e){const r=e[n];if(n=ua(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Te(r)?r.map(l=>l&&On(l)):[r&&On(r)]).forEach(l=>{l!==void 0&&(t+=(t.length?"&":"")+n,l!=null&&(t+="="+l))})}return t}function Ha(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Te(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const Xa=Symbol(""),Tr=Symbol(""),Sn=Symbol(""),Bo=Symbol(""),zn=Symbol("");function wt(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ke(e,t,n,r,o,l=a=>a()){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((s,c)=>{const p=d=>{d===!1?c(pt(4,{from:n,to:t})):d instanceof Error?c(d):Pa(d)?c(pt(2,{from:t,to:d})):(a&&r.enterCallbacks[o]===a&&typeof d=="function"&&a.push(d),s())},i=l(()=>e.call(r&&r.instances[o],t,n,p));let u=Promise.resolve(i);e.length<3&&(u=u.then(p)),u.catch(d=>c(d))})}function In(e,t,n,r,o=l=>l()){const l=[];for(const a of e)for(const s in a.components){let c=a.components[s];if(!(t!=="beforeRouteEnter"&&!a.instances[s]))if(Do(c)){const i=(c.__vccOpts||c)[t];i&&l.push(Ke(i,n,r,a,s,o))}else{let p=c();l.push(()=>p.then(i=>{if(!i)throw new Error(`Couldn't resolve component "${s}" at "${a.path}"`);const u=Jl(i)?i.default:i;a.mods[s]=i,a.components[s]=u;const h=(u.__vccOpts||u)[t];return h&&Ke(h,n,r,a,s,o)()}))}}return l}function Or(e){const t=dt(Sn),n=dt(Bo),r=Ce(()=>{const c=de(e.to);return t.resolve(c)}),o=Ce(()=>{const{matched:c}=r.value,{length:p}=c,i=c[p-1],u=n.matched;if(!i||!u.length)return-1;const d=u.findIndex(ft.bind(null,i));if(d>-1)return d;const h=Lr(c[p-2]);return p>1&&Lr(i)===h&&u[u.length-1].path!==h?u.findIndex(ft.bind(null,c[p-2])):d}),l=Ce(()=>o.value>-1&&Za(n.params,r.value.params)),a=Ce(()=>o.value>-1&&o.value===n.matched.length-1&&Oo(n.params,r.value.params));function s(c={}){if(Qa(c)){const p=t[de(e.replace)?"replace":"push"](de(e.to)).catch(Pt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>p),p}return Promise.resolve()}return{route:r,href:Ce(()=>r.value.href),isActive:l,isExactActive:a,navigate:s}}function Ga(e){return e.length===1?e[0]:e}const Ka=Eo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Or,setup(e,{slots:t}){const n=Ue(Or(e)),{options:r}=dt(Sn),o=Ce(()=>({[zr(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[zr(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const l=t.default&&Ga(t.default(n));return e.custom?l:Ro("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},l)}}}),Wa=Ka;function Qa(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Za(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!Te(o)||o.length!==r.length||r.some((l,a)=>l!==o[a]))return!1}return!0}function Lr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const zr=(e,t,n)=>e??t??n,Ja=Eo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=dt(zn),o=Ce(()=>e.route||r.value),l=dt(Tr,0),a=Ce(()=>{let p=de(l);const{matched:i}=o.value;let u;for(;(u=i[p])&&!u.components;)p++;return p}),s=Ce(()=>o.value.matched[a.value]);Vn(Tr,Ce(()=>a.value+1)),Vn(Xa,s),Vn(zn,o);const c=M();return ut(()=>[c.value,s.value,e.name],([p,i,u],[d,h,b])=>{i&&(i.instances[u]=p,h&&h!==i&&p&&p===d&&(i.leaveGuards.size||(i.leaveGuards=h.leaveGuards),i.updateGuards.size||(i.updateGuards=h.updateGuards))),p&&i&&(!h||!ft(i,h)||!d)&&(i.enterCallbacks[u]||[]).forEach(P=>P(p))},{flush:"post"}),()=>{const p=o.value,i=e.name,u=s.value,d=u&&u.components[i];if(!d)return Fr(n.default,{Component:d,route:p});const h=u.props[i],b=h?h===!0?p.params:typeof h=="function"?h(p):h:null,_=Ro(d,ue({},b,t,{onVnodeUnmounted:E=>{E.component.isUnmounted&&(u.instances[i]=null)},ref:c}));return Fr(n.default,{Component:_,route:p})||_}}});function Fr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ja=Ja;function ei(e){const t=La(e.routes,e),n=e.parseQuery||Ba,r=e.stringifyQuery||Ur,o=e.history,l=wt(),a=wt(),s=wt(),c=zl(Ge);let p=Ge;at&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const i=$n.bind(null,$=>""+$),u=$n.bind(null,da),d=$n.bind(null,Mt);function h($,G){let Y,x;return zo($)?(Y=t.getRecordMatcher($),x=G):x=$,t.addRoute(x,Y)}function b($){const G=t.getRecordMatcher($);G&&t.removeRoute(G)}function P(){return t.getRoutes().map($=>$.record)}function _($){return!!t.getRecordMatcher($)}function E($,G){if(G=ue({},G||c.value),typeof $=="string"){const re=Pn(n,$,G.path),g=t.resolve({path:re.path},G),S=o.createHref(re.fullPath);return ue(re,g,{params:d(g.params),hash:Mt(re.hash),redirectedFrom:void 0,href:S})}let Y;if($.path!=null)Y=ue({},$,{path:Pn(n,$.path,G.path).path});else{const re=ue({},$.params);for(const g in re)re[g]==null&&delete re[g];Y=ue({},$,{params:u(re)}),G.params=u(G.params)}const x=t.resolve(Y,G),ie=$.hash||"";x.params=i(d(x.params));const ge=ha(r,ue({},$,{hash:sa(ie),path:x.path})),oe=o.createHref(ge);return ue({fullPath:ge,hash:ie,query:r===Ur?Ha($.query):$.query||{}},x,{redirectedFrom:void 0,href:oe})}function k($){return typeof $=="string"?Pn(n,$,c.value.path):ue({},$)}function D($,G){if(p!==$)return pt(8,{from:G,to:$})}function N($){return U($)}function B($){return N(ue(k($),{replace:!0}))}function F($){const G=$.matched[$.matched.length-1];if(G&&G.redirect){const{redirect:Y}=G;let x=typeof Y=="function"?Y($):Y;return typeof x=="string"&&(x=x.includes("?")||x.includes("#")?x=k(x):{path:x},x.params={}),ue({query:$.query,hash:$.hash,params:x.path!=null?{}:$.params},x)}}function U($,G){const Y=p=E($),x=c.value,ie=$.state,ge=$.force,oe=$.replace===!0,re=F(Y);if(re)return U(ue(k(re),{state:typeof re=="object"?ue({},ie,re.state):ie,force:ge,replace:oe}),G||Y);const g=Y;g.redirectedFrom=G;let S;return!ge&&ma(r,x,Y)&&(S=pt(16,{to:g,from:x}),L(x,x,!0,!1)),(S?Promise.resolve(S):m(g,x)).catch(ee=>Ye(ee)?Ye(ee,2)?ee:q(ee):w(ee,g,x)).then(ee=>{if(ee){if(Ye(ee,2))return U(ue({replace:oe},k(ee.to),{state:typeof ee.to=="object"?ue({},ie,ee.to.state):ie,force:ge}),G||g)}else ee=A(g,x,!0,oe,ie);return y(g,x,ee),ee})}function O($,G){const Y=D($,G);return Y?Promise.reject(Y):Promise.resolve()}function T($){const G=we.values().next().value;return G&&typeof G.runWithContext=="function"?G.runWithContext($):$()}function m($,G){let Y;const[x,ie,ge]=ti($,G);Y=In(x.reverse(),"beforeRouteLeave",$,G);for(const re of x)re.leaveGuards.forEach(g=>{Y.push(Ke(g,$,G))});const oe=O.bind(null,$,G);return Y.push(oe),ce(Y).then(()=>{Y=[];for(const re of l.list())Y.push(Ke(re,$,G));return Y.push(oe),ce(Y)}).then(()=>{Y=In(ie,"beforeRouteUpdate",$,G);for(const re of ie)re.updateGuards.forEach(g=>{Y.push(Ke(g,$,G))});return Y.push(oe),ce(Y)}).then(()=>{Y=[];for(const re of ge)if(re.beforeEnter)if(Te(re.beforeEnter))for(const g of re.beforeEnter)Y.push(Ke(g,$,G));else Y.push(Ke(re.beforeEnter,$,G));return Y.push(oe),ce(Y)}).then(()=>($.matched.forEach(re=>re.enterCallbacks={}),Y=In(ge,"beforeRouteEnter",$,G,T),Y.push(oe),ce(Y))).then(()=>{Y=[];for(const re of a.list())Y.push(Ke(re,$,G));return Y.push(oe),ce(Y)}).catch(re=>Ye(re,8)?re:Promise.reject(re))}function y($,G,Y){s.list().forEach(x=>T(()=>x($,G,Y)))}function A($,G,Y,x,ie){const ge=D($,G);if(ge)return ge;const oe=G===Ge,re=at?history.state:{};Y&&(x||oe?o.replace($.fullPath,ue({scroll:oe&&re&&re.scroll},ie)):o.push($.fullPath,ie)),c.value=$,L($,G,Y,oe),q()}let K;function Q(){K||(K=o.listen(($,G,Y)=>{if(!Z.listening)return;const x=E($),ie=F(x);if(ie){U(ue(ie,{replace:!0,force:!0}),x).catch(Pt);return}p=x;const ge=c.value;at&&Sa(Rr(ge.fullPath,Y.delta),xn()),m(x,ge).catch(oe=>Ye(oe,12)?oe:Ye(oe,2)?(U(ue(k(oe.to),{force:!0}),x).then(re=>{Ye(re,20)&&!Y.delta&&Y.type===Nt.pop&&o.go(-1,!1)}).catch(Pt),Promise.reject()):(Y.delta&&o.go(-Y.delta,!1),w(oe,x,ge))).then(oe=>{oe=oe||A(x,ge,!1),oe&&(Y.delta&&!Ye(oe,8)?o.go(-Y.delta,!1):Y.type===Nt.pop&&Ye(oe,20)&&o.go(-1,!1)),y(x,ge,oe)}).catch(Pt)}))}let C=wt(),X=wt(),V;function w($,G,Y){q($);const x=X.list();return x.length?x.forEach(ie=>ie($,G,Y)):console.error($),Promise.reject($)}function R(){return V&&c.value!==Ge?Promise.resolve():new Promise(($,G)=>{C.add([$,G])})}function q($){return V||(V=!$,Q(),C.list().forEach(([G,Y])=>$?Y($):G()),C.reset()),$}function L($,G,Y,x){const{scrollBehavior:ie}=e;if(!at||!ie)return Promise.resolve();const ge=!Y&&ka(Rr($.fullPath,0))||(x||!Y)&&history.state&&history.state.scroll||null;return on().then(()=>ie($,G,ge)).then(oe=>oe&&xa(oe)).catch(oe=>w(oe,$,G))}const j=$=>o.go($);let se;const we=new Set,Z={currentRoute:c,listening:!0,addRoute:h,removeRoute:b,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:P,resolve:E,options:e,push:N,replace:B,go:j,back:()=>j(-1),forward:()=>j(1),beforeEach:l.add,beforeResolve:a.add,afterEach:s.add,onError:X.add,isReady:R,install($){const G=this;$.component("RouterLink",Wa),$.component("RouterView",ja),$.config.globalProperties.$router=G,Object.defineProperty($.config.globalProperties,"$route",{enumerable:!0,get:()=>de(c)}),at&&!se&&c.value===Ge&&(se=!0,N(o.location).catch(ie=>{}));const Y={};for(const ie in Ge)Object.defineProperty(Y,ie,{get:()=>c.value[ie],enumerable:!0});$.provide(Sn,G),$.provide(Bo,Fl(Y)),$.provide(zn,c);const x=$.unmount;we.add($),$.unmount=function(){we.delete($),we.size<1&&(p=Ge,K&&K(),K=null,c.value=Ge,se=!1,V=!1),x()}}};function ce($){return $.reduce((G,Y)=>G.then(()=>T(Y)),Promise.resolve())}return Z}function ti(e,t){const n=[],r=[],o=[],l=Math.max(t.matched.length,e.matched.length);for(let a=0;a<l;a++){const s=t.matched[a];s&&(e.matched.find(p=>ft(p,s))?r.push(s):n.push(s));const c=e.matched[a];c&&(t.matched.find(p=>ft(p,c))||o.push(c))}return[n,r,o]}function ni(){return dt(Sn)}const ri={class:"app-container"},oi={class:"grid-container"},li={class:"header"},ai={class:"header-left"},ii={class:"user-info"},si={class:"el-dropdown-link"},ui={class:"user-name"},ci={class:"sidebar"},di={class:"content",ref:"contentRef"},fi={__name:"App",setup(e){const t=ni(),n=M(""),r=M([]),o=M(!1),l=M(""),a=M(null);Qe(async()=>{await s(),await c(),t.push("/unit-management")});const s=async()=>{const F=await te.post("/api/get_user_info.php");n.value=F.user.name},c=async()=>{const F=await te.post("/api/get_user_app.php");r.value=F.data},p=F=>{console.log("点击的菜单对应的路由是:",F),t.push(F)},i=()=>{o.value=!o.value},u=M(!1),d=M({oldPassword:"",newPassword:"",confirmPassword:""}),h=M(null),b=M(!1),P=M(!1),_=()=>{b.value=!b.value},E=()=>{P.value=!P.value},k=async F=>{F==="logout"?(await te.get("/api/logout.php"),window.location.href="login.html"):F==="changePassword"&&(u.value=!0)},D=async()=>{h.value&&await h.value.validate(F=>{if(F){if(d.value.newPassword!==d.value.confirmPassword){H.error("两次输入的密码不一致");return}const U=new FormData;U.append("old_password",d.value.oldPassword),U.append("new_password",d.value.newPassword),te.post("/api/change_password.php",U).then(()=>{H.success("密码修改成功，请重新登录"),u.value=!1,d.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{H.error("密码修改失败")})}})},N=Ue({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(F,U,O)=>{U!==d.value.newPassword?O(new Error("两次输入的密码不一致")):O()},trigger:"blur"}]}),B=()=>{const F=a.value;F&&(document.fullscreenElement?document.exitFullscreen():F.requestFullscreen().catch(U=>{console.error("全屏失败:",U),H.error("全屏功能不支持")}))};return(F,U)=>{const O=z("el-button"),T=z("el-icon"),m=z("el-avatar"),y=z("el-dropdown-item"),A=z("el-dropdown-menu"),K=z("el-dropdown"),Q=z("el-menu-item"),C=z("el-menu"),X=z("router-view"),V=z("el-input"),w=z("el-form-item"),R=z("el-form"),q=z("el-dialog");return J(),le(_e,null,[W("div",ri,[W("div",oi,[W("div",li,[W("div",ai,[f(O,{onClick:i,type:"text",class:"menu-btn"},{default:v(()=>U[5]||(U[5]=[W("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),U[6]||(U[6]=W("img",{src:ql,alt:"Logo",class:"header-logo"},null,-1)),U[7]||(U[7]=W("span",{class:"logo"},"CloudPivot",-1))])]),W("div",ii,[f(K,{onCommand:k},{dropdown:v(()=>[f(A,null,{default:v(()=>[f(y,{command:"profile"},{default:v(()=>U[8]||(U[8]=[ne("个人信息")])),_:1,__:[8]}),f(y,{command:"changePassword"},{default:v(()=>U[9]||(U[9]=[ne("修改密码")])),_:1,__:[9]}),f(y,{command:"logout"},{default:v(()=>U[10]||(U[10]=[ne("退出登录")])),_:1,__:[10]})]),_:1})]),default:v(()=>[W("span",si,[f(m,{size:"small"},{default:v(()=>[f(T,null,{default:v(()=>[f(de(Yl),{style:{color:"#409EFF"}})]),_:1})]),_:1}),W("span",ui,Re(n.value),1)])]),_:1})]),W("div",ci,[f(C,{"default-active":l.value,class:"el-menu-vertical",mode:"vertical",collapse:o.value,onOpen:F.handleOpen,onClose:F.handleClose},{default:v(()=>[(J(!0),le(_e,null,ye(r.value,L=>(J(),he(Q,{key:L.id,index:L.id.toString(),router:L.url,onClick:j=>p(L.url)},{title:v(()=>[W("span",null,Re(L.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),W("div",di,[f(O,{onClick:B,type:"text",class:"fullscreen-btn"},{default:v(()=>[f(T,null,{default:v(()=>[f(de(Bl))]),_:1})]),_:1}),W("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:a},[f(X,{ref:"routerViewRef"},null,512)],512)],512)])]),f(q,{modelValue:u.value,"onUpdate:modelValue":U[4]||(U[4]=L=>u.value=L),width:"400px"},{default:v(()=>[f(R,{model:d.value,ref_key:"passwordFormRef",ref:h,rules:N,"label-width":"120px",onSubmit:Vo(D,["prevent"])},{default:v(()=>[f(w,{label:"旧密码",prop:"oldPassword",required:""},{default:v(()=>[f(V,{modelValue:d.value.oldPassword,"onUpdate:modelValue":U[0]||(U[0]=L=>d.value.oldPassword=L),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),f(w,{label:"新密码",prop:"newPassword",required:""},{default:v(()=>[f(V,{modelValue:d.value.newPassword,"onUpdate:modelValue":U[1]||(U[1]=L=>d.value.newPassword=L),type:b.value?"text":"password",placeholder:"请输入新密码"},{suffix:v(()=>[f(O,{icon:b.value?de(Sr):de(kr),onClick:_,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),f(w,{label:"确认新密码",prop:"confirmPassword",required:""},{default:v(()=>[f(V,{modelValue:d.value.confirmPassword,"onUpdate:modelValue":U[2]||(U[2]=L=>d.value.confirmPassword=L),type:P.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:v(()=>[f(O,{icon:P.value?de(Sr):de(kr),onClick:E,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),f(w,null,{default:v(()=>[f(O,{type:"primary","native-type":"submit"},{default:v(()=>U[11]||(U[11]=[ne("确定")])),_:1,__:[11]}),f(O,{onClick:U[3]||(U[3]=L=>u.value=!1)},{default:v(()=>U[12]||(U[12]=[ne("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},pi=rt(fi,[["__scopeId","data-v-873a0a85"]]),hi={class:"unit-management"},mi={style:{"text-align":"right",margin:"10px"}},_i={__name:"UnitManagement",setup(e){const t=M(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=m=>{if(m&&m.length>0){const y=m[m.length-1],A=l(y);A&&(o.parentId=y,u.value=A.children||[])}else o.parentId=null,u.value=[]},o=Ue({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:0});ut(()=>o.parentId,(m,y)=>{if(console.log("parentId 发生变化，旧值: ",y,"新值: ",m),m){const A=l(m);A&&(u.value=A.children||[])}else u.value=[]},{immediate:!1});const l=(m,y=a.value)=>{for(let A=0;A<y.length;A++){if(y[A].id===m)return y[A];if(y[A].children){const K=l(m,y[A].children);if(K)return K}}return null},a=M([]),s=M(new Set),c=Ce(()=>{const m=[],y=(A,K=0,Q=null)=>{A.forEach(C=>{m.push({...C,level:K,parentId:Q,expanded:s.value.has(C.id)}),C.children&&y(C.children,K+1,C.id)})};return y(a.value),m}),p=Ce(()=>{const m=[],y=A=>{if(A.level===0)return!0;let K=c.value.find(Q=>Q.id===A.parentId);for(;K;){if(!K.expanded)return!1;K=c.value.find(Q=>Q.id===K.parentId)}return!0};return c.value.forEach(A=>{y(A)&&m.push(A)}),m}),i=M(!1),u=M([]),d=M(null);Qe(async()=>{await k()});const h=M(!1),b=m=>{const y=[];function A(K,Q){for(const C of K){const X=[...Q,C.id];if(C.id===m)return y.push(...X),!0;if(C.children&&C.children.length>0&&A(C.children,X))return!0}return!1}return A(a.value,[]),y},P=m=>{if(d.value&&d.value.resetFields(),t.value=!1,o.id=null,o.name="",o.code="",o.parentId=null,o.parentIdPath=[],o.sort_order=0,h.value=!!m,m){const y=l(m);y&&(o.parentId=m,console.log("parentId位置一:",m),o.parentIdPath=b(m),u.value=y.children||[])}else o.parentId=null,o.parentIdPath=[],u.value=[];i.value=!0,console.log("dialogVisible 已设为 true")},_=m=>{d.value&&d.value.resetFields(),t.value=!0,o.id=m.id;let y={currentUnit:m.unit_name,selectedSortOrder:null,selectedUnit:null};o.name=m.unit_name,o.code=m.code,o.parentId=m.parent_id,o.parentIdPath=b(m.parent_id);const A=l(m.parent_id);if(A){u.value=(A.children||[]).filter(C=>C.id!==m.id);const K=A.children||[],Q=K.findIndex(C=>C.id===m.id);if(Q>0){const C=K[Q-1];o.sort_order=C.sort_order,y.selectedUnit=C.unit_name}else o.sort_order=0,y.selectedUnit="最前";y.selectedSortOrder=o.sort_order}else{u.value=a.value.filter(Q=>Q.id!==m.id);const K=a.value.findIndex(Q=>Q.id===m.id);if(K>0){const Q=a.value[K-1];o.sort_order=Q.sort_order,y.selectedUnit=Q.unit_name}else o.sort_order=0,y.selectedUnit="最前";y.selectedSortOrder=o.sort_order}console.log("排序选项:",u.value),console.log("自动选择结果:",y),i.value=!0},E=()=>{d.value.validate(async m=>{if(m)try{const y=new FormData;o.id?(y.append("action","edit"),y.append("id",o.id)):y.append("action","add"),y.append("sort_order",o.sort_order+1),y.append("unit_name",o.name),y.append("code",o.code),y.append("parent_id",o.parentId||null);const A=await te.post("api/unit_manage.php",y,{headers:{"Content-Type":"multipart/form-data"}});A.status===1?(await k(),i.value=!1,H.success(o.id?"编辑成功":"新增成功")):H.error(A.message)}catch(y){console.error("保存单位失败:",y),H.error("保存单位失败，请稍后重试")}})},k=async()=>{const m=await te.post("api/get_unit_info.php");a.value=[m.data],console.log("获取单位数据成功:",a.value);const y=A=>{A.forEach(K=>{K.children&&K.children.length>0&&(s.value.add(K.id),y(K.children))})};y(a.value)},D=m=>{Dt.confirm(`确定要删除 ${m.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const y=new FormData;y.append("action","del"),y.append("id",m.id),await te.post("api/unit_manage.php",y),H.success("删除成功"),await k()})},N=m=>{const y=new Set(s.value);y.has(m.id)?y.delete(m.id):y.add(m.id),s.value=y},B=m=>((m.parentId?l(m.parentId):{children:a.value}).children||[]).findIndex(Q=>Q.id===m.id)===0,F=m=>{const A=(m.parentId?l(m.parentId):{children:a.value}).children||[];return A.findIndex(Q=>Q.id===m.id)===A.length-1},U=async m=>{const y=new FormData;y.append("action","edit"),y.append("id",m.id),y.append("sort_order",m.sort_order-1),y.append("unit_name",m.unit_name),y.append("code",m.code),y.append("parent_id",m.parentId),await te.post("api/unit_manage.php",y),await k()},O=async m=>{const y=new FormData;y.append("action","edit"),y.append("id",m.id),y.append("sort_order",m.sort_order+1),y.append("unit_name",m.unit_name),y.append("code",m.code),y.append("parent_id",m.parentId),await te.post("api/unit_manage.php",y),await k()},T=Ce(()=>u.value.filter(m=>m.id!==o.id));return(m,y)=>{const A=z("el-button"),K=z("el-col"),Q=z("el-row"),C=z("el-table-column"),X=z("el-icon"),V=z("el-button-group"),w=z("el-table"),R=z("el-input"),q=z("el-form-item"),L=z("el-cascader"),j=z("el-option"),se=z("el-select"),we=z("el-form");return J(),le("div",hi,[f(Q,null,{default:v(()=>[f(K,{span:24},{default:v(()=>[W("div",mi,[f(A,{type:"primary",round:"",onClick:y[0]||(y[0]=Z=>P(null))},{default:v(()=>y[7]||(y[7]=[ne("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),f(w,{data:p.value,style:{width:"100%",height:"calc(100vh - 200px)","overflow-y":"auto"},border:""},{default:v(()=>[f(C,{label:"操作",width:"60",align:"center"},{default:v(Z=>[Z.row.children&&Z.row.children.length?(J(),he(A,{key:0,size:"mini",type:"text",onClick:ce=>N(Z.row)},{default:v(()=>[ne(Re(Z.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):$t("",!0)]),_:1}),f(C,{prop:"unit_name",label:"单位名称"},{default:v(Z=>[W("span",{style:ur({paddingLeft:`${Z.row.level*20}px`})},Re(Z.row.unit_name),5)]),_:1}),f(C,{prop:"code",label:"组织机构代码",width:"250"}),f(C,{label:"操作",width:"350"},{default:v(Z=>[f(V,null,{default:v(()=>[f(A,{size:"mini",type:"primary",onClick:ce=>P(Z.row.id)},{default:v(()=>[f(X,null,{default:v(()=>[f(de($o))]),_:1})]),_:2},1032,["onClick"]),f(A,{size:"mini",type:"warning",onClick:ce=>_(Z.row)},{default:v(()=>[f(X,null,{default:v(()=>[f(de(wn))]),_:1})]),_:2},1032,["onClick"]),f(A,{size:"mini",type:"danger",onClick:ce=>D(Z.row)},{default:v(()=>[f(X,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"]),f(A,{size:"mini",type:"info",onClick:ce=>U(Z.row),disabled:B(Z.row)},{default:v(()=>[f(X,null,{default:v(()=>[f(de(Po))]),_:1})]),_:2},1032,["onClick","disabled"]),f(A,{size:"mini",type:"info",onClick:ce=>O(Z.row),disabled:F(Z.row)},{default:v(()=>[f(X,null,{default:v(()=>[f(de(Io))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),f(de(Hl),{modelValue:i.value,"onUpdate:modelValue":y[6]||(y[6]=Z=>i.value=Z),title:"",width:"450px","close-on-click-modal":!1},{footer:v(()=>[f(A,{onClick:y[5]||(y[5]=Z=>i.value=!1)},{default:v(()=>y[8]||(y[8]=[ne("取消")])),_:1,__:[8]}),f(A,{type:"primary",onClick:E},{default:v(()=>y[9]||(y[9]=[ne("确定")])),_:1,__:[9]})]),default:v(()=>[f(we,{model:o,ref_key:"formRef",ref:d,"label-width":"130px"},{default:v(()=>[f(q,{label:"单位名称",prop:"name",required:""},{default:v(()=>[f(R,{modelValue:o.name,"onUpdate:modelValue":y[1]||(y[1]=Z=>o.name=Z),required:""},null,8,["modelValue"])]),_:1}),f(q,{label:"组织机构代码",prop:"code"},{default:v(()=>[f(R,{modelValue:o.code,"onUpdate:modelValue":y[2]||(y[2]=Z=>o.code=Z)},null,8,["modelValue"])]),_:1}),f(q,{label:"上级单位",prop:"parentId"},{default:v(()=>[f(L,{modelValue:o.parentIdPath,"onUpdate:modelValue":y[3]||(y[3]=Z=>o.parentIdPath=Z),options:a.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),f(q,{label:"排序",prop:"sort",required:""},{default:v(()=>[f(se,{modelValue:o.sort_order,"onUpdate:modelValue":y[4]||(y[4]=Z=>o.sort_order=Z),placeholder:"请选择排序位置"},{default:v(()=>[f(j,{label:"置于 最前",value:0}),(J(!0),le(_e,null,ye(T.value,Z=>(J(),he(j,{key:Z.id,label:`置于 ${Z.unit_name} 之后`,value:Z.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},vi=rt(_i,[["__scopeId","data-v-bcca994a"]]),gi={class:"user-management-container"},yi={class:"left-panel"},wi=["onClick"],bi={class:"right-panel"},xi={class:"action-buttons",style:{display:"flex","align-items":"center"}},Si={class:"left-buttons",style:{display:"flex","align-items":"center",gap:"8px"}},ki={class:"right-switches",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},Ci={class:"form-row"},Ei={class:"form-row"},Ri={class:"form-row"},Vi={class:"form-row"},$i={class:"form-row"},Pi={class:"form-row"},Ii={class:"form-row"},Di={class:"form-row"},Mi={class:"dialog-footer"},Ni={__name:"UserManagement",setup(e){const t=M([]),n=M([]),r=M([]),o=M(null);M("");const l=M([]),a=M([]),s=Ce(()=>{if(!o.value)return"";const g=d.value.find(S=>S.id===o.value);return console.log("当前选中的部门ID:",o.value),console.log("当前选中的部门:",g.unit_name),g?g.unit_name:""});M(null);const c=M(1),p=M(10),i=M(0),u=M([]),d=M([]),h=M(""),b=M([]),P=M([]),_=M([]),E=M([]),k=M(!1),D=M(""),N=M(!1),B=M({});M(!1);const F=M(!1),U=M(null),O=()=>{N.value?k.value=!1:G()},T=Ce(()=>h.value?d.value.filter(g=>g.show&&g.unit_name.toLowerCase().includes(h.value.toLowerCase())):d.value.filter(g=>g.show)),m=Ue({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sort_order:null,desc:""}),y={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},A=M(null),K=async()=>{try{const g=new FormData;g.append("type","identity");const S=await te({url:"/api/person_info_api.php",method:"post",data:g});S.status===1?t.value=S.data.map(ee=>({label:ee,value:ee})):H.error("获取人员身份数据失败："+S.message)}catch{H.error("网络请求失败")}},Q=async()=>{try{const g=new FormData;g.append("type","status");const S=await te({url:"/api/person_info_api.php",method:"post",data:g});S.status===1?n.value=S.data.map(ee=>({label:ee,value:ee})):H.error("获取人员状态数据失败："+S.message)}catch{H.error("网络请求失败")}},C=async()=>{try{const g=new FormData;g.append("type","rank");const S=await te({url:"/api/person_info_api.php",method:"post",data:g});S.status===1?r.value=S.data.map(ee=>({label:ee,value:ee})):H.error("获取职级数据失败："+S.message)}catch{H.error("网络请求失败")}};Qe(async()=>{try{const g=await te.post("api/get_unit_info.php");u.value=[g.data],V(g.data),b.value=u.value,P.value=u.value,console.log("获取部门数据成功:",u.value),await X(),K(),Q(),C()}catch(g){console.error("获取数据失败:",g),H.error("获取数据失败，请稍后重试")}});const X=async()=>{console.log("开始获取用户数据...");try{const g=new FormData;g.append("controlCode","query"),g.append("page",c.value),g.append("pagesize",p.value),o.value&&g.append("organization_unit",o.value),q.value?g.append("isShowInPersonnel","1"):g.append("isShowInPersonnel","0"),R.value?g.append("isShowOutPersonnel","1"):g.append("isShowOutPersonnel","0");const S=await te.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});_.value=S.data,console.log("获取用户数据成功:",_.value),i.value=S.total}catch(g){console.error("获取用户数据失败:",g),H.error("获取用户数据失败，请稍后重试")}},V=(g,S=0,ee=null)=>{const me={...g,level:S,expanded:g.children&&g.children.length>0,parent:ee,indent:S*20,show:!0};d.value.push(me),g.children&&g.children.length>0&&g.children.forEach(be=>{V(be,S+1,me)})};console.log("扁平化后的部门列表:",d.value);const w=g=>{g.expanded=!g.expanded;const S=d.value.indexOf(g)+1;let ee=g.level+1;for(let me=S;me<d.value.length;me++){const be=d.value[me];if(be.level<=g.level)break;be.level===ee?be.show=g.expanded:be.level>ee&&(be.show=g.expanded&&d.value[me-1].show)}},R=M(!0),q=M(!0),L=()=>{k.value=!0,A.value.resetFields()},j=async()=>{if(E.value.length===0){H.warning("请先选择要删除的用户");return}try{await Dt.confirm(`确定要删除选中的 ${E.value.length} 个用户吗？删除后数据不可恢复`,"批量删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const g=new FormData;g.append("controlCode","del");const S=E.value.map(ee=>ee.id);g.append("id",S.join(",")),await te.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}}),H.success(`成功删除 ${E.value.length} 个用户`),await X(),E.value=[]}catch(g){g!=="cancel"&&H.error(`删除失败：${g.message||"未知错误"}`)}},se=async g=>{A.value&&A.value.resetFields(),F.value=!0,U.value=g.id,k.value=!0,D.value="编辑用户信息",N.value=!1,m.name=g.name,m.id_number=g.id_number,m.phone=g.phone,m.archive_birthdate=g.archive_birthdate,m.gender=g.gender,console.log("性别值:",g.gender),m.short_code=g.short_code,m.alt_phone_1=g.alt_phone_1,m.alt_phone_2=g.alt_phone_2,m.landline=g.landline,m.organization_unit=g.organization_unit,m.work_unit=g.work_unit,m.employment_date=g.employment_date,m.political_status=g.political_status,m.party_join_date=g.party_join_date,m.personnel_type=g.personnel_type,m.police_number=g.police_number,m.is_assisting_officer=g.is_assisting_officer,m.employment_status=g.employment_status,m.job_rank=g.job_rank,m.current_rank_date=g.current_rank_date,m.position=g.position,m.current_position_date=g.current_position_date,await oe(g.organization_unit),m.sort_order=g.sort_order,console.log("排序值:",g.sort_order),m.desc=g.desc},we=g=>{D.value="查看用户详情",B.value={...g},N.value=!0,k.value=!0},Z=async g=>{try{await Dt.confirm(`确定要删除用户 ${g.name} 吗？删除后数据不可恢复`,"删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const S=new FormData;S.append("controlCode","del"),S.append("id",g.id),await te.post("api/user_manage.php",S,{headers:{"Content-Type":"multipart/form-data"}}),H.success("用户删除成功"),await X()}catch(S){S!=="cancel"&&H.error(`删除失败：${S.message||"未知错误"}`)}},ce=g=>{E.value=g},$=async g=>{console.log("点击的部门名称:",g),o.value=g.id,await X()},G=async()=>{try{await A.value.validate();const g=new FormData;F.value?(g.append("controlCode","modify"),g.append("id",U.value)):g.append("controlCode","add"),g.append("name",m.name),g.append("id_number",m.id_number||""),g.append("phone",m.phone),g.append("archive_birthdate",m.archive_birthdate||""),g.append("gender",m.gender||""),g.append("short_code",m.short_code||""),g.append("alt_phone_1",m.alt_phone_1||""),g.append("alt_phone_2",m.alt_phone_2||""),g.append("landline",m.landline||"");const S=Array.isArray(m.organization_unit)?m.organization_unit[m.organization_unit.length-1]:m.organization_unit;g.append("organization_unit",S||"");const ee=Array.isArray(m.work_unit)?m.work_unit[m.work_unit.length-1]:m.work_unit;g.append("work_unit",ee||S),g.append("employment_date",m.employment_date||""),g.append("political_status",m.political_status||""),g.append("party_join_date",m.party_join_date||""),g.append("personnel_type",m.personnel_type||""),g.append("police_number",m.police_number||""),g.append("assisting_officer",m.is_assisting_officer||""),g.append("employment_status",m.employment_status||""),g.append("job_rank",m.job_rank||""),g.append("current_rank_date",m.current_rank_date||""),g.append("position",m.position||""),g.append("current_position_date",m.current_position_date||""),g.append("sort_order",m.sort_order+1),g.append("desc",m.desc||""),await te.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});const me=F.value?"编辑":"添加";H.success(`用户${me}成功`),k.value=!1,F.value=!1,U.value=null,await X(),A.value.resetFields()}catch(g){H.error("提交失败："+(g.message||"未知错误"))}},Y=Ce(()=>{const g=new Map;return d.value.forEach(S=>{g.set(S.id,S.unit_name)}),g}),x=g=>(console.log("单位ID:",g),console.log("组织映射:",Y.value),console.log("单位名称:",Y.value.get(g)),Y.value.get(g)||"未知单位"),ie=async g=>{try{const S=new FormData;S.append("controlCode","modify"),S.append("id",g.id);const ee=g.sort_order-1;S.append("sort_order",ee),await te.post("api/user_manage.php",S),await X(),H.success("上移成功")}catch{H.error("上移失败，请稍后重试")}},ge=async g=>{try{const S=new FormData;S.append("controlCode","modify"),S.append("id",g.id);const ee=g.sort_order+1;S.append("sort_order",ee),await te.post("api/user_manage.php",S),await X(),H.success("下移成功")}catch{H.error("下移失败，请稍后重试")}};ut(q,async g=>{c.value=1,await X()}),ut(R,async g=>{c.value=1,await X()});const oe=async g=>{if(!g){a.value=[];return}try{const S=new FormData;S.append("controlCode","getSortOptions"),S.append("organization_unit",g);const ee=await te.post("/api/user_manage.php",S,{headers:{"Content-Type":"multipart/form-data"}});a.value=ee.data,console.log("获取排序选项成功:",a.value)}catch(S){console.error("获取排序选项失败:",S),H.error("获取排序选项失败，请稍后重试"),a.value=[]}},re=async g=>{if(!g){l.value=[];return}try{const S=new FormData;S.append("controlCode","getPspInfo"),S.append("organization_unit",g);const ee=await te({url:"/api/user_manage.php",method:"post",data:S});ee.status===1?l.value=ee.data.map(me=>({label:me.name,value:me.id})):H.error("获取带辅民警数据失败")}catch{H.error("网络请求失败")}};return ut(()=>m.organization_unit,async g=>{const S=Array.isArray(g)?g[g.length-1]:g;await oe(S),await re(S)}),(g,S)=>{const ee=z("el-input"),me=z("el-button"),be=z("el-table-column"),wr=z("el-table"),Al=z("el-tag"),br=z("el-switch"),gt=z("el-icon"),Ul=z("el-button-group"),Tl=z("el-pagination"),pe=z("el-form-item"),yt=z("el-date-picker"),Ee=z("el-option"),Ze=z("el-select"),xr=z("el-cascader"),Ol=z("el-form"),Ll=z("el-dialog");return J(),le("div",gi,[W("div",yi,[f(ee,{modelValue:h.value,"onUpdate:modelValue":S[0]||(S[0]=I=>h.value=I),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),f(wr,{data:T.value,border:"",class:"department-table"},{default:v(()=>[f(be,{label:"操作",width:"55"},{default:v(({row:I})=>[I.children&&I.children.length>0?(J(),he(me,{key:0,type:"text",size:"small",onClick:Vo(lt=>w(I),["stop"])},{default:v(()=>[ne(Re(I.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):$t("",!0)]),_:1}),f(be,{prop:"unit_name",label:"单位名称"},{default:v(({row:I})=>[W("span",{class:"indent",style:ur({width:`${I.indent}px`})},null,4),W("span",{onClick:lt=>$(I),style:{cursor:"pointer"}},Re(I.unit_name),9,wi)]),_:1})]),_:1},8,["data"])]),W("div",bi,[W("div",xi,[W("div",Si,[f(me,{type:"primary",onClick:L},{default:v(()=>S[31]||(S[31]=[ne("新增")])),_:1,__:[31]}),f(me,{type:"danger",onClick:j},{default:v(()=>S[32]||(S[32]=[ne("删除")])),_:1,__:[32]})]),W("div",ki,[s.value?(J(),he(Al,{key:0,type:"info",style:{"margin-left":"10px"}},{default:v(()=>[ne(" 当前单位："+Re(s.value),1)]),_:1})):$t("",!0),f(br,{modelValue:R.value,"onUpdate:modelValue":S[1]||(S[1]=I=>R.value=I),"inline-prompt":"","active-text":"显示抽出人员","inactive-text":"不显示抽出人员",style:{"margin-left":"10px"}},null,8,["modelValue"]),f(br,{modelValue:q.value,"onUpdate:modelValue":S[2]||(S[2]=I=>q.value=I),"active-text":"显示抽入人员","inactive-text":"显示抽入人员","inline-prompt":"",style:{"margin-left":"10px"}},null,8,["modelValue"])])]),f(wr,{data:_.value,border:"",class:"user-table",onSelectionChange:ce},{default:v(()=>[f(be,{type:"selection",width:"55"}),f(be,{prop:"name",label:"姓名",width:"70"}),f(be,{prop:"id_number",label:"身份证号",width:"100"}),f(be,{prop:"phone",label:"手机号码",width:"115"}),f(be,{label:"性别",width:"55"},{default:v(I=>[ne(Re(I.row.gender===1?"男":I.row.gender===2?"女":"未知"),1)]),_:1}),f(be,{label:"编制单位"},{default:v(I=>[ne(Re(x(I.row.organization_unit)),1)]),_:1}),f(be,{label:"工作单位"},{default:v(I=>[ne(Re(x(I.row.work_unit)),1)]),_:1}),f(be,{prop:"personnel_type",label:"人员身份"}),f(be,{prop:"employment_status",label:"人员状态"}),f(be,{prop:"desc",label:"备注"}),f(be,{label:"操作",width:"260",align:"center"},{default:v(({row:I})=>[f(Ul,null,{default:v(()=>[f(me,{type:"warning",size:"mini",onClick:lt=>se(I)},{default:v(()=>[f(gt,null,{default:v(()=>[f(de(wn))]),_:1})]),_:2},1032,["onClick"]),f(me,{type:"success",size:"mini",onClick:lt=>we(I)},{default:v(()=>[f(gt,null,{default:v(()=>[f(de(Xl))]),_:1})]),_:2},1032,["onClick"]),f(me,{type:"danger",size:"mini",onClick:lt=>Z(I)},{default:v(()=>[f(gt,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"]),f(me,{size:"mini",type:"info",onClick:lt=>ie(I),disabled:I.sort_order<=1},{default:v(()=>[f(gt,null,{default:v(()=>[f(de(Po))]),_:1})]),_:2},1032,["onClick","disabled"]),f(me,{size:"mini",type:"info",onClick:lt=>ge(I),disabled:I.sort_order>=I.sortMax},{default:v(()=>[f(gt,null,{default:v(()=>[f(de(Io))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),f(Tl,{"current-page":c.value,"page-size":p.value,total:i.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:S[3]||(S[3]=I=>{c.value=I,X()}),onSizeChange:S[4]||(S[4]=I=>{p.value=I,c.value=1,X()})},null,8,["current-page","page-size","total"])]),f(Ll,{modelValue:k.value,"onUpdate:modelValue":S[30]||(S[30]=I=>k.value=I),title:D.value,width:"1000px"},{footer:v(()=>[W("span",Mi,[N.value?$t("",!0):(J(),he(me,{key:0,onClick:S[29]||(S[29]=I=>k.value=!1)},{default:v(()=>S[34]||(S[34]=[ne("取消")])),_:1,__:[34]})),f(me,{type:"primary",onClick:O},{default:v(()=>S[35]||(S[35]=[ne("确定")])),_:1,__:[35]})])]),default:v(()=>[f(Ol,{model:m,rules:y,ref_key:"newUserFormRef",ref:A,"label-width":"120px",inline:!1,class:"user-form",disabled:N.value},{default:v(()=>[W("div",Ci,[f(pe,{label:"姓名",prop:"name",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.name,"onUpdate:modelValue":S[5]||(S[5]=I=>m.name=I)},null,8,["modelValue"])]),_:1}),f(pe,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.id_number,"onUpdate:modelValue":S[6]||(S[6]=I=>m.id_number=I),maxlength:"18"},null,8,["modelValue"])]),_:1}),f(pe,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.phone,"onUpdate:modelValue":S[7]||(S[7]=I=>m.phone=I),maxlength:"11"},null,8,["modelValue"])]),_:1})]),W("div",Ei,[f(pe,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:v(()=>[f(yt,{modelValue:m.archive_birthdate,"onUpdate:modelValue":S[8]||(S[8]=I=>m.archive_birthdate=I),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(pe,{label:"性别",prop:"gender",style:{flex:"1"}},{default:v(()=>[f(Ze,{modelValue:m.gender,"onUpdate:modelValue":S[9]||(S[9]=I=>m.gender=I)},{default:v(()=>[f(Ee,{label:"未知",value:0}),f(Ee,{label:"男",value:1}),f(Ee,{label:"女",value:2})]),_:1},8,["modelValue"])]),_:1}),f(pe,{label:"备注",prop:"desc",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.desc,"onUpdate:modelValue":S[10]||(S[10]=I=>m.desc=I)},null,8,["modelValue"])]),_:1})]),W("div",Ri,[f(pe,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.short_code,"onUpdate:modelValue":S[11]||(S[11]=I=>m.short_code=I)},null,8,["modelValue"]),S[33]||(S[33]=W("div",{class:""},null,-1))]),_:1,__:[33]}),f(pe,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.alt_phone_1,"onUpdate:modelValue":S[12]||(S[12]=I=>m.alt_phone_1=I)},null,8,["modelValue"])]),_:1}),f(pe,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.alt_phone_2,"onUpdate:modelValue":S[13]||(S[13]=I=>m.alt_phone_2=I)},null,8,["modelValue"])]),_:1})]),W("div",Vi,[f(pe,{label:"座机",prop:"landline",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.landline,"onUpdate:modelValue":S[14]||(S[14]=I=>m.landline=I)},null,8,["modelValue"])]),_:1}),f(pe,{label:"编制单位",prop:"organization_unit",required:""},{default:v(()=>[f(xr,{modelValue:m.organization_unit,"onUpdate:modelValue":S[15]||(S[15]=I=>m.organization_unit=I),options:b.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),f(pe,{label:"工作单位",prop:"work_unit"},{default:v(()=>[f(xr,{modelValue:m.work_unit,"onUpdate:modelValue":S[16]||(S[16]=I=>m.work_unit=I),options:P.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),W("div",$i,[f(pe,{label:"参工日期",prop:"employment_date",style:{flex:"1"}},{default:v(()=>[f(yt,{modelValue:m.employment_date,"onUpdate:modelValue":S[17]||(S[17]=I=>m.employment_date=I),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(pe,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:v(()=>[f(Ze,{modelValue:m.political_status,"onUpdate:modelValue":S[18]||(S[18]=I=>m.political_status=I),placeholder:"请选择政治面貌"},{default:v(()=>[f(Ee,{label:"中共党员",value:"中共党员"}),f(Ee,{label:"中共预备党员",value:"中共预备党员"}),f(Ee,{label:"共青团员",value:"共青团员"}),f(Ee,{label:"民主党派",value:"民主党派"}),f(Ee,{label:"无党派人士",value:"无党派人士"}),f(Ee,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),f(pe,{label:"加入组织日期",prop:"party_join_date",style:{flex:"1"}},{default:v(()=>[f(yt,{modelValue:m.party_join_date,"onUpdate:modelValue":S[19]||(S[19]=I=>m.party_join_date=I),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),W("div",Pi,[f(pe,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:v(()=>[f(Ze,{modelValue:m.personnel_type,"onUpdate:modelValue":S[20]||(S[20]=I=>m.personnel_type=I),placeholder:"请选择人员身份"},{default:v(()=>[(J(!0),le(_e,null,ye(t.value,I=>(J(),he(Ee,{key:I.value,label:I.label,value:I.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(pe,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.police_number,"onUpdate:modelValue":S[21]||(S[21]=I=>m.police_number=I)},null,8,["modelValue"])]),_:1}),f(pe,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:v(()=>[f(Ze,{modelValue:m.is_assisting_officer,"onUpdate:modelValue":S[22]||(S[22]=I=>m.is_assisting_officer=I),placeholder:"请选择带辅民警",clearable:""},{default:v(()=>[(J(!0),le(_e,null,ye(l.value,I=>(J(),he(Ee,{key:I.value,label:I.label,value:I.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),W("div",Ii,[f(pe,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:v(()=>[f(Ze,{modelValue:m.employment_status,"onUpdate:modelValue":S[23]||(S[23]=I=>m.employment_status=I)},{default:v(()=>[(J(!0),le(_e,null,ye(n.value,I=>(J(),he(Ee,{key:I.value,label:I.label,value:I.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(pe,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:v(()=>[f(Ze,{modelValue:m.job_rank,"onUpdate:modelValue":S[24]||(S[24]=I=>m.job_rank=I)},{default:v(()=>[(J(!0),le(_e,null,ye(r.value,I=>(J(),he(Ee,{key:I.value,label:I.label,value:I.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(pe,{label:"任现职级日期",prop:"current_rank_date",style:{flex:"1"}},{default:v(()=>[f(yt,{modelValue:m.current_rank_date,"onUpdate:modelValue":S[25]||(S[25]=I=>m.current_rank_date=I),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),W("div",Di,[f(pe,{label:"职务",prop:"position",style:{flex:"1"}},{default:v(()=>[f(ee,{modelValue:m.position,"onUpdate:modelValue":S[26]||(S[26]=I=>m.position=I)},null,8,["modelValue"])]),_:1}),f(pe,{label:"任现职务日期",prop:"current_position_date",style:{flex:"1"}},{default:v(()=>[f(yt,{modelValue:m.current_position_date,"onUpdate:modelValue":S[27]||(S[27]=I=>m.current_position_date=I),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(pe,{label:"人员排序",prop:"sort_order",style:{flex:"1"}},{default:v(()=>[f(Ze,{modelValue:m.sort_order,"onUpdate:modelValue":S[28]||(S[28]=I=>m.sort_order=I),placeholder:"请选择人员排序"},{default:v(()=>[f(Ee,{label:"置于 最前",value:"0"}),(J(!0),le(_e,null,ye(a.value,I=>(J(),he(Ee,{key:I.id,label:`置于 ${I.name} 之后`,value:I.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","disabled"])]),_:1},8,["modelValue","title"])])}}},Ai=rt(Ni,[["__scopeId","data-v-5df0d9eb"]]);class et{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let o=0;o<this._n&&o<32;o++){const l=n[o],a=t+l,s=Math.abs(t)<Math.abs(l)?t-(a-l):l-(a-t);s&&(n[r++]=s),t=a}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,o,l,a=0;if(n>0){for(a=t[--n];n>0&&(r=a,o=t[--n],a=r+o,l=o-(a-r),!l););n>0&&(l<0&&t[n-1]<0||l>0&&t[n-1]>0)&&(o=l*2,r=a+o,o==r-a&&(a=r))}return a}}function*Ui(e){for(const t of e)yield*t}function Ho(e){return Array.from(Ui(e))}var Ti={value:()=>{}};function Xo(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new en(n)}function en(e){this._=e}function Oi(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}en.prototype=Xo.prototype={constructor:en,on:function(e,t){var n=this._,r=Oi(e+"",n),o,l=-1,a=r.length;if(arguments.length<2){for(;++l<a;)if((o=(e=r[l]).type)&&(o=Li(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++l<a;)if(o=(e=r[l]).type)n[o]=qr(n[o],e.name,t);else if(t==null)for(o in n)n[o]=qr(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new en(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,l;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(l=this._[e],r=0,o=l.length;r<o;++r)l[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,l=r.length;o<l;++o)r[o].value.apply(t,n)}};function Li(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function qr(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=Ti,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var Fn="http://www.w3.org/1999/xhtml";const Yr={svg:"http://www.w3.org/2000/svg",xhtml:Fn,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function kn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Yr.hasOwnProperty(t)?{space:Yr[t],local:e}:e}function zi(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Fn&&t.documentElement.namespaceURI===Fn?t.createElement(e):t.createElementNS(n,e)}}function Fi(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Go(e){var t=kn(e);return(t.local?Fi:zi)(t)}function qi(){}function dr(e){return e==null?qi:function(){return this.querySelector(e)}}function Yi(e){typeof e!="function"&&(e=dr(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var l=t[o],a=l.length,s=r[o]=new Array(a),c,p,i=0;i<a;++i)(c=l[i])&&(p=e.call(c,c.__data__,i,l))&&("__data__"in c&&(p.__data__=c.__data__),s[i]=p);return new Ie(r,this._parents)}function Bi(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Hi(){return[]}function Ko(e){return e==null?Hi:function(){return this.querySelectorAll(e)}}function Xi(e){return function(){return Bi(e.apply(this,arguments))}}function Gi(e){typeof e=="function"?e=Xi(e):e=Ko(e);for(var t=this._groups,n=t.length,r=[],o=[],l=0;l<n;++l)for(var a=t[l],s=a.length,c,p=0;p<s;++p)(c=a[p])&&(r.push(e.call(c,c.__data__,p,a)),o.push(c));return new Ie(r,o)}function Wo(e){return function(){return this.matches(e)}}function Qo(e){return function(t){return t.matches(e)}}var Ki=Array.prototype.find;function Wi(e){return function(){return Ki.call(this.children,e)}}function Qi(){return this.firstElementChild}function Zi(e){return this.select(e==null?Qi:Wi(typeof e=="function"?e:Qo(e)))}var Ji=Array.prototype.filter;function ji(){return Array.from(this.children)}function es(e){return function(){return Ji.call(this.children,e)}}function ts(e){return this.selectAll(e==null?ji:es(typeof e=="function"?e:Qo(e)))}function ns(e){typeof e!="function"&&(e=Wo(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var l=t[o],a=l.length,s=r[o]=[],c,p=0;p<a;++p)(c=l[p])&&e.call(c,c.__data__,p,l)&&s.push(c);return new Ie(r,this._parents)}function Zo(e){return new Array(e.length)}function rs(){return new Ie(this._enter||this._groups.map(Zo),this._parents)}function ln(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}ln.prototype={constructor:ln,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function os(e){return function(){return e}}function ls(e,t,n,r,o,l){for(var a=0,s,c=t.length,p=l.length;a<p;++a)(s=t[a])?(s.__data__=l[a],r[a]=s):n[a]=new ln(e,l[a]);for(;a<c;++a)(s=t[a])&&(o[a]=s)}function as(e,t,n,r,o,l,a){var s,c,p=new Map,i=t.length,u=l.length,d=new Array(i),h;for(s=0;s<i;++s)(c=t[s])&&(d[s]=h=a.call(c,c.__data__,s,t)+"",p.has(h)?o[s]=c:p.set(h,c));for(s=0;s<u;++s)h=a.call(e,l[s],s,l)+"",(c=p.get(h))?(r[s]=c,c.__data__=l[s],p.delete(h)):n[s]=new ln(e,l[s]);for(s=0;s<i;++s)(c=t[s])&&p.get(d[s])===c&&(o[s]=c)}function is(e){return e.__data__}function ss(e,t){if(!arguments.length)return Array.from(this,is);var n=t?as:ls,r=this._parents,o=this._groups;typeof e!="function"&&(e=os(e));for(var l=o.length,a=new Array(l),s=new Array(l),c=new Array(l),p=0;p<l;++p){var i=r[p],u=o[p],d=u.length,h=us(e.call(i,i&&i.__data__,p,r)),b=h.length,P=s[p]=new Array(b),_=a[p]=new Array(b),E=c[p]=new Array(d);n(i,u,P,_,E,h,t);for(var k=0,D=0,N,B;k<b;++k)if(N=P[k]){for(k>=D&&(D=k+1);!(B=_[D])&&++D<b;);N._next=B||null}}return a=new Ie(a,r),a._enter=s,a._exit=c,a}function us(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function cs(){return new Ie(this._exit||this._groups.map(Zo),this._parents)}function ds(e,t,n){var r=this.enter(),o=this,l=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?l.remove():n(l),r&&o?r.merge(o).order():o}function fs(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,l=r.length,a=Math.min(o,l),s=new Array(o),c=0;c<a;++c)for(var p=n[c],i=r[c],u=p.length,d=s[c]=new Array(u),h,b=0;b<u;++b)(h=p[b]||i[b])&&(d[b]=h);for(;c<o;++c)s[c]=n[c];return new Ie(s,this._parents)}function ps(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,l=r[o],a;--o>=0;)(a=r[o])&&(l&&a.compareDocumentPosition(l)^4&&l.parentNode.insertBefore(a,l),l=a);return this}function hs(e){e||(e=ms);function t(u,d){return u&&d?e(u.__data__,d.__data__):!u-!d}for(var n=this._groups,r=n.length,o=new Array(r),l=0;l<r;++l){for(var a=n[l],s=a.length,c=o[l]=new Array(s),p,i=0;i<s;++i)(p=a[i])&&(c[i]=p);c.sort(t)}return new Ie(o,this._parents).order()}function ms(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function _s(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function vs(){return Array.from(this)}function gs(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,l=r.length;o<l;++o){var a=r[o];if(a)return a}return null}function ys(){let e=0;for(const t of this)++e;return e}function ws(){return!this.node()}function bs(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],l=0,a=o.length,s;l<a;++l)(s=o[l])&&e.call(s,s.__data__,l,o);return this}function xs(e){return function(){this.removeAttribute(e)}}function Ss(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ks(e,t){return function(){this.setAttribute(e,t)}}function Cs(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function Es(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function Rs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function Vs(e,t){var n=kn(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?Ss:xs:typeof t=="function"?n.local?Rs:Es:n.local?Cs:ks)(n,t))}function Jo(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function $s(e){return function(){this.style.removeProperty(e)}}function Ps(e,t,n){return function(){this.style.setProperty(e,t,n)}}function Is(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function Ds(e,t,n){return arguments.length>1?this.each((t==null?$s:typeof t=="function"?Is:Ps)(e,t,n??"")):ht(this.node(),e)}function ht(e,t){return e.style.getPropertyValue(t)||Jo(e).getComputedStyle(e,null).getPropertyValue(t)}function Ms(e){return function(){delete this[e]}}function Ns(e,t){return function(){this[e]=t}}function As(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function Us(e,t){return arguments.length>1?this.each((t==null?Ms:typeof t=="function"?As:Ns)(e,t)):this.node()[e]}function jo(e){return e.trim().split(/^|\s+/)}function fr(e){return e.classList||new el(e)}function el(e){this._node=e,this._names=jo(e.getAttribute("class")||"")}el.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function tl(e,t){for(var n=fr(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function nl(e,t){for(var n=fr(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function Ts(e){return function(){tl(this,e)}}function Os(e){return function(){nl(this,e)}}function Ls(e,t){return function(){(t.apply(this,arguments)?tl:nl)(this,e)}}function zs(e,t){var n=jo(e+"");if(arguments.length<2){for(var r=fr(this.node()),o=-1,l=n.length;++o<l;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?Ls:t?Ts:Os)(n,t))}function Fs(){this.textContent=""}function qs(e){return function(){this.textContent=e}}function Ys(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Bs(e){return arguments.length?this.each(e==null?Fs:(typeof e=="function"?Ys:qs)(e)):this.node().textContent}function Hs(){this.innerHTML=""}function Xs(e){return function(){this.innerHTML=e}}function Gs(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Ks(e){return arguments.length?this.each(e==null?Hs:(typeof e=="function"?Gs:Xs)(e)):this.node().innerHTML}function Ws(){this.nextSibling&&this.parentNode.appendChild(this)}function Qs(){return this.each(Ws)}function Zs(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Js(){return this.each(Zs)}function js(e){var t=typeof e=="function"?e:Go(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function eu(){return null}function tu(e,t){var n=typeof e=="function"?e:Go(e),r=t==null?eu:typeof t=="function"?t:dr(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function nu(){var e=this.parentNode;e&&e.removeChild(this)}function ru(){return this.each(nu)}function ou(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function lu(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function au(e){return this.select(e?lu:ou)}function iu(e){return arguments.length?this.property("__data__",e):this.node().__data__}function su(e){return function(t){e.call(this,t,this.__data__)}}function uu(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function cu(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,l;n<o;++n)l=t[n],(!e.type||l.type===e.type)&&l.name===e.name?this.removeEventListener(l.type,l.listener,l.options):t[++r]=l;++r?t.length=r:delete this.__on}}}function du(e,t,n){return function(){var r=this.__on,o,l=su(t);if(r){for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=l,o.options=n),o.value=t;return}}this.addEventListener(e.type,l,n),o={type:e.type,name:e.name,value:t,listener:l,options:n},r?r.push(o):this.__on=[o]}}function fu(e,t,n){var r=uu(e+""),o,l=r.length,a;if(arguments.length<2){var s=this.node().__on;if(s){for(var c=0,p=s.length,i;c<p;++c)for(o=0,i=s[c];o<l;++o)if((a=r[o]).type===i.type&&a.name===i.name)return i.value}return}for(s=t?du:cu,o=0;o<l;++o)this.each(s(r[o],t,n));return this}function rl(e,t,n){var r=Jo(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function pu(e,t){return function(){return rl(this,e,t)}}function hu(e,t){return function(){return rl(this,e,t.apply(this,arguments))}}function mu(e,t){return this.each((typeof t=="function"?hu:pu)(e,t))}function*_u(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,l=r.length,a;o<l;++o)(a=r[o])&&(yield a)}var ol=[null];function Ie(e,t){this._groups=e,this._parents=t}function Yt(){return new Ie([[document.documentElement]],ol)}function vu(){return this}Ie.prototype=Yt.prototype={constructor:Ie,select:Yi,selectAll:Gi,selectChild:Zi,selectChildren:ts,filter:ns,data:ss,enter:rs,exit:cs,join:ds,merge:fs,selection:vu,order:ps,sort:hs,call:_s,nodes:vs,node:gs,size:ys,empty:ws,each:bs,attr:Vs,style:Ds,property:Us,classed:zs,text:Bs,html:Ks,raise:Qs,lower:Js,append:js,insert:tu,remove:ru,clone:au,datum:iu,on:fu,dispatch:mu,[Symbol.iterator]:_u};function Ht(e){return typeof e=="string"?new Ie([[document.querySelector(e)]],[document.documentElement]):new Ie([[e]],ol)}function pr(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function ll(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Bt(){}var At=.7,an=1/At,ct="\\s*([+-]?\\d+)\\s*",Ut="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Fe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",gu=/^#([0-9a-f]{3,8})$/,yu=new RegExp(`^rgb\\(${ct},${ct},${ct}\\)$`),wu=new RegExp(`^rgb\\(${Fe},${Fe},${Fe}\\)$`),bu=new RegExp(`^rgba\\(${ct},${ct},${ct},${Ut}\\)$`),xu=new RegExp(`^rgba\\(${Fe},${Fe},${Fe},${Ut}\\)$`),Su=new RegExp(`^hsl\\(${Ut},${Fe},${Fe}\\)$`),ku=new RegExp(`^hsla\\(${Ut},${Fe},${Fe},${Ut}\\)$`),Br={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};pr(Bt,Tt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Hr,formatHex:Hr,formatHex8:Cu,formatHsl:Eu,formatRgb:Xr,toString:Xr});function Hr(){return this.rgb().formatHex()}function Cu(){return this.rgb().formatHex8()}function Eu(){return al(this).formatHsl()}function Xr(){return this.rgb().formatRgb()}function Tt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=gu.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Gr(t):n===3?new Ve(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Xt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Xt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=yu.exec(e))?new Ve(t[1],t[2],t[3],1):(t=wu.exec(e))?new Ve(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=bu.exec(e))?Xt(t[1],t[2],t[3],t[4]):(t=xu.exec(e))?Xt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=Su.exec(e))?Qr(t[1],t[2]/100,t[3]/100,1):(t=ku.exec(e))?Qr(t[1],t[2]/100,t[3]/100,t[4]):Br.hasOwnProperty(e)?Gr(Br[e]):e==="transparent"?new Ve(NaN,NaN,NaN,0):null}function Gr(e){return new Ve(e>>16&255,e>>8&255,e&255,1)}function Xt(e,t,n,r){return r<=0&&(e=t=n=NaN),new Ve(e,t,n,r)}function Ru(e){return e instanceof Bt||(e=Tt(e)),e?(e=e.rgb(),new Ve(e.r,e.g,e.b,e.opacity)):new Ve}function qn(e,t,n,r){return arguments.length===1?Ru(e):new Ve(e,t,n,r??1)}function Ve(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}pr(Ve,qn,ll(Bt,{brighter(e){return e=e==null?an:Math.pow(an,e),new Ve(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?At:Math.pow(At,e),new Ve(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ve(je(this.r),je(this.g),je(this.b),sn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Kr,formatHex:Kr,formatHex8:Vu,formatRgb:Wr,toString:Wr}));function Kr(){return`#${Je(this.r)}${Je(this.g)}${Je(this.b)}`}function Vu(){return`#${Je(this.r)}${Je(this.g)}${Je(this.b)}${Je((isNaN(this.opacity)?1:this.opacity)*255)}`}function Wr(){const e=sn(this.opacity);return`${e===1?"rgb(":"rgba("}${je(this.r)}, ${je(this.g)}, ${je(this.b)}${e===1?")":`, ${e})`}`}function sn(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function je(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Je(e){return e=je(e),(e<16?"0":"")+e.toString(16)}function Qr(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Ae(e,t,n,r)}function al(e){if(e instanceof Ae)return new Ae(e.h,e.s,e.l,e.opacity);if(e instanceof Bt||(e=Tt(e)),!e)return new Ae;if(e instanceof Ae)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),l=Math.max(t,n,r),a=NaN,s=l-o,c=(l+o)/2;return s?(t===l?a=(n-r)/s+(n<r)*6:n===l?a=(r-t)/s+2:a=(t-n)/s+4,s/=c<.5?l+o:2-l-o,a*=60):s=c>0&&c<1?0:a,new Ae(a,s,c,e.opacity)}function $u(e,t,n,r){return arguments.length===1?al(e):new Ae(e,t,n,r??1)}function Ae(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}pr(Ae,$u,ll(Bt,{brighter(e){return e=e==null?an:Math.pow(an,e),new Ae(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?At:Math.pow(At,e),new Ae(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new Ve(Dn(e>=240?e-240:e+120,o,r),Dn(e,o,r),Dn(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new Ae(Zr(this.h),Gt(this.s),Gt(this.l),sn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=sn(this.opacity);return`${e===1?"hsl(":"hsla("}${Zr(this.h)}, ${Gt(this.s)*100}%, ${Gt(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Zr(e){return e=(e||0)%360,e<0?e+360:e}function Gt(e){return Math.max(0,Math.min(1,e||0))}function Dn(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const il=e=>()=>e;function Pu(e,t){return function(n){return e+n*t}}function Iu(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function Du(e){return(e=+e)==1?sl:function(t,n){return n-t?Iu(t,n,e):il(isNaN(t)?n:t)}}function sl(e,t){var n=t-e;return n?Pu(e,n):il(isNaN(e)?t:e)}const Jr=function e(t){var n=Du(t);function r(o,l){var a=n((o=qn(o)).r,(l=qn(l)).r),s=n(o.g,l.g),c=n(o.b,l.b),p=sl(o.opacity,l.opacity);return function(i){return o.r=a(i),o.g=s(i),o.b=c(i),o.opacity=p(i),o+""}}return r.gamma=e,r}(1);function We(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Yn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Mn=new RegExp(Yn.source,"g");function Mu(e){return function(){return e}}function Nu(e){return function(t){return e(t)+""}}function Au(e,t){var n=Yn.lastIndex=Mn.lastIndex=0,r,o,l,a=-1,s=[],c=[];for(e=e+"",t=t+"";(r=Yn.exec(e))&&(o=Mn.exec(t));)(l=o.index)>n&&(l=t.slice(n,l),s[a]?s[a]+=l:s[++a]=l),(r=r[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,c.push({i:a,x:We(r,o)})),n=Mn.lastIndex;return n<t.length&&(l=t.slice(n),s[a]?s[a]+=l:s[++a]=l),s.length<2?c[0]?Nu(c[0].x):Mu(t):(t=c.length,function(p){for(var i=0,u;i<t;++i)s[(u=c[i]).i]=u.x(p);return s.join("")})}var jr=180/Math.PI,Bn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function ul(e,t,n,r,o,l){var a,s,c;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,c/=s),e*r<t*n&&(e=-e,t=-t,c=-c,a=-a),{translateX:o,translateY:l,rotate:Math.atan2(t,e)*jr,skewX:Math.atan(c)*jr,scaleX:a,scaleY:s}}var Kt;function Uu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Bn:ul(t.a,t.b,t.c,t.d,t.e,t.f)}function Tu(e){return e==null||(Kt||(Kt=document.createElementNS("http://www.w3.org/2000/svg","g")),Kt.setAttribute("transform",e),!(e=Kt.transform.baseVal.consolidate()))?Bn:(e=e.matrix,ul(e.a,e.b,e.c,e.d,e.e,e.f))}function cl(e,t,n,r){function o(p){return p.length?p.pop()+" ":""}function l(p,i,u,d,h,b){if(p!==u||i!==d){var P=h.push("translate(",null,t,null,n);b.push({i:P-4,x:We(p,u)},{i:P-2,x:We(i,d)})}else(u||d)&&h.push("translate("+u+t+d+n)}function a(p,i,u,d){p!==i?(p-i>180?i+=360:i-p>180&&(p+=360),d.push({i:u.push(o(u)+"rotate(",null,r)-2,x:We(p,i)})):i&&u.push(o(u)+"rotate("+i+r)}function s(p,i,u,d){p!==i?d.push({i:u.push(o(u)+"skewX(",null,r)-2,x:We(p,i)}):i&&u.push(o(u)+"skewX("+i+r)}function c(p,i,u,d,h,b){if(p!==u||i!==d){var P=h.push(o(h)+"scale(",null,",",null,")");b.push({i:P-4,x:We(p,u)},{i:P-2,x:We(i,d)})}else(u!==1||d!==1)&&h.push(o(h)+"scale("+u+","+d+")")}return function(p,i){var u=[],d=[];return p=e(p),i=e(i),l(p.translateX,p.translateY,i.translateX,i.translateY,u,d),a(p.rotate,i.rotate,u,d),s(p.skewX,i.skewX,u,d),c(p.scaleX,p.scaleY,i.scaleX,i.scaleY,u,d),p=i=null,function(h){for(var b=-1,P=d.length,_;++b<P;)u[(_=d[b]).i]=_.x(h);return u.join("")}}}var Ou=cl(Uu,"px, ","px)","deg)"),Lu=cl(Tu,", ",")",")"),mt=0,xt=0,bt=0,dl=1e3,un,St,cn=0,tt=0,Cn=0,Ot=typeof performance=="object"&&performance.now?performance:Date,fl=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function hr(){return tt||(fl(zu),tt=Ot.now()+Cn)}function zu(){tt=0}function dn(){this._call=this._time=this._next=null}dn.prototype=pl.prototype={constructor:dn,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?hr():+n)+(t==null?0:+t),!this._next&&St!==this&&(St?St._next=this:un=this,St=this),this._call=e,this._time=n,Hn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Hn())}};function pl(e,t,n){var r=new dn;return r.restart(e,t,n),r}function Fu(){hr(),++mt;for(var e=un,t;e;)(t=tt-e._time)>=0&&e._call.call(void 0,t),e=e._next;--mt}function eo(){tt=(cn=Ot.now())+Cn,mt=xt=0;try{Fu()}finally{mt=0,Yu(),tt=0}}function qu(){var e=Ot.now(),t=e-cn;t>dl&&(Cn-=t,cn=e)}function Yu(){for(var e,t=un,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:un=n);St=e,Hn(r)}function Hn(e){if(!mt){xt&&(xt=clearTimeout(xt));var t=e-tt;t>24?(e<1/0&&(xt=setTimeout(eo,e-Ot.now()-Cn)),bt&&(bt=clearInterval(bt))):(bt||(cn=Ot.now(),bt=setInterval(qu,dl)),mt=1,fl(eo))}}function to(e,t,n){var r=new dn;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var Bu=Xo("start","end","cancel","interrupt"),Hu=[],hl=0,no=1,Xn=2,tn=3,ro=4,Gn=5,nn=6;function En(e,t,n,r,o,l){var a=e.__transition;if(!a)e.__transition={};else if(n in a)return;Xu(e,n,{name:t,index:r,group:o,on:Bu,tween:Hu,time:l.time,delay:l.delay,duration:l.duration,ease:l.ease,timer:null,state:hl})}function mr(e,t){var n=Oe(e,t);if(n.state>hl)throw new Error("too late; already scheduled");return n}function qe(e,t){var n=Oe(e,t);if(n.state>tn)throw new Error("too late; already running");return n}function Oe(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Xu(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=pl(l,0,n.time);function l(p){n.state=no,n.timer.restart(a,n.delay,n.time),n.delay<=p&&a(p-n.delay)}function a(p){var i,u,d,h;if(n.state!==no)return c();for(i in r)if(h=r[i],h.name===n.name){if(h.state===tn)return to(a);h.state===ro?(h.state=nn,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[i]):+i<t&&(h.state=nn,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[i])}if(to(function(){n.state===tn&&(n.state=ro,n.timer.restart(s,n.delay,n.time),s(p))}),n.state=Xn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Xn){for(n.state=tn,o=new Array(d=n.tween.length),i=0,u=-1;i<d;++i)(h=n.tween[i].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(p){for(var i=p<n.duration?n.ease.call(null,p/n.duration):(n.timer.restart(c),n.state=Gn,1),u=-1,d=o.length;++u<d;)o[u].call(e,i);n.state===Gn&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){n.state=nn,n.timer.stop(),delete r[t];for(var p in r)return;delete e.__transition}}function Gu(e,t){var n=e.__transition,r,o,l=!0,a;if(n){t=t==null?null:t+"";for(a in n){if((r=n[a]).name!==t){l=!1;continue}o=r.state>Xn&&r.state<Gn,r.state=nn,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[a]}l&&delete e.__transition}}function Ku(e){return this.each(function(){Gu(this,e)})}function Wu(e,t){var n,r;return function(){var o=qe(this,e),l=o.tween;if(l!==n){r=n=l;for(var a=0,s=r.length;a<s;++a)if(r[a].name===t){r=r.slice(),r.splice(a,1);break}}o.tween=r}}function Qu(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var l=qe(this,e),a=l.tween;if(a!==r){o=(r=a).slice();for(var s={name:t,value:n},c=0,p=o.length;c<p;++c)if(o[c].name===t){o[c]=s;break}c===p&&o.push(s)}l.tween=o}}function Zu(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Oe(this.node(),n).tween,o=0,l=r.length,a;o<l;++o)if((a=r[o]).name===e)return a.value;return null}return this.each((t==null?Wu:Qu)(n,e,t))}function _r(e,t,n){var r=e._id;return e.each(function(){var o=qe(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return Oe(o,r).value[t]}}function ml(e,t){var n;return(typeof t=="number"?We:t instanceof Tt?Jr:(n=Tt(t))?(t=n,Jr):Au)(e,t)}function Ju(e){return function(){this.removeAttribute(e)}}function ju(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ec(e,t,n){var r,o=n+"",l;return function(){var a=this.getAttribute(e);return a===o?null:a===r?l:l=t(r=a,n)}}function tc(e,t,n){var r,o=n+"",l;return function(){var a=this.getAttributeNS(e.space,e.local);return a===o?null:a===r?l:l=t(r=a,n)}}function nc(e,t,n){var r,o,l;return function(){var a,s=n(this),c;return s==null?void this.removeAttribute(e):(a=this.getAttribute(e),c=s+"",a===c?null:a===r&&c===o?l:(o=c,l=t(r=a,s)))}}function rc(e,t,n){var r,o,l;return function(){var a,s=n(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local),c=s+"",a===c?null:a===r&&c===o?l:(o=c,l=t(r=a,s)))}}function oc(e,t){var n=kn(e),r=n==="transform"?Lu:ml;return this.attrTween(e,typeof t=="function"?(n.local?rc:nc)(n,r,_r(this,"attr."+e,t)):t==null?(n.local?ju:Ju)(n):(n.local?tc:ec)(n,r,t))}function lc(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function ac(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function ic(e,t){var n,r;function o(){var l=t.apply(this,arguments);return l!==r&&(n=(r=l)&&ac(e,l)),n}return o._value=t,o}function sc(e,t){var n,r;function o(){var l=t.apply(this,arguments);return l!==r&&(n=(r=l)&&lc(e,l)),n}return o._value=t,o}function uc(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=kn(e);return this.tween(n,(r.local?ic:sc)(r,t))}function cc(e,t){return function(){mr(this,e).delay=+t.apply(this,arguments)}}function dc(e,t){return t=+t,function(){mr(this,e).delay=t}}function fc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?cc:dc)(t,e)):Oe(this.node(),t).delay}function pc(e,t){return function(){qe(this,e).duration=+t.apply(this,arguments)}}function hc(e,t){return t=+t,function(){qe(this,e).duration=t}}function mc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?pc:hc)(t,e)):Oe(this.node(),t).duration}function _c(e,t){if(typeof t!="function")throw new Error;return function(){qe(this,e).ease=t}}function vc(e){var t=this._id;return arguments.length?this.each(_c(t,e)):Oe(this.node(),t).ease}function gc(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;qe(this,e).ease=n}}function yc(e){if(typeof e!="function")throw new Error;return this.each(gc(this._id,e))}function wc(e){typeof e!="function"&&(e=Wo(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var l=t[o],a=l.length,s=r[o]=[],c,p=0;p<a;++p)(c=l[p])&&e.call(c,c.__data__,p,l)&&s.push(c);return new Xe(r,this._parents,this._name,this._id)}function bc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,l=Math.min(r,o),a=new Array(r),s=0;s<l;++s)for(var c=t[s],p=n[s],i=c.length,u=a[s]=new Array(i),d,h=0;h<i;++h)(d=c[h]||p[h])&&(u[h]=d);for(;s<r;++s)a[s]=t[s];return new Xe(a,this._parents,this._name,this._id)}function xc(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function Sc(e,t,n){var r,o,l=xc(t)?mr:qe;return function(){var a=l(this,e),s=a.on;s!==r&&(o=(r=s).copy()).on(t,n),a.on=o}}function kc(e,t){var n=this._id;return arguments.length<2?Oe(this.node(),n).on.on(e):this.each(Sc(n,e,t))}function Cc(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function Ec(){return this.on("end.remove",Cc(this._id))}function Rc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=dr(e));for(var r=this._groups,o=r.length,l=new Array(o),a=0;a<o;++a)for(var s=r[a],c=s.length,p=l[a]=new Array(c),i,u,d=0;d<c;++d)(i=s[d])&&(u=e.call(i,i.__data__,d,s))&&("__data__"in i&&(u.__data__=i.__data__),p[d]=u,En(p[d],t,n,d,p,Oe(i,n)));return new Xe(l,this._parents,t,n)}function Vc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Ko(e));for(var r=this._groups,o=r.length,l=[],a=[],s=0;s<o;++s)for(var c=r[s],p=c.length,i,u=0;u<p;++u)if(i=c[u]){for(var d=e.call(i,i.__data__,u,c),h,b=Oe(i,n),P=0,_=d.length;P<_;++P)(h=d[P])&&En(h,t,n,P,d,b);l.push(d),a.push(i)}return new Xe(l,a,t,n)}var $c=Yt.prototype.constructor;function Pc(){return new $c(this._groups,this._parents)}function Ic(e,t){var n,r,o;return function(){var l=ht(this,e),a=(this.style.removeProperty(e),ht(this,e));return l===a?null:l===n&&a===r?o:o=t(n=l,r=a)}}function _l(e){return function(){this.style.removeProperty(e)}}function Dc(e,t,n){var r,o=n+"",l;return function(){var a=ht(this,e);return a===o?null:a===r?l:l=t(r=a,n)}}function Mc(e,t,n){var r,o,l;return function(){var a=ht(this,e),s=n(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),ht(this,e))),a===c?null:a===r&&c===o?l:(o=c,l=t(r=a,s))}}function Nc(e,t){var n,r,o,l="style."+t,a="end."+l,s;return function(){var c=qe(this,e),p=c.on,i=c.value[l]==null?s||(s=_l(t)):void 0;(p!==n||o!==i)&&(r=(n=p).copy()).on(a,o=i),c.on=r}}function Ac(e,t,n){var r=(e+="")=="transform"?Ou:ml;return t==null?this.styleTween(e,Ic(e,r)).on("end.style."+e,_l(e)):typeof t=="function"?this.styleTween(e,Mc(e,r,_r(this,"style."+e,t))).each(Nc(this._id,e)):this.styleTween(e,Dc(e,r,t),n).on("end.style."+e,null)}function Uc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function Tc(e,t,n){var r,o;function l(){var a=t.apply(this,arguments);return a!==o&&(r=(o=a)&&Uc(e,a,n)),r}return l._value=t,l}function Oc(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,Tc(e,t,n??""))}function Lc(e){return function(){this.textContent=e}}function zc(e){return function(){var t=e(this);this.textContent=t??""}}function Fc(e){return this.tween("text",typeof e=="function"?zc(_r(this,"text",e)):Lc(e==null?"":e+""))}function qc(e){return function(t){this.textContent=e.call(this,t)}}function Yc(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&qc(o)),t}return r._value=e,r}function Bc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Yc(e))}function Hc(){for(var e=this._name,t=this._id,n=vl(),r=this._groups,o=r.length,l=0;l<o;++l)for(var a=r[l],s=a.length,c,p=0;p<s;++p)if(c=a[p]){var i=Oe(c,t);En(c,e,n,p,a,{time:i.time+i.delay+i.duration,delay:0,duration:i.duration,ease:i.ease})}return new Xe(r,this._parents,e,n)}function Xc(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(l,a){var s={value:a},c={value:function(){--o===0&&l()}};n.each(function(){var p=qe(this,r),i=p.on;i!==e&&(t=(e=i).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),p.on=t}),o===0&&l()})}var Gc=0;function Xe(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function vl(){return++Gc}var Be=Yt.prototype;Xe.prototype={constructor:Xe,select:Rc,selectAll:Vc,selectChild:Be.selectChild,selectChildren:Be.selectChildren,filter:wc,merge:bc,selection:Pc,transition:Hc,call:Be.call,nodes:Be.nodes,node:Be.node,size:Be.size,empty:Be.empty,each:Be.each,on:kc,attr:oc,attrTween:uc,style:Ac,styleTween:Oc,text:Fc,textTween:Bc,remove:Ec,tween:Zu,delay:fc,duration:mc,ease:vc,easeVarying:yc,end:Xc,[Symbol.iterator]:Be[Symbol.iterator]};function Kc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Wc={time:null,delay:0,duration:250,ease:Kc};function Qc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Zc(e){var t,n;e instanceof Xe?(t=e._id,e=e._name):(t=vl(),(n=Wc).time=hr(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,l=0;l<o;++l)for(var a=r[l],s=a.length,c,p=0;p<s;++p)(c=a[p])&&En(c,e,t,p,a,n||Qc(c,t));return new Xe(r,this._parents,e,t)}Yt.prototype.interrupt=Ku;Yt.prototype.transition=Zc;function Jc(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function jc(e,t){return fetch(e,t).then(Jc)}var fe=1e-6,ae=Math.PI,$e=ae/2,oo=ae/4,De=ae*2,Pe=180/ae,ke=ae/180,ve=Math.abs,gl=Math.atan,Lt=Math.atan2,xe=Math.cos,ed=Math.exp,td=Math.log,Se=Math.sin,nd=Math.sign||function(e){return e>0?1:e<0?-1:0},ot=Math.sqrt,rd=Math.tan;function od(e){return e>1?0:e<-1?ae:Math.acos(e)}function zt(e){return e>1?$e:e<-1?-$e:Math.asin(e)}function Ne(){}function fn(e,t){e&&ao.hasOwnProperty(e.type)&&ao[e.type](e,t)}var lo={Feature:function(e,t){fn(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,o=n.length;++r<o;)fn(n[r].geometry,t)}},ao={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){Kn(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)Kn(n[r],t,0)},Polygon:function(e,t){io(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)io(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,o=n.length;++r<o;)fn(n[r],t)}};function Kn(e,t,n){var r=-1,o=e.length-n,l;for(t.lineStart();++r<o;)l=e[r],t.point(l[0],l[1],l[2]);t.lineEnd()}function io(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)Kn(e[n],t,1);t.polygonEnd()}function it(e,t){e&&lo.hasOwnProperty(e.type)?lo[e.type](e,t):fn(e,t)}function Wn(e){return[Lt(e[1],e[0]),zt(e[2])]}function _t(e){var t=e[0],n=e[1],r=xe(n);return[r*xe(t),r*Se(t),Se(n)]}function Wt(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function pn(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function Nn(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function Qt(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function Qn(e){var t=ot(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function Zn(e,t){function n(r,o){return r=e(r,o),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,o){return r=t.invert(r,o),r&&e.invert(r[0],r[1])}),n}function Jn(e,t){return ve(e)>ae&&(e-=Math.round(e/De)*De),[e,t]}Jn.invert=Jn;function yl(e,t,n){return(e%=De)?t||n?Zn(uo(e),co(t,n)):uo(e):t||n?co(t,n):Jn}function so(e){return function(t,n){return t+=e,ve(t)>ae&&(t-=Math.round(t/De)*De),[t,n]}}function uo(e){var t=so(e);return t.invert=so(-e),t}function co(e,t){var n=xe(e),r=Se(e),o=xe(t),l=Se(t);function a(s,c){var p=xe(c),i=xe(s)*p,u=Se(s)*p,d=Se(c),h=d*n+i*r;return[Lt(u*o-h*l,i*n-d*r),zt(h*o+u*l)]}return a.invert=function(s,c){var p=xe(c),i=xe(s)*p,u=Se(s)*p,d=Se(c),h=d*o-u*l;return[Lt(u*o+d*l,i*n+h*r),zt(h*n-i*r)]},a}function ld(e){e=yl(e[0]*ke,e[1]*ke,e.length>2?e[2]*ke:0);function t(n){return n=e(n[0]*ke,n[1]*ke),n[0]*=Pe,n[1]*=Pe,n}return t.invert=function(n){return n=e.invert(n[0]*ke,n[1]*ke),n[0]*=Pe,n[1]*=Pe,n},t}function ad(e,t,n,r,o,l){if(n){var a=xe(t),s=Se(t),c=r*n;o==null?(o=t+r*De,l=t-c/2):(o=fo(a,o),l=fo(a,l),(r>0?o<l:o>l)&&(o+=r*De));for(var p,i=o;r>0?i>l:i<l;i-=c)p=Wn([a,-s*xe(i),-s*Se(i)]),e.point(p[0],p[1])}}function fo(e,t){t=_t(t),t[0]-=e,Qn(t);var n=od(-t[1]);return((-t[2]<0?-n:n)+De-fe)%De}function wl(){var e=[],t;return{point:function(n,r,o){t.push([n,r,o])},lineStart:function(){e.push(t=[])},lineEnd:Ne,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function rn(e,t){return ve(e[0]-t[0])<fe&&ve(e[1]-t[1])<fe}function Zt(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function bl(e,t,n,r,o){var l=[],a=[],s,c;if(e.forEach(function(b){if(!((P=b.length-1)<=0)){var P,_=b[0],E=b[P],k;if(rn(_,E)){if(!_[2]&&!E[2]){for(o.lineStart(),s=0;s<P;++s)o.point((_=b[s])[0],_[1]);o.lineEnd();return}E[0]+=2*fe}l.push(k=new Zt(_,b,null,!0)),a.push(k.o=new Zt(_,null,k,!1)),l.push(k=new Zt(E,b,null,!1)),a.push(k.o=new Zt(E,null,k,!0))}}),!!l.length){for(a.sort(t),po(l),po(a),s=0,c=a.length;s<c;++s)a[s].e=n=!n;for(var p=l[0],i,u;;){for(var d=p,h=!0;d.v;)if((d=d.n)===p)return;i=d.z,o.lineStart();do{if(d.v=d.o.v=!0,d.e){if(h)for(s=0,c=i.length;s<c;++s)o.point((u=i[s])[0],u[1]);else r(d.x,d.n.x,1,o);d=d.n}else{if(h)for(i=d.p.z,s=i.length-1;s>=0;--s)o.point((u=i[s])[0],u[1]);else r(d.x,d.p.x,-1,o);d=d.p}d=d.o,i=d.z,h=!h}while(!d.v);o.lineEnd()}}}function po(e){if(t=e.length){for(var t,n=0,r=e[0],o;++n<t;)r.n=o=e[n],o.p=r,r=o;r.n=o=e[0],o.p=r}}function An(e){return ve(e[0])<=ae?e[0]:nd(e[0])*((ve(e[0])+ae)%De-ae)}function id(e,t){var n=An(t),r=t[1],o=Se(r),l=[Se(n),-xe(n),0],a=0,s=0,c=new et;o===1?r=$e+fe:o===-1&&(r=-$e-fe);for(var p=0,i=e.length;p<i;++p)if(d=(u=e[p]).length)for(var u,d,h=u[d-1],b=An(h),P=h[1]/2+oo,_=Se(P),E=xe(P),k=0;k<d;++k,b=N,_=F,E=U,h=D){var D=u[k],N=An(D),B=D[1]/2+oo,F=Se(B),U=xe(B),O=N-b,T=O>=0?1:-1,m=T*O,y=m>ae,A=_*F;if(c.add(Lt(A*T*Se(m),E*U+A*xe(m))),a+=y?O+T*De:O,y^b>=n^N>=n){var K=pn(_t(h),_t(D));Qn(K);var Q=pn(l,K);Qn(Q);var C=(y^O>=0?-1:1)*zt(Q[2]);(r>C||r===C&&(K[0]||K[1]))&&(s+=y^O>=0?1:-1)}}return(a<-1e-6||a<fe&&c<-1e-12)^s&1}function xl(e,t,n,r){return function(o){var l=t(o),a=wl(),s=t(a),c=!1,p,i,u,d={point:h,lineStart:P,lineEnd:_,polygonStart:function(){d.point=E,d.lineStart=k,d.lineEnd=D,i=[],p=[]},polygonEnd:function(){d.point=h,d.lineStart=P,d.lineEnd=_,i=Ho(i);var N=id(p,r);i.length?(c||(o.polygonStart(),c=!0),bl(i,ud,N,n,o)):N&&(c||(o.polygonStart(),c=!0),o.lineStart(),n(null,null,1,o),o.lineEnd()),c&&(o.polygonEnd(),c=!1),i=p=null},sphere:function(){o.polygonStart(),o.lineStart(),n(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function h(N,B){e(N,B)&&o.point(N,B)}function b(N,B){l.point(N,B)}function P(){d.point=b,l.lineStart()}function _(){d.point=h,l.lineEnd()}function E(N,B){u.push([N,B]),s.point(N,B)}function k(){s.lineStart(),u=[]}function D(){E(u[0][0],u[0][1]),s.lineEnd();var N=s.clean(),B=a.result(),F,U=B.length,O,T,m;if(u.pop(),p.push(u),u=null,!!U){if(N&1){if(T=B[0],(O=T.length-1)>0){for(c||(o.polygonStart(),c=!0),o.lineStart(),F=0;F<O;++F)o.point((m=T[F])[0],m[1]);o.lineEnd()}return}U>1&&N&2&&B.push(B.pop().concat(B.shift())),i.push(B.filter(sd))}}return d}}function sd(e){return e.length>1}function ud(e,t){return((e=e.x)[0]<0?e[1]-$e-fe:$e-e[1])-((t=t.x)[0]<0?t[1]-$e-fe:$e-t[1])}const ho=xl(function(){return!0},cd,fd,[-ae,-$e]);function cd(e){var t=NaN,n=NaN,r=NaN,o;return{lineStart:function(){e.lineStart(),o=1},point:function(l,a){var s=l>0?ae:-ae,c=ve(l-t);ve(c-ae)<fe?(e.point(t,n=(n+a)/2>0?$e:-$e),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),e.point(l,n),o=0):r!==s&&c>=ae&&(ve(t-r)<fe&&(t-=r*fe),ve(l-s)<fe&&(l-=s*fe),n=dd(t,n,l,a),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),o=0),e.point(t=l,n=a),r=s},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-o}}}function dd(e,t,n,r){var o,l,a=Se(e-n);return ve(a)>fe?gl((Se(t)*(l=xe(r))*Se(n)-Se(r)*(o=xe(t))*Se(e))/(o*l*a)):(t+r)/2}function fd(e,t,n,r){var o;if(e==null)o=n*$e,r.point(-ae,o),r.point(0,o),r.point(ae,o),r.point(ae,0),r.point(ae,-o),r.point(0,-o),r.point(-ae,-o),r.point(-ae,0),r.point(-ae,o);else if(ve(e[0]-t[0])>fe){var l=e[0]<t[0]?ae:-ae;o=n*l/2,r.point(-l,o),r.point(0,o),r.point(l,o)}else r.point(t[0],t[1])}function pd(e){var t=xe(e),n=2*ke,r=t>0,o=ve(t)>fe;function l(i,u,d,h){ad(h,e,n,d,i,u)}function a(i,u){return xe(i)*xe(u)>t}function s(i){var u,d,h,b,P;return{lineStart:function(){b=h=!1,P=1},point:function(_,E){var k=[_,E],D,N=a(_,E),B=r?N?0:p(_,E):N?p(_+(_<0?ae:-ae),E):0;if(!u&&(b=h=N)&&i.lineStart(),N!==h&&(D=c(u,k),(!D||rn(u,D)||rn(k,D))&&(k[2]=1)),N!==h)P=0,N?(i.lineStart(),D=c(k,u),i.point(D[0],D[1])):(D=c(u,k),i.point(D[0],D[1],2),i.lineEnd()),u=D;else if(o&&u&&r^N){var F;!(B&d)&&(F=c(k,u,!0))&&(P=0,r?(i.lineStart(),i.point(F[0][0],F[0][1]),i.point(F[1][0],F[1][1]),i.lineEnd()):(i.point(F[1][0],F[1][1]),i.lineEnd(),i.lineStart(),i.point(F[0][0],F[0][1],3)))}N&&(!u||!rn(u,k))&&i.point(k[0],k[1]),u=k,h=N,d=B},lineEnd:function(){h&&i.lineEnd(),u=null},clean:function(){return P|(b&&h)<<1}}}function c(i,u,d){var h=_t(i),b=_t(u),P=[1,0,0],_=pn(h,b),E=Wt(_,_),k=_[0],D=E-k*k;if(!D)return!d&&i;var N=t*E/D,B=-t*k/D,F=pn(P,_),U=Qt(P,N),O=Qt(_,B);Nn(U,O);var T=F,m=Wt(U,T),y=Wt(T,T),A=m*m-y*(Wt(U,U)-1);if(!(A<0)){var K=ot(A),Q=Qt(T,(-m-K)/y);if(Nn(Q,U),Q=Wn(Q),!d)return Q;var C=i[0],X=u[0],V=i[1],w=u[1],R;X<C&&(R=C,C=X,X=R);var q=X-C,L=ve(q-ae)<fe,j=L||q<fe;if(!L&&w<V&&(R=V,V=w,w=R),j?L?V+w>0^Q[1]<(ve(Q[0]-C)<fe?V:w):V<=Q[1]&&Q[1]<=w:q>ae^(C<=Q[0]&&Q[0]<=X)){var se=Qt(T,(-m+K)/y);return Nn(se,U),[Q,Wn(se)]}}}function p(i,u){var d=r?e:ae-e,h=0;return i<-d?h|=1:i>d&&(h|=2),u<-d?h|=4:u>d&&(h|=8),h}return xl(a,s,l,r?[0,-e]:[-ae,e-ae])}function hd(e,t,n,r,o,l){var a=e[0],s=e[1],c=t[0],p=t[1],i=0,u=1,d=c-a,h=p-s,b;if(b=n-a,!(!d&&b>0)){if(b/=d,d<0){if(b<i)return;b<u&&(u=b)}else if(d>0){if(b>u)return;b>i&&(i=b)}if(b=o-a,!(!d&&b<0)){if(b/=d,d<0){if(b>u)return;b>i&&(i=b)}else if(d>0){if(b<i)return;b<u&&(u=b)}if(b=r-s,!(!h&&b>0)){if(b/=h,h<0){if(b<i)return;b<u&&(u=b)}else if(h>0){if(b>u)return;b>i&&(i=b)}if(b=l-s,!(!h&&b<0)){if(b/=h,h<0){if(b>u)return;b>i&&(i=b)}else if(h>0){if(b<i)return;b<u&&(u=b)}return i>0&&(e[0]=a+i*d,e[1]=s+i*h),u<1&&(t[0]=a+u*d,t[1]=s+u*h),!0}}}}}var Jt=1e9,jt=-1e9;function md(e,t,n,r){function o(p,i){return e<=p&&p<=n&&t<=i&&i<=r}function l(p,i,u,d){var h=0,b=0;if(p==null||(h=a(p,u))!==(b=a(i,u))||c(p,i)<0^u>0)do d.point(h===0||h===3?e:n,h>1?r:t);while((h=(h+u+4)%4)!==b);else d.point(i[0],i[1])}function a(p,i){return ve(p[0]-e)<fe?i>0?0:3:ve(p[0]-n)<fe?i>0?2:1:ve(p[1]-t)<fe?i>0?1:0:i>0?3:2}function s(p,i){return c(p.x,i.x)}function c(p,i){var u=a(p,1),d=a(i,1);return u!==d?u-d:u===0?i[1]-p[1]:u===1?p[0]-i[0]:u===2?p[1]-i[1]:i[0]-p[0]}return function(p){var i=p,u=wl(),d,h,b,P,_,E,k,D,N,B,F,U={point:O,lineStart:A,lineEnd:K,polygonStart:m,polygonEnd:y};function O(C,X){o(C,X)&&i.point(C,X)}function T(){for(var C=0,X=0,V=h.length;X<V;++X)for(var w=h[X],R=1,q=w.length,L=w[0],j,se,we=L[0],Z=L[1];R<q;++R)j=we,se=Z,L=w[R],we=L[0],Z=L[1],se<=r?Z>r&&(we-j)*(r-se)>(Z-se)*(e-j)&&++C:Z<=r&&(we-j)*(r-se)<(Z-se)*(e-j)&&--C;return C}function m(){i=u,d=[],h=[],F=!0}function y(){var C=T(),X=F&&C,V=(d=Ho(d)).length;(X||V)&&(p.polygonStart(),X&&(p.lineStart(),l(null,null,1,p),p.lineEnd()),V&&bl(d,s,C,l,p),p.polygonEnd()),i=p,d=h=b=null}function A(){U.point=Q,h&&h.push(b=[]),B=!0,N=!1,k=D=NaN}function K(){d&&(Q(P,_),E&&N&&u.rejoin(),d.push(u.result())),U.point=O,N&&i.lineEnd()}function Q(C,X){var V=o(C,X);if(h&&b.push([C,X]),B)P=C,_=X,E=V,B=!1,V&&(i.lineStart(),i.point(C,X));else if(V&&N)i.point(C,X);else{var w=[k=Math.max(jt,Math.min(Jt,k)),D=Math.max(jt,Math.min(Jt,D))],R=[C=Math.max(jt,Math.min(Jt,C)),X=Math.max(jt,Math.min(Jt,X))];hd(w,R,e,t,n,r)?(N||(i.lineStart(),i.point(w[0],w[1])),i.point(R[0],R[1]),V||i.lineEnd(),F=!1):V&&(i.lineStart(),i.point(C,X),F=!1)}k=C,D=X,N=V}return U}}const jn=e=>e;var Un=new et,er=new et,Sl,kl,tr,nr,He={point:Ne,lineStart:Ne,lineEnd:Ne,polygonStart:function(){He.lineStart=_d,He.lineEnd=gd},polygonEnd:function(){He.lineStart=He.lineEnd=He.point=Ne,Un.add(ve(er)),er=new et},result:function(){var e=Un/2;return Un=new et,e}};function _d(){He.point=vd}function vd(e,t){He.point=Cl,Sl=tr=e,kl=nr=t}function Cl(e,t){er.add(nr*e-tr*t),tr=e,nr=t}function gd(){Cl(Sl,kl)}var vt=1/0,hn=vt,Ft=-vt,mn=Ft,_n={point:yd,lineStart:Ne,lineEnd:Ne,polygonStart:Ne,polygonEnd:Ne,result:function(){var e=[[vt,hn],[Ft,mn]];return Ft=mn=-(hn=vt=1/0),e}};function yd(e,t){e<vt&&(vt=e),e>Ft&&(Ft=e),t<hn&&(hn=t),t>mn&&(mn=t)}var rr=0,or=0,kt=0,vn=0,gn=0,st=0,lr=0,ar=0,Ct=0,El,Rl,Le,ze,Me={point:nt,lineStart:mo,lineEnd:_o,polygonStart:function(){Me.lineStart=xd,Me.lineEnd=Sd},polygonEnd:function(){Me.point=nt,Me.lineStart=mo,Me.lineEnd=_o},result:function(){var e=Ct?[lr/Ct,ar/Ct]:st?[vn/st,gn/st]:kt?[rr/kt,or/kt]:[NaN,NaN];return rr=or=kt=vn=gn=st=lr=ar=Ct=0,e}};function nt(e,t){rr+=e,or+=t,++kt}function mo(){Me.point=wd}function wd(e,t){Me.point=bd,nt(Le=e,ze=t)}function bd(e,t){var n=e-Le,r=t-ze,o=ot(n*n+r*r);vn+=o*(Le+e)/2,gn+=o*(ze+t)/2,st+=o,nt(Le=e,ze=t)}function _o(){Me.point=nt}function xd(){Me.point=kd}function Sd(){Vl(El,Rl)}function kd(e,t){Me.point=Vl,nt(El=Le=e,Rl=ze=t)}function Vl(e,t){var n=e-Le,r=t-ze,o=ot(n*n+r*r);vn+=o*(Le+e)/2,gn+=o*(ze+t)/2,st+=o,o=ze*e-Le*t,lr+=o*(Le+e),ar+=o*(ze+t),Ct+=o*3,nt(Le=e,ze=t)}function $l(e){this._context=e}$l.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,De);break}}},result:Ne};var ir=new et,Tn,Pl,Il,Et,Rt,qt={point:Ne,lineStart:function(){qt.point=Cd},lineEnd:function(){Tn&&Dl(Pl,Il),qt.point=Ne},polygonStart:function(){Tn=!0},polygonEnd:function(){Tn=null},result:function(){var e=+ir;return ir=new et,e}};function Cd(e,t){qt.point=Dl,Pl=Et=e,Il=Rt=t}function Dl(e,t){Et-=e,Rt-=t,ir.add(ot(Et*Et+Rt*Rt)),Et=e,Rt=t}let vo,yn,go,yo;class wo{constructor(t){this._append=t==null?Ml:Ed(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==go||this._append!==yn){const r=this._radius,o=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,go=r,yn=this._append,yo=this._,this._=o}this._+=yo;break}}}result(){const t=this._;return this._="",t.length?t:null}}function Ml(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function Ed(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return Ml;if(t!==vo){const n=10**t;vo=t,yn=function(o){let l=1;this._+=o[0];for(const a=o.length;l<a;++l)this._+=Math.round(arguments[l]*n)/n+o[l]}}return yn}function bo(e,t){let n=3,r=4.5,o,l;function a(s){return s&&(typeof r=="function"&&l.pointRadius(+r.apply(this,arguments)),it(s,o(l))),l.result()}return a.area=function(s){return it(s,o(He)),He.result()},a.measure=function(s){return it(s,o(qt)),qt.result()},a.bounds=function(s){return it(s,o(_n)),_n.result()},a.centroid=function(s){return it(s,o(Me)),Me.result()},a.projection=function(s){return arguments.length?(o=s==null?(e=null,jn):(e=s).stream,a):e},a.context=function(s){return arguments.length?(l=s==null?(t=null,new wo(n)):new $l(t=s),typeof r!="function"&&l.pointRadius(r),a):t},a.pointRadius=function(s){return arguments.length?(r=typeof s=="function"?s:(l.pointRadius(+s),+s),a):r},a.digits=function(s){if(!arguments.length)return n;if(s==null)n=null;else{const c=Math.floor(s);if(!(c>=0))throw new RangeError(`invalid digits: ${s}`);n=c}return t===null&&(l=new wo(n)),a},a.projection(e).digits(n).context(t)}function vr(e){return function(t){var n=new sr;for(var r in e)n[r]=e[r];return n.stream=t,n}}function sr(){}sr.prototype={constructor:sr,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function gr(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),it(n,e.stream(_n)),t(_n.result()),r!=null&&e.clipExtent(r),e}function Nl(e,t,n){return gr(e,function(r){var o=t[1][0]-t[0][0],l=t[1][1]-t[0][1],a=Math.min(o/(r[1][0]-r[0][0]),l/(r[1][1]-r[0][1])),s=+t[0][0]+(o-a*(r[1][0]+r[0][0]))/2,c=+t[0][1]+(l-a*(r[1][1]+r[0][1]))/2;e.scale(150*a).translate([s,c])},n)}function Rd(e,t,n){return Nl(e,[[0,0],t],n)}function Vd(e,t,n){return gr(e,function(r){var o=+t,l=o/(r[1][0]-r[0][0]),a=(o-l*(r[1][0]+r[0][0]))/2,s=-l*r[0][1];e.scale(150*l).translate([a,s])},n)}function $d(e,t,n){return gr(e,function(r){var o=+t,l=o/(r[1][1]-r[0][1]),a=-l*r[0][0],s=(o-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([a,s])},n)}var xo=16,Pd=xe(30*ke);function So(e,t){return+t?Dd(e,t):Id(e)}function Id(e){return vr({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function Dd(e,t){function n(r,o,l,a,s,c,p,i,u,d,h,b,P,_){var E=p-r,k=i-o,D=E*E+k*k;if(D>4*t&&P--){var N=a+d,B=s+h,F=c+b,U=ot(N*N+B*B+F*F),O=zt(F/=U),T=ve(ve(F)-1)<fe||ve(l-u)<fe?(l+u)/2:Lt(B,N),m=e(T,O),y=m[0],A=m[1],K=y-r,Q=A-o,C=k*K-E*Q;(C*C/D>t||ve((E*K+k*Q)/D-.5)>.3||a*d+s*h+c*b<Pd)&&(n(r,o,l,a,s,c,y,A,T,N/=U,B/=U,F,P,_),_.point(y,A),n(y,A,T,N,B,F,p,i,u,d,h,b,P,_))}}return function(r){var o,l,a,s,c,p,i,u,d,h,b,P,_={point:E,lineStart:k,lineEnd:N,polygonStart:function(){r.polygonStart(),_.lineStart=B},polygonEnd:function(){r.polygonEnd(),_.lineStart=k}};function E(O,T){O=e(O,T),r.point(O[0],O[1])}function k(){u=NaN,_.point=D,r.lineStart()}function D(O,T){var m=_t([O,T]),y=e(O,T);n(u,d,i,h,b,P,u=y[0],d=y[1],i=O,h=m[0],b=m[1],P=m[2],xo,r),r.point(u,d)}function N(){_.point=E,r.lineEnd()}function B(){k(),_.point=F,_.lineEnd=U}function F(O,T){D(o=O,T),l=u,a=d,s=h,c=b,p=P,_.point=D}function U(){n(u,d,i,h,b,P,l,a,o,s,c,p,xo,r),_.lineEnd=N,N()}return _}}var Md=vr({point:function(e,t){this.stream.point(e*ke,t*ke)}});function Nd(e){return vr({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function Ad(e,t,n,r,o){function l(a,s){return a*=r,s*=o,[t+e*a,n-e*s]}return l.invert=function(a,s){return[(a-t)/e*r,(n-s)/e*o]},l}function ko(e,t,n,r,o,l){if(!l)return Ad(e,t,n,r,o);var a=xe(l),s=Se(l),c=a*e,p=s*e,i=a/e,u=s/e,d=(s*n-a*t)/e,h=(s*t+a*n)/e;function b(P,_){return P*=r,_*=o,[c*P-p*_+t,n-p*P-c*_]}return b.invert=function(P,_){return[r*(i*P-u*_+d),o*(h-u*P-i*_)]},b}function Ud(e){return Td(function(){return e})()}function Td(e){var t,n=150,r=480,o=250,l=0,a=0,s=0,c=0,p=0,i,u=0,d=1,h=1,b=null,P=ho,_=null,E,k,D,N=jn,B=.5,F,U,O,T,m;function y(C){return O(C[0]*ke,C[1]*ke)}function A(C){return C=O.invert(C[0],C[1]),C&&[C[0]*Pe,C[1]*Pe]}y.stream=function(C){return T&&m===C?T:T=Md(Nd(i)(P(F(N(m=C)))))},y.preclip=function(C){return arguments.length?(P=C,b=void 0,Q()):P},y.postclip=function(C){return arguments.length?(N=C,_=E=k=D=null,Q()):N},y.clipAngle=function(C){return arguments.length?(P=+C?pd(b=C*ke):(b=null,ho),Q()):b*Pe},y.clipExtent=function(C){return arguments.length?(N=C==null?(_=E=k=D=null,jn):md(_=+C[0][0],E=+C[0][1],k=+C[1][0],D=+C[1][1]),Q()):_==null?null:[[_,E],[k,D]]},y.scale=function(C){return arguments.length?(n=+C,K()):n},y.translate=function(C){return arguments.length?(r=+C[0],o=+C[1],K()):[r,o]},y.center=function(C){return arguments.length?(l=C[0]%360*ke,a=C[1]%360*ke,K()):[l*Pe,a*Pe]},y.rotate=function(C){return arguments.length?(s=C[0]%360*ke,c=C[1]%360*ke,p=C.length>2?C[2]%360*ke:0,K()):[s*Pe,c*Pe,p*Pe]},y.angle=function(C){return arguments.length?(u=C%360*ke,K()):u*Pe},y.reflectX=function(C){return arguments.length?(d=C?-1:1,K()):d<0},y.reflectY=function(C){return arguments.length?(h=C?-1:1,K()):h<0},y.precision=function(C){return arguments.length?(F=So(U,B=C*C),Q()):ot(B)},y.fitExtent=function(C,X){return Nl(y,C,X)},y.fitSize=function(C,X){return Rd(y,C,X)},y.fitWidth=function(C,X){return Vd(y,C,X)},y.fitHeight=function(C,X){return $d(y,C,X)};function K(){var C=ko(n,0,0,d,h,u).apply(null,t(l,a)),X=ko(n,r-C[0],o-C[1],d,h,u);return i=yl(s,c,p),U=Zn(t,X),O=Zn(i,U),F=So(U,B),Q()}function Q(){return T=m=null,y}return function(){return t=e.apply(this,arguments),y.invert=t.invert&&A,K()}}function yr(e,t){return[e,td(rd(($e+t)/2))]}yr.invert=function(e,t){return[e,2*gl(ed(t))-$e]};function Od(){return Ld(yr).scale(961/De)}function Ld(e){var t=Ud(e),n=t.center,r=t.scale,o=t.translate,l=t.clipExtent,a=null,s,c,p;t.scale=function(u){return arguments.length?(r(u),i()):r()},t.translate=function(u){return arguments.length?(o(u),i()):o()},t.center=function(u){return arguments.length?(n(u),i()):n()},t.clipExtent=function(u){return arguments.length?(u==null?a=s=c=p=null:(a=+u[0][0],s=+u[0][1],c=+u[1][0],p=+u[1][1]),i()):a==null?null:[[a,s],[c,p]]};function i(){var u=ae*r(),d=t(ld(t.rotate()).invert([0,0]));return l(a==null?[[d[0]-u,d[1]-u],[d[0]+u,d[1]+u]]:e===yr?[[Math.max(d[0]-u,a),s],[Math.min(d[0]+u,c),p]]:[[a,Math.max(d[1]-u,s)],[c,Math.min(d[1]+u,p)]])}return i()}function Vt(e,t,n){this.k=e,this.x=t,this.y=n}Vt.prototype={constructor:Vt,scale:function(e){return e===1?this:new Vt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new Vt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};Vt.prototype;const zd={class:"clock-container"},Fd={class:"holographic-clock",viewBox:"0 0 100 100"},qd={class:"clock-markers"},Yd=["x1","y1","x2","y2"],Bd={class:"clock-hands"},Hd=["x2","y2"],Xd=["x2","y2"],Gd=["x2","y2"],Kd=["id"],Wd=["id"],Qd={__name:"VisualScreen",setup(e){const t=M(null),n=M(null),r=M(0),o=M(!1),l=new Map,a=M({x:0,y:0}),s=M(0),c=M(0),p=M(0),i=M(0);let u;const d=M([{id:"china",url:"/public/geo/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"/public/geo/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"/public/geo/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"/public/geo/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),h={longitude:106.43,latitude:30.55},b=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},P=()=>{const O=new Date;s.value=O.getHours()%12,c.value=O.getMinutes(),p.value=O.getSeconds(),i.value=O.getMilliseconds()},_=O=>{if(l.has(O.id))return;const T=b(),m=Ht(`#${O.id}-map`).attr("viewBox",`0 0 ${T.width} ${T.height}`).attr("preserveAspectRatio","xMidYMid meet");jc(O.url).then(y=>{const A=Od().center(O.center).scale(Math.min(T.width,T.height)*O.scaleFactor).translate([T.width/2,T.height/2]);l.set(O.id,{projection:A,data:y}),m.selectAll(".boundary").data(y.features).enter().append("path").attr("class","boundary").attr("d",bo().projection(A)),E(O.id)}).catch(y=>console.error("地图加载失败:",y))},E=O=>{const{projection:T}=l.get(O)||{};if(!T)return;const[m,y]=T([h.longitude,h.latitude]);a.value={x:m,y}},k=()=>{const O=b();d.value.forEach(T=>{const{projection:m,data:y}=l.get(T.id)||{};!m||!y||(m.scale(Math.min(O.width,O.height)*T.scaleFactor).translate([O.width/2,O.height/2]),Ht(`#${T.id}-map`).attr("viewBox",`0 0 ${O.width} ${O.height}`).selectAll(".boundary").attr("d",bo().projection(m)),T.id===d.value[r.value].id&&E(T.id))})},D=O=>{if(o.value)return;o.value=!0;const T=d.value.length,m=(r.value+O+T)%T,y=d.value[r.value],A=d.value[m];Ht(`#${y.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{Ht(`#${A.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=m,o.value=!1,E(A.id)})})},N=()=>{const{width:T,height:m}=b();for(let y=0;y<100;y++){const A=document.createElement("div");A.className="particle";const K=Math.random()*T,Q=Math.random()*m,C=Math.random()*10,X=Math.random()*2+1;A.style.left=`${K}px`,A.style.top=`${Q}px`,A.style.animationDelay=`${C}s`,A.style.width=`${X}px`,A.style.height=`${X}px`,n.value.appendChild(A)}};let B;const F=()=>{B=setInterval(()=>{D(1)},d.value[0].duration+1500)};Qe(()=>{P(),u=setInterval(P,50),d.value.forEach(_),N(),F(),document.addEventListener("fullscreenchange",U)}),Gl(()=>{clearInterval(u),clearInterval(B),l.clear(),document.removeEventListener("fullscreenchange",U)});const U=()=>{on(()=>{k(),n.value&&(n.value.innerHTML=""),N()})};return ut([()=>window.innerWidth,()=>window.innerHeight,()=>{var O;return(O=t.value)==null?void 0:O.clientWidth},()=>{var O;return(O=t.value)==null?void 0:O.clientHeight}],()=>{on(k)}),(O,T)=>(J(),le("div",{class:"holographic-container",ref_key:"container",ref:t},[T[3]||(T[3]=W("div",{class:"slogan-container"},[W("div",null,"对党忠诚  服务人民"),W("div",null,"执法公正  纪律严明")],-1)),T[4]||(T[4]=W("div",{class:"title-container"},[W("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),W("div",zd,[(J(),le("svg",Fd,[T[0]||(T[0]=W("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),T[1]||(T[1]=W("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),W("g",qd,[(J(),le(_e,null,ye(12,m=>W("line",{key:m,x1:50+38*Math.cos((m*30-90)*Math.PI/180),y1:50+38*Math.sin((m*30-90)*Math.PI/180),x2:50+42*Math.cos((m*30-90)*Math.PI/180),y2:50+42*Math.sin((m*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,Yd)),64))]),W("g",Bd,[W("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((s.value*30+c.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((s.value*30+c.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,Hd),W("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((c.value*6+p.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((c.value*6+p.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Xd),W("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((p.value*6+i.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((p.value*6+i.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,Gd)]),T[2]||(T[2]=W("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),W("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),T[5]||(T[5]=W("div",{class:"hologram-grid"},null,-1)),T[6]||(T[6]=W("div",{class:"scan-line-vertical"},null,-1)),T[7]||(T[7]=W("div",{class:"scan-line-horizontal"},null,-1)),T[8]||(T[8]=W("div",{class:"hologram-frame"},[W("div")],-1)),(J(!0),le(_e,null,ye(d.value,(m,y)=>(J(),le("div",{key:m.id,id:`${m.id}-container`,class:Kl(["map-container",{"map-visible":r.value===y}])},[(J(),le("svg",{id:`${m.id}-map`,class:"map-svg"},null,8,Wd)),r.value===y?(J(),le("div",{key:0,class:"location-marker",style:ur({left:a.value.x+"px",top:a.value.y+"px"})},null,4)):$t("",!0)],10,Kd))),128))],512))}},Zd={class:"app-management-container"},Jd={class:"clearfix"},jd={__name:"AppManagement",setup(e){const t=M([]),n=M([]),r=M(0),o=M(!1);M(!1),M("");const l=M(null),a=Ue({id:null,name:"",url:"",isPublic:"",roles:[]}),s=Ue({name:[{required:!0,message:"请输入应用名称",trigger:"blur"}],url:[{required:!0,message:"请输入应用URL",trigger:"blur"}],isPublic:[{required:!0,message:"请选择可见范围",trigger:"change"}],roles:[{type:"array",required:!0,validator:(_,E,k)=>{E&&E.length>0?k():k(new Error("至少选择一个角色组"))},trigger:"change"}]}),c=_=>{try{return JSON.parse(_).map(k=>{const D=n.value.find(N=>String(N.value)===String(k));return D?D.label:k})}catch(E){return console.error("解析 roleList 失败:",E),[]}},p=async()=>{try{const _=new FormData;_.append("controlCode","query");const E=await te.post("/api/application_manage.php",_);E.status===1&&(t.value=E.data.application,n.value=E.data.rolelist.map(k=>({value:k.id,label:k.roleName})),r.value=E.data.application.length)}catch(_){console.error("获取应用列表失败:",_),H.error("获取应用列表失败")}};Qe(()=>{p()});const i=_=>{switch(_){case"0":return"公开";case"1":return"非第三方人员";case"2":return"民警";case"3":return"授权用户";default:return"未知"}},u=()=>{h(),o.value=!0},d=_=>{if(h(),n.value.length===0){console.error("角色选项未加载完成"),H.error("角色数据加载中，请稍后再试");return}a.id=_.id,a.name=_.application_name,a.url=_.url,a.isPublic=_.public;try{const E=JSON.parse(_.roleList);a.roles=E.map(k=>String(k)),on(()=>{a.roles=[...a.roles]})}catch(E){console.error("解析角色列表失败:",E),a.roles=[]}o.value=!0},h=()=>{l.value&&l.value.resetFields(),a.id=null,a.name="",a.url="",a.isPublic="",a.roles=[]},b=_=>{Dt.confirm(`确定要删除应用 "${_.application_name}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const E=new FormData;E.append("controlCode","del"),E.append("id",_.id);const k=await te.post("/api/application_manage.php",E);k.status===1?(H.success("删除成功"),p()):H.error("删除失败: "+(k.message||"未知错误"))}catch(E){console.error("删除应用失败:",E),H.error("删除应用失败: "+E.message)}}).catch(()=>{H.info("已取消删除")})},P=async()=>{try{await l.value.validate()}catch(_){console.error("表单验证失败:",_);return}try{const _=new FormData;_.append("controlCode",a.id?"modify":"add"),_.append("id",a.id),_.append("application_name",a.name),_.append("url",a.url),_.append("public",a.isPublic),_.append("roleList",JSON.stringify(a.roles));const E=await te.post("/api/application_manage.php",_);E.status===1?(H.success("更新成功"),o.value=!1,p()):H.error(E.message||"更新失败")}catch(_){console.error("更新应用失败:",_),H.error("更新应用失败: "+_.message)}};return(_,E)=>{const k=z("el-button"),D=z("el-table-column"),N=z("el-tag"),B=z("el-icon"),F=z("el-button-group"),U=z("el-table"),O=z("el-card"),T=z("el-input"),m=z("el-form-item"),y=z("el-option"),A=z("el-select"),K=z("el-checkbox"),Q=z("el-checkbox-group"),C=z("el-form"),X=z("el-dialog");return J(),le("div",Zd,[f(O,{class:"box-card"},{header:v(()=>[W("div",Jd,[f(k,{style:{float:"right"},round:"",type:"primary",onClick:u},{default:v(()=>E[6]||(E[6]=[ne(" 添加应用 ")])),_:1,__:[6]})])]),default:v(()=>[f(U,{data:t.value,stripe:"",border:"",fit:"","highlight-current-row":"",onRowDblclick:d,style:{width:"100%"}},{default:v(()=>[f(D,{label:"序号",width:"80",align:"center"},{default:v(V=>[ne(Re(V.$index+1),1)]),_:1}),f(D,{prop:"application_name",label:"应用名称","min-width":"120"}),f(D,{prop:"url",label:"URL","min-width":"150"}),f(D,{prop:"public",label:"是否公开",width:"150",align:"center"},{default:v(V=>[ne(Re(i(V.row.public)),1)]),_:1}),f(D,{prop:"roles",label:"授权角色组","min-width":"180"},{default:v(V=>[(J(!0),le(_e,null,ye(c(V.row.roleList),w=>(J(),he(N,{key:w,size:"small",type:"info"},{default:v(()=>[ne(Re(w),1)]),_:2},1024))),128))]),_:1}),f(D,{label:"操作",width:"160",align:"center"},{default:v(V=>[f(F,null,{default:v(()=>[f(k,{size:"mini",type:"warning",onClick:w=>d(V.row)},{default:v(()=>[f(B,null,{default:v(()=>[f(de(wn))]),_:1})]),_:2},1032,["onClick"]),f(k,{size:"mini",type:"danger",onClick:w=>b(V.row)},{default:v(()=>[f(B,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1}),f(X,{modelValue:o.value,"onUpdate:modelValue":E[5]||(E[5]=V=>o.value=V),title:a.id?"编辑应用":"添加应用",width:"50%"},{footer:v(()=>[f(k,{onClick:E[4]||(E[4]=V=>o.value=!1)},{default:v(()=>E[7]||(E[7]=[ne("取消")])),_:1,__:[7]}),f(k,{type:"primary",onClick:P},{default:v(()=>E[8]||(E[8]=[ne("保存")])),_:1,__:[8]})]),default:v(()=>[f(C,{model:a,rules:s,ref_key:"formRef",ref:l,"label-width":"100px"},{default:v(()=>[f(m,{label:"应用名称",prop:"name"},{default:v(()=>[f(T,{modelValue:a.name,"onUpdate:modelValue":E[0]||(E[0]=V=>a.name=V),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1}),f(m,{label:"URL",prop:"url"},{default:v(()=>[f(T,{modelValue:a.url,"onUpdate:modelValue":E[1]||(E[1]=V=>a.url=V),placeholder:"请输入应用URL"},null,8,["modelValue"])]),_:1}),f(m,{label:"是否公开",prop:"isPublic"},{default:v(()=>[f(A,{modelValue:a.isPublic,"onUpdate:modelValue":E[2]||(E[2]=V=>a.isPublic=V),placeholder:"请选择是否公开"},{default:v(()=>[f(y,{value:"0",label:"公开"}),f(y,{value:"1",label:"非第三方人员"}),f(y,{value:"2",label:"民警"}),f(y,{value:"3",label:"授权用户"})]),_:1},8,["modelValue"])]),_:1}),f(m,{label:"授权角色组",prop:"roles"},{default:v(()=>[f(Q,{modelValue:a.roles,"onUpdate:modelValue":E[3]||(E[3]=V=>a.roles=V)},{default:v(()=>[(J(!0),le(_e,null,ye(n.value,V=>(J(),he(K,{key:V.value,label:V.value},{default:v(()=>[ne(Re(V.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}},ef=rt(jd,[["__scopeId","data-v-2a67e87b"]]),tf={class:"container"},nf={class:"left-section"},rf={class:"query-bar"},of={class:"user-table"},lf={class:"pagination"},af={class:"right-section"},sf={class:"query-bar"},uf={class:"authorization-table"},Co=10,cf={__name:"PowerManagement",setup(e){const t=M([]);M("");const n=M(""),r=M(""),o=M(1),l=M(0),a=M([]),s=M([]),c=M([]),p=M([]),i=M([]),u=M(!1),d=M(!1),h=M("add"),b=M(null),P=Ue({unitId:null,unitIdPath:[]}),_=Ue({id:null,userId:null,userName:"",appId:null,roleId:null,unitIdPath:[],unitId:null});Ce(()=>_.appId?p.value.filter(V=>V.applicationId==_.appId):[]);const E={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"};Qe(()=>{k(),D(),N()});const k=async()=>{try{const V=new FormData;V.append("controlCode","query");const w=await te.post("/api/application_manage.php",V);w.status===1&&(c.value=w.data.application)}catch(V){console.error("获取应用列表失败:",V),H.error("获取应用列表失败")}},D=async()=>{try{const V=await te.post("api/get_unit_info.php");s.value=V.data?[V.data]:[]}catch(V){console.error("获取单位数据失败:",V),H.error("获取单位数据失败")}},N=async()=>{try{const V=new FormData;V.append("controlCode","query"),V.append("page",o.value),V.append("pagesize",Co),r.value&&V.append("search_keyword",r.value),P.unitId&&V.append("organization_unit",P.unitId);const w=await te.post("/api/user_manage.php",V);w.status===1?(l.value=w.total,a.value=w.data.map(R=>{const q=C(s.value,R.organization_unit);return{...R,unit_name:q?q.unit_name:"未知单位"}})):H.error("获取用户数据失败")}catch(V){console.error("查询用户失败:",V),H.error("查询用户失败")}},B=V=>{V&&V.length>0?P.unitId=V[V.length-1]:P.unitId=null,console.log("Selected Unit ID:",P.unitId),N()},F=V=>{o.value=V,N()},U=async()=>{if(!n.value){H.warning("请先选择应用");return}const V=new FormData;V.append("controlCode","getAppUser"),V.append("appId",n.value);try{const w=await te.post("/api/user_Role_manage.php",V);console.log("响应",w),w.status===1?a.value=w.data||[]:H.error("查询授权信息失败")}catch(w){console.error("查询授权失败:",w),H.error("查询授权失败")}},O=async V=>{const w=new FormData;w.append("controlCode","query"),w.append("userId",V);try{const R=await te.post("/api/user_Role_manage.php",w);R.status===1?i.value=R.data.map(q=>{const L=c.value.find(we=>we.id==q.appId),j=C(s.value,q.unitId),se=p.value.find(we=>we.id==q.roleId);return{id:q.id,appName:L?L.application_name:"未知应用",unitName:j?j.unit_name:"未知单位",roleName:se?se.roleName:"未知角色",...q}}):H.error("获取用户授权失败")}catch(R){console.error("获取用户授权失败:",R),H.error("获取用户授权失败")}},T=V=>{if(h.value="add",_.id=null,_.userId=V.id,_.userName=V.name,_.appId=null,_.roleId=null,_.unitIdPath=[],_.unitId=null,V.organization_unit){const w=X(s.value,V.organization_unit);_.unitIdPath=w,_.unitId=V.organization_unit}u.value=!0},m=V=>{h.value="edit",_.id=V.id,_.userId=V.userId,_.userName=V.userName||"",_.appId=String(V.appId),y(),_.roleId=V.roleId;const w=X(s.value,V.unitId);_.unitIdPath=w,_.unitId=V.unitId,u.value=!0},y=async()=>{const V=_.appId;if(!V){t.value=[];return}try{const w=new FormData;w.append("controlCode","query"),w.append("id",V);const R=await te.post("/api/application_manage.php",w);R.status===1?t.value=R.data.rolelist.map(q=>({id:q.id,roleName:q.roleName})):(H.error("获取角色列表失败"),t.value=[])}catch(w){console.error("加载角色失败:",w),H.error("加载角色失败"),t.value=[]}},A=async()=>{if(_.unitIdPath&&_.unitIdPath.length>0)_.unitId=_.unitIdPath[_.unitIdPath.length-1];else{H.warning("请选择单位");return}if(!_.roleId){H.warning("请选择角色");return}try{const V=h.value==="add"?"add":"modify",w=new FormData;w.append("controlCode",V),w.append("userId",_.userId),w.append("appId",_.appId),w.append("unitId",_.unitId),w.append("roleId",_.roleId),h.value==="edit"&&w.append("id",_.id),(await te.post("/api/user_Role_manage.php",w)).status===1?(H.success("授权保存成功"),u.value=!1,h.value==="add"?O(_.userId):U()):H.error("授权保存失败")}catch(V){console.error("保存授权失败:",V),H.error("保存授权失败")}},K=V=>{b.value=V,d.value=!0},Q=async()=>{try{const V=new FormData;V.append("controlCode","del"),V.append("id",b.value),(await te.post("/api/user_Role_manage.php",V)).status===1?(H.success("授权删除成功"),d.value=!1,O()):H.error("授权删除失败")}catch(V){console.error("删除授权失败:",V),H.error("删除授权失败")}},C=(V,w)=>{for(const R of V){if(R.id==w)return R;if(R.children){const q=C(R.children,w);if(q)return q}}return null},X=(V,w,R=[])=>{for(const q of V){const L=[...R,q.id];if(q.id==w)return L;if(q.children&&q.children.length>0){const j=X(q.children,w,L);if(j)return j}}return null};return(V,w)=>{const R=z("el-cascader"),q=z("el-input"),L=z("el-button"),j=z("el-table-column"),se=z("el-table"),we=z("el-pagination"),Z=z("el-option"),ce=z("el-select"),$=z("el-form-item"),G=z("el-form"),Y=z("el-dialog");return J(),le("div",tf,[W("div",nf,[W("div",rf,[f(R,{modelValue:P.unitIdPath,"onUpdate:modelValue":w[0]||(w[0]=x=>P.unitIdPath=x),options:s.value,props:E,onChange:B,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择单位"},null,8,["modelValue","options"]),f(q,{modelValue:r.value,"onUpdate:modelValue":w[1]||(w[1]=x=>r.value=x),placeholder:"输入姓名、警号或身份证号",class:"input-user"},null,8,["modelValue"]),f(L,{type:"primary",onClick:N},{default:v(()=>w[11]||(w[11]=[ne("查询用户")])),_:1,__:[11]})]),W("div",of,[w[14]||(w[14]=W("h4",null,"用户列表",-1)),f(se,{data:a.value,"empty-text":"No Data",class:"table"},{default:v(()=>[f(j,{prop:"unit_name",label:"单位"}),f(j,{prop:"name",label:"姓名"}),f(j,{prop:"police_number",label:"警号"}),f(j,{prop:"id_number",label:"身份证号"}),f(j,{prop:"personnel_type",label:"身份"}),f(j,{label:"操作",width:"180"},{default:v(({row:x})=>[f(L,{size:"small",onClick:ie=>T(x)},{default:v(()=>w[12]||(w[12]=[ne("添加权限")])),_:2,__:[12]},1032,["onClick"]),f(L,{size:"small",onClick:ie=>O(x.id),type:"success"},{default:v(()=>w[13]||(w[13]=[ne("查看权限")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),W("div",lf,[f(we,{layout:"prev, pager, next","current-page":o.value,"page-size":Co,total:l.value,onCurrentChange:F},null,8,["current-page","total"])])])]),W("div",af,[W("div",sf,[f(ce,{modelValue:n.value,"onUpdate:modelValue":w[2]||(w[2]=x=>n.value=x),placeholder:"选择应用",class:"select-app"},{default:v(()=>[f(Z,{label:"选择应用",value:""}),(J(!0),le(_e,null,ye(c.value,x=>(J(),he(Z,{key:x.id,label:x.application_name,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),f(L,{type:"primary",onClick:U},{default:v(()=>w[15]||(w[15]=[ne("查询授权人员")])),_:1,__:[15]})]),W("div",uf,[w[18]||(w[18]=W("h4",null,"授权信息",-1)),f(se,{data:i.value,"empty-text":"No Data",class:"table"},{default:v(()=>[f(j,{prop:"appName",label:"授权应用"}),f(j,{prop:"unitName",label:"授权单位"}),f(j,{prop:"roleName",label:"授权角色"}),f(j,{label:"操作",width:"150"},{default:v(({row:x})=>[f(L,{size:"small",onClick:ie=>m(x)},{default:v(()=>w[16]||(w[16]=[ne("修改")])),_:2,__:[16]},1032,["onClick"]),f(L,{size:"small",onClick:ie=>K(x.id),type:"danger"},{default:v(()=>w[17]||(w[17]=[ne("删除")])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),f(Y,{modelValue:u.value,"onUpdate:modelValue":w[8]||(w[8]=x=>u.value=x),title:h.value==="add"?"添加授权":"修改授权"},{footer:v(()=>[f(L,{onClick:w[7]||(w[7]=x=>u.value=!1)},{default:v(()=>w[19]||(w[19]=[ne("取消")])),_:1,__:[19]}),f(L,{type:"primary",onClick:A},{default:v(()=>w[20]||(w[20]=[ne("确认")])),_:1,__:[20]})]),default:v(()=>[f(G,{model:_,"label-width":"120px"},{default:v(()=>[f($,{label:"用户姓名",prop:"userName"},{default:v(()=>[f(q,{modelValue:_.userName,"onUpdate:modelValue":w[3]||(w[3]=x=>_.userName=x),disabled:""},null,8,["modelValue"])]),_:1}),f($,{label:"应用选择",prop:"appId",required:""},{default:v(()=>[f(ce,{modelValue:_.appId,"onUpdate:modelValue":w[4]||(w[4]=x=>_.appId=x),onChange:y,placeholder:"请选择应用"},{default:v(()=>[(J(!0),le(_e,null,ye(c.value,x=>(J(),he(Z,{key:x.id,label:x.application_name,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f($,{label:"单位选择",prop:"unitId",required:""},{default:v(()=>[f(R,{modelValue:_.unitIdPath,"onUpdate:modelValue":w[5]||(w[5]=x=>_.unitIdPath=x),options:s.value,props:E,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择单位"},null,8,["modelValue","options"])]),_:1}),f($,{label:"角色选择",prop:"roleId",required:""},{default:v(()=>[f(ce,{modelValue:_.roleId,"onUpdate:modelValue":w[6]||(w[6]=x=>_.roleId=x),placeholder:"请选择角色"},{default:v(()=>[(J(!0),le(_e,null,ye(t.value,x=>(J(),he(Z,{key:x.id,label:x.roleName,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),f(Y,{modelValue:d.value,"onUpdate:modelValue":w[10]||(w[10]=x=>d.value=x),title:"确认删除",width:"30%"},{footer:v(()=>[f(L,{onClick:w[9]||(w[9]=x=>d.value=!1)},{default:v(()=>w[21]||(w[21]=[ne("取消")])),_:1,__:[21]}),f(L,{type:"danger",onClick:Q},{default:v(()=>w[22]||(w[22]=[ne("确认删除")])),_:1,__:[22]})]),default:v(()=>[w[23]||(w[23]=W("p",null,"确定要删除此授权信息吗？",-1))]),_:1,__:[23]},8,["modelValue"])])}}},df=rt(cf,[["__scopeId","data-v-1ed7837f"]]),ff={class:"role-management-container"},pf={class:"toolbar flex justify-end items-center mb-4"},hf={__name:"RoleManagement",setup(e){const t=M([]),n=M(null),r=async()=>{try{const i=new FormData;i.append("controlCode","query");const u=await te.post("/api/role_List_manage.php",i);u.status===1?t.value=u.data.map(d=>({id:d.id,name:d.roleName,desc:d.roleDesc})):H.error(u.message||"获取角色列表失败")}catch(i){console.error("获取角色列表失败:",i),H.error("获取角色列表失败")}};Qe(()=>{r()});const o=M(!1),l=Ue({id:null,name:"",desc:""}),a=()=>{n.value&&n.value.resetFields(),l.id=null,l.name="",l.desc="",o.value=!0},s=i=>{n.value&&n.value.resetFields(),l.id=i.id,l.name=i.name,l.desc=i.desc,o.value=!0},c=i=>{Dt.confirm(`确定要删除角色 "${i.name}" 吗？`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const u=new FormData;u.append("controlCode","del"),u.append("id",i.id);const d=await te.post("/api/role_List_manage.php",u);d.status===1?(H.success("删除成功"),r()):H.error(d.message||"删除失败")}catch(u){console.error("删除角色失败:",u),H.error("删除角色失败: "+u.message)}}).catch(()=>{H.info("已取消删除")})},p=async()=>{if(!l.name){H.error("角色名称不能为空");return}try{const i=new FormData;i.append("controlCode",l.id?"modify":"add"),i.append("roleName",l.name),i.append("roleDesc",l.desc),l.id&&i.append("id",l.id);const u=await te.post("/api/role_List_manage.php",i);u.status===1?(H.success(l.id?"更新成功":"添加成功"),o.value=!1,r()):H.error(u.message||(l.id?"更新失败":"添加失败"))}catch(i){console.error("操作失败:",i),H.error("操作失败: "+i.message)}};return(i,u)=>{const d=z("el-icon"),h=z("el-button"),b=z("el-table-column"),P=z("el-button-group"),_=z("el-table"),E=z("el-input"),k=z("el-form-item"),D=z("el-form"),N=z("el-dialog");return J(),le("div",ff,[W("div",pf,[f(h,{type:"primary",onClick:a},{default:v(()=>[f(d,null,{default:v(()=>[f(de($o))]),_:1}),u[4]||(u[4]=ne(" 添加角色 "))]),_:1,__:[4]})]),f(_,{data:t.value,style:{width:"100%"},border:""},{default:v(()=>[f(b,{label:"序号",width:"80",align:"center"},{default:v(B=>[ne(Re(B.$index+1),1)]),_:1}),f(b,{prop:"name",label:"角色名称"}),f(b,{prop:"desc",label:"角色描述"}),f(b,{label:"操作"},{default:v(B=>[f(P,null,{default:v(()=>[f(h,{size:"mini",type:"warning",onClick:F=>s(B.row)},{default:v(()=>[f(d,null,{default:v(()=>[f(de(wn))]),_:1})]),_:2},1032,["onClick"]),f(h,{size:"mini",type:"danger",onClick:F=>c(B.row)},{default:v(()=>[f(d,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),f(N,{modelValue:o.value,"onUpdate:modelValue":u[3]||(u[3]=B=>o.value=B),width:"20%"},{default:v(()=>[f(D,{model:l,"label-width":"100px",ref_key:"formRef",ref:n},{default:v(()=>[f(k,{label:"角色名称",prop:"name"},{default:v(()=>[f(E,{modelValue:l.name,"onUpdate:modelValue":u[0]||(u[0]=B=>l.name=B)},null,8,["modelValue"])]),_:1}),f(k,{label:"角色描述",prop:"desc"},{default:v(()=>[f(E,{modelValue:l.desc,"onUpdate:modelValue":u[1]||(u[1]=B=>l.desc=B),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),footer:v(()=>[f(h,{onClick:u[2]||(u[2]=B=>o.value=!1)},{default:v(()=>u[5]||(u[5]=[ne("取消")])),_:1,__:[5]}),f(h,{type:"primary",onClick:p},{default:v(()=>u[6]||(u[6]=[ne("确定")])),_:1,__:[6]})]),_:1},8,["modelValue"])])}}},mf=rt(hf,[["__scopeId","data-v-2a4abc11"]]),_f={class:"duty-management"},vf={__name:"DutyManagement",setup(e){const t=M([]),n=M(!1),r=M([]),o=M(!1),l=M([]),a=M(!1),s=M([]),c=M(!1),p=M([]),i=M(!1),u=M([]),d=M(!1),h=M(1),b=M(10),P=M(0),_=Ue({time:"",aLeader:"",bLeader:"",aCommander:"",bCommander:"",dutyLeader:"",dutyStaff:[],techRoom:"",innerSecurity:[]}),E=M(!1),k=M(""),D=Ue([]),N=M({}),B=async w=>{if(!w){t.value=[];return}n.value=!0;try{const R=new FormData;R.append("controlCode","query"),R.append("search_keyword",w);const q=await te.post("/api/user_manage.php",R);q.status===1?t.value=q.data:t.value=[]}catch{t.value=[]}finally{n.value=!1}},F=async()=>{const w=new FormData;w.append("controlCode","query"),w.append("page",h.value),w.append("pagesize",b.value);try{const R=await te.post("/DutySched/api/sched_manage.php",w);if(R.status===1){P.value=R.totalCount;const q=new Map(R.userInfo.map(j=>[j.userId,j.userName])),L=R.data.map(j=>({id:j.id,schedDate:j.sched_date,aDir:q.get(j.a_dir)||"未知用户",bDir:q.get(j.b_dir)||"未知用户",aCom:q.get(j.a_com)||"未知用户",bCom:q.get(j.b_com)||"未知用户",sub:q.get(j.sub)||"未知用户",op:j.op.map(se=>q.get(se)||"未知用户").join("、"),tech:q.get(j.tech)||"未知用户",sec:j.sec.map(se=>q.get(se)||"未知用户").join("、")}));D.length=0,D.push(...L)}else H.error(R.message||"获取值班记录失败")}catch{H.error("网络请求失败")}},U=w=>{h.value=w,F()},O=w=>{b.value=w,F()},T=async()=>{const w=new FormData;w.append("controlCode","getRole");const R=await te.post("/DutySched/api/sched_manage.php",w);if(R.status===1){const q={};R.data.forEach(L=>{q[L.roleName]=L.id}),N.value=q}},m=w=>{E.value=!0,k.value="新增值班记录",console.log("获取局领导角色ID:",N.value.局领导),A(N.value.局领导),K(N.value.指挥长),Q(N.value.值班长),C(N.value.技术室),X(N.value.值班员)};Qe(()=>{F(),T()});const y=async w=>{if(await H.confirm("确定删除该值班记录？","提示",{type:"warning"})==="confirm")try{const q=new FormData;q.append("controlCode","delete"),q.append("id",w.id);const L=await te.post("/DutySched/api/sched_manage.php",q);if(L.status===1){const j=D.findIndex(se=>se.id===w.id);j!==-1&&(D.splice(j,1),H.success("删除成功"))}else H.error("删除失败："+L.msg)}catch{H.error("删除请求失败")}},A=async w=>{o.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",w);const q=await te.post("/DutySched/api/sched_manage.php",R);q.status===1&&(r.value=q.data.map(L=>({value:L.userId,label:`${L.name} (${L.unitName})`})),console.log("局领导用户列表:",r.value))}catch(R){console.error("获取角色用户失败:",R)}finally{o.value=!1}},K=async w=>{a.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",w);const q=await te.post("/DutySched/api/sched_manage.php",R);q.status===1&&(l.value=q.data.map(L=>({value:L.userId,label:`${L.name} (${L.unitName})`})))}catch(R){console.error("获取指挥长用户失败:",R)}finally{a.value=!1}},Q=async w=>{c.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",w);const q=await te.post("/DutySched/api/sched_manage.php",R);s.value=q.data.map(L=>({value:L.userId,label:`${L.name}(${L.unitName})`}))}catch(R){console.error("获取值班长列表失败:",R)}finally{c.value=!1}},C=async w=>{i.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",w);const q=await te.post("/DutySched/api/sched_manage.php",R);p.value=q.data.map(L=>({value:L.userId,label:`${L.name}(${L.unitName})`}))}catch(R){console.error("获取技术室人员列表失败:",R)}finally{i.value=!1}},X=async w=>{d.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",w);const q=await te.post("/DutySched/api/sched_manage.php",R);u.value=q.data.map(L=>({value:L.userId,label:`${L.name}(${L.unitName})`})),console.log("值班员",u.value)}catch(R){console.error("获取值班员列表失败:",R)}finally{d.value=!1}},V=async()=>{try{const w=new FormData;w.append("controlCode","add"),w.append("sched_date",_.time),w.append("a_dir",_.aLeader),w.append("b_dir",_.bLeader),w.append("a_com",_.aCommander),w.append("b_com",_.bCommander),w.append("sub",_.dutyLeader),console.log("值班员",_.dutyStaff),w.append("op",_.dutyStaff.join(",")),w.append("tech",_.techRoom),w.append("sec",_.innerSecurity.join(","));const R=await te.post("/DutySched/api/sched_manage.php",w);R.status===1?(H.success("保存成功"),_.time="",_.aLeader="",_.bLeader="",_.aCommander="",_.bCommander="",_.dutyLeader="",_.dutyStaff=[],_.techRoom="",_.innerSecurity=[],h.value=1,E.value=!1,F()):H.error(R.message||"保存失败")}catch(w){console.error("保存失败:",w),H.error("保存失败，请稍后重试")}};return(w,R)=>{const q=z("el-button"),L=z("el-table-column"),j=z("el-table"),se=z("el-pagination"),we=z("el-date-picker"),Z=z("el-form-item"),ce=z("el-option"),$=z("el-select"),G=z("el-form"),Y=z("el-dialog");return J(),le("div",_f,[f(q,{type:"primary",onClick:R[0]||(R[0]=x=>m())},{default:v(()=>R[12]||(R[12]=[ne("新增记录")])),_:1,__:[12]}),f(j,{data:D,style:{width:"100%"},border:""},{default:v(()=>[f(L,{prop:"schedDate",label:"值班日期",width:"120"}),f(L,{prop:"aDir",label:"A岗领导",width:"100"}),f(L,{prop:"bDir",label:"B岗领导",width:"100"}),f(L,{prop:"aCom",label:"A岗指挥长",width:"120"}),f(L,{prop:"bCom",label:"B岗指挥长",width:"120"}),f(L,{prop:"sub",label:"值班员",width:"100"}),f(L,{prop:"op",label:"操作人",width:"180"}),f(L,{prop:"tech",label:"技术室",width:"100"}),f(L,{prop:"sec",label:"内部安全值班"}),f(L,{label:"操作",width:"100"},{default:v(x=>[f(q,{type:"danger",size:"small",onClick:ie=>y(x.row)},{default:v(()=>R[13]||(R[13]=[ne(" 删除 ")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),f(se,{"current-page":h.value,"page-size":b.value,total:P.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:U,onSizeChange:O},null,8,["current-page","page-size","total"]),f(Y,{modelValue:E.value,"onUpdate:modelValue":R[11]||(R[11]=x=>E.value=x),title:k.value,width:"60%"},{footer:v(()=>[f(q,{onClick:R[10]||(R[10]=x=>E.value=!1)},{default:v(()=>R[14]||(R[14]=[ne("取消")])),_:1,__:[14]}),f(q,{type:"primary",onClick:V},{default:v(()=>R[15]||(R[15]=[ne("保存")])),_:1,__:[15]})]),default:v(()=>[f(G,{model:_,"label-width":"120px"},{default:v(()=>[f(Z,{label:"时间"},{default:v(()=>[f(we,{modelValue:_.time,"onUpdate:modelValue":R[1]||(R[1]=x=>_.time=x),type:"date",placeholder:"选择时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(Z,{label:"局领导"},{default:v(()=>[f($,{modelValue:_.aLeader,"onUpdate:modelValue":R[2]||(R[2]=x=>_.aLeader=x),placeholder:"请选择局领导",loading:o.value,options:r.value,filterable:"",clearable:""},{default:v(()=>[(J(!0),le(_e,null,ye(r.value,x=>(J(),he(ce,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(Z,{label:"B岗局领导"},{default:v(()=>[f($,{modelValue:_.bLeader,"onUpdate:modelValue":R[3]||(R[3]=x=>_.bLeader=x),placeholder:"请选择B岗局领导",loading:o.value,options:r.value,filterable:"",clearable:""},{default:v(()=>[(J(!0),le(_e,null,ye(r.value,x=>(J(),he(ce,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(Z,{label:"A岗指挥长"},{default:v(()=>[f($,{modelValue:_.aCommander,"onUpdate:modelValue":R[4]||(R[4]=x=>_.aCommander=x),placeholder:"请选择A岗指挥长",loading:a.value,options:l.value,filterable:"",clearable:""},{default:v(()=>[(J(!0),le(_e,null,ye(l.value,x=>(J(),he(ce,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(Z,{label:"B岗指挥长"},{default:v(()=>[f($,{modelValue:_.bCommander,"onUpdate:modelValue":R[5]||(R[5]=x=>_.bCommander=x),placeholder:"请选择B岗指挥长",loading:a.value,options:l.value,filterable:"",clearable:""},{default:v(()=>[(J(!0),le(_e,null,ye(l.value,x=>(J(),he(ce,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(Z,{label:"值班长"},{default:v(()=>[f($,{modelValue:_.dutyLeader,"onUpdate:modelValue":R[6]||(R[6]=x=>_.dutyLeader=x),placeholder:"请选择值班长",loading:c.value,options:s.value,filterable:"",clearable:""},{default:v(()=>[(J(!0),le(_e,null,ye(s.value,x=>(J(),he(ce,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(Z,{label:"值班员"},{default:v(()=>[f($,{modelValue:_.dutyStaff,"onUpdate:modelValue":R[7]||(R[7]=x=>_.dutyStaff=x),options:u.value,loading:d.value,multiple:"",filterable:"",clearable:"",ip:"",placeholder:"请选择值班员"},{default:v(()=>[(J(!0),le(_e,null,ye(u.value,x=>(J(),he(ce,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","options","loading"])]),_:1}),f(Z,{label:"技术室"},{default:v(()=>[f($,{modelValue:_.techRoom,"onUpdate:modelValue":R[8]||(R[8]=x=>_.techRoom=x),placeholder:"请选择技术员",loading:i.value,options:p.value,filterable:"",clearable:""},{default:v(()=>[(J(!0),le(_e,null,ye(p.value,x=>(J(),he(ce,{key:x.value,label:x.label,value:x.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(Z,{label:"内部安全值班"},{default:v(()=>[f($,{modelValue:_.innerSecurity,"onUpdate:modelValue":R[9]||(R[9]=x=>_.innerSecurity=x),multiple:"",filterable:"",remote:"","remote-method":B,loading:n.value,placeholder:"请选择/搜索人员",style:{width:"300px"}},{default:v(()=>[(J(!0),le(_e,null,ye(t.value,x=>(J(),he(ce,{key:x.id,label:x.name,value:x.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},gf=rt(vf,[["__scopeId","data-v-2539861b"]]),yf=[{path:"/unit-management",name:"UnitManagement",component:vi},{path:"/user-management",name:"UserManagement",component:Ai},{path:"/visual-screen",name:"VisualScreen",component:Qd},{path:"/app-management",name:"AppManagement",component:ef},{path:"/role-management",name:"RoleManagement",component:mf},{path:"/power-management",name:"PowerManagement",component:df},{path:"/duty-management",name:"DutyManagement",component:gf}],wf=ei({history:$a(),routes:yf}),Rn=Wl(pi);Rn.use(Ql);for(const[e,t]of Object.entries(Zl))Rn.component(e,t);Rn.use(wf);Rn.mount("#app");
