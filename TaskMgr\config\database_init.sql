-- TaskMgr 计划任务管理器数据库初始化脚本
-- 适用于MySQL 5.7+ / MariaDB 10.2+

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `taskmgr` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `taskmgr`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `email` varchar(100) DEFAULT NULL,
    `role` enum('admin','user') NOT NULL DEFAULT 'user',
    `status` enum('active','inactive') NOT NULL DEFAULT 'active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON> (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建任务表
CREATE TABLE IF NOT EXISTS `tasks` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `description` text,
    `command` text NOT NULL,
    `cron_expression` varchar(100) NOT NULL,
    `status` enum('enabled','disabled','running','completed','failed') NOT NULL DEFAULT 'enabled',
    `user_id` int(11) NOT NULL,
    `last_run_at` timestamp NULL DEFAULT NULL,
    `next_run_at` timestamp NULL DEFAULT NULL,
    `run_count` int(11) NOT NULL DEFAULT 0,
    `success_count` int(11) NOT NULL DEFAULT 0,
    `failure_count` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_next_run` (`next_run_at`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建任务执行日志表
CREATE TABLE IF NOT EXISTS `task_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `task_id` int(11) NOT NULL,
    `status` enum('running','success','failed','timeout') NOT NULL,
    `output` longtext,
    `error_message` text,
    `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `end_time` timestamp NULL DEFAULT NULL,
    `execution_time` decimal(10,3) DEFAULT NULL COMMENT '执行时间(秒)',
    `memory_usage` int(11) DEFAULT NULL COMMENT '内存使用(字节)',
    `exit_code` int(11) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_task_id` (`task_id`),
    KEY `idx_status` (`status`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建系统设置表
CREATE TABLE IF NOT EXISTS `settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `key` varchar(100) NOT NULL UNIQUE,
    `value` text,
    `description` varchar(255) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建会话表
CREATE TABLE IF NOT EXISTS `sessions` (
    `id` varchar(128) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text,
    `payload` longtext NOT NULL,
    `last_activity` int(11) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO `users` (`username`, `password`, `email`, `role`, `status`) VALUES
('admin', '$argon2id$v=19$m=65536,t=4,p=3$YWRtaW4xMjM$8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8K8', '<EMAIL>', 'admin', 'active')
ON DUPLICATE KEY UPDATE `password` = VALUES(`password`);

-- 插入默认系统设置
INSERT INTO `settings` (`key`, `value`, `description`) VALUES
('system_name', 'TaskMgr', '系统名称'),
('max_concurrent_tasks', '5', '最大并发任务数'),
('log_retention_days', '30', '日志保留天数'),
('email_notifications', '0', '邮件通知开关'),
('timezone', 'Asia/Shanghai', '系统时区')
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`);
