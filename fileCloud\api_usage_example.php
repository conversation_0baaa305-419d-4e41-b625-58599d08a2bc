<?php
/**
 * 管理员批量删除文件API使用示例
 * 创建时间: 2025-07-08
 */

// 示例1：软删除文件
function softDeleteFiles($fileIds) {
    $postData = [
        'csrf_token' => $_SESSION['csrf_token'], // 需要有效的CSRF令牌
        'file_ids' => implode(',', $fileIds),    // 文件ID列表，如 "1,2,3"
        'delete_type' => 'soft',                 // 删除类型：软删除
        'confirmed' => 'true'                    // 必须确认
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'api/admin_batch_delete_files.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// 示例2：硬删除文件（永久删除）
function hardDeleteFiles($fileIds) {
    $postData = [
        'csrf_token' => $_SESSION['csrf_token'], // 需要有效的CSRF令牌
        'file_ids' => implode(',', $fileIds),    // 文件ID列表，如 "1,2,3"
        'delete_type' => 'hard',                 // 删除类型：硬删除
        'confirmed' => 'true',                   // 基础确认
        'hard_confirmed' => 'true'               // 硬删除额外确认
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'api/admin_batch_delete_files.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response, true);
}

// 示例3：JavaScript/AJAX调用
?>
<script>
// 软删除示例
function softDeleteFilesAjax(fileIds) {
    const formData = new FormData();
    formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');
    formData.append('file_ids', fileIds.join(','));
    formData.append('delete_type', 'soft');
    formData.append('confirmed', 'true');
    
    fetch('api/admin_batch_delete_files.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('软删除成功:', data.message);
            console.log('删除的文件:', data.deleted_files);
        } else {
            console.error('删除失败:', data.message);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
    });
}

// 硬删除示例
function hardDeleteFilesAjax(fileIds) {
    const formData = new FormData();
    formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');
    formData.append('file_ids', fileIds.join(','));
    formData.append('delete_type', 'hard');
    formData.append('confirmed', 'true');
    formData.append('hard_confirmed', 'true');
    
    fetch('api/admin_batch_delete_files.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('硬删除成功:', data.message);
            console.log('删除的文件:', data.deleted_files);
            console.log('清理的数据表:', data.deleted_files[0].cleaned_tables);
        } else {
            console.error('删除失败:', data.message);
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
    });
}

// 使用示例
// softDeleteFilesAjax([1, 2, 3]);  // 软删除文件ID 1,2,3
// hardDeleteFilesAjax([4, 5, 6]);  // 硬删除文件ID 4,5,6
</script>

<?php
// 示例4：表单提交方式
?>
<form method="POST" action="api/admin_batch_delete_files.php">
    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
    <input type="text" name="file_ids" placeholder="文件ID，用逗号分隔" required>
    
    <select name="delete_type" required>
        <option value="soft">软删除</option>
        <option value="hard">硬删除</option>
    </select>
    
    <label>
        <input type="checkbox" name="confirmed" value="true" required>
        我确认要删除这些文件
    </label>
    
    <label id="hardConfirmLabel" style="display: none;">
        <input type="checkbox" name="hard_confirmed" value="true">
        我确认要永久删除这些文件（无法恢复）
    </label>
    
    <button type="submit">删除文件</button>
</form>

<script>
// 显示/隐藏硬删除确认
document.querySelector('select[name="delete_type"]').addEventListener('change', function() {
    const hardConfirmLabel = document.getElementById('hardConfirmLabel');
    if (this.value === 'hard') {
        hardConfirmLabel.style.display = 'block';
        hardConfirmLabel.querySelector('input').required = true;
    } else {
        hardConfirmLabel.style.display = 'none';
        hardConfirmLabel.querySelector('input').required = false;
    }
});
</script>

<?php
/**
 * 重要说明：
 * 
 * 1. 确认参数是必需的安全机制：
 *    - confirmed: 'true' - 基础删除确认
 *    - hard_confirmed: 'true' - 硬删除额外确认（仅硬删除时需要）
 * 
 * 2. CSRF令牌是必需的：
 *    - 需要有效的会话和CSRF令牌
 *    - 可以通过 generateCsrfToken() 函数获取
 * 
 * 3. 管理员权限是必需的：
 *    - 必须以管理员身份登录
 *    - 系统会自动验证权限
 * 
 * 4. 删除类型说明：
 *    - soft: 软删除，仅标记为已删除，可恢复
 *    - hard: 硬删除，永久删除物理文件和数据库记录，不可恢复
 * 
 * 5. 批量限制：
 *    - 单次最多删除50个文件
 *    - 文件ID用逗号分隔，如 "1,2,3,4,5"
 */
?>
