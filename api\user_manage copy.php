<?php
require_once '../conn_waf.php';

header('Content-Type: application/json');
$APP_ID = 3; // 应用ID
function validateIDCard($idCard) {
    if(empty($idCard)) return true;
    if(strlen($idCard) != 18) return false;
    
    $idCard = strtoupper($idCard);
    
    $factor = [7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2];
    $verify = ['1','0','X','9','8','7','6','5','4','3','2'];
    $sum = 0;
    
    for($i=0; $i<17; $i++){
        $sum += $idCard[$i] * $factor[$i];
    }
    
    return $verify[$sum % 11] == $idCard[17];
}
// 递归获取所有子单位
function getChildUnits($parentId, $conn) {
    $units = [];
    
    // 首先查询父单位信息
    $parentStmt = $conn->prepare("SELECT * FROM 2_unit WHERE id = ?");
    $parentStmt->bind_param("i", $parentId);
    $parentStmt->execute();
    $parentResult = $parentStmt->get_result();
    
    if ($parentRow = $parentResult->fetch_assoc()) {
        $parentRow['is_parent'] = true;
        $units[$parentRow['id']] = $parentRow;
    }
    
    // 查询直接子单位
    $stmt = $conn->prepare("SELECT * FROM 2_unit WHERE parent_id = ? ORDER BY sort_order ASC");
    $stmt->bind_param("i", $parentId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        if (!isset($units[$row['id']])) {
            $row['is_parent'] = false;
            $units[$row['id']] = $row;
            // 递归查询子单位的子单位
            $childUnits = getChildUnits($row['id'], $conn);
            foreach ($childUnits as $childUnit) {
                if (!isset($units[$childUnit['id']])) {
                    $units[$childUnit['id']] = $childUnit;
                }
            }
        }
    }
    
    return $units;
}
function getPspInfo(){
    global $conn;
    try {
        $organizationUnit = $_POST['organization_unit'] ?? '';
        if (empty($organizationUnit)) {
            throw new Exception('缺少organization_unit参数');
        }
        
        $sql = "SELECT id, name FROM 3_user WHERE organization_unit = ? AND personnel_type = '民警' ORDER BY sort_order ASC";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('SQL准备失败: ' . $conn->error);
        }
        
        if (!$stmt->bind_param('i', $organizationUnit)) {
            throw new Exception('参数绑定失败: ' . $stmt->error);
        }
        
        if (!$stmt->execute()) {
            throw new Exception('SQL执行失败: ' . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $users = [];
        while ($row = $result->fetch_assoc()) {
            unset($row['password']);
            $users[] = $row;
        }
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM 3_user WHERE organization_unit = ? AND personnel_type = '民警'";
        $countStmt = $conn->prepare($countSql);
        $countStmt->bind_param('i', $organizationUnit);
        $countStmt->execute();
        $countResult = $countStmt->get_result();
        $total = $countResult->fetch_assoc()['total'];
        
        echo json_encode([
            'status' => 1, 
            'data' => $users,
            'total' => $total,
            'error' => ''
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0, 
            'message' => '操作失败: ' . $e->getMessage(),
            'error' => $e->getMessage()
        ]);
    }
    exit();
}
function getSortOptions(){
    global $conn;
    try {
        $organizationUnit = $_POST['organization_unit'] ?? '';
        if (empty($organizationUnit)) {
            throw new Exception('缺少organization_unit参数');
        }
        
        $sql = "SELECT id, name, sort_order FROM 3_user WHERE organization_unit = ? ORDER BY sort_order ASC";
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('SQL准备失败: ' . $conn->error);
        }
        
        if (!$stmt->bind_param('i', $organizationUnit)) {
            throw new Exception('参数绑定失败: ' . $stmt->error);
        }
        
        if (!$stmt->execute()) {
            throw new Exception('SQL执行失败: ' . $stmt->error);
        }
        
        $result = $stmt->get_result();
        $users = [];
        while ($row = $result->fetch_assoc()) {
            unset($row['password']);
            $users[] = $row;
        }
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM 3_user WHERE organization_unit = ? AND personnel_type = '民警'";
        $countStmt = $conn->prepare($countSql);
        $countStmt->bind_param('i', $organizationUnit);
        $countStmt->execute();
        $countResult = $countStmt->get_result();
        $total = $countResult->fetch_assoc()['total'];
        
        echo json_encode([
            'status' => 1, 
            'data' => $users,
            'total' => $total,
            'error' => ''
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'status' => 0, 
            'message' => '操作失败',
            'error' => $e->getMessage()
        ]);
    }
    exit();
}
/**
 * 处理用户查询请求并返回JSON格式结果
 * 
 * @return void
 * @throws Exception
 */
function queryuser() {
    global $conn;
    
    // 参数处理
    $params = processQueryParameters();
    extract($params);
    
    // 构建查询
    list($sql, $types, $queryParams) = buildUserQuery($params);
    
    // 执行查询
    $result = executeUserQuery($sql, $types, $queryParams);
    
    // 获取总数
    $total = getTotalUserCount($sql, $types, $queryParams);
    
    // 处理数据
    $data = processUserData($result, $userIds, $keyword, $orgUnit, $pageSize, $page, $conn);
    
    // 生成响应
    generateJsonResponse($data, $total, $page, $pageSize);
}

/**
 * 处理并验证查询参数
 * 
 * @return array 包含所有处理后的参数
 */
function processQueryParameters() {
    // 当有搜索关键字且没有分页参数时，返回所有匹配结果
    if(!empty($_POST['search_keyword']) && (empty($_POST['page']) || $_POST['page'] === '') && (empty($_POST['pagesize']) || $_POST['pagesize'] === '')) {
        $page = 1;
        $pageSize = PHP_INT_MAX; // 设置为最大整数以获取所有结果
    } else {
        $page = isset($_POST['page']) && $_POST['page'] !== '' ? intval($_POST['page']) : 1;
        $pageSize = isset($_POST['pagesize']) && $_POST['pagesize'] !== '' ? max(1, intval($_POST['pagesize'])) : 10;
    }
    
    return [
        'page' => $page,
        'pageSize' => $pageSize,
        'orgUnit' => isset($_POST['organization_unit']) && $_POST['organization_unit'] !== '' ? intval($_POST['organization_unit']) : 1,
        'showIn' => isset($_POST['showInPersonnel']) && $_POST['showInPersonnel'] !== '' ? ($_POST['showInPersonnel'] == '1' ? 1 : 0) : 1,
        'showOut' => isset($_POST['showOutPersonnel']) && $_POST['showOutPersonnel'] !== '' ? ($_POST['showOutPersonnel'] == '1' ? 1 : 0) : 1,
        'userIds' => isset($_POST['userIds']) ? array_map('intval', explode(',', $_POST['userIds'])) : [],
        'keyword' => $_POST['search_keyword'] ?? ''
    ];
}

/**
 * 构建用户查询SQL语句
 * 
 * @param array $params 处理后的查询参数
 * @return array 包含SQL语句、参数类型和参数数组
 */
function buildUserQuery($params) {
    extract($params);
    global $conn;
    
    // 构建基础查询
    $sql = "SELECT * FROM 3_user WHERE 1=1";
    $types = '';
    $params = [];
    
    // 单位筛选 - 当没有指定userIds时才应用
    if ($orgUnit && empty($userIds)) {
        $childUnits = getChildUnits($orgUnit, $conn);
        $unitIds = array_column($childUnits, 'id');
        if (!in_array($orgUnit, $unitIds)) {
            $unitIds[] = $orgUnit;
        }
        $placeholders = implode(',', array_fill(0, count($unitIds), '?'));
        $sql .= " AND organization_unit IN ($placeholders)";
        $types .= str_repeat('i', count($unitIds));
        $params = array_merge($params, $unitIds);
    }
    
    // 调入调出状态
    if ($showIn == 1 && $showOut == 1) {
        // 同时显示调入和调出人员
        $sql .= " AND (1=1)";
    } elseif ($showIn == 1) {
        // 仅显示调入人员
        $sql .= " AND organization_unit = ?";
        $types .= 'i';
        array_push($params, $orgUnit);
    } elseif ($showOut == 1) {
        // 仅显示调出人员
        $sql .= " AND work_unit != ?";
        $types .= 'i';
        array_push($params, $orgUnit);
    }
    
    // 用户ID筛选
    if (!empty($userIds)) {
        $placeholders = implode(',', array_fill(0, count($userIds), '?'));
        $sql .= " AND id IN ($placeholders)";
        $types .= str_repeat('i', count($userIds));
        $params = array_merge($params, $userIds);
    }
    
    // 关键字搜索
    if (!empty($keyword)) {
        $searchFields = ['name', 'id_number', 'phone', 'short_code', 'personnel_type', 'employment_status', 'police_number'];
        $sql .= " AND (";
        foreach ($searchFields as $i => $field) {
            if ($i > 0) $sql .= " OR ";
            $sql .= "$field LIKE ?";
            $types .= 's';
            $params[] = "%$keyword%";
        }
        // 处理desc字段，需要转义
        $sql .= " OR `desc` LIKE ?";
        $types .= 's';
        $params[] = "%$keyword%";
        $sql .= ")";
    }
    
    // 排序和分页
    $sql .= " ORDER BY sort_order ASC LIMIT ? OFFSET ?";
    $types .= 'ii';
    $offset = ($page - 1) * $pageSize;
    array_push($params, $pageSize, $offset);
    
    return [$sql, $types, $params];
}

/**
 * 执行用户查询并返回结果集
 * 
 * @param string $sql SQL语句
 * @param string $types 参数类型
 * @param array $params 参数数组
 * @return mysqli_result 查询结果集
 * @throws Exception
 */
function executeUserQuery($sql, $types, $params) {
    global $conn;
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('SQL准备失败: ' . $conn->error);
    }
    
    if ($types) {
        $refParams = [];
        foreach ($params as $key => $value) {
            $refParams[$key] = &$params[$key];
        }
        $bindArgs = array_merge([$types], $refParams);
        if (!call_user_func_array([$stmt, 'bind_param'], $bindArgs)) {
            throw new Exception('参数绑定失败: ' . $stmt->error);
        }
    }
    
    if (!$stmt->execute()) {
        throw new Exception('SQL执行失败: ' . $stmt->error);
    }
    
    return $stmt->get_result();
}

/**
 * 获取符合条件的用户总数
 * 
 * @param string $sql SQL语句
 * @param string $types 参数类型
 * @param array $params 参数数组
 * @return int 用户总数
 * @throws Exception
 */
function getTotalUserCount($sql, $types, $params) {
    global $conn;
    
    $countSql = preg_replace('/LIMIT.*/', '', $sql);
    $countParams = array_slice($params, 0, count($params) - 2);  // 移除分页参数
    
    $countStmt = $conn->prepare("SELECT COUNT(*) FROM ($countSql) AS total");
    if (!$countStmt) {
        throw new Exception('总数SQL准备失败: ' . $conn->error);
    }
    
    if ($types) {
        $countTypes = substr($types, 0, -2);  // 移除分页类型
        $refCountParams = [];
        foreach ($countParams as $key => $value) {
            $refCountParams[$key] = &$countParams[$key];
        }
        $bindCountArgs = array_merge([$countTypes], $refCountParams);
        if (!call_user_func_array([$countStmt, 'bind_param'], $bindCountArgs)) {
            throw new Exception('总数参数绑定失败: ' . $countStmt->error);
        }
    }
    
    $countStmt->execute();
    return $countStmt->get_result()->fetch_row()[0];
}

/**
 * 根据单位ID获取单位名称
 * 
 * @param int $unitId 单位ID
 * @param mysqli $conn 数据库连接
 * @return string|null 单位名称或null
 */
function getUnitNameById($unitId, $conn) {
    if (empty($unitId)) {
        return null;
    }
    
    $stmt = $conn->prepare("SELECT unit_name FROM 2_unit WHERE id = ?");
    $stmt->bind_param('i', $unitId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['unit_name'];
    }
    
    return null;
}

/**
 * 处理用户数据并添加单位名称信息
 * 
 * @param mysqli_result $result 查询结果集
 * @param array $userIds 用户ID列表
 * @param string $keyword 搜索关键字
 * @param int $orgUnit 组织单位ID
 * @param int $pageSize 每页条数
 * @param int $page 当前页码
 * @param mysqli $conn 数据库连接
 * @return array 处理后的用户数据数组
 */
function processUserData($result, $userIds, $keyword, $orgUnit, $pageSize, $page, $conn) {
    $data = [];
    
    if (!empty($userIds) || !empty($keyword)) {
        while ($row = $result->fetch_assoc()) {
            $data[] = processUserRow($row, $conn);
        }
    } else {
        // 获取单位排序数组
        $units = getChildUnits($orgUnit, $conn);
        
        // 处理分页逻辑 - 按单位顺序依次获取用户
        $remaining = $pageSize;
        $currentOffset = ($page - 1) * $pageSize;
        $remaining = $pageSize;
        
        foreach ($units as $unit) {
            $unitId = $unit['id'];
            
            // 查询当前单位用户总数
            $countStmt = $conn->prepare("SELECT COUNT(*) FROM 3_user WHERE organization_unit = ?");
            $countStmt->bind_param('i', $unitId);
            $countStmt->execute();
            $unitUserCount = $countStmt->get_result()->fetch_row()[0];
            
            if ($currentOffset >= $unitUserCount) {
                $currentOffset -= $unitUserCount;
                continue;
            }
            
            $limit = min($remaining, $unitUserCount - $currentOffset);
            
            // 查询当前单位用户
            $userStmt = $conn->prepare("SELECT * FROM 3_user WHERE organization_unit = ? ORDER BY sort_order ASC LIMIT ? OFFSET ?");
            $userStmt->bind_param('iii', $unitId, $limit, $currentOffset);
            $userStmt->execute();
            $userResult = $userStmt->get_result();
            
            // 处理用户数据
            while ($row = $userResult->fetch_assoc()) {
                $row['sortMax'] = $unitUserCount;
                $data[] = processUserRow($row, $conn);
            }
            
            $remaining -= $limit;
            $currentOffset = 0;
            
            if ($remaining <= 0) {
                break;
            }
        }
    }
    
    return $data;
}

/**
 * 处理单个用户行数据
 * 
 * @param array $row 用户数据行
 * @param mysqli $conn 数据库连接
 * @return array 处理后的用户数据行
 */
function processUserRow($row, $conn) {
    unset($row['password']);
    
    // 查询组织单位名称
    $row['organization_unitName'] = getUnitNameById($row['organization_unit'], $conn);
    
    // 查询工作单位名称
    $row['work_unitName'] = getUnitNameById($row['work_unit'], $conn);
    
    return $row;
}

/**
 * 生成JSON响应并退出
 * 
 * @param array $data 用户数据数组
 * @param int $total 总记录数
 * @param int $page 当前页码
 * @param int $pageSize 每页记录数
 * @return void
 */
function generateJsonResponse($data, $total, $page, $pageSize) {
    echo json_encode([
        'status' => 1,
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'pageMax' => $pageSize > 0 ? ceil($total / $pageSize) : 1
    ]);
    exit();
}
function adduser(){
    global $conn;
    // 新增用户
    // 必填字段检查
    $requiredFields = [
        'name' => ['type' => 'string', 'max' => 50],
        'phone' => ['type' => 'string', 'max' => 11],
        'organization_unit' => ['type' => 'int'],
        'sort_order' => ['type' => 'int']
    ];

    // 可选字段及其验证规则
    $optionalFields = [
        'gender' => ['type' => 'int', 'values' => [0,1,2]],
        'id_number' => ['type' => 'string', 'length' => 18, 'validate' => ['type' => 'function', 'func' => 'validateIDCard']],
        'archive_birthdate' => ['type' => 'date'],
        'short_code' => ['type' => 'string', 'length' => 10],
        'alt_phone_1' => ['type' => 'string', 'length' => 20],
        'alt_phone_2' => ['type' => 'string', 'length' => 20],
        'landline' => ['type' => 'string', 'length' => 15],
        'work_unit' => ['type' => 'int'],
        'employment_date' => ['type' => 'date'],
        'political_status' => ['type' => 'string', 'length' => 50],
        'party_join_date' => ['type' => 'date'],
        'personnel_type' => ['type' => 'string', 'length' => 100],
        'police_number' => ['type' => 'string', 'length' => 20],
        'assisting_officer' => ['type' => 'int'],
        'job_rank' => ['type' => 'string', 'length' => 50],
        'current_rank_date' => ['type' => 'date'],
        'position' => ['type' => 'string', 'length' => 100],
        'current_position_date' => ['type' => 'date'],
        'desc' => ['type' => 'string', 'length' => 255]
    ];

    foreach ($requiredFields as $field => $rules) {
        if (!isset($_POST[$field]) || $_POST[$field] === '') {
            throw new Exception("缺少必填字段: " . $field);
        }
    }

    
    // 修正 isset 函数的使用，原代码中逻辑错误，应分别判断
    if (isset($_POST['id_number']) && !validateIDCard($_POST['id_number'])) {
        throw new Exception('身份证号格式错误');
    }


    
    // 检查SQL语句是否准备成功
    $sql = "INSERT INTO $dbTable (name, id_number, phone, password, archive_birthdate, gender, short_code, alt_phone_1, alt_phone_2, landline, organization_unit, work_unit, employment_date, political_status, party_join_date, personnel_type, police_number, assisting_officer, employment_status, job_rank, current_rank_date, position, current_position_date, sort_order,`desc`) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('SQL准备失败: ' . $conn->error);
    }
    
    // 处理所有日期字段
    $dateFields = ['employment_date', 'party_join_date', 'current_rank_date', 'current_position_date'];
    $processedDates = [];
    
    foreach ($dateFields as $field) {
        $dateValue = !empty($_POST[$field]) ? $_POST[$field] : null;
        if ($dateValue !== null && $dateValue !== '' && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateValue)) {
            throw new Exception('日期字段格式错误，应为YYYY-MM-DD');
        }
        $processedDates[$field] = ($dateValue === '') ? null : $dateValue;
    }
    
    // 准备字段值
    $fields = [
        'name', 'id_number', 'phone', 'password', 'archive_birthdate', 'gender', 'short_code',
        'alt_phone_1', 'alt_phone_2', 'landline', 'organization_unit', 'work_unit', 'employment_date',
        'political_status', 'party_join_date', 'personnel_type', 'police_number', 'assisting_officer',
        'employment_status', 'job_rank', 'current_rank_date', 'position', 'current_position_date','sort_order', 'desc'
    ];
    
    // 处理所有字段值
    $values = [];
    foreach ($fields as $field) {
        // 处理日期字段
        if (in_array($field, ['archive_birthdate', 'employment_date', 'party_join_date', 'current_rank_date', 'current_position_date'])) {
            $value = !empty($_POST[$field]) ? $_POST[$field] : null;
            if($value == 'null'){
                $value = null;
            }
            if ($value !== null && $value !== '' && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                throw new Exception('日期字段格式错误，应为YYYY-MM-DD');
            }
            $values[$field] = ($value === '') ? null : $value;
        } 
        // 处理整数类型字段
        elseif (in_array($field, ['organization_unit', 'work_unit', 'assisting_officer','sort_order'])) {
            $value = isset($_POST[$field]) ? $_POST[$field] : null;
            $values[$field] = ($value === '') ? null : $value;
        } else {
            // 处理其他字段 - 可选字段默认为null，仅在传入数据时赋值
            $values[$field] = (isset($_POST[$field]) && $_POST[$field] !== '') ? $_POST[$field] : null;
        }
    }
    
    // 强制设置加密后的默认密码
    // 强制设置加密后的默认密码并确保字段存在
    $values['password'] = password_hash('123456', PASSWORD_DEFAULT);
    $fields[] = 'password';
    
    $stmt->bind_param('sssssssssssssssssssssssss', 
        $values['name'], $values['id_number'], $values['phone'], $values['password'], 
        $values['archive_birthdate'], $values['gender'], $values['short_code'], 
        $values['alt_phone_1'], $values['alt_phone_2'], $values['landline'], 
        $values['organization_unit'], $values['work_unit'], $values['employment_date'],
        $values['political_status'], $values['party_join_date'], $values['personnel_type'], 
        $values['police_number'], $values['assisting_officer'], $values['employment_status'], 
        $values['job_rank'], $values['current_rank_date'], $values['position'], 
        $values['current_position_date'], $values['sort_order'],$values['desc']
    );
    
    $success = $stmt->execute();
    if (!$success) {
        throw new Exception('执行SQL失败: ' . $stmt->error);
    }
    $insertId = $conn->insert_id;
    if ($insertId <= 0) {
        throw new Exception('获取插入ID失败');
    }
    // 记录新增日志
    logOperation($conn, $_SESSION['user_id'], $APP_ID, '新增用户 ID:'. $insertId);
    echo json_encode(['status' => 1, 'message' => '插入成功', '新增id' => $insertId], JSON_UNESCAPED_UNICODE);
    exit();
}
function modifyuser(){
    global $conn;
    // 修改用户
    if (empty($_POST['id'])) {
        throw new Exception('缺少用户ID');
    }
    
    $updateFields = [];
    $params = [];
    $types = '';
    
    // 定义所有可修改字段及其类型
    $modifiableFields = [
        'name' => 's', 'id_number' => 's', 'phone' => 's', 'password' => 's',
        'archive_birthdate' => 's', 'gender' => 'i', 'short_code' => 's',
        'alt_phone_1' => 's', 'alt_phone_2' => 's', 'landline' => 's',
        'organization_unit' => 'i', 'work_unit' => 'i', 'employment_date' => 's',
        'political_status' => 's', 'party_join_date' => 's', 'personnel_type' => 's',
        'police_number' => 's', 'assisting_officer' => 'i', 'employment_status' => 's',
        'job_rank' => 's', 'current_rank_date' => 's', 'position' => 's',
        'current_position_date' => 's','sort_order' => 'i', 'desc' => 's'
    ];
    
    // 仅处理传入的字段，不检查必填字段
    foreach ($_POST as $key => $value) {
        if ($key !== 'id' && array_key_exists($key, $modifiableFields) && $value !== '') {
            $updateFields[$key] = $value;
            $types .= $modifiableFields[$key];
            
            // 处理日期和整数类型字段
            if (in_array($key, ['employment_date', 'party_join_date', 'current_rank_date', 'current_position_date', 'archive_birthdate'])) {
                $params[] = ($value === '') ? null : $value;
            } elseif (in_array($key, ['organization_unit', 'work_unit', 'assisting_officer', 'gender','sort_order'])) {
                $params[] = ($value === '') ? null : intval($value);
            } else {
                $params[] = $value;
            }
        }
    }
    
    // 执行数据库更新
    if (!empty($updateFields)) {
        // 先检查ID是否存在
        $checkStmt = $conn->prepare("SELECT id FROM $dbTable WHERE id = ?");
        $checkStmt->bind_param("i", $_POST['id']);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        
        if ($checkResult->num_rows === 0) {
            throw new Exception('用户ID不存在');
        }
        
        $setParts = array();
        foreach($updateFields as $field => $value) {
            // 对MySQL保留字desc使用反引号转义
            $escapedField = $field === 'desc' ? "`$field`" : $field;
            $setParts[] = "$escapedField = ?";
        }
        $sql = "UPDATE $dbTable SET " . implode(', ', $setParts) . " WHERE id = ?";
        $types .= 'i';
        $params[] = $_POST['id'];
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('SQL准备失败: ' . $conn->error);
        }
        
        if (!$stmt->bind_param($types, ...$params)) {
            throw new Exception('参数绑定失败: ' . $stmt->error);
        }
        
        if (!$stmt->execute()) {
            throw new Exception('SQL执行失败: ' . $stmt->error);
        }
        
        if ($stmt->affected_rows > 0) {
        // 记录修改日志
        // 构建修改内容描述
        $operationDesc = "修改用户信息: id=" . $_POST['id'] . " -> [";
        $changedFields = array();
        foreach($updateFields as $field => $value) {
            $changedFields[] = $field." = ".$value;
        }
        $operationDesc = $operationDesc.implode(', ', $changedFields).']';
        logOperation($conn, $_SESSION['user_id'], $APP_ID, $operationDesc);
        echo json_encode(['status' => 1, 'message' => '修改成功','affected' => $stmt->affected_rows], JSON_UNESCAPED_UNICODE);
        } else {
            throw new Exception('数据未发生变化，可能原值与新值相同');
        }
    } else {
        throw new Exception('没有可修改的字段');
    }
    exit();
}
function deluser(){
    global $conn;
    // 删除用户
    // 处理单个或多个ID参数
    $ids = is_array($_POST['id']) ? $_POST['id'] : explode(',', $_POST['id']);
    $ids = array_map('intval', $ids);
    
    if (empty($ids)) {
        throw new Exception('缺少有效的用户ID');
    }
    
    // 双重安全检查（授权记录+操作日志）
    $placeholders = rtrim(str_repeat('?,', count($ids)), ',');
    
    // 1. 检查权限授权记录
    $authCheck = $conn->prepare("SELECT roleId,userId FROM 3_user_Role WHERE userId IN ($placeholders) LIMIT 1");
    $authCheck->bind_param(str_repeat('i', count($ids)), ...$ids);
    $authCheck->execute();
    if ($authResult = $authCheck->get_result()->fetch_assoc()) {
        throw new Exception("用户ID:{$authResult['userId']} 存在角色授权(角色ID:{$authResult['roleId']})");
    }
    
    // 2. 检查用户操作日志
    $logCheck = $conn->prepare("SELECT user_id FROM log WHERE user_id IN ($placeholders) LIMIT 1");
    $logCheck->bind_param(str_repeat('i', count($ids)), ...$ids);
    $logCheck->execute();
    if ($logResult = $logCheck->get_result()->fetch_assoc()) {
        throw new Exception("用户ID:{$logResult['user_id']} 存在操作日志记录，不允许删除");
    }

    // 构建删除SQL
    $delStmt = $conn->prepare("DELETE FROM $dbTable WHERE id IN ($placeholders)");
    $delStmt->bind_param(str_repeat('i', count($ids)), ...$ids);
    $delStmt->execute();
    $affected = $delStmt->affected_rows;
    if ($affected === 0) {
        throw new Exception('没有删除任何记录');
    }
    // 记录删除日志
    logOperation($conn, $_SESSION['user_id'], $APP_ID, '删除用户:'.implode(',', $ids));
    echo json_encode(['status' => 1, 'message' => '删除成功', 'affected' => $affected], JSON_UNESCAPED_UNICODE);
    exit();
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
// 主逻辑处理
try {

    if (!isset($_SESSION['user_id']) || !isAdmin()) {
        throw new Exception('当前用户无权限操作');
    }
            
    $controlCode = $_POST['controlCode'] ?? '';
    $dbTable = '3_user';

    switch ($controlCode) {
        case 'getPspInfo':
            getPspInfo();
            break;
        case 'getSortOptions':
            getSortOptions();
        case 'query':
            queryuser();
        case 'add':
            isHasPerm();
            adduser();
        case 'modify':
            isHasPerm();
            modifyuser();
        case 'del':
            isHasPerm();
            deluser();
        default:
            throw new Exception('无效的操作类型');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['status' => 0, 'message' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>