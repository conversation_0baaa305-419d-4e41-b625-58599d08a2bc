<?php
/**
 * 文件网盘系统 - 登录页面
 * 创建时间: 2025-06-23
 */

require_once 'functions.php';

// 如果已经登录，跳转到主页
if (isLoggedIn()) {
    redirect('index.php');
}
function callLoginAPI($id, $pw) {
            $apiUrl = 'http://10.152.110.168/api/login.php';
            $postData = [
                'id_number' => $id,
                'password' => $pw
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            echo "<script>console.log('$response');</script>";
            if ($httpCode !== 200) {
                return ['success' => false, 'message' => 'API请求失败'];
            }
            
            $result = json_decode($response, true);
            
            if ($result['status'] == 1) {
                // 登录成功，设置用户会话
                $_SESSION['user_id'] = $result['user']['id'];
                $_SESSION['username'] = $result['user']['name'];
                $_SESSION['is_admin'] = $result['user']['is_admin'];
                return ['success' => true];
            } else {
                return ['success' => false, 'message' => $result['message']];
            }
}
$error = '';

// 处理登录表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_number = trim($_POST['id_number'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($id_number) || empty($password)) {
        $error = '请输入身份证号码和密码';
    } else {
        // 调用外部登录API
        $loginResult = callLoginAPI($id_number, $password);
        if ($loginResult['success']) {
            redirect('index.php');
        } else {
            $error = $loginResult['message'] ?? '登录失败，请检查身份证号码和密码';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 用户登录</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .login-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: white;
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .login-body {
            background: white;
            padding: 2rem;
        }
        
        .form-control {
            border: 2px solid #eee;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 16px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .demo-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-5">
                <div class="card login-card">
                    <div class="login-header">
                        <div class="login-logo">
                            <i class="bi bi-cloud-upload"></i>
                        </div>
                        <h4 class="mb-0 text-primary"><?= h(SITE_TITLE) ?></h4>
                        <p class="text-muted mb-0 mt-1"><?= h(SITE_DESCRIPTION) ?></p>
                    </div>
                    
                    <div class="login-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-circle me-2"></i><?= h($error) ?>
                        </div>
                        <?php endif; ?>
                        
                        <form method="post">
                            <div class="mb-3">
                                <label for="id_number" class="form-label">
                                    <i class="bi bi-person-badge me-1"></i>身份证号码
                                </label>
                                <input type="text" class="form-control" id="id_number" name="id_number" 
                                       value="<?= h($_POST['id_number'] ?? '') ?>" required autofocus
                                       placeholder="请输入18位身份证号码" maxlength="18">
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>密码
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                                </button>
                            </div>
                        </form>

                        
                        <div class="text-center mt-3">
                            <a href="download.php" class="text-muted text-decoration-none">
                                <i class="bi bi-download me-1"></i>不登录，直接下载文件
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-white-50">
                        &copy; 2025 <?= h(SITE_TITLE) ?>. All rights reserved.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 身份证号码格式验证
        document.addEventListener('DOMContentLoaded', function() {
            const idNumberInput = document.getElementById('id_number');
            
            idNumberInput.addEventListener('input', function(e) {
                // 只允许输入数字和字母X
                let value = e.target.value.toUpperCase();
                value = value.replace(/[^0-9X]/g, '');
                e.target.value = value;
            });
            
            // 提交前验证身份证号码格式
            document.querySelector('form').addEventListener('submit', function(e) {
                const idNumber = idNumberInput.value.trim();
                if (idNumber.length !== 18) {
                    e.preventDefault();
                    alert('请输入18位身份证号码');
                    idNumberInput.focus();
                    return false;
                }
                
                // 简单的身份证号码格式验证
                const idPattern = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;
                if (!idPattern.test(idNumber)) {
                    e.preventDefault();
                    alert('身份证号码格式不正确');
                    idNumberInput.focus();
                    return false;
                }
            });
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                document.querySelector('form').submit();
            }
        });
    </script>
</body>
</html>
