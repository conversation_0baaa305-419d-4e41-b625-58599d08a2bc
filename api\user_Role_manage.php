<?php
require_once '../conn_waf.php';

/**
 * 添加用户角色记录
 * @return array 包含操作结果的关联数组
 */
function addRole() {
    global $conn;
    $userId = $_POST['userId'] ?? '';
    $roleId = $_POST['roleId'] ?? '';
    $unitId = $_POST['unitId'] ?? '';
    $appId = $_POST['appId'] ?? '';
    
    if (empty($userId) || empty($roleId) || empty($unitId) || empty($appId)) {
        throw new Exception('用户ID、角色ID、单位ID和应用ID不能为空');
    }
    $sql = "INSERT INTO 3_user_Role (userId, roleId, unitId, appId) VALUES (?, ?, ?, ?)";
    $params = [$userId, $roleId, $unitId, $appId];
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'iiii', $params[0], $params[1], $params[2], $params[3]);
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('插入失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('插入失败: 没有影响任何行');
    }
    
    return ['status' => 1, 'message' => '插入成功', 'data' => [], 'affected_rows' => $affectedRows];
}

/**
 * 修改用户角色记录
 * @return array 包含操作结果的关联数组
 */
function modifyRole() {
    global $conn;
    
    $id = $_POST['id'] ?? '';
    $userId = $_POST['userId'] ?? '';
    $roleId = $_POST['roleId'] ?? '';
    $unitId = $_POST['unitId'] ?? '';
    $appId = $_POST['appId'] ?? '';
    
    if (empty($id)) {
        throw new Exception('缺少必要参数');
    }
    
    $sql = "UPDATE 3_user_Role SET userId = ?, roleId = ?, unitId = ?, appId = ? WHERE id = ?";
    $params = [$userId, $roleId, $unitId, $appId, $id];
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'iiiii', $params[0], $params[1], $params[2], $params[3], $params[4]);
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('修改失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        //throw new Exception('修改失败: 没有影响任何行');
        return ['status' => 0, 'message' => '修改失败: 没有影响任何行', 'data' => []];
    }
    
    return ['status' => 1, 'message' => '修改成功', 'data' => []];
}

/**
 * 删除用户角色记录
 * @return array 包含操作结果的关联数组
 */
function deleteRole() {
    global $conn;
    
    $id = $_POST['id'] ?? '';
    
    if (empty($id)) {
        throw new Exception('缺少必要参数');
    }
    
    // 处理批量删除
    if (strpos($id, ',') !== false) {
        $ids = explode(',', $id);
        $ids = array_map('intval', $ids);
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "DELETE FROM 3_user_Role WHERE id IN ($placeholders)";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, str_repeat('i', count($ids)), ...$ids);
    } else {
        // 单个删除
        $sql = "DELETE FROM 3_user_Role WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, 'i', $id);
    }
    
    $executeResult = mysqli_stmt_execute($stmt);
    
    if ($executeResult === false) {
        throw new Exception('删除失败: ' . mysqli_error($conn));
    }
    
    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('删除失败: 没有影响任何行');
    }
    
    return ['status' => 1, 'message' => '删除成功', 'data' => []];
}

/**
 * 查询用户角色记录
 * @return array 包含查询结果的关联数组
 */
function queryRoles() {
    global $conn;
    
    // 处理分页参数
    $page = !empty($_POST['page']) ? intval($_POST['page']) : 1;
    $pageSize = !empty($_POST['pagesize']) ? intval($_POST['pagesize']) : 10;
    
    $sql = "SELECT * FROM 3_user_Role";
    $where = [];
    $params = [];
    $types = '';
    
    if (!empty($_POST['appId'])) {
        $where[] = "appId = ?";
        $params[] = $_POST['appId'];
        $types .= 'i';
    }
    
    if (!empty($_POST['userId'])) {
        $where[] = "userId = ?";
        $params[] = $_POST['userId'];
        $types .= 'i';
    }
    
    if (!empty($_POST['unitId'])) {
        $where[] = "unitId = ?";
        $params[] = $_POST['unitId'];
        $types .= 'i';
    }
    
    if (!empty($_POST['roleId'])) {
        $where[] = "roleId = ?";
        $params[] = $_POST['roleId'];
        $types .= 'i';
    }

    // 获取总数(包含查询条件)
    $countSql = "SELECT COUNT(*) as total FROM 3_user_Role";
    if (!empty($where)) {
        $countSql .= " WHERE " . implode(' AND ', $where);
    }
    $countStmt = mysqli_prepare($conn, $countSql);
    if (!empty($params)) {
        mysqli_stmt_bind_param($countStmt, $types, ...$params);
    }
    mysqli_stmt_execute($countStmt);
    $countResult = mysqli_stmt_get_result($countStmt);
    $total = mysqli_fetch_assoc($countResult)['total'];
    
    if (!empty($where)) {
        $sql .= " WHERE " . implode(' AND ', $where);
    }
    
    // 添加分页
    $sql .= " LIMIT " . (($page - 1) * $pageSize) . ", " . $pageSize;
    
    $stmt = mysqli_prepare($conn, $sql);
    if (!empty($params)) {
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }
    mysqli_stmt_execute($stmt);
    $resultQuery = mysqli_stmt_get_result($stmt);
    
    $data = [];
    if (mysqli_num_rows($resultQuery) > 0) {
        while ($row = mysqli_fetch_assoc($resultQuery)) {
            // 查询用户名称
            $userSql = "SELECT name FROM 3_user WHERE id = ?";
            $userStmt = mysqli_prepare($conn, $userSql);
            mysqli_stmt_bind_param($userStmt, 'i', $row['userId']);
            mysqli_stmt_execute($userStmt);
            $userResult = mysqli_stmt_get_result($userStmt);
            $userName = '';
            if (mysqli_num_rows($userResult) > 0) {
                $userRow = mysqli_fetch_assoc($userResult);
                $userName = $userRow['name'];
            }
            
            // 查询角色名称
            $roleSql = "SELECT roleName FROM 4_Role_List WHERE id = ?";
            $roleStmt = mysqli_prepare($conn, $roleSql);
            mysqli_stmt_bind_param($roleStmt, 'i', $row['roleId']);
            mysqli_stmt_execute($roleStmt);
            $roleResult = mysqli_stmt_get_result($roleStmt);
            $roleName = '';
            if (mysqli_num_rows($roleResult) > 0) {
                $roleRow = mysqli_fetch_assoc($roleResult);
                $roleName = $roleRow['roleName'];
            }
            
            // 查询单位名称
            $unitSql = "SELECT unit_name FROM 2_unit WHERE id = ?";
            $unitStmt = mysqli_prepare($conn, $unitSql);
            mysqli_stmt_bind_param($unitStmt, 'i', $row['unitId']);
            mysqli_stmt_execute($unitStmt);
            $unitResult = mysqli_stmt_get_result($unitStmt);
            $unitName = '';
            if (mysqli_num_rows($unitResult) > 0) {
                $unitRow = mysqli_fetch_assoc($unitResult);
                $unitName = $unitRow['unit_name'];
            }
            
            // 查询应用名称
            $appSql = "SELECT application_name FROM 5_application WHERE id = ?";
            $appStmt = mysqli_prepare($conn, $appSql);
            mysqli_stmt_bind_param($appStmt, 'i', $row['appId']);
            mysqli_stmt_execute($appStmt);
            $appResult = mysqli_stmt_get_result($appStmt);
            $appName = '';
            if (mysqli_num_rows($appResult) > 0) {
                $appRow = mysqli_fetch_assoc($appResult);
                $appName = $appRow['application_name'];
            }
            
            // 添加新增字段
            $row['userName'] = $userName;
            $row['roleName'] = $roleName;
            $row['unitName'] = $unitName;
            $row['appName'] = $appName;
            // 将unitId转换为整数
            $data[] = $row;
        }
    }
    
    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
    ];
}

/**
 * 获取应用用户信息
 * @return array 包含用户信息的关联数组
 */
function getAppUsers() {
    global $conn;
    $appId = $_POST['appId'] ?? '';
    
    if (empty($appId)) {
        throw new Exception('appId参数不能为空');
    }
    
    // 查询3_user_Role表获取相关userId
    $sql = "SELECT DISTINCT userId FROM 3_user_Role WHERE appId = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $appId);
    mysqli_stmt_execute($stmt);
    $roleResult = mysqli_stmt_get_result($stmt);
    
    $userIds = [];
    while ($row = mysqli_fetch_assoc($roleResult)) {
        $userIds[] = $row['userId'];
    }
    
    if (empty($userIds)) {
        return ['status' => 1, 'message' => '未找到相关用户', 'data' => []];
    }
    
    // 查询3_user表获取用户信息
    $placeholders = implode(',', array_fill(0, count($userIds), '?'));
    $userSql = "SELECT id, name, id_number, organization_unit, police_number FROM 3_user WHERE id IN ($placeholders)";
    $userStmt = mysqli_prepare($conn, $userSql);
    
    // 绑定参数
    $types = str_repeat('i', count($userIds));
    mysqli_stmt_bind_param($userStmt, $types, ...$userIds);
    mysqli_stmt_execute($userStmt);
    $userResult = mysqli_stmt_get_result($userStmt);
    
    $data = [];
    while ($user = mysqli_fetch_assoc($userResult)) {
        // 查询2_unit表获取单位名称
        // 根据id_number查询单位信息，修正查询字段
        $unitSql = "SELECT unit_name FROM 2_unit WHERE id = ?";
        $unitStmt = mysqli_prepare($conn, $unitSql);
        mysqli_stmt_bind_param($unitStmt, 'i', $user['organization_unit']);
        mysqli_stmt_execute($unitStmt);
        $unitResult = mysqli_stmt_get_result($unitStmt);
        
        $unitName = '';
        if (mysqli_num_rows($unitResult) > 0) {
            $unitRow = mysqli_fetch_assoc($unitResult);
            $unitName = $unitRow['unit_name'];
        }
        
        // 组装返回数据
        $data[] = [
            'id' => $user['id'],
            'name' => $user['name'],
            'id_number' => $user['id_number'],
            'unit_name' => $unitName,
            'police_number' => $user['police_number']
        ];
        
        mysqli_stmt_close($unitStmt);
    }
    
    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $data
    ];
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
// 开启数据库事务
$conn->autocommit(FALSE);

try {
    // 获取参数
    $controlCode = $_POST['controlCode'] ?? '';
    
    switch ($controlCode) {
        case 'add': // 插入操作
            isHasPerm();
            $result=addRole();
            break;

        case 'modify': // 修改操作
            isHasPerm();
            $result=modifyRole();
            break;

        case 'del': // 删除操作
            isHasPerm();
            $result=deleteRole();
            break;
            
        case 'query': // 查询操作
            $result=queryRoles();
            break;
            
        case 'getAppUser': // 根据appId获取用户及单位信息
            $result=getAppUsers();
            break;
            
        default:
            throw new Exception('无效的控制码');
    }

    // 提交事务
    $conn->commit();
    echo json_encode($result);
} catch (Exception $e) {
    // 回滚事务并记录错误日志
    $conn->rollback();
    //error_log('['.date('Y-m-d H:i:s').'] userRole_manage.php Error: '.$e->getMessage()."\n", 3, 'error.log');
    
    // 返回更详细的错误信息
    $errorResponse = [
        'status' => 0,
        'message' => '操作失败: ' . $e->getMessage(),
        'data' => []
    ];
    
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode($errorResponse);
}