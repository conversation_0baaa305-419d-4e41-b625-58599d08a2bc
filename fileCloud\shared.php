<?php
/**
 * 文件网盘系统 - 与我共享页面
 * 创建时间: 2025-06-23
 */

require_once 'functions.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    redirect('login.php');
}

$currentUserId = $_SESSION['user_id'];
$currentUsername = $_SESSION['username'] ?? '用户';

// 获取与我共享的文件列表
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    if ($conn->connect_error) {
        die("数据库连接失败: " . $conn->connect_error);
    }
    
    $sql = "
        SELECT s.share_id, s.share_time, s.from_user_id,
               f.file_id, f.original_name, f.file_size, f.file_type, 
               f.upload_time, f.share_code, f.download_count,
               u.name as from_username
        FROM filecloud_share s
        JOIN filecloud_info f ON s.file_id = f.file_id
        LEFT JOIN 3_user u ON s.from_user_id = u.id
        WHERE s.to_user_id = ? AND s.is_deleted = 0 AND f.is_deleted = 0
        ORDER BY s.share_time DESC
    ";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        die("SQL准备失败: " . $conn->error);
    }
    $stmt->bind_param("i", $currentUserId);
    $stmt->execute();
    $result = $stmt->get_result();
    $sharedFiles = $result->fetch_all(MYSQLI_ASSOC);
} catch (Exception $e) {
    $sharedFiles = [];
    $error = '获取共享文件列表失败';
}

// 获取我分享给别人的文件列表
try {
    $sql = "
        SELECT s.share_id, s.share_time, s.to_user_id,
               f.file_id, f.original_name, f.file_size, f.file_type, 
               f.upload_time, f.share_code, f.download_count,
               u.name as to_username
        FROM filecloud_share s
        JOIN filecloud_info f ON s.file_id = f.file_id
        LEFT JOIN 3_user u ON s.to_user_id = u.id
        WHERE s.from_user_id = ? AND s.is_deleted = 0 AND f.is_deleted = 0
        ORDER BY s.share_time DESC
    ";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        throw new Exception("SQL准备失败: " . $conn->error);
    }
    $stmt->bind_param("i", $currentUserId);
    if (!$stmt->execute()) {
        throw new Exception("SQL执行失败: " . $stmt->error);
    }
    $result = $stmt->get_result();
    $myShares = $result->fetch_all(MYSQLI_ASSOC);
} catch (Exception $e) {
    $myShares = [];
}

$csrfToken = generateCsrfToken();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 与我共享</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-files me-1"></i>我的文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>上传文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="shared.php">
                            <i class="bi bi-share me-1"></i>与我共享
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <?php if (isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin.php">
                            <i class="bi bi-gear me-1"></i>系统管理
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?= h($currentUsername) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 错误消息 -->
        <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?= h($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-0">收到的文件</h5>
                                <h3 class="mb-0"><?= count($sharedFiles) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-inbox"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-0">我的分享</h5>
                                <h3 class="mb-0"><?= count($myShares) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-share"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 标签页 -->
        <ul class="nav nav-tabs mb-4" id="shareTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="received-tab" data-bs-toggle="tab" data-bs-target="#received" type="button" role="tab">
                    <i class="bi bi-inbox me-2"></i>收到的文件 (<?= count($sharedFiles) ?>)
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab">
                    <i class="bi bi-share me-2"></i>我的分享 (<?= count($myShares) ?>)
                </button>
            </li>
        </ul>

        <div class="tab-content" id="shareTabContent">
            <!-- 收到的文件 -->
            <div class="tab-pane fade show active" id="received" role="tabpanel">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="bi bi-inbox me-2"></i>收到的文件
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($sharedFiles)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">还没有收到任何文件</h5>
                            <p class="text-muted">当其他用户分享文件给您时，会显示在这里</p>
                        </div>
                        <?php else: ?>
                        <!-- 信息栏 - 仅显示文件数量 -->
                        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                            <div class="d-flex align-items-center">
                                <span class="text-muted small">
                                    <i class="bi bi-info-circle me-1"></i>
                                    共 <?= count($sharedFiles) ?> 个文件
                                </span>
                            </div>
                            <div class="text-muted small">
                                <i class="bi bi-eye me-1"></i>仅可查看和下载
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 35%;">文件名</th>
                                        <th style="width: 10%;">大小</th>
                                        <th style="width: 8%;">类型</th>
                                        <th style="width: 15%;">分享者</th>
                                        <th style="width: 12%;">分享时间</th>
                                        <th style="width: 8%;">下载量</th>
                                        <th style="width: 12%;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sharedFiles as $file): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark me-2 text-primary"></i>
                                                <span class="text-truncate" style="max-width: 300px;" title="<?= h($file['original_name']) ?>">
                                                    <?= h($file['original_name']) ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td><?= formatFileSize($file['file_size']) ?></td>
                                        <td>
                                            <span class="badge bg-secondary"><?= h(strtoupper($file['file_type'] ?? 'Unknown')) ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-person-circle me-1 text-muted"></i>
                                                <?= h($file['from_username'] ?? '未知用户') ?>
                                            </div>
                                        </td>
                                        <td><?= date('Y-m-d H:i', strtotime($file['share_time'])) ?></td>
                                        <td>
                                            <span class="badge bg-info"><?= number_format($file['download_count']) ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="download.php?code=<?= h($file['share_code']) ?>" class="btn btn-outline-success" title="下载">
                                                    <i class="bi bi-download"></i>
                                                </a>
                                                <button class="btn btn-outline-primary" onclick="showFileInfo(<?= $file['file_id'] ?>)" title="详情">
                                                    <i class="bi bi-info-circle"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 我的分享 -->
            <div class="tab-pane fade" id="sent" role="tabpanel">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="bi bi-share me-2"></i>我的分享
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($myShares)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-share display-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">还没有分享任何文件</h5>
                            <p class="text-muted">在"我的文件"中可以将文件分享给其他用户</p>
                            <a href="index.php" class="btn btn-primary">
                                <i class="bi bi-files me-1"></i>查看我的文件
                            </a>
                        </div>
                        <?php else: ?>
                        <!-- 批量操作工具栏 -->
                        <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                            <div class="d-flex align-items-center">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" id="selectAllSent">
                                    <label class="form-check-label" for="selectAllSent">
                                        全选
                                    </label>
                                </div>
                                <span class="text-muted small" id="selectedCountSent">已选择 0 个文件</span>
                            </div>
                            <div>
                                <button class="btn btn-danger btn-sm" id="batchDeleteSentBtn" disabled onclick="batchDeleteSent()">
                                    <i class="bi bi-trash me-1"></i>批量取消分享
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAllSentHeader">
                                            </div>
                                        </th>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>类型</th>
                                        <th>接收者</th>
                                        <th>分享时间</th>
                                        <th>下载量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($myShares as $file): ?>
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input file-checkbox-sent" type="checkbox"
                                                       value="<?= $file['share_id'] ?>"
                                                       data-file-name="<?= h($file['original_name']) ?>">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark me-2 text-primary"></i>
                                                <span class="text-truncate" style="max-width: 200px;" title="<?= h($file['original_name']) ?>">
                                                    <?= h($file['original_name']) ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td><?= formatFileSize($file['file_size']) ?></td>
                                        <td>
                                            <span class="badge bg-secondary"><?= h(strtoupper($file['file_type'] ?? 'Unknown')) ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-person-circle me-1 text-muted"></i>
                                                <?= h($file['to_username'] ?? '未知用户') ?>
                                            </div>
                                        </td>
                                        <td><?= date('Y-m-d H:i', strtotime($file['share_time'])) ?></td>
                                        <td>
                                            <span class="badge bg-info"><?= number_format($file['download_count']) ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="download.php?code=<?= h($file['share_code']) ?>" class="btn btn-outline-success" title="下载">
                                                    <i class="bi bi-download"></i>
                                                </a>
                                                <button class="btn btn-outline-danger" onclick="removeShare(<?= $file['share_id'] ?>)" title="取消分享">
                                                    <i class="bi bi-x-circle"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件详情模态框 -->
    <div class="modal fade" id="fileInfoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">文件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="fileInfoContent">
                    <!-- 文件详情内容将通过JavaScript加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 取消分享确认模态框 -->
    <div class="modal fade" id="removeShareModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认取消分享</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要取消这个文件的分享吗？接收者将无法再访问此文件。</p>
                    <form id="removeShareForm" method="post" action="api/remove_share.php">
                        <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                        <input type="hidden" id="removeShareId" name="share_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmRemoveShare()">确认取消分享</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量删除确认模态框 -->
    <div class="modal fade" id="batchDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="batchDeleteModalTitle">确认批量操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span id="batchDeleteMessage">确定要执行此批量操作吗？</span>
                    </div>
                    <div id="selectedFilesList" class="mt-3">
                        <!-- 选中的文件列表将在这里显示 -->
                    </div>
                    <form id="batchDeleteForm" method="post" action="api/batch_delete_shares.php">
                        <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                        <input type="hidden" id="batchDeleteShareIds" name="share_ids">
                        <input type="hidden" id="batchDeleteType" name="type">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmBatchDeleteBtn" onclick="confirmBatchDelete()">
                        <i class="bi bi-trash me-1"></i>确认操作
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        function showFileInfo(fileId) {
            // 这里应该通过AJAX获取文件详情
            fetch(`api/file_info.php?file_id=${fileId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const file = data.file;
                        const content = `
                            <div class="row">
                                <div class="col-sm-4"><strong>文件名：</strong></div>
                                <div class="col-sm-8">${file.original_name}</div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4"><strong>文件大小：</strong></div>
                                <div class="col-sm-8">${file.file_size_formatted}</div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4"><strong>文件类型：</strong></div>
                                <div class="col-sm-8">${file.file_type}</div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4"><strong>上传时间：</strong></div>
                                <div class="col-sm-8">${file.upload_time}</div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4"><strong>分享码：</strong></div>
                                <div class="col-sm-8">
                                    <code>${file.share_code}</code>
                                    <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('${file.share_code}')">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-sm-4"><strong>下载次数：</strong></div>
                                <div class="col-sm-8">${file.download_count}</div>
                            </div>
                        `;
                        document.getElementById('fileInfoContent').innerHTML = content;
                        new bootstrap.Modal(document.getElementById('fileInfoModal')).show();
                    } else {
                        showToast(data.message, 'error');
                    }
                })
                .catch(error => {
                    showToast('获取文件信息失败', 'error');
                });
        }

        function removeShare(shareId) {
            document.getElementById('removeShareId').value = shareId;
            new bootstrap.Modal(document.getElementById('removeShareModal')).show();
        }

        function confirmRemoveShare() {
            document.getElementById('removeShareForm').submit();
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('已复制到剪贴板', 'success');
            }).catch(() => {
                showToast('复制失败', 'error');
            });
        }

        function showToast(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);
            
            setTimeout(() => {
                const alert = document.querySelector('.alert:last-child');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        // 批量操作功能
        function initBatchOperations() {
            // 我的分享 - 全选/取消全选
            const selectAllSent = document.getElementById('selectAllSent');
            const selectAllSentHeader = document.getElementById('selectAllSentHeader');
            const fileCheckboxesSent = document.querySelectorAll('.file-checkbox-sent');
            const batchDeleteSentBtn = document.getElementById('batchDeleteSentBtn');
            const selectedCountSent = document.getElementById('selectedCountSent');

            // 同步主复选框 - 带表头复选框版本
            function syncSelectAllWithHeader(mainCheckbox, headerCheckbox, fileCheckboxes, countElement, batchBtn) {
                const updateUI = () => {
                    const checkedCount = Array.from(fileCheckboxes).filter(cb => cb.checked).length;
                    const allChecked = checkedCount === fileCheckboxes.length && fileCheckboxes.length > 0;
                    const someChecked = checkedCount > 0;

                    mainCheckbox.checked = allChecked;
                    headerCheckbox.checked = allChecked;
                    mainCheckbox.indeterminate = someChecked && !allChecked;
                    headerCheckbox.indeterminate = someChecked && !allChecked;

                    countElement.textContent = `已选择 ${checkedCount} 个文件`;
                    batchBtn.disabled = !someChecked;
                };

                const toggleAll = (checked) => {
                    fileCheckboxes.forEach(cb => cb.checked = checked);
                    updateUI();
                };

                mainCheckbox.addEventListener('change', () => toggleAll(mainCheckbox.checked));
                headerCheckbox.addEventListener('change', () => toggleAll(headerCheckbox.checked));
                fileCheckboxes.forEach(cb => cb.addEventListener('change', updateUI));

                updateUI();
            }

            // 同步主复选框 - 仅工具栏版本
            function syncSelectAllToolbarOnly(mainCheckbox, fileCheckboxes, countElement, batchBtn) {
                const updateUI = () => {
                    const checkedCount = Array.from(fileCheckboxes).filter(cb => cb.checked).length;
                    const allChecked = checkedCount === fileCheckboxes.length && fileCheckboxes.length > 0;
                    const someChecked = checkedCount > 0;

                    mainCheckbox.checked = allChecked;
                    mainCheckbox.indeterminate = someChecked && !allChecked;

                    countElement.textContent = `已选择 ${checkedCount} 个文件`;
                    batchBtn.disabled = !someChecked;
                };

                const toggleAll = (checked) => {
                    fileCheckboxes.forEach(cb => cb.checked = checked);
                    updateUI();
                };

                mainCheckbox.addEventListener('change', () => toggleAll(mainCheckbox.checked));
                fileCheckboxes.forEach(cb => cb.addEventListener('change', updateUI));

                updateUI();
            }

            // 初始化我的分享批量操作
            if (fileCheckboxesSent.length > 0) {
                syncSelectAllWithHeader(selectAllSent, selectAllSentHeader, fileCheckboxesSent, selectedCountSent, batchDeleteSentBtn);
            }
        }



        // 批量取消分享
        function batchDeleteSent() {
            const checkedBoxes = document.querySelectorAll('.file-checkbox-sent:checked');
            if (checkedBoxes.length === 0) {
                showToast('请选择要取消分享的文件', 'error');
                return;
            }

            const shareIds = Array.from(checkedBoxes).map(cb => cb.value);
            const fileNames = Array.from(checkedBoxes).map(cb => cb.dataset.fileName);

            showBatchDeleteModal('sent', shareIds, fileNames, '取消选中文件的分享');
        }

        // 显示批量删除确认模态框
        function showBatchDeleteModal(type, shareIds, fileNames, actionText) {
            document.getElementById('batchDeleteModalTitle').textContent = '确认批量操作';
            document.getElementById('batchDeleteMessage').textContent = `确定要${actionText}吗？此操作不可撤销。`;
            document.getElementById('batchDeleteShareIds').value = shareIds.join(',');
            document.getElementById('batchDeleteType').value = type;

            // 显示选中的文件列表
            const filesList = document.getElementById('selectedFilesList');
            filesList.innerHTML = `
                <h6>选中的文件 (${fileNames.length} 个):</h6>
                <ul class="list-group list-group-flush">
                    ${fileNames.map(name => `<li class="list-group-item py-1 px-0"><i class="bi bi-file-earmark me-2"></i>${name}</li>`).join('')}
                </ul>
            `;

            new bootstrap.Modal(document.getElementById('batchDeleteModal')).show();
        }

        // 确认批量删除
        function confirmBatchDelete() {
            const form = document.getElementById('batchDeleteForm');
            const formData = new FormData(form);

            // 禁用确认按钮防止重复提交
            const confirmBtn = document.getElementById('confirmBatchDeleteBtn');
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>处理中...';

            fetch('api/batch_delete_shares.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    // 关闭模态框并刷新页面
                    bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal')).hide();
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast(data.message, 'error');
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '<i class="bi bi-trash me-1"></i>确认操作';
                }
            })
            .catch(error => {
                showToast('操作失败，请重试', 'error');
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="bi bi-trash me-1"></i>确认操作';
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initBatchOperations();
        });

        // 自动刷新页面（如果有需要）
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('refresh') === '1') {
            setTimeout(() => {
                window.location.href = 'shared.php';
            }, 1000);
        }
    </script>
</body>
</html>
