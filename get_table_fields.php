<?php
// 引入配置文件
require_once 'config.php';

// 连接数据库
$conn = new mysqli($servername, $username, $password, $dbname);

// 检查连接
if ($conn->connect_error) {
    die('数据库连接失败: ' . $conn->connect_error);
}

// 获取表名
$table = $_GET['table'] ?? '';

// 获取表字段信息
$sql = "SHOW FULL COLUMNS FROM $table";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo '<table>';
    echo '<tr><th>字段名</th><th>类型</th><th>备注</th></tr>';
    while ($row = $result->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . $row['Field'] . '</td>';
        echo '<td>' . $row['Type'] . '</td>';
        echo '<td>' . $row['Comment'] . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '未找到表字段信息。';
}

$conn->close();
?>