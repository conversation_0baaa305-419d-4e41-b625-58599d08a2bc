<?php
/**
 * 文件网盘系统 - 分享码下载页面
 * 创建时间: 2025-06-23
 */

require_once 'functions.php';

$fileInfo = null;
$error = null;

// 处理分享码查询
if (isset($_GET['code']) || isset($_POST['share_code'])) {
    $shareCode = $_GET['code'] ?? $_POST['share_code'] ?? '';
    $shareCode = trim($shareCode);
    
    if (empty($shareCode)) {
        $error = '请输入分享码';
    } elseif (strlen($shareCode) !== 6) {
        $error = '分享码格式不正确（应为6位字符）';
    } else {
        try {
            // 获取当前用户ID，如果未登录则为0
            $currentUserId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;

            $stmt = $pdo->prepare("
                SELECT f.*, u.name as uploader_name
                FROM filecloud_info f
                LEFT JOIN 3_user u ON f.user_id = u.id
                LEFT JOIN filecloud_share s ON f.file_id = s.file_id AND s.to_user_id = ? AND s.is_deleted = 0
                WHERE BINARY f.share_code = ? AND f.is_deleted = 0 AND (f.is_public = 1 OR f.user_id = ? OR s.share_id IS NOT NULL)
            ");
            $stmt->execute([$currentUserId, $shareCode, $currentUserId]);
            $fileInfo = $stmt->fetch();
            
            if (!$fileInfo) {
                $error = '分享码不存在或文件已被删除';
            } elseif (isShareCodeExpired($fileInfo['expiration_time'])) {
                $error = '分享码已过期';
            }
        } catch (PDOException $e) {
            $error = '查询失败，请稍后重试';
        }
    }
}

// 处理文件下载
if (isset($_GET['download']) && isset($_GET['code'])) {
    $shareCode = $_GET['code'];

    try {
        // 获取当前用户ID，如果未登录则为0
        $currentUserId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;

        $stmt = $pdo->prepare("\n            SELECT f.* FROM filecloud_info f
            LEFT JOIN filecloud_share s ON f.file_id = s.file_id AND s.to_user_id = ? AND s.is_deleted = 0
            WHERE BINARY f.share_code = ? AND f.is_deleted = 0 AND (f.is_public = 1 OR f.user_id = ? OR s.share_id IS NOT NULL)\n        ");
        $stmt->execute([$currentUserId, $shareCode, $currentUserId]);
        $file = $stmt->fetch();
        
        if (!$file) {
            die('文件不存在');
        }
        
        if (isShareCodeExpired($file['expiration_time'])) {
            die('分享码已过期');
        }
        
        $filePath = $file['file_path'];
        if (!file_exists($filePath)) {
            die('文件已丢失');
        }
        
        // 增加下载次数
        incrementDownloadCount($file['file_id']);
        
        // 设置下载头部
        $mimeType = getMimeType($filePath);
        $filename = $file['original_name'];
        
        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: no-cache, must-revalidate');
        header('Pragma: no-cache');
        
        // 输出文件内容
        readfile($filePath);
        exit;
        
    } catch (PDOException $e) {
        die('下载失败');
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 分享码下载</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .download-container {
            min-height: calc(100vh - 200px);
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        
        .download-card {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 15px;
            overflow: hidden;
        }
        
        .download-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .share-code-input {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .share-code-input input {
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            letter-spacing: 3px;
            text-transform: none;
        }
        
        .file-preview {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            margin: 1.5rem 0;
        }
        
        .file-icon {
            font-size: 4rem;
            color: #0d6efd;
            margin-bottom: 1rem;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .error-container {
            text-align: center;
            padding: 3rem 2rem;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }

        /* 使用说明模块样式 */
        .usage-guide {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .usage-step {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .usage-step:hover {
            transform: translateY(-5px);
            border-color: rgba(13, 110, 253, 0.2);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .feature-item {
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-2px);
        }

        .feature-item i {
            transition: all 0.3s ease;
        }

        .feature-item:hover i {
            transform: scale(1.1);
        }

        /* 消除卡片间隙 */
        .usage-guide-container {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        .download-card {
            margin-bottom: 0 !important;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 简化的导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-cloud-download me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="anonymous_upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>匿名上传
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-house me-1"></i>首页
                        </a>
                    </li>
                    <?php if (!isLoggedIn()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">
                            <i class="bi bi-box-arrow-in-right me-1"></i>登录
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container download-container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7 col-sm-9">
                <div class="card download-card">
                    <div class="download-header">
                        <h2 class="mb-0">
                            <i class="bi bi-download me-2"></i>文件下载
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">输入6位分享码下载文件</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                        <!-- 错误信息 -->
                        <div class="error-container">
                            <div class="error-icon">
                                <i class="bi bi-x-circle-fill"></i>
                            </div>
                            <h5 class="text-danger mb-3"><?= h($error) ?></h5>
                            <button class="btn btn-outline-primary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise me-1"></i>重新查询
                            </button>
                        </div>
                        
                        <?php elseif ($fileInfo): ?>
                        <!-- 文件信息展示 -->
                        <div class="file-preview">
                            <div class="file-icon">
                                <?php
                                $iconMap = [
                                    'pdf' => 'bi-file-pdf',
                                    'doc' => 'bi-file-word', 'docx' => 'bi-file-word',
                                    'xls' => 'bi-file-excel', 'xlsx' => 'bi-file-excel',
                                    'ppt' => 'bi-file-ppt', 'pptx' => 'bi-file-ppt',
                                    'jpg' => 'bi-file-image', 'jpeg' => 'bi-file-image', 'png' => 'bi-file-image', 'gif' => 'bi-file-image',
                                    'zip' => 'bi-file-zip', 'rar' => 'bi-file-zip',
                                    'txt' => 'bi-file-text'
                                ];
                                $iconClass = $iconMap[$fileInfo['file_type']] ?? 'bi-file-earmark';
                                ?>
                                <i class="bi <?= $iconClass ?>"></i>
                            </div>
                            <h5 class="mb-3"><?= h($fileInfo['original_name']) ?></h5>
                            
                            <div class="info-item">
                                <span><i class="bi bi-hdd me-2"></i>文件大小</span>
                                <strong><?= formatFileSize($fileInfo['file_size']) ?></strong>
                            </div>
                            
                            <div class="info-item">
                                <span><i class="bi bi-calendar me-2"></i>上传时间</span>
                                <strong><?= date('Y-m-d H:i', strtotime($fileInfo['upload_time'])) ?></strong>
                            </div>
                            
                            <?php if (!empty($fileInfo['uploader_name'])): ?>
                            <div class="info-item">
                                <span><i class="bi bi-person me-2"></i>上传者</span>
                                <strong><?= h($fileInfo['uploader_name']) ?></strong>
                            </div>
                            <?php endif; ?>
                            
                            <div class="info-item">
                                <span><i class="bi bi-download me-2"></i>下载次数</span>
                                <strong><?= number_format($fileInfo['download_count']) ?></strong>
                            </div>
                            
                            <?php if (!empty($fileInfo['expiration_time'])): ?>
                            <div class="info-item">
                                <span><i class="bi bi-clock me-2"></i>过期时间</span>
                                <strong><?= date('Y-m-d H:i', strtotime($fileInfo['expiration_time'])) ?></strong>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="text-center">
                            <a href="download.php?download=1&code=<?= h($fileInfo['share_code']) ?>" 
                               class="btn btn-success download-btn btn-lg">
                                <i class="bi bi-download me-2"></i>立即下载
                            </a>
                        </div>
                        
                        <div class="text-center mt-3">
                            <button class="btn btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise me-1"></i>查询其他文件
                            </button>
                        </div>
                        
                        <?php else: ?>
                        <!-- 分享码输入表单 -->
                        <form method="post" id="shareCodeForm">
                            <div class="share-code-input">
                                <label for="shareCode" class="form-label text-center w-100">
                                    <i class="bi bi-key me-2"></i>请输入6位分享码
                                </label>
                                <input type="text" 
                                       class="form-control form-control-lg" 
                                       id="shareCode" 
                                       name="share_code" 
                                       maxlength="6" 
                                       placeholder=""
                                       autocomplete="off"
                                       required>
                                <div class="form-text text-center">
                                    分享码由数字和字母组成，不区分大小写
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-search me-2"></i>查询文件
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="bi bi-shield-check me-1"></i>
                                安全下载 · 无需注册 · 便捷有效
                            </small>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用说明卡片 - 在同一个容器内 -->
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7 col-sm-9">
                <div class="card border-0 shadow-sm usage-guide">
                    <div class="card-body py-4 px-4">
                        <h5 class="text-center text-dark mb-4 fw-bold">
                            <i class="bi bi-info-circle me-2 text-primary"></i>文件下载使用说明
                        </h5>
                        <div class="row g-3 mb-4">
                            <div class="col-lg-4 col-md-4 col-sm-12">
                                <div class="usage-step text-center p-3 bg-white rounded-3 h-100 shadow-sm">
                                    <div class="mb-2">
                                        <i class="bi bi-1-circle-fill text-primary" style="font-size: 2rem;"></i>
                                    </div>
                                    <h6 class="mb-2 fw-bold text-dark">输入分享码</h6>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-12">
                                <div class="usage-step text-center p-3 bg-white rounded-3 h-100 shadow-sm">
                                    <div class="mb-2">
                                        <i class="bi bi-2-circle-fill text-success" style="font-size: 2rem;"></i>
                                    </div>
                                    <h6 class="mb-2 fw-bold text-dark">查看文件信息</h6>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-sm-12">
                                <div class="usage-step text-center p-3 bg-white rounded-3 h-100 shadow-sm">
                                    <div class="mb-2">
                                        <i class="bi bi-3-circle-fill text-warning" style="font-size: 2rem;"></i>
                                    </div>
                                    <h6 class="mb-2 fw-bold text-dark">点击立即下载</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 分享码输入格式化
        const shareCodeInput = document.getElementById('shareCode');
        if (shareCodeInput) {
            shareCodeInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/[^A-Za-z0-9]/g, '');
                if (value.length > 6) {
                    value = value.substring(0, 6);
                }
                e.target.value = value;
            });
            
            // 自动提交（输入6位后）
            shareCodeInput.addEventListener('input', function(e) {
                if (e.target.value.length === 6) {
                    setTimeout(() => {
                        document.getElementById('shareCodeForm').submit();
                    }, 500);
                }
            });
            
            // 自动获取焦点
            shareCodeInput.focus();
        }
        
        // 复制分享码功能（如果从URL传入）
        const urlParams = new URLSearchParams(window.location.search);
        const codeFromUrl = urlParams.get('code');
        if (codeFromUrl && shareCodeInput) {
            shareCodeInput.value = codeFromUrl.toUpperCase();
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // 按ESC键清空输入
            if (e.key === 'Escape' && shareCodeInput) {
                shareCodeInput.value = '';
                shareCodeInput.focus();
            }
            
            // 按Enter键提交表单
            if (e.key === 'Enter' && shareCodeInput && shareCodeInput.value.length === 6) {
                document.getElementById('shareCodeForm').submit();
            }
        });
        
        // 下载按钮点击效果
        const downloadBtn = document.querySelector('.download-btn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', function() {
                this.innerHTML = '<i class="bi bi-download me-2"></i>下载中...';
                this.disabled = true;
                
                // 3秒后恢复按钮状态
                setTimeout(() => {
                    this.innerHTML = '<i class="bi bi-download me-2"></i>立即下载';
                    this.disabled = false;
                }, 3000);
            });
        }
    </script>
</body>
</html>
