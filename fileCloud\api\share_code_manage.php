<?php
/**
 * 文件网盘系统 - 分享码管理API
 * 创建时间: 2025-07-29
 */

require_once '../functions.php';

header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => '请先登录'], 401);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => '无效的请求方法'], 405);
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => '无效的请求'], 400);
}

$action = $_POST['action'] ?? '';
$fileId = (int)($_POST['file_id'] ?? 0);
$currentUserId = $_SESSION['user_id'];

// 验证文件ID
if ($fileId <= 0) {
    jsonResponse(['success' => false, 'message' => '参数错误：无效的文件ID'], 400);
}

try {
    // 检查文件是否存在且属于当前用户
    $stmt = $pdo->prepare("
        SELECT file_id, original_name, share_code, expiration_time 
        FROM filecloud_info 
        WHERE file_id = ? AND user_id = ? AND is_deleted = 0
    ");
    $stmt->execute([$fileId, $currentUserId]);
    $file = $stmt->fetch();
    
    if (!$file) {
        jsonResponse(['success' => false, 'message' => '文件不存在或无权限'], 404);
    }

    if ($action === 'generate_share_code') {
        // 生成新的分享码（仅当文件没有分享码时）
        if (!empty($file['share_code'])) {
            jsonResponse(['success' => false, 'message' => '文件已有分享码，请使用重新生成功能'], 400);
        }

        $shareCode = generateShareCode();
        $expirationTime = date('Y-m-d H:i:s', time() + (DEFAULT_EXPIRE_DAYS * 24 * 60 * 60));

        $updateStmt = $pdo->prepare("
            UPDATE filecloud_info 
            SET share_code = ?, expiration_time = ?, is_public = 1 
            WHERE file_id = ?
        ");
        $updateStmt->execute([$shareCode, $expirationTime, $fileId]);

        jsonResponse([
            'success' => true,
            'message' => '分享码生成成功',
            'share_code' => $shareCode,
            'expiration_time' => $expirationTime
        ]);

    } elseif ($action === 'regenerate_share_code') {
        // 重新生成分享码（覆盖原有的）
        $shareCode = generateShareCode();
        $expirationTime = date('Y-m-d H:i:s', time() + (DEFAULT_EXPIRE_DAYS * 24 * 60 * 60));

        $updateStmt = $pdo->prepare("
            UPDATE filecloud_info
            SET share_code = ?, expiration_time = ?, is_public = 1
            WHERE file_id = ?
        ");
        $updateStmt->execute([$shareCode, $expirationTime, $fileId]);

        jsonResponse([
            'success' => true,
            'message' => '分享码重新生成成功',
            'share_code' => $shareCode,
            'expiration_time' => $expirationTime
        ]);

    } elseif ($action === 'cancel_share_code') {
        // 取消分享码（删除分享码并设置为私有）
        $updateStmt = $pdo->prepare("
            UPDATE filecloud_info
            SET share_code = NULL, expiration_time = NULL, is_public = 0
            WHERE file_id = ?
        ");
        $updateStmt->execute([$fileId]);

        jsonResponse([
            'success' => true,
            'message' => '分享码已取消'
        ]);

    } else {
        jsonResponse(['success' => false, 'message' => '无效的操作'], 400);
    }

} catch (PDOException $e) {
    error_log('分享码管理错误: ' . $e->getMessage() . ' | 文件: ' . $e->getFile() . ' | 行号: ' . $e->getLine());
    jsonResponse(['success' => false, 'message' => '操作失败: ' . $e->getMessage()], 500);
}
?>
