import{_ as Ve,h as s,x as q,j as he,k as we,m as _,A,r as m,d as w,o as c,b as d,a as o,w as n,n as ke,t as te,c as y,$ as oe,l as B,F as U,e as M,f as xe,i as De,g as Ye}from"./index-D8fAg56F.js";import{s as g}from"./requests-B1avZrnq.js";/* empty css              */const Ue={class:"user-management-container"},Me={class:"left-panel"},Ce=["onClick"],ze={class:"right-panel"},Ie={class:"action-buttons",style:{display:"flex","align-items":"center"}},je={class:"left-buttons",style:{display:"flex","align-items":"center",gap:"3px"}},Se={class:"right-switches",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},Fe={class:"form-row"},Oe={class:"form-row"},Ae={class:"form-row"},Ne={class:"form-row"},Pe={class:"form-row"},Te={class:"form-row"},$e={class:"form-row"},Ee={class:"form-row"},qe={class:"dialog-footer"},Be={__name:"AddressBook",setup(R){const C=s([]),X=s([]),G=s([]),V=s(null);s("");const k=s(""),N=s([]),x=s([]),H=q(()=>{if(!V.value)return"";const l=h.value.find(e=>e.id===V.value);return console.log("当前选中的部门ID:",V.value),console.log("当前选中的部门:",l.unit_name),l?l.unit_name:""});s(null);const f=s(1),z=s(50),J=s(0),I=s([]),h=s([]),j=s(""),K=s([]),Q=s([]),P=s([]);s([]);const D=s(!1),ne=s(""),T=s(!1);s({}),s(!1);const $=s(!1),W=s(null),re=()=>{T.value?D.value=!1:_e()},se=q(()=>j.value?h.value.filter(l=>l.show&&l.unit_name.toLowerCase().includes(j.value.toLowerCase())):h.value.filter(l=>l.show)),t=he({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sort_order:null,desc:""}),ue={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},E=s(null),pe=async()=>{try{const l=new FormData;l.append("type","identity");const e=await g({url:"/api/person_info_api.php",method:"post",data:l});e.status===1?C.value=e.data.map(r=>({label:r,value:r})):_.error("获取人员身份数据失败："+e.message)}catch{_.error("网络请求失败")}},de=async()=>{try{const l=new FormData;l.append("type","status");const e=await g({url:"/api/person_info_api.php",method:"post",data:l});e.status===1?X.value=e.data.map(r=>({label:r,value:r})):_.error("获取人员状态数据失败："+e.message)}catch{_.error("网络请求失败")}},ie=async()=>{try{const l=new FormData;l.append("type","rank");const e=await g({url:"/api/person_info_api.php",method:"post",data:l});e.status===1?G.value=e.data.map(r=>({label:r,value:r})):_.error("获取职级数据失败："+e.message)}catch{_.error("网络请求失败")}};we(async()=>{try{const l=await g.post("api/get_unit_info.php");I.value=[l.data],Z(l.data),K.value=I.value,Q.value=I.value,console.log("获取部门数据成功:",I.value),await v(),pe(),de(),ie()}catch(l){console.error("获取数据失败:",l),_.error("获取数据失败，请稍后重试")}});const v=async()=>{console.log("开始获取用户数据...");try{const l=new FormData;l.append("controlCode","query"),l.append("page",f.value),l.append("pagesize",z.value),V.value&&l.append("organization_unit",V.value),k.value&&l.append("search_keyword",k.value),F.value?l.append("isShowInPersonnel","1"):l.append("isShowInPersonnel","0"),S.value?l.append("isShowOutPersonnel","1"):l.append("isShowOutPersonnel","0");const e=await g.post("api/user_manage.php",l,{headers:{"Content-Type":"multipart/form-data"}});P.value=e.data,console.log("获取用户数据成功:",P.value),J.value=e.total}catch(l){console.error("获取用户数据失败:",l),_.error("获取用户数据失败，请稍后重试")}},Z=(l,e=0,r=null)=>{const p={...l,level:e,expanded:l.children&&l.children.length>0,parent:r,indent:e*20,show:!0};h.value.push(p),l.children&&l.children.length>0&&l.children.forEach(O=>{Z(O,e+1,p)})};console.log("扁平化后的部门列表:",h.value);const S=s(!0),F=s(!0),me=async l=>{console.log("点击的部门名称:",l),V.value=l.id,await v()},_e=async()=>{try{await E.value.validate();const l=new FormData;$.value?(l.append("controlCode","modify"),l.append("id",W.value)):l.append("controlCode","add"),l.append("name",t.name),l.append("id_number",t.id_number||""),l.append("phone",t.phone),l.append("archive_birthdate",t.archive_birthdate||""),l.append("gender",t.gender||""),l.append("short_code",t.short_code||""),l.append("alt_phone_1",t.alt_phone_1||""),l.append("alt_phone_2",t.alt_phone_2||""),l.append("landline",t.landline||"");const e=Array.isArray(t.organization_unit)?t.organization_unit[t.organization_unit.length-1]:t.organization_unit;l.append("organization_unit",e||"");const r=Array.isArray(t.work_unit)?t.work_unit[t.work_unit.length-1]:t.work_unit;l.append("work_unit",r||e),l.append("employment_date",t.employment_date||""),l.append("political_status",t.political_status||""),l.append("party_join_date",t.party_join_date||""),l.append("personnel_type",t.personnel_type||""),l.append("police_number",t.police_number||""),l.append("assisting_officer",t.is_assisting_officer||""),l.append("employment_status",t.employment_status||""),l.append("job_rank",t.job_rank||""),l.append("current_rank_date",t.current_rank_date||""),l.append("position",t.position||""),l.append("current_position_date",t.current_position_date||""),l.append("sort_order",t.sort_order+1),l.append("desc",t.desc||""),await g.post("api/user_manage.php",l,{headers:{"Content-Type":"multipart/form-data"}});const p=$.value?"编辑":"添加";_.success(`用户${p}成功`),D.value=!1,$.value=!1,W.value=null,await v(),E.value.resetFields()}catch(l){_.error("提交失败："+(l.message||"未知错误"))}};q(()=>{const l=new Map;return h.value.forEach(e=>{l.set(e.id,e.unit_name)}),l}),A(F,async l=>{f.value=1,await v()}),A(S,async l=>{f.value=1,await v()}),A(k,async l=>{f.value=1,await v()});const ce=async l=>{if(!l){x.value=[];return}try{const e=new FormData;e.append("controlCode","getSortOptions"),e.append("organization_unit",l);const r=await g.post("/api/user_manage.php",e,{headers:{"Content-Type":"multipart/form-data"}});x.value=r.data,console.log("获取排序选项成功:",x.value)}catch(e){console.error("获取排序选项失败:",e),_.error("获取排序选项失败，请稍后重试"),x.value=[]}},fe=async l=>{if(!l){N.value=[];return}try{const e=new FormData;e.append("controlCode","getPspInfo"),e.append("organization_unit",l);const r=await g({url:"/api/user_manage.php",method:"post",data:e});r.status===1?N.value=r.data.map(p=>({label:p.name,value:p.id})):_.error("获取带辅民警数据失败")}catch{_.error("网络请求失败")}};return A(()=>t.organization_unit,async l=>{const e=Array.isArray(l)?l[l.length-1]:l;await ce(e),await fe(e)}),(l,e)=>{const r=m("el-input"),p=m("el-table-column"),O=m("el-table"),ve=m("el-tag"),ee=m("el-switch"),ge=m("el-pagination"),u=m("el-form-item"),Y=m("el-date-picker"),i=m("el-option"),b=m("el-select"),le=m("el-cascader"),be=m("el-form"),ae=m("el-button"),ye=m("el-dialog");return c(),w("div",Ue,[d("div",Me,[o(r,{modelValue:j.value,"onUpdate:modelValue":e[0]||(e[0]=a=>j.value=a),placeholder:"搜索单位",class:"department-search",clearable:""},null,8,["modelValue"]),o(O,{data:se.value,border:"",stripe:"",fit:"",class:"department-table"},{default:n(()=>[o(p,{prop:"unit_name",label:"单位名称"},{default:n(({row:a})=>[d("span",{class:"indent",style:ke({width:`${a.indent}px`})},null,4),d("span",{onClick:Re=>me(a),style:{cursor:"pointer"}},te(a.unit_name),9,Ce)]),_:1})]),_:1},8,["data"])]),d("div",ze,[d("div",Ie,[d("div",je,[o(r,{modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=a=>k.value=a),placeholder:"搜索用户...",style:{width:"300px"},clearable:""},null,8,["modelValue"])]),d("div",Se,[H.value?(c(),y(ve,{key:0,type:"info",style:{"margin-left":"10px"}},{default:n(()=>[B(" 当前单位："+te(H.value),1)]),_:1})):oe("",!0),o(ee,{modelValue:S.value,"onUpdate:modelValue":e[2]||(e[2]=a=>S.value=a),"inline-prompt":"","active-text":"显示抽出人员","inactive-text":"不显示抽出人员",style:{"margin-left":"10px"}},null,8,["modelValue"]),o(ee,{modelValue:F.value,"onUpdate:modelValue":e[3]||(e[3]=a=>F.value=a),"active-text":"显示抽入人员","inactive-text":"显示抽入人员","inline-prompt":"",style:{"margin-left":"10px"}},null,8,["modelValue"])])]),o(O,{data:P.value,border:"",stripe:"",fit:"",class:"user-table"},{default:n(()=>[o(p,{type:"index",label:"序号",width:"60",align:"center",index:a=>(f.value-1)*z.value+a+1},null,8,["index"]),o(p,{prop:"name",label:"姓名",width:"80",align:"center"}),o(p,{label:"编制单位",prop:"organization_unitName",width:"120",align:"center"}),o(p,{label:"工作单位",prop:"work_unitName",width:"120",align:"center"}),o(p,{prop:"position",label:"职务",align:"center"}),o(p,{prop:"phone",label:"电话I",align:"center"}),o(p,{prop:"alt_phone_1",label:"电话II",align:"center"}),o(p,{prop:"alt_phone_2",label:"电话III",align:"center"}),o(p,{prop:"short_code",label:"短号",align:"center"}),o(p,{prop:"landline",label:"座机",align:"center"}),o(p,{prop:"desc",label:"备注",align:"center"})]),_:1},8,["data"]),o(ge,{"current-page":f.value,"page-size":z.value,total:J.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:e[4]||(e[4]=a=>{f.value=a,v()}),onSizeChange:e[5]||(e[5]=a=>{z.value=a,f.value=1,v()})},null,8,["current-page","page-size","total"])]),o(ye,{modelValue:D.value,"onUpdate:modelValue":e[31]||(e[31]=a=>D.value=a),title:ne.value,width:"1000px"},{footer:n(()=>[d("span",qe,[T.value?oe("",!0):(c(),y(ae,{key:0,onClick:e[30]||(e[30]=a=>D.value=!1)},{default:n(()=>e[33]||(e[33]=[B("取消")])),_:1,__:[33]})),o(ae,{type:"primary",onClick:re},{default:n(()=>e[34]||(e[34]=[B("确定")])),_:1,__:[34]})])]),default:n(()=>[o(be,{model:t,rules:ue,ref_key:"newUserFormRef",ref:E,"label-width":"120px",inline:!1,class:"user-form",disabled:T.value},{default:n(()=>[d("div",Fe,[o(u,{label:"姓名",prop:"name",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.name,"onUpdate:modelValue":e[6]||(e[6]=a=>t.name=a)},null,8,["modelValue"])]),_:1}),o(u,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.id_number,"onUpdate:modelValue":e[7]||(e[7]=a=>t.id_number=a),maxlength:"18"},null,8,["modelValue"])]),_:1}),o(u,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.phone,"onUpdate:modelValue":e[8]||(e[8]=a=>t.phone=a),maxlength:"11"},null,8,["modelValue"])]),_:1})]),d("div",Oe,[o(u,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:n(()=>[o(Y,{modelValue:t.archive_birthdate,"onUpdate:modelValue":e[9]||(e[9]=a=>t.archive_birthdate=a),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),o(u,{label:"性别",prop:"gender",style:{flex:"1"}},{default:n(()=>[o(b,{modelValue:t.gender,"onUpdate:modelValue":e[10]||(e[10]=a=>t.gender=a)},{default:n(()=>[o(i,{label:"未知",value:0}),o(i,{label:"男",value:1}),o(i,{label:"女",value:2})]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"备注",prop:"desc",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.desc,"onUpdate:modelValue":e[11]||(e[11]=a=>t.desc=a)},null,8,["modelValue"])]),_:1})]),d("div",Ae,[o(u,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.short_code,"onUpdate:modelValue":e[12]||(e[12]=a=>t.short_code=a)},null,8,["modelValue"]),e[32]||(e[32]=d("div",{class:""},null,-1))]),_:1,__:[32]}),o(u,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.alt_phone_1,"onUpdate:modelValue":e[13]||(e[13]=a=>t.alt_phone_1=a)},null,8,["modelValue"])]),_:1}),o(u,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.alt_phone_2,"onUpdate:modelValue":e[14]||(e[14]=a=>t.alt_phone_2=a)},null,8,["modelValue"])]),_:1})]),d("div",Ne,[o(u,{label:"座机",prop:"landline",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.landline,"onUpdate:modelValue":e[15]||(e[15]=a=>t.landline=a)},null,8,["modelValue"])]),_:1}),o(u,{label:"编制单位",prop:"organization_unit",required:""},{default:n(()=>[o(le,{modelValue:t.organization_unit,"onUpdate:modelValue":e[16]||(e[16]=a=>t.organization_unit=a),options:K.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),o(u,{label:"工作单位",prop:"work_unit"},{default:n(()=>[o(le,{modelValue:t.work_unit,"onUpdate:modelValue":e[17]||(e[17]=a=>t.work_unit=a),options:Q.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),d("div",Pe,[o(u,{label:"参工日期",prop:"employment_date",style:{flex:"1"}},{default:n(()=>[o(Y,{modelValue:t.employment_date,"onUpdate:modelValue":e[18]||(e[18]=a=>t.employment_date=a),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),o(u,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:n(()=>[o(b,{modelValue:t.political_status,"onUpdate:modelValue":e[19]||(e[19]=a=>t.political_status=a),placeholder:"请选择政治面貌"},{default:n(()=>[o(i,{label:"中共党员",value:"中共党员"}),o(i,{label:"中共预备党员",value:"中共预备党员"}),o(i,{label:"共青团员",value:"共青团员"}),o(i,{label:"民主党派",value:"民主党派"}),o(i,{label:"无党派人士",value:"无党派人士"}),o(i,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"加入组织日期",prop:"party_join_date",style:{flex:"1"}},{default:n(()=>[o(Y,{modelValue:t.party_join_date,"onUpdate:modelValue":e[20]||(e[20]=a=>t.party_join_date=a),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),d("div",Te,[o(u,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:n(()=>[o(b,{modelValue:t.personnel_type,"onUpdate:modelValue":e[21]||(e[21]=a=>t.personnel_type=a),placeholder:"请选择人员身份"},{default:n(()=>[(c(!0),w(U,null,M(C.value,a=>(c(),y(i,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.police_number,"onUpdate:modelValue":e[22]||(e[22]=a=>t.police_number=a)},null,8,["modelValue"])]),_:1}),o(u,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:n(()=>[o(b,{modelValue:t.is_assisting_officer,"onUpdate:modelValue":e[23]||(e[23]=a=>t.is_assisting_officer=a),placeholder:"请选择带辅民警",clearable:""},{default:n(()=>[(c(!0),w(U,null,M(N.value,a=>(c(),y(i,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),d("div",$e,[o(u,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:n(()=>[o(b,{modelValue:t.employment_status,"onUpdate:modelValue":e[24]||(e[24]=a=>t.employment_status=a)},{default:n(()=>[(c(!0),w(U,null,M(X.value,a=>(c(),y(i,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:n(()=>[o(b,{modelValue:t.job_rank,"onUpdate:modelValue":e[25]||(e[25]=a=>t.job_rank=a)},{default:n(()=>[(c(!0),w(U,null,M(G.value,a=>(c(),y(i,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(u,{label:"任现职级日期",prop:"current_rank_date",style:{flex:"1"}},{default:n(()=>[o(Y,{modelValue:t.current_rank_date,"onUpdate:modelValue":e[26]||(e[26]=a=>t.current_rank_date=a),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),d("div",Ee,[o(u,{label:"职务",prop:"position",style:{flex:"1"}},{default:n(()=>[o(r,{modelValue:t.position,"onUpdate:modelValue":e[27]||(e[27]=a=>t.position=a)},null,8,["modelValue"])]),_:1}),o(u,{label:"任现职务日期",prop:"current_position_date",style:{flex:"1"}},{default:n(()=>[o(Y,{modelValue:t.current_position_date,"onUpdate:modelValue":e[28]||(e[28]=a=>t.current_position_date=a),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),o(u,{label:"人员排序",prop:"sort_order",style:{flex:"1"}},{default:n(()=>[o(b,{modelValue:t.sort_order,"onUpdate:modelValue":e[29]||(e[29]=a=>t.sort_order=a),placeholder:"请选择人员排序"},{default:n(()=>[o(i,{label:"置于 最前",value:0}),(c(!0),w(U,null,M(x.value,a=>(c(),y(i,{key:a.id,label:`置于 ${a.name} 之后`,value:a.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","disabled"])]),_:1},8,["modelValue","title"])])}}},Le=Ve(Be,[["__scopeId","data-v-5b282c81"]]),L=xe(Le);L.use(De);for(const[R,C]of Object.entries(Ye))L.component(R,C);L.mount("#app");
