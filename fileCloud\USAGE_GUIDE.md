# 管理员批量删除文件功能使用指南

## 🎯 功能概述

系统现在提供了两种文件管理界面和完整的删除功能：

### 1. 标准管理界面（兼容模式）
- **文件**：`admin_files.php`
- **功能**：与原有界面完全兼容，仅支持软删除
- **特点**：无需额外确认，保持原有操作习惯

### 2. 增强管理界面（完整功能）
- **文件**：`admin_files_enhanced.php`
- **功能**：支持软删除和硬删除，提供完整的确认机制
- **特点**：可视化选择删除类型，双重确认保护

## 🔧 删除类型说明

### 软删除（Soft Delete）
- **操作**：仅标记文件为已删除（`is_deleted = 1`）
- **特点**：
  - 物理文件保留在服务器上
  - 数据库记录保留
  - 可以恢复（需要开发恢复功能）
  - 不释放存储空间
- **用途**：日常管理，防止误删

### 硬删除（Hard Delete）
- **操作**：完全删除文件和数据库记录
- **特点**：
  - 物理文件从服务器删除
  - 数据库记录完全删除
  - 清理相关分享记录
  - 不可恢复
  - 释放存储空间
- **用途**：永久清理，释放空间

## 📱 使用方法

### 方法1：使用标准界面（推荐日常使用）

1. 访问 `admin_files.php`
2. 选择要删除的文件（单选或多选）
3. 点击删除按钮
4. 在确认对话框中点击"确认删除"
5. 系统自动执行软删除

**特点**：
- 操作简单，与原有界面一致
- 自动使用软删除，安全性高
- 无需额外确认步骤

### 方法2：使用增强界面（推荐高级用户）

1. 访问 `admin_files_enhanced.php`
2. 选择要删除的文件
3. 选择删除类型：
   - 点击"批量软删除"按钮（软删除）
   - 点击"批量硬删除"按钮（硬删除）
4. 在弹出的对话框中：
   - 选择删除类型（软删除/硬删除）
   - 勾选确认框
   - 如果是硬删除，还需勾选额外确认框
5. 点击"确认删除"

**特点**：
- 功能完整，支持两种删除类型
- 多重确认机制，防止误操作
- 可视化界面，操作直观

### 方法3：直接API调用

```javascript
// 软删除示例
const formData = new FormData();
formData.append('csrf_token', 'your_csrf_token');
formData.append('file_ids', '1,2,3');
formData.append('delete_type', 'soft');
formData.append('confirmed', 'true');

fetch('api/admin_batch_delete_files.php', {
    method: 'POST',
    body: formData
});

// 硬删除示例
const formData = new FormData();
formData.append('csrf_token', 'your_csrf_token');
formData.append('file_ids', '1,2,3');
formData.append('delete_type', 'hard');
formData.append('confirmed', 'true');
formData.append('hard_confirmed', 'true');

fetch('api/admin_batch_delete_files.php', {
    method: 'POST',
    body: formData
});
```

## 🛡️ 安全机制

### 权限验证
- 必须以管理员身份登录
- 自动验证CSRF令牌
- 检查用户权限

### 确认机制
- **基础确认**：所有删除操作都需要确认
- **硬删除额外确认**：硬删除需要双重确认
- **兼容模式**：标准界面自动通过确认

### 操作限制
- 单次最多删除50个文件
- 严格的参数验证
- 事务完整性保证

## 📝 操作日志

所有删除操作都会记录到系统日志中，包含：
- 操作时间
- 操作用户
- 删除类型
- 文件列表
- 操作结果
- IP地址

可以通过以下方式查看日志：
1. 访问 `test_admin_batch_delete.php` 的"操作日志查看"部分
2. 直接调用 `api/admin_operation_logs.php`

## 🔄 兼容性说明

### 现有界面兼容
- `admin_files.php` 保持原有功能不变
- 自动添加兼容模式参数
- 用户无需改变操作习惯

### API兼容
- 支持兼容模式调用
- 向后兼容现有代码
- 新功能通过新参数启用

## ⚠️ 注意事项

### 硬删除风险
- **不可恢复**：硬删除的文件无法恢复
- **数据丢失**：会永久删除物理文件和数据库记录
- **谨慎操作**：建议先使用软删除，确认无误后再硬删除

### 存储空间
- 软删除不会释放存储空间
- 硬删除会立即释放存储空间
- 定期清理软删除文件可以释放空间

### 性能影响
- 批量操作会消耗更多系统资源
- 建议分批处理大量文件
- 硬删除比软删除耗时更长

## 🚀 推荐使用流程

### 日常管理
1. 使用标准界面进行日常文件删除（软删除）
2. 定期检查已删除文件列表
3. 确认不需要的文件使用硬删除清理

### 空间清理
1. 使用增强界面查看所有文件（包含已删除）
2. 对确认不需要的文件执行硬删除
3. 检查操作日志确认删除结果

### 批量清理
1. 使用搜索功能筛选目标文件
2. 批量选择文件
3. 根据需要选择软删除或硬删除
4. 确认操作并执行

---

**更新时间**：2025-07-08  
**版本**：v1.0  
**状态**：✅ 可用
