# 人员调动模拟系统 (PerSimTS)

## 系统简介

人员调动模拟系统是一个基于Web的模拟平台，允许用户在不影响生产数据的情况下进行人员调动的模拟操作，支持统计分析和数据导出功能。

## 文件结构

```
PerSimTS/
├── index.html                    # 主界面入口
├── README.md                     # 系统说明文档
├── DEVELOPMENT_GUIDE.md          # 开发指南
├── database_init.sql             # 数据库初始化脚本
├── dev-tools.html                # 开发者工具
├── test_drag.html                # 拖拽功能测试页面
├── assets/                       # 静态资源目录
│   ├── css/
│   │   └── simulation.css        # 主样式文件
│   └── js/
│       ├── simulation.js         # 主要JavaScript逻辑
│       └── version.js            # 版本管理和缓存控制
└── api/                          # API接口目录
    ├── personnel_simulation_manage.php  # 主要API接口
    ├── simulation_export.php            # 数据导出API
    └── test.php                         # API测试文件
```

## 功能特性

### 核心功能
- **模拟方案管理**: 创建、选择和管理多个模拟方案
- **数据初始化**: 从现有组织架构复制数据到模拟环境
- **人员调动**: 支持单个和批量人员调动操作
- **实时统计**: 提供详细的单位人员统计信息
- **数据导出**: 支持人员信息、统计数据和调动日志的导出

### 界面特性
- **直观的树形结构**: 清晰展示组织架构层级
- **响应式设计**: 支持不同屏幕尺寸的设备
- **实时搜索**: 快速查找特定人员
- **批量操作**: 支持多选和批量调动
- **操作日志**: 完整记录所有调动操作

## 安装部署

### 环境要求
- PHP 7.4+
- MySQL 5.7+
- Web服务器 (Apache/Nginx)
- 现有的用户权限系统

### 安装步骤

1. **创建目录结构**
   ```
   PerSimTS/
   ├── index.html          # 主界面
   ├── simulation.css      # 样式文件
   ├── simulation.js       # 前端逻辑
   ├── database_init.sql   # 数据库初始化脚本
   └── README.md          # 说明文档
   
   api/
   ├── personnel_simulation_manage.php  # 主要API接口
   └── simulation_export.php           # 数据导出API
   ```

2. **执行数据库初始化**
   ```sql
   -- 在MySQL中执行
   source PerSimTS/database_init.sql;
   ```

3. **配置权限**
   - 确保API文件具有适当的访问权限
   - 验证数据库连接配置

4. **访问系统**
   - 打开浏览器访问 `PerSimTS/index.html`
   - 使用现有用户账号登录

## 使用指南

### 1. 创建模拟方案

1. 点击"新建方案"按钮
2. 填写方案名称和描述
3. 选择是否复制当前组织架构数据
4. 点击"创建方案"完成

### 2. 选择工作方案

1. 在顶部下拉框中选择要操作的模拟方案
2. 系统将自动加载该方案的组织架构和人员数据

### 3. 查看组织架构

- 左侧面板显示树形的组织架构
- 点击单位名称可展开/折叠下级单位
- 单位名称后的数字表示该单位的人员数量

### 4. 查看人员信息

1. 在左侧单位树中点击选择单位
2. 右侧将显示该单位的人员列表
3. 可使用搜索框快速查找特定人员

### 5. 执行人员调动

#### 单个调动
1. 选择要调动的人员（勾选复选框）
2. 点击"批量调动"按钮
3. 在弹出窗口中选择目标单位
4. 填写调动原因和备注（可选）
5. 点击"确认调动"完成

#### 批量调动
1. 使用"全选"或逐个选择多个人员
2. 按照单个调动的步骤执行

### 6. 查看统计报表

1. 点击"统计报表"按钮
2. 查看各单位的人员分布统计
3. 包括民警、辅警、其他人员的数量
4. 显示调入和调出的人员数量

### 7. 导出数据

1. 点击"导出数据"按钮
2. 选择导出类型：
   - 人员信息：导出人员基本信息和当前单位
   - 统计信息：导出各单位人员统计数据
   - 调动日志：导出所有调动操作记录
3. 可选择特定单位进行筛选
4. 点击"开始导出"下载CSV文件

## 数据结构

### 主要数据表

#### sim_scenarios (模拟方案表)
- 存储模拟方案的基本信息
- 支持多方案并行管理

#### sim_units (模拟单位表)
- 存储模拟环境中的组织架构
- 维护单位层级关系

#### sim_personnel (模拟人员表)
- 存储模拟环境中的人员信息
- 记录人员当前所属单位

#### sim_transfer_logs (调动日志表)
- 记录所有调动操作的详细日志
- 支持操作追溯和审计

## API接口

### 主要接口

#### 方案管理
- `POST /api/personnel_simulation_manage.php?operation=createScenario` - 创建方案
- `POST /api/personnel_simulation_manage.php?operation=getScenarios` - 获取方案列表

#### 数据查询
- `POST /api/personnel_simulation_manage.php?operation=getUnitTree` - 获取单位树
- `POST /api/personnel_simulation_manage.php?operation=getPersonnelList` - 获取人员列表
- `POST /api/personnel_simulation_manage.php?operation=getUnitStatistics` - 获取统计信息

#### 调动操作
- `POST /api/personnel_simulation_manage.php?operation=transferPersonnel` - 执行人员调动

#### 数据导出
- `GET /api/simulation_export.php?type=personnel` - 导出人员信息
- `GET /api/simulation_export.php?type=statistics` - 导出统计信息
- `GET /api/simulation_export.php?type=transfer_logs` - 导出调动日志

## 安全特性

### 数据安全
- 模拟数据与生产数据完全隔离
- 所有操作仅影响模拟环境
- 支持方案级别的数据隔离

### 访问控制
- 基于现有用户权限系统
- 支持操作权限验证
- 记录操作者信息

### 输入验证
- 防止SQL注入攻击
- 输入数据格式验证
- 错误处理和异常捕获

## 技术特点

### 前端技术
- 原生JavaScript，无外部依赖
- 响应式CSS设计
- 模块化代码结构

### 后端技术
- PHP面向对象编程
- 预处理语句防止SQL注入
- RESTful API设计

### 数据库设计
- 规范化表结构设计
- 适当的索引优化
- 外键约束保证数据完整性

## 故障排除

### 常见问题

1. **无法加载方案列表**
   - 检查数据库连接配置
   - 确认用户登录状态
   - 查看浏览器控制台错误信息

2. **人员调动失败**
   - 确认目标单位存在
   - 检查用户操作权限
   - 查看调动日志中的错误信息

3. **导出功能异常**
   - 检查服务器文件写入权限
   - 确认导出参数正确性
   - 查看服务器错误日志

### 日志查看
- 浏览器开发者工具控制台
- 服务器PHP错误日志
- 数据库查询日志

## 版本信息

- **版本**: 1.0.0
- **发布日期**: 2025-07-14
- **兼容性**: PHP 7.4+, MySQL 5.7+

## 联系支持

如有问题或建议，请联系系统管理员或查看相关技术文档。
