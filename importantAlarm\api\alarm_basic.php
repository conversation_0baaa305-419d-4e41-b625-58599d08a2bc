<?php
/**
 * 重点警情基本情况表API接口
 * 支持增删改查操作
 */

// 引入数据库连接文件
require_once '../../conn_waf.php';
$APP_ID = 7; // 应用ID; 7表示重点警情事件应用


// 设置响应头为JSON
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'status' => 0,
        'message' => '仅支持POST请求',
        'data' => []
    ]);
    exit;
}

// 检查所有输入参数
waf_check_all_inputs();

// 获取控制参数
$controlCode = isset($_POST['controlCode']) ? $_POST['controlCode'] : '';

// 根据控制参数执行相应操作
switch ($controlCode) {
    case 'add':
        isHasPerm();
        addAlarmBasic();
        break;
    case 'del':
        isHasPerm();
        deleteAlarmBasic();
        break;
    case 'modify':
        isHasPerm();
        modifyAlarmBasic();
        break;
    case 'query':
        queryAlarmBasic();
        break;
    default:
        echo json_encode([
            'status' => 0,
            'message' => '无效的控制参数',
            'data' => []
        ]);
        break;
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
/**
 * 新增重点警情基本情况
 */
function addAlarmBasic() {
    global $conn;
    
    // 获取参数
    $alarmName = isset($_POST['alarm_name']) ? $_POST['alarm_name'] : '';
    $alarmContent = isset($_POST['alarm_content']) ? $_POST['alarm_content'] : '';
    $occurTime = isset($_POST['occur_time']) ? $_POST['occur_time'] : '';
    $areaId = isset($_POST['area_id']) ? intval($_POST['area_id']) : 0;
    $alarmLevel = isset($_POST['alarm_level']) ? intval($_POST['alarm_level']) : 0;
    $alarmStatus = isset($_POST['alarm_status']) ? $_POST['alarm_status'] : '';
    $isDisplay = isset($_POST['is_display']) ? intval($_POST['is_display']) : 0;
    $isTop = isset($_POST['is_top']) ? intval($_POST['is_top']) : 0;
    
    // 验证必填参数
    if (empty($alarmName) || empty($alarmContent) || empty($occurTime) || $areaId <= 0 || $alarmLevel <= 0 || empty($alarmStatus)) {
        echo json_encode([
            'status' => 0,
            'message' => '缺少必要参数',
            'data' => []
        ]);
        return;
    }
    
    // 验证辖区ID是否存在
    $checkAreaSql = "SELECT id FROM 2_unit WHERE id = ?";
    $stmt = $conn->prepare($checkAreaSql);
    $stmt->bind_param("i", $areaId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        echo json_encode([
            'status' => 0,
            'message' => '指定的辖区ID不存在',
            'data' => []
        ]);
        return;
    }
    
    // 验证警情状态
    if ($alarmStatus != '处置完毕' && $alarmStatus != '处置中') {
        echo json_encode([
            'status' => 0,
            'message' => '警情状态必须为"处置完毕"或"处置中"',
            'data' => []
        ]);
        return;
    }
    
    // 插入数据
    $sql = "INSERT INTO 7_important_alarm_basic (alarm_name, alarm_content, occur_time, area_id, alarm_level, alarm_status, is_display, is_top) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssiisii", $alarmName, $alarmContent, $occurTime, $areaId, $alarmLevel, $alarmStatus, $isDisplay, $isTop);
    
    if ($stmt->execute()) {
        $insertId = $stmt->insert_id;
        echo json_encode([
            'status' => 1,
            'message' => '新增警情成功',
            'data' => ['id' => $insertId]
        ]);
        
        // 记录操作日志
        // 获取当前用户ID和IP地址
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
        global $APP_ID;
        logOperation($conn, $userId, $APP_ID, "新增重点警情，新增警情ID: $insertId, 警情名称: $alarmName");
    } else {
        echo json_encode([
            'status' => 0,
            'message' => '新增警情失败: ' . $stmt->error,
            'data' => []
        ]);
    }
    
    $stmt->close();
}

/**
 * 删除重点警情基本情况
 * 支持单个删除(id=1)和批量删除(id=1,2,3)
 */
function deleteAlarmBasic() {
    global $conn;
    
    // 获取参数
    $ids = isset($_POST['id']) ? $_POST['id'] : '';
    
    // 处理参数
    if (is_numeric($ids)) {
        $idList = [intval($ids)];
    } else {
        $idList = array_filter(array_map('intval', explode(',', $ids)));
    }
    
    // 验证参数
    if (empty($idList)) {
        echo json_encode([
            'status' => 0,
            'message' => '无效的警情ID',
            'data' => []
        ]);
        return;
    }
    
    // 开始事务
    $conn->begin_transaction();
    
    try {
        // 构建IN条件
        $placeholders = implode(',', array_fill(0, count($idList), '?'));
        $types = str_repeat('i', count($idList));
        // 先删除关联的处置情况记录
        $deleteHandlingSql = "DELETE FROM 7_important_alarm_handling WHERE alarm_id IN ($placeholders)";
        $stmt = $conn->prepare($deleteHandlingSql);
        if(!$stmt) {
            throw new Exception("预处理语句准备失败: ".$conn->error);
        }
        $stmt->bind_param($types, ...$idList);
        $stmt->execute();
        $stmt->close();
        
        // 再删除警情基本情况
        $deleteBasicSql = "DELETE FROM 7_important_alarm_basic WHERE id IN ($placeholders)";
        $stmt = $conn->prepare($deleteBasicSql);
        if(!$stmt) {
            throw new Exception("预处理语句准备失败: ".$conn->error);
        }
        $stmt->bind_param($types, ...$idList);
        $stmt->execute();
        
        if ($stmt->affected_rows > 0) {
            // 提交事务
            $conn->commit();
            
            echo json_encode([
                'status' => 1,
                'message' => '删除警情成功',
                'data' => []
            ]);
            
            // 记录操作日志
            // 获取当前用户ID和IP地址
            $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
            global $APP_ID;
            logOperation($conn, $userId, $APP_ID, "删除警情ID: " . implode(',', $idList));
        } else {
            // 回滚事务
            $conn->rollback();
            
            echo json_encode([
                'status' => 0,
                'message' => '警情不存在',
                'data' => []
            ]);
        }
        
        $stmt->close();
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        
        echo json_encode([
            'status' => 0,
            'message' => '删除警情失败: ' . $e->getMessage(),
            'data' => []
        ]);
    }
}

/**
 * 修改重点警情基本情况
 */
function modifyAlarmBasic() {
    global $conn;
    
    // 获取参数
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $alarmName = isset($_POST['alarm_name']) ? $_POST['alarm_name'] : null;
    $alarmContent = isset($_POST['alarm_content']) ? $_POST['alarm_content'] : null;
    $occurTime = isset($_POST['occur_time']) ? $_POST['occur_time'] : null;
    $areaId = isset($_POST['area_id']) ? intval($_POST['area_id']) : null;
    $alarmLevel = isset($_POST['alarm_level']) ? intval($_POST['alarm_level']) : null;
    $alarmStatus = isset($_POST['alarm_status']) ? $_POST['alarm_status'] : null;
    $isDisplay = isset($_POST['is_display']) ? intval($_POST['is_display']) : null;
    $isTop = isset($_POST['is_top']) ? intval($_POST['is_top']) : null;
    
    // 验证参数
    if ($id <= 0) {
        echo json_encode([
            'status' => 0,
            'message' => '无效的警情ID',
            'data' => []
        ]);
        return;
    }
    
    // 检查警情是否存在
    $checkSql = "SELECT id FROM 7_important_alarm_basic WHERE id = ?";
    $stmt = $conn->prepare($checkSql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        echo json_encode([
            'status' => 0,
            'message' => '警情不存在',
            'data' => []
        ]);
        $stmt->close();
        return;
    }
    $stmt->close();
    
    // 如果提供了辖区ID，验证其是否存在
    if ($areaId !== null && $areaId > 0) {
        $checkAreaSql = "SELECT id FROM 2_unit WHERE id = ?";
        $stmt = $conn->prepare($checkAreaSql);
        $stmt->bind_param("i", $areaId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            echo json_encode([
                'status' => 0,
                'message' => '指定的辖区ID不存在',
                'data' => []
            ]);
            $stmt->close();
            return;
        }
        $stmt->close();
    }
    
    // 如果提供了警情状态，验证其有效性
    if ($alarmStatus !== null && $alarmStatus != '处置完毕' && $alarmStatus != '处置中') {
        echo json_encode([
            'status' => 0,
            'message' => '警情状态必须为"处置完毕"或"处置中"',
            'data' => []
        ]);
        return;
    }
    
    // 构建更新SQL
    $updateFields = [];
    $params = [];
    $types = "";
    
    if ($alarmName !== null) {
        $updateFields[] = "alarm_name = ?";
        $params[] = $alarmName;
        $types .= "s";
    }
    
    if ($alarmContent !== null) {
        $updateFields[] = "alarm_content = ?";
        $params[] = $alarmContent;
        $types .= "s";
    }
    
    if ($occurTime !== null) {
        $updateFields[] = "occur_time = ?";
        $params[] = $occurTime;
        $types .= "s";
    }
    
    if ($areaId !== null) {
        $updateFields[] = "area_id = ?";
        $params[] = $areaId;
        $types .= "i";
    }
    
    if ($alarmLevel !== null) {
        $updateFields[] = "alarm_level = ?";
        $params[] = $alarmLevel;
        $types .= "i";
    }
    
    if ($alarmStatus !== null) {
        $updateFields[] = "alarm_status = ?";
        $params[] = $alarmStatus;
        $types .= "s";
    }
    
    if ($isDisplay !== null) {
        $updateFields[] = "is_display = ?";
        $params[] = $isDisplay;
        $types .= "i";
    }
    
    if ($isTop !== null) {
        $updateFields[] = "is_top = ?";
        $params[] = $isTop;
        $types .= "i";
    }
    
    // 如果没有要更新的字段
    if (empty($updateFields)) {
        echo json_encode([
            'status' => 0,
            'message' => '没有提供要更新的字段',
            'data' => []
        ]);
        return;
    }
    
    // 添加ID参数
    $params[] = $id;
    $types .= "i";
    
    // 执行更新
    $sql = "UPDATE 7_important_alarm_basic SET " . implode(", ", $updateFields) . " WHERE id = ?";
    $stmt = $conn->prepare($sql);
    
    // 动态绑定参数
    $bindParams = array_merge([$stmt, $types], $params);
    // 确保所有参数都通过引用传递
    $refParams = [];
    foreach($bindParams as $key => $value) {
        $refParams[$key] = &$bindParams[$key];
    }
    call_user_func_array('mysqli_stmt_bind_param', $refParams);
    
    if ($stmt->execute()) {
        echo json_encode([
            'status' => 1,
            'message' => '更新警情成功',
            'data' => []
        ]);
        
        // 记录操作日志
        // 获取当前用户ID和IP地址
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
        global $APP_ID;
        $updateDetails = "更新警情ID: $id, 警情名称: $alarmName";
        if(!empty($updateFields)) {
            $updateDetails .= ", 内容: ";
            $fieldDetails = [];
            foreach($updateFields as $field) {
                $fieldName = explode(" = ?", $field)[0];
                // 转换字段名以匹配POST参数格式
                $paramName = strtolower(str_replace(" ", "", $fieldName));
                $fieldValue = isset($_POST[$paramName]) ? $_POST[$paramName] : '未设置';
                // 特殊处理空值情况
                if(empty($fieldValue) && $fieldValue !== '0') {
                    $fieldValue = '未设置';
                }
                $fieldDetails[] = "$fieldName: $fieldValue";
            }
            $updateDetails .= implode(", ", $fieldDetails);
        }
        logOperation($conn, $userId, $APP_ID, $updateDetails);
    } else {
        echo json_encode([
            'status' => 0,
            'message' => '更新警情失败: ' . $stmt->error,
            'data' => []
        ]);
    }
    
    $stmt->close();
}

/**
 * 查询重点警情基本情况
 * 
 * 所有参数除了controlCode=query外都是可选的
 * @return void 直接输出JSON结果
 */
function queryAlarmBasic() {
    global $conn;
    
    // 检查必须参数
    if (!isset($_POST['controlCode']) || $_POST['controlCode'] !== 'query') {
        echo json_encode([
            'status' => 0,
            'message' => '缺少必要参数controlCode=query',
            'data' => []
        ]);
        return;
    }
    
    // 获取分页参数（可选，有默认值）
    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $pageSize = isset($_POST['pagesize']) ? intval($_POST['pagesize']) : 10;
    
    // 验证分页参数
    if ($page < 1) $page = 1;
    if ($pageSize < 1) $pageSize = 10;
    
    // 计算偏移量
    $offset = ($page - 1) * $pageSize;
    
    // 获取筛选参数（全部可选）
    $id = isset($_POST['id']) && $_POST['id'] !== '' ? intval($_POST['id']) : null;
    $alarmName = isset($_POST['alarm_name']) ? trim($_POST['alarm_name']) : null;
    $alarm_content = isset($_POST['alarm_content']) ? trim($_POST['alarm_content']) : null;
    $areaId = isset($_POST['area_id']) && $_POST['area_id'] !== '' ? intval($_POST['area_id']) : null;
    $alarmLevel = isset($_POST['alarm_level']) && $_POST['alarm_level'] !== '' ? intval($_POST['alarm_level']) : null;
    $alarmStatus = isset($_POST['alarm_status']) ? trim($_POST['alarm_status']) : null;
    $isDisplay = isset($_POST['is_display']) && $_POST['is_display'] !== '' ? intval($_POST['is_display']) : null;
    $isTop = isset($_POST['is_top']) && $_POST['is_top'] !== '' ? intval($_POST['is_top']) : null;
    $startTime = isset($_POST['start_time']) ? trim($_POST['start_time']) : null;
    $endTime = isset($_POST['end_time']) ? trim($_POST['end_time']) : null;
    
    // 构建查询条件
    $conditions = [];
    $params = [];
    $types = "";
    
    // 只有当参数有效时才添加到查询条件
    if ($id !== null && $id > 0) {
        $conditions[] = "a.id = ?";
        $params[] = $id;
        $types .= "i";
    }
    
    if ($alarmName !== null && $alarmName !== '') {
        $conditions[] = "a.alarm_name LIKE ?";
        $params[] = "%$alarmName%";
        $types .= "s";
    }

    if ($alarm_content !== null && $alarm_content !== '') {
        $conditions[] = "a.alarm_content LIKE ?";
        $params[] = "%$alarm_content%";
        $types .= "s";
    }
    
    if ($areaId !== null && $areaId > 0) {
        $conditions[] = "a.area_id = ?";
        $params[] = $areaId;
        $types .= "i";
    }
    
    if ($alarmLevel !== null && $alarmLevel > 0) {
        $conditions[] = "a.alarm_level = ?";
        $params[] = $alarmLevel;
        $types .= "i";
    }
    
    if ($alarmStatus !== null && $alarmStatus !== '') {
        $conditions[] = "a.alarm_status = ?";
        $params[] = $alarmStatus;
        $types .= "s";
    }
    
    // 对于布尔类型参数，只要不是null就添加条件（包括0值）
    if ($isDisplay !== null) {
        $conditions[] = "a.is_display = ?";
        $params[] = $isDisplay;
        $types .= "i";
    }
    
    if ($isTop !== null) {
        $conditions[] = "a.is_top = ?";
        $params[] = $isTop;
        $types .= "i";
    }
    
    // 添加时间范围筛选条件
    if ($startTime !== null && $startTime !== '') {
        $conditions[] = "a.occur_time >= ?";
        $params[] = $startTime;
        $types .= "s";
    }
    
    if ($endTime !== null && $endTime !== '') {
        $conditions[] = "a.occur_time <= ?";
        $params[] = $endTime;
        $types .= "s";
    }
    
    // 构建WHERE子句
    $whereClause = "";
    if (!empty($conditions)) {
        $whereClause = "WHERE " . implode(" AND ", $conditions);
    }
    
    // 查询总记录数
    $countSql = "SELECT COUNT(*) as total FROM 7_important_alarm_basic a $whereClause";
    $stmt = $conn->prepare($countSql);
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 0,
            'message' => '查询准备失败: ' . $conn->error,
            'data' => []
        ]);
        return;
    }
    
    // 只有当有参数时才绑定参数
    if (!empty($params)) {
        $bindParams = array_merge([$stmt, $types], $params);
        // 确保所有参数都通过引用传递
        $refParams = [];
        foreach($bindParams as $key => $value) {
            $refParams[$key] = &$bindParams[$key];
        }
        call_user_func_array('mysqli_stmt_bind_param', $refParams);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $total = $row['total'];
    $stmt->close();
    
    // 查询数据
    $sql = "SELECT a.*, u.unit_name as area_name 
            FROM 7_important_alarm_basic a 
            LEFT JOIN 2_unit u ON a.area_id = u.id 
            $whereClause 
            ORDER BY a.is_top DESC, a.id DESC 
            LIMIT ?, ?";
    
    $stmt = $conn->prepare($sql);
    
    if ($stmt === false) {
        echo json_encode([
            'status' => 0,
            'message' => '查询准备失败: ' . $conn->error,
            'data' => []
        ]);
        return;
    }
    
    // 添加分页参数
    $params[] = $offset;
    $params[] = $pageSize;
    $types .= "ii";
    
    $bindParams = array_merge([$stmt, $types], $params);
    $refParams = [];
    foreach($bindParams as $key => $value) {
        $refParams[$key] = &$bindParams[$key];
    }
    call_user_func_array('mysqli_stmt_bind_param', $refParams);
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'pageSize' => $pageSize,
    ]);
    
    $stmt->close();
}