<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户授权管理</title>
    <link rel="stylesheet" href="rolemanager.css">
</head>
<body>
    <div class="search-panel">
        
            <label for="organizationUnit">单位：</label>
            <select id="organizationUnit">
                <option value="">所有单位</option>
            </select>
            <label for="searchKeyword">关键字：</label>
            <input type="text" id="searchKeyword" placeholder="输入姓名、警号或身份证号">
            <button id="searchBtn">查询用户</button>
            <lAbel for="searchAuth">关键字：</label>
            <select id="app_select">
                <option value="">选择应用</option>
            </select>
            <button id="searchAuthBtn">查询授权人员</button>
        
    </div>
    
    <div class="main-content">
        <div class="user-list">
            <h3>用户列表</h3>
            <table id="userTable">
                <thead>
                    <tr>
                        <th>单位</th>
                        <th>姓名</th>
                        <th>警号</th>
                        <th>身份证号</th>
                        <th>身份</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 用户数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
            <div class="pagination">
                <button id="prevPage">上一页</button>
                <span id="pageInfo">第1页</span>
                <button id="nextPage">下一页</button>
            </div>
        </div>
        
        <div class="auth-list">
            <h3>授权信息</h3>
            <table id="authTable">
                <thead>
                    <tr>
                        <th>授权应用</th>
                        <th>授权单位</th>
                        <th>授权角色</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 授权数据将通过JavaScript动态填充 -->
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 添加/编辑授权对话框 -->
    <div class="dialog" id="authDialog">
        <div class="dialog-content">
            <h3 id="dialogTitle">添加授权</h3>
            <form id="authForm">
                <input type="hidden" id="authId">
                <input type="hidden" id="userId">
                <input type="hidden" id="operationType" value="add">
                
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="userName" readonly>
                </div>
                
                <div class="form-group">
                    <label>授权应用</label>
                    <select id="appId" required>
                        <option value="">请选择应用</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>授权单位</label>
                    <select id="unitId" required>
                        <option value="">请选择单位</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>授权角色</label>
                    <select id="roleId" required>
                        <option value="">请选择角色</option>
                    </select>
                </div>
                
                <div class="dialog-footer">
                    <button type="button" id="cancelBtn">取消</button>
                    <button type="submit" id="confirmBtn">确定</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 删除确认对话框 -->
    <div class="dialog" id="confirmDialog">
        <div class="dialog-content">
            <h3>确认删除</h3>
            <p>确定要删除这条授权记录吗？</p>
            <input type="hidden" id="deleteId">
            <div class="dialog-footer">
                <button type="button" id="cancelDeleteBtn">取消</button>
                <button type="button" id="confirmDeleteBtn">确定</button>
            </div>
        </div>
    </div>

    <script src="rolemanager.js"></script>
</body>
</html>