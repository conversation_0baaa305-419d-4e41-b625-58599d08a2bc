<?php
header('Content-Type: application/json');
require_once __DIR__.'/../config.php';

// 验证管理员权限
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'admin') {
    echo json_encode(['status' => 'error', 'message' => '无权限访问']);
    exit;
}

// 获取请求参数
$action = $_POST['action'] ?? '';
$application_id = intval($_POST['application_id'] ?? 0);
$permission_value = intval($_POST['permission_value'] ?? 0);
$permission_description = trim($_POST['permission_description'] ?? '');
$id = intval($_POST['id'] ?? 0); // 用于编辑/删除的权限ID

// 数据库连接
try {
    // 使用mysqli连接
    $conn = new mysqli($servername, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception('数据库连接失败: ' . $conn->connect_error);
    }

    // 添加日志记录函数
    function logOperation($conn, $user_id, $application_id, $operation, $ip) {
        $stmt = $conn->prepare("INSERT INTO log (user_id, application_id, operation, ip) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("iiss", $user_id, $application_id, $operation, $ip);
        $stmt->execute();
    }

    // 获取用户IP
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    switch ($action) {
        case 'add':
            // 检查权限值是否已存在
            $stmt = $conn->prepare("SELECT * FROM permission_description WHERE application_id = ? AND permission_value = ?");
            $stmt->bind_param("ii", $application_id, $permission_value);
            $stmt->execute();
            if ($stmt->get_result()->fetch_assoc()) {
                throw new Exception('权限值已存在');
            }

            // 添加新权限
            $stmt = $conn->prepare("INSERT INTO permission_description (application_id, permission_value, permission_description) VALUES (?, ?, ?)");
            $stmt->bind_param("iis", $application_id, $permission_value, $permission_description);
            $stmt->execute();
            logOperation($conn, $_SESSION['user_id'], $application_id, 
                "添加权限: 应用ID={$application_id}, 权限值={$permission_value}, 描述='{$permission_description}'", $ip);
            echo json_encode(['status' => 'success', 'message' => '添加成功', 'id' => $stmt->insert_id]);
            break;

        case 'edit':
            // 更新权限信息
            $stmt = $conn->prepare("UPDATE permission_description SET application_id = ?, permission_value = ?, permission_description = ? WHERE id = ?");
            $stmt->bind_param("iisi", $application_id, $permission_value, $permission_description, $id);
            $stmt->execute();
            logOperation($conn, $_SESSION['user_id'], $application_id, 
                "修改权限: ID={$id}, 新应用ID={$application_id}, 新权限值={$permission_value}, 新描述='{$permission_description}'", $ip);
            echo json_encode(['status' => 'success', 'message' => '更新成功']);
            break;

        case 'delete':
            // 验证必填字段
            if ($id <= 0) {
                throw new Exception('缺少权限ID');
            }

            // 删除权限
            $stmt = $db->prepare("DELETE FROM permission_description WHERE id = ?");
            $stmt->execute([$id]);
            logOperation($conn, $_SESSION['user_id'], $application_id, 
                "删除权限: ID={$id}", $ip);
            echo json_encode(['status' => 'success', 'message' => '删除成功']);
            break;

        case 'list':
            // 查询权限列表
            $query = "SELECT * FROM permission_description";
            $params = [];
            
            // 支持按应用ID筛选
            if ($application_id > 0) {
                $query .= " WHERE application_id = ?";
                $params[] = $application_id;
            }
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo json_encode(['status' => 'success', 'data' => $permissions]);
            break;

        default:
            throw new Exception('无效操作');
    }
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>