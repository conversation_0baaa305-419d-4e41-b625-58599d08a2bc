<?php
/**
 * 文件网盘系统 - 数据库配置文件
 * 创建时间: 2025-06-23
 */
require_once __DIR__ . '/../conn_waf.php';
// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'application');
define('DB_USER', 'root');
define('DB_PASS', 'YC@yc110');
define('DB_CHARSET', 'utf8mb4');

// 系统配置
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 524288000); // 50MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar']);

// 安全配置
define('SHARE_CODE_LENGTH', 6);
define('DEFAULT_EXPIRE_DAYS', 30);

// 站点信息
define('SITE_TITLE', '文件快递柜');
define('SITE_DESCRIPTION', '安全、便捷的文件分享平台');

// 错误报告（生产环境设为0）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 时区设置
date_default_timezone_set('Asia/Shanghai');

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    die('数据库连接失败: ' . $e->getMessage());
}

// 启动会话
//if (session_status() == PHP_SESSION_NONE) {
//    session_start();
//}
?>
