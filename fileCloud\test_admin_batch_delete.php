<?php
/**
 * 管理员批量删除文件功能测试页面
 * 创建时间: 2025-07-08
 */

require_once 'functions.php';

// 检查是否为管理员
if (!isLoggedIn() || !isAdmin()) {
    die('需要管理员权限才能访问此页面');
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员批量删除文件测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .btn-danger { background-color: #dc3545; color: white; border: none; }
        .btn-warning { background-color: #ffc107; color: black; border: none; }
        .btn-primary { background-color: #007bff; color: white; border: none; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input[type="text"], select { width: 300px; padding: 5px; }
        .checkbox-group { margin: 10px 0; }
        .result { margin: 15px 0; padding: 10px; border-radius: 5px; }
        .result.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .result.error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员批量删除文件测试</h1>
        
        <div class="section">
            <h2>1. 查看文件列表</h2>
            <button onclick="loadFileList()">加载文件列表</button>
            <div id="fileList"></div>
        </div>
        
        <div class="section">
            <h2>2. 批量删除测试</h2>
            <form id="deleteForm">
                <div class="form-group">
                    <label for="fileIds">文件ID列表（逗号分隔）:</label>
                    <input type="text" id="fileIds" name="file_ids" placeholder="例如: 1,2,3">
                </div>
                
                <div class="form-group">
                    <label for="deleteType">删除类型:</label>
                    <select id="deleteType" name="delete_type">
                        <option value="soft">软删除（标记为已删除）</option>
                        <option value="hard">硬删除（永久删除）</option>
                    </select>
                </div>
                
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="confirmed" name="confirmed" value="true">
                        我确认要删除这些文件
                    </label>
                </div>
                
                <div class="checkbox-group" id="hardConfirmGroup" style="display: none;">
                    <label>
                        <input type="checkbox" id="hardConfirmed" name="hard_confirmed" value="true">
                        我确认要永久删除这些文件（无法恢复）
                    </label>
                </div>
                
                <button type="button" onclick="testBatchDelete()" class="btn-danger">执行删除测试</button>
            </form>
            
            <div id="deleteResult"></div>
        </div>
        
        <div class="section">
            <h2>3. 操作日志查看</h2>
            <button onclick="loadOperationLogs()">查看最近的操作日志</button>
            <div id="operationLogs"></div>
        </div>
    </div>

    <script>
        // 显示/隐藏硬删除确认
        document.getElementById('deleteType').addEventListener('change', function() {
            const hardConfirmGroup = document.getElementById('hardConfirmGroup');
            if (this.value === 'hard') {
                hardConfirmGroup.style.display = 'block';
            } else {
                hardConfirmGroup.style.display = 'none';
            }
        });

        // 加载文件列表
        function loadFileList() {
            fetch('api/admin_file_list.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'csrf_token=<?php echo generateCsrfToken(); ?>'
            })
            .then(response => response.json())
            .then(data => {
                const fileListDiv = document.getElementById('fileList');
                if (data.success && data.files) {
                    let html = '<table><tr><th>文件ID</th><th>文件名</th><th>上传者</th><th>大小</th><th>状态</th><th>上传时间</th></tr>';
                    data.files.forEach(file => {
                        html += `<tr>
                            <td>${file.file_id}</td>
                            <td>${file.original_name}</td>
                            <td>${file.username || '匿名'}</td>
                            <td>${file.file_size_formatted || file.file_size}</td>
                            <td>${file.is_deleted == 1 ? '已删除' : '正常'}</td>
                            <td>${file.upload_time}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    fileListDiv.innerHTML = html;
                } else {
                    fileListDiv.innerHTML = '<p class="error">加载文件列表失败: ' + (data.message || '未知错误') + '</p>';
                }
            })
            .catch(error => {
                document.getElementById('fileList').innerHTML = '<p class="error">请求失败: ' + error.message + '</p>';
            });
        }

        // 测试批量删除
        function testBatchDelete() {
            const form = document.getElementById('deleteForm');
            const formData = new FormData(form);
            formData.append('csrf_token', '<?php echo generateCsrfToken(); ?>');

            // 验证必填项
            if (!formData.get('file_ids')) {
                alert('请输入文件ID列表');
                return;
            }

            if (!formData.get('confirmed')) {
                alert('请确认删除操作');
                return;
            }

            if (formData.get('delete_type') === 'hard' && !formData.get('hard_confirmed')) {
                alert('硬删除需要额外确认');
                return;
            }

            fetch('api/admin_batch_delete_files.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('deleteResult');
                let html = '<div class="result ' + (data.success ? 'success' : 'error') + '">';
                html += '<h3>删除结果</h3>';
                html += '<p><strong>状态:</strong> ' + (data.success ? '成功' : '失败') + '</p>';
                html += '<p><strong>消息:</strong> ' + data.message + '</p>';
                
                if (data.success) {
                    html += '<p><strong>删除类型:</strong> ' + (data.delete_type === 'soft' ? '软删除' : '硬删除') + '</p>';
                    html += '<p><strong>成功删除:</strong> ' + data.deleted_count + ' 个文件</p>';
                    html += '<p><strong>请求总数:</strong> ' + data.total_requested + ' 个文件</p>';
                    
                    if (data.deleted_files && data.deleted_files.length > 0) {
                        html += '<h4>已删除的文件:</h4><ul>';
                        data.deleted_files.forEach(file => {
                            html += '<li>' + file.original_name + ' (ID: ' + file.file_id + ', 用户: ' + (file.username || '匿名') + ')</li>';
                        });
                        html += '</ul>';
                    }
                    
                    if (data.errors && data.errors.length > 0) {
                        html += '<h4>错误信息:</h4><ul>';
                        data.errors.forEach(error => {
                            html += '<li class="error">' + error + '</li>';
                        });
                        html += '</ul>';
                    }
                }
                
                html += '</div>';
                resultDiv.innerHTML = html;
                
                // 刷新文件列表
                loadFileList();
            })
            .catch(error => {
                document.getElementById('deleteResult').innerHTML = '<div class="result error"><p>请求失败: ' + error.message + '</p></div>';
            });
        }

        // 加载操作日志
        function loadOperationLogs() {
            fetch('api/admin_operation_logs.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'csrf_token=<?php echo generateCsrfToken(); ?>&limit=20'
            })
            .then(response => response.json())
            .then(data => {
                const logsDiv = document.getElementById('operationLogs');
                if (data.success && data.logs) {
                    let html = '<table><tr><th>时间</th><th>用户ID</th><th>操作</th><th>IP地址</th></tr>';
                    data.logs.forEach(log => {
                        html += `<tr>
                            <td>${log.timestamp}</td>
                            <td>${log.user_id}</td>
                            <td>${log.operation}</td>
                            <td>${log.ip}</td>
                        </tr>`;
                    });
                    html += '</table>';
                    logsDiv.innerHTML = html;
                } else {
                    logsDiv.innerHTML = '<p class="error">加载操作日志失败: ' + (data.message || '未知错误') + '</p>';
                }
            })
            .catch(error => {
                document.getElementById('operationLogs').innerHTML = '<p class="error">请求失败: ' + error.message + '</p>';
            });
        }

        // 页面加载时自动加载文件列表
        window.onload = function() {
            loadFileList();
        };
    </script>
</body>
</html>
