一、用户管理接口文档 - 列出用户
1. 接口基本信息
​​接口名称​​: 列出用户
​​接口路径​​: /api/user_manage.php
​​请求方法​​: POST
​​Content-Type​​: application/x-www-form-urlencoded 或 multipart/form-data
2. 请求参数
参数名	类型	必填	描述
action	string	是	固定值"list"
unit_id	int	是	要查询的单位ID
page_size	int	否	每页显示条数，默认10，最大100
page	int	否	页码，默认1
3. 响应格式
响应数据为JSON格式，包含以下字段：

status: 请求状态（"success"或"error"）
action: 接口类型（固定为"list"）
data: 数据对象
users: 用户数组
pagination: 分页信息
total: 总记录数
total_pages: 总页数
current_page: 当前页
page_size: 每页条数
message: 错误信息（仅当status为error时存在）
4. 用户对象字段说明
字段名	类型	描述
id	int	用户ID
name	string	姓名
id_number	string	身份证号
phone	string	手机号码
gender	int	性别(0:未知 1:男 2:女)
organization_unit	int	编制单位ID
organization_unit_name	string	编制单位名称
work_unit	int	工作单位ID
work_unit_name	string	工作单位名称
personnel_type	string	人员身份
employment_status	string	人员状态
job_rank	string	职级
position	string	职务
police_number	string	警号/辅警号
sorted_order	int	排序值
...	...	其他用户字段
5. 排序规则
按单位层级排序（上级单位在前，下级单位在后）
同一单位内按单位排序值(sort_order)排序
同一单位内的人员按人员排序值(sorted_order)排序
6. 请求示例
POST /api/user_manage.php HTTP/1.1
Content-Type: application/x-www-form-urlencoded

action=list&unit_id=1&page_size=10&page=1
7. 响应示例
成功响应：

{
  "status": "success",
  "action": "list",
  "data": {
    "users": [
      {
        "id": 1,
        "name": "张三",
        "phone": "13800138000",
        "gender": 1,
        "organization_unit": 1,
        "organization_unit_name": "总公司",
        "work_unit": 1,
        "work_unit_name": "总公司",
        "personnel_type": "正式员工",
        "employment_status": "在职",
        "sorted_order": 1
      }
    ],
    "pagination": {
      "total": 25,
      "total_pages": 3,
      "current_page": 1,
      "page_size": 10
    }
  }
}
错误响应：

{
  "status": "error",
  "action": "list",
  "message": "单位ID参数错误"
}
8. 错误码说明
错误信息	原因	解决方案
缺少必填字段: unit_id	未提供unit_id参数	提供有效的unit_id参数
单位ID参数错误	unit_id不是有效正整数	提供有效的单位ID
每页显示条数应在1-100之间	page_size超出范围	调整page_size在1-100之间
页码参数错误	page不是有效正整数	提供有效的页码
数据库查询失败	服务器内部错误	检查服务器日志
9. 注意事项
接口不返回用户密码等敏感信息
分页参数不合法时会使用默认值
单位不存在时会返回空用户列表
建议前端对分页参数进行校验


二、 用户管理接口文档 - 添加用户

`POST /api/user_manage.php`

## 1. 接口说明
本接口用于向系统添加新用户，自动处理人员排序逻辑

## 2. 请求参数

### Headers
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | string | 是 | 固定为 `application/x-www-form-urlencoded` |

### Body (form-data)
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| action | string | 是 | 无 | 固定值 `"add"` |
| name | string | 是 | 无 | 用户姓名 |
| phone | string | 是 | 无 | 手机号码(11位) |
| gender | int | 是 | 无 | 性别(0:未知 1:男 2:女) |
| organization_unit | int | 是 | 无 | 编制单位ID |
| personnel_type | string | 是 | 无 | 人员身份 |
| employment_status | string | 是 | 无 | 人员状态 |
| sorted_order | int | 是 | 无 | 排序值(≥1) |
| id_number | string | 否 | 空 | 身份证号(18位) |
| job_rank | string | 否 | 空 | 职级 |
| position | string | 否 | 空 | 职务 |
| police_number | string | 否 | 空 | 警号/辅警号 |

## 3. 排序逻辑
1. 系统会自动将`≥sorted_order`的现有人员排序值****. 新用户将占用指定的`sorted_order`位置
3. 采用事务处理确保数据一致性

## 4. 响应格式
```json
{
  "status": "success/error",
  "action": "add",
  "data": {
    "user_id": "新增用户ID",
    "message": "操作结果描述"
  },
  "message": "错误详情(status=error时存在)"
}

默认密码：511621

三、删除用户
删除用户功能实现
以下是完整的删除用户功能的PHP代码实现，包含接口处理和数据库操作：

/**
 * 处理删除用户
 */
function handleDelete() {
    global $conn;
    
    // 验证必须参数
    if (!isset($_POST['user_id']) || !is_numeric($_POST['user_id'])) {
        echo json_encode([
            'status' => 'error',
            'action' => 'delete',
            'message' => '用户ID参数错误'
        ]);
        exit;
    }
    
    $user_id = intval($_POST['user_id']);
    
    // 开始事务
    $conn->begin_transaction();
    
    try {
        // 1. 获取用户信息（用于排序调整）
        $user_sql = "SELECT organization_unit, sorted_order FROM user WHERE id = ?";
        $user_stmt = $conn->prepare($user_sql);
        $user_stmt->bind_param("i", $user_id);
        $user_stmt->execute();
        $user_result = $user_stmt->get_result();
        
        if ($user_result->num_rows === 0) {
            throw new Exception("用户不存在");
        }
        
        $user_data = $user_result->fetch_assoc();
        $org_unit = $user_data['organization_unit'];
        $sorted_order = $user_data['sorted_order'];
        
        // 2. 删除用户
        $delete_sql = "DELETE FROM user WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $user_id);
        $delete_stmt->execute();
        
        if ($delete_stmt->affected_rows !== 1) {
            throw new Exception("删除用户失败");
        }
        
        // 3. 调整排序值：将大于被删除用户排序值的记录-1
        $adjust_sql = "UPDATE user 
                      SET sorted_order = sorted_order - 1 
                      WHERE organization_unit = ? AND sorted_order > ?";
        $adjust_stmt = $conn->prepare($adjust_sql);
        $adjust_stmt->bind_param("ii", $org_unit, $sorted_order);
        $adjust_stmt->execute();
        
        // 提交事务
        $conn->commit();
        
        echo json_encode([
            'status' => 'success',
            'action' => 'delete',
            'data' => [
                'deleted_user_id' => $user_id,
                'message' => '用户删除成功'
            ]
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        
        error_log("删除用户失败: " . $e->getMessage());
        echo json_encode([
            'status' => 'error',
            'action' => 'delete',
            'message' => '删除用户失败: ' . $e->getMessage()
        ]);
    }
}
功能说明
​​请求参数​​：
action=delete：指定操作为删除用户
user_id：要删除的用户ID（必填）
​​删除逻辑​​：
先获取用户的编制单位和排序值
删除指定用户
调整同一编制单位中排序值大于被删除用户的记录（排序值-1）
​​事务处理​​：
使用事务确保删除和排序调整的原子性
任一操作失败则回滚整个事务
​​响应格式​​：
成功：返回被删除的用户ID
失败：返回具体的错误信息
接口文档
请求示例
POST /api/user_manage.php HTTP/1.1
Content-Type: application/x-www-form-urlencoded

action=delete&user_id=123
成功响应
{
    "status": "success",
    "action": "delete",
    "data": {
        "deleted_user_id": 123,
        "message": "用户删除成功"
    }
}
错误响应
{
    "status": "error",
    "action": "delete",
    "message": "用户不存在"
}

四、# 用户管理接口文档 - 编辑用户

`POST /api/user_manage.php`

## 1. 接口说明
本接口用于修改用户信息，当修改编制单位时自动处理原单位和新单位的排序值调整

## 2. 请求参数

### Headers
| 参数名         | 类型     | 必填 | 说明                         |
|----------------|----------|------|------------------------------|
| Content-Type   | string   | 是   | 固定为 `application/x-www-form-urlencoded` |

### Body (form-data)
| 参数名               | 类型     | 必填 | 说明                          |
|----------------------|----------|------|-------------------------------|
| action               | string   | 是   | 固定值 `"edit"`               |
| user_id              | int      | 是   | 要编辑的用户ID                |
| name                 | string   | 否   | 用户姓名                      |
| phone                | string   | 否   | 手机号码(11位)                |
| gender               | int      | 否   | 性别(0:未知 1:男 2:女)        |
| organization_unit    | int      | 否   | 新编制单位ID                  |
| work_unit            | int      | 否   | 新工作单位ID                  |
| personnel_type       | string   | 否   | 人员身份                      |
| employment_status    | string   | 否   | 人员状态                      |
| sorted_order         | int      | 否   | 新排序值(≥1)                  |
| id_number            | string   | 否   | 身份证号(18位)                |
| job_rank             | string   | 否   | 职级                          |
| position             | string   | 否   | 职务                          |
| police_number        | string   | 否   | 警号/辅警号                   |

## 3. 排序调整逻辑

### 当修改编制单位时：
1. **原单位处理**：
   - 将该用户从原单位移除
   - 原单位中排序值大于该用户的记录自动-1

2. **新单位处理**：
   - 在新单位插入该用户
   - 新单位中排序值≥新排序值的记录自动+1

### 仅修改排序值时：
- 排序值调大：原位置到新位置之间的记录排序值-1
- 排序值调小：新位置到原位置之间的记录排序值+1

## 4. 响应格式
```json
{
  "status": "success/error",
  "action": "edit",
  "data": {
    "user_id": "被编辑的用户ID",
    "message": "操作结果描述"
  },
  "message": "错误详情(status=error时存在)"
}
5. 请求示例
POST /api/user_manage.php HTTP/1.1
Content-Type: application/x-www-form-urlencoded

action=edit&user_id=123&organization_unit=2&sorted_order=2&name=张三新名字
6. 成功响应
{
  "status": "success",
  "action": "edit",
  "data": {
    "user_id": 123,
    "message": "用户信息更新成功"
  }
}
7. 错误响应
{
  "status": "error",
  "action": "edit",
  "message": "编制单位不存在"
}
8. 错误码说明
错误信息	HTTP状态码	原因
用户ID参数错误	400	user_id无效或缺失
用户不存在	404	指定ID的用户不存在
编制单位不存在	404	organization_unit无效
排序值必须大于0	400	sorted_order≤0
手机号格式错误	400	非11位数字
数据库操作失败	500	服务器内部错误
9. 注意事项
采用事务处理确保数据一致性
修改工作单位(work_unit)不会触发排序调整
同时修改单位和排序值时，以新排序值为准
建议前端先调用单位列表接口验证unit_id有效性
所有时间字段需符合YYYY-MM-DD格式
响应中不会返回密码等敏感字段
10. 特殊场景处理
修改场景	系统行为
仅修改基本信息	直接更新字段，不影响排序
修改单位+排序值	先移除原单位位置，再在新单位插入，触发两次排序调整
新排序值＞原排序值	原位置到新位置之间的记录排序值-1
新排序值＜原排序值	新位置到原位置之间的记录排序值+1
修改单位但排序值不变	使用原排序值在新单位插入
五、# 用户管理接口文档 - 重置密码

`POST /api/user_manage.php`

## 1. 接口说明
本接口用于将指定用户的密码重置为系统默认密码

## 2. 请求参数

### Headers
| 参数名         | 类型     | 必填 | 说明                         |
|----------------|----------|------|------------------------------|
| Content-Type   | string   | 是   | 固定为 `application/x-www-form-urlencoded` |

### Body (form-data)
| 参数名      | 类型   | 必填 | 说明                |
|-------------|--------|------|---------------------|
| action      | string | 是   | 固定值 `"reset_password"` |
| id          | int    | 是   | 要重置密码的用户ID  |

## 3. 安全要求
1. 需要管理员或具有权限的用户才能操作
2. 建议配合CSRF Token使用
3. 默认密码建议通过配置文件设置

## 4. 响应格式
```json
{
  "status": "success/error",
  "message": "操作结果描述",
  "data": {
    "user_id": "被操作用户ID",
    "default_password": "重置后的默认密码（可选）"
  }
}

六、导入用户接口
# 用户导入接口规范

`POST /api/user_manage.php`

## 请求参数
### Headers
| 参数名         | 类型     | 必填 | 说明                         |
|----------------|----------|------|------------------------------|
| Content-Type   | string   | 是   | 固定为 `multipart/form-data` |

### Body (form-data)
| 参数名      | 类型   | 必填 | 说明                |
|-------------|--------|------|---------------------|
| action      | string | 是   | 固定值 `"import"`   |
| csv_file    | file   | 是   | CSV格式的用户数据文件 |

## CSV文件要求
### 必填字段
- `name` (姓名)
- `phone` (手机号)
- `gender` (性别: 1男/2女)
- `organization_unit` (编制单位ID)
- `personnel_type` (人员身份)
- `employment_status` (人员状态)

### 可选字段
- `id_number` (身份证号)
- `job_rank` (职级)
- `position` (职务)
- `police_number` (警号/辅警号)

### 示例CSV
```csv
name,phone,gender,organization_unit,personnel_type,employment_status,id_number
张三,13800138000,1,31,正式员工,在职,110101199001011234
李四,13800138001,2,32,合同工,在职,110101199002021235

# 人员信息查询接口文档
## 一、接口概述
此接口主要用于获取人员的身份、状态以及职级信息，以 JSON 格式返回数据。接口文件为 `person_info_api.php` 。

## 二、接口信息
### 1. 接口地址
```
z:\api\person_info_api.php
```
### 2. 请求方法
post

### 3. 响应格式
application/json

## 三、请求参数
参数名 类型 是否必填 描述 type string 是 查询类型，可选值有 identity （人员身份）、 status （人员状态）、 rank （人员职级）

## 四、响应示例
### 1. 查询人员身份
- 请求示例 ：
```
api/person_info_api.php?type=identity
```
- 响应内容 ：
```
[
    "民警",
    "事业",
    "机关工勤",
    "辅警",
    "三方合作人员"
]
```
### 2. 查询人员状态
- 请求示例 ：
```
api/person_info_api.php?type=status
```
- 响应内容 ：
```
[
    "在职",
    "退休",
    "调离",
    "开除",
    "抽调出局"
]
```
### 3. 查询人员职级
- 请求示例 ：
```
api/person_info_api.php?type=rank
```
- 响应内容 ：
```
[
    "正科级",
    "副科级",
    "股级"
]

