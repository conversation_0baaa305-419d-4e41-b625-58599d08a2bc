<?php
/**
 * 匿名上传功能测试页面
 * 创建时间: 2025-07-03
 */

require_once 'functions.php';

// 测试用户搜索功能
function testUserSearch() {
    echo "<h3>测试用户搜索功能</h3>";
    
    // 测试搜索关键词
    $testKeywords = ['张', '李', '管理', '技术'];
    
    foreach ($testKeywords as $keyword) {
        echo "<h4>搜索关键词: '$keyword'</h4>";
        $users = getUserInfo($keyword);
        
        if (empty($users)) {
            echo "<p style='color: orange;'>未找到匹配的用户</p>";
        } else {
            echo "<ul>";
            foreach ($users as $user) {
                echo "<li>ID: {$user['id']}, 姓名: {$user['name']}, 单位: {$user['organization_unitName']}</li>";
            }
            echo "</ul>";
        }
        echo "<hr>";
    }
}

// 测试数据库连接
function testDatabaseConnection() {
    global $pdo;
    echo "<h3>测试数据库连接</h3>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM 3_user");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✓ 数据库连接正常，用户表中有 {$result['count']} 个用户</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM 2_unit");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✓ 组织单位表中有 {$result['count']} 个单位</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM filecloud_info");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✓ 文件信息表中有 {$result['count']} 个文件</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM filecloud_share");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✓ 文件分享表中有 {$result['count']} 条分享记录</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
}

// 测试文件上传目录
function testUploadDirectory() {
    echo "<h3>测试文件上传目录</h3>";
    
    $uploadPath = getUploadPath();
    
    if (!file_exists($uploadPath)) {
        echo "<p style='color: orange;'>⚠ 上传目录不存在，尝试创建...</p>";
        if (mkdir($uploadPath, 0755, true)) {
            echo "<p style='color: green;'>✓ 上传目录创建成功: $uploadPath</p>";
        } else {
            echo "<p style='color: red;'>✗ 上传目录创建失败: $uploadPath</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ 上传目录存在: $uploadPath</p>";
    }
    
    if (is_writable($uploadPath)) {
        echo "<p style='color: green;'>✓ 上传目录可写</p>";
    } else {
        echo "<p style='color: red;'>✗ 上传目录不可写</p>";
    }
}

// 测试配置常量
function testConfiguration() {
    echo "<h3>测试系统配置</h3>";
    
    $configs = [
        'MAX_FILE_SIZE' => MAX_FILE_SIZE,
        'ALLOWED_EXTENSIONS' => implode(', ', ALLOWED_EXTENSIONS),
        'SHARE_CODE_LENGTH' => SHARE_CODE_LENGTH,
        'DEFAULT_EXPIRE_DAYS' => DEFAULT_EXPIRE_DAYS,
        'SITE_TITLE' => SITE_TITLE,
        'UPLOAD_PATH' => UPLOAD_PATH
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>配置项</th><th>值</th></tr>";
    foreach ($configs as $key => $value) {
        echo "<tr><td>$key</td><td>$value</td></tr>";
    }
    echo "</table>";
}

// 测试API接口
function testApiEndpoint() {
    echo "<h3>测试API接口</h3>";
    
    $apiFile = 'api/user_manage.php';
    if (file_exists($apiFile)) {
        echo "<p style='color: green;'>✓ API文件存在: $apiFile</p>";
    } else {
        echo "<p style='color: red;'>✗ API文件不存在: $apiFile</p>";
    }
    
    // 测试API调用
    echo "<h4>测试API调用</h4>";
    echo "<p>可以在浏览器开发者工具中查看网络请求来测试API</p>";
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匿名上传功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        h3 { color: #666; border-bottom: 2px solid #ddd; padding-bottom: 5px; }
        h4 { color: #888; }
        .test-section { margin-bottom: 30px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>匿名上传功能测试</h1>
    <p>此页面用于测试匿名上传功能的各个组件是否正常工作。</p>
    
    <div class="test-section">
        <?php testDatabaseConnection(); ?>
    </div>
    
    <div class="test-section">
        <?php testConfiguration(); ?>
    </div>
    
    <div class="test-section">
        <?php testUploadDirectory(); ?>
    </div>
    
    <div class="test-section">
        <?php testApiEndpoint(); ?>
    </div>
    
    <div class="test-section">
        <?php testUserSearch(); ?>
    </div>
    
    <div class="test-section">
        <h3>功能链接</h3>
        <ul>
            <li><a href="anonymous_upload.php" target="_blank">匿名上传页面</a></li>
            <li><a href="download.php" target="_blank">分享码下载页面</a></li>
            <li><a href="login.php" target="_blank">用户登录页面</a></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>测试步骤建议</h3>
        <ol>
            <li>确保所有测试项目都显示为绿色（成功状态）</li>
            <li>访问匿名上传页面，测试用户搜索功能</li>
            <li>选择一个或多个用户</li>
            <li>上传一个测试文件（建议使用小文件）</li>
            <li>验证文件上传成功并获得分享码</li>
            <li>使用分享码在下载页面测试下载功能</li>
            <li>登录系统查看文件是否正确分享给选定用户</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h3>故障排除</h3>
        <ul>
            <li><strong>数据库连接失败</strong>：检查config.php中的数据库配置</li>
            <li><strong>用户搜索无结果</strong>：确保3_user和2_unit表中有测试数据</li>
            <li><strong>上传目录问题</strong>：检查uploads目录的权限设置</li>
            <li><strong>API调用失败</strong>：检查api/user_manage.php文件是否存在</li>
            <li><strong>文件上传失败</strong>：检查PHP配置中的upload_max_filesize和post_max_size</li>
        </ul>
    </div>
</body>
</html>
