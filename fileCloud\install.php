<?php
/**
 * 文件网盘系统 - 安装向导
 * 创建时间: 2025-06-23
 */

// 防止重复安装
if (file_exists('config.php') && filesize('config.php') > 1000) {
    die('系统已安装，如需重新安装请删除config.php文件');
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// 处理安装步骤
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // 环境检测
            $step = 3;
            break;
            
        case 3:
            // 数据库配置
            $dbHost = $_POST['db_host'] ?? '';
            $dbName = $_POST['db_name'] ?? '';
            $dbUser = $_POST['db_user'] ?? '';
            $dbPass = $_POST['db_pass'] ?? '';
            
            if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
                $error = '请填写完整的数据库信息';
                break;
            }
            
            // 测试数据库连接
            try {
                $pdo = new PDO("mysql:host=$dbHost;charset=utf8mb4", $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // 创建数据库
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$dbName`");
                
                // 导入数据库结构
                $sql = file_get_contents('database_schema.sql');
                if ($sql) {
                    $statements = explode(';', $sql);
                    foreach ($statements as $statement) {
                        $statement = trim($statement);
                        if (!empty($statement)) {
                            $pdo->exec($statement);
                        }
                    }
                }
                
                // 保存配置文件
                $configContent = generateConfigFile($dbHost, $dbName, $dbUser, $dbPass);
                file_put_contents('config.php', $configContent);
                
                $step = 4;
                $success = '数据库配置成功';
                
            } catch (Exception $e) {
                $error = '数据库连接失败: ' . $e->getMessage();
            }
            break;
            
        case 4:
            // 完成安装
            $step = 5;
            break;
    }
}

function generateConfigFile($host, $name, $user, $pass) {
    return <<<PHP
<?php
/**
 * 文件网盘系统 - 数据库配置文件
 * 创建时间: 2025-06-23
 */

// 数据库配置
define('DB_HOST', '$host');
define('DB_NAME', '$name');
define('DB_USER', '$user');
define('DB_PASS', '$pass');
define('DB_CHARSET', 'utf8mb4');

// 系统配置
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 52428800); // 50MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar']);

// 安全配置
define('SHARE_CODE_LENGTH', 6);
define('DEFAULT_EXPIRE_DAYS', 30);

// 站点信息
define('SITE_TITLE', '文件网盘系统');
define('SITE_DESCRIPTION', '安全、便捷的文件分享平台');

// 错误报告（生产环境设为0）
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 时区设置
date_default_timezone_set('Asia/Shanghai');

try {
    \$pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException \$e) {
    die('数据库连接失败: ' . \$e->getMessage());
}

// 启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
PHP;
}

function checkEnvironment() {
    $checks = [
        'PHP版本 >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO扩展' => extension_loaded('pdo'),
        'PDO MySQL扩展' => extension_loaded('pdo_mysql'),
        'GD扩展' => extension_loaded('gd'),
        '文件上传支持' => ini_get('file_uploads'),
        'uploads目录可写' => is_writable('uploads') || mkdir('uploads', 0755, true),
        'session支持' => function_exists('session_start'),
    ];
    
    return $checks;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件网盘系统 - 安装向导</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .install-container { padding: 2rem 0; }
        .install-card { border: none; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
        .step { width: 40px; height: 40px; border-radius: 50%; background: #e9ecef; display: flex; align-items: center; justify-content: center; margin: 0 10px; color: #6c757d; font-weight: bold; }
        .step.active { background: #0d6efd; color: white; }
        .step.completed { background: #198754; color: white; }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card install-card">
                    <div class="card-header bg-primary text-white text-center">
                        <h3 class="mb-0">
                            <i class="bi bi-cloud-upload me-2"></i>文件网盘系统安装向导
                        </h3>
                    </div>
                    
                    <div class="card-body">
                        <!-- 步骤指示器 -->
                        <div class="step-indicator">
                            <div class="step <?= $step >= 1 ? 'completed' : '' ?>">1</div>
                            <div class="step <?= $step >= 2 ? 'completed' : '' ?> <?= $step == 2 ? 'active' : '' ?>">2</div>
                            <div class="step <?= $step >= 3 ? 'completed' : '' ?> <?= $step == 3 ? 'active' : '' ?>">3</div>
                            <div class="step <?= $step >= 4 ? 'completed' : '' ?> <?= $step == 4 ? 'active' : '' ?>">4</div>
                            <div class="step <?= $step == 5 ? 'active' : '' ?>">5</div>
                        </div>
                        
                        <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-circle me-2"></i><?= htmlspecialchars($error) ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i><?= htmlspecialchars($success) ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($step == 1): ?>
                        <!-- 步骤1: 欢迎页面 -->
                        <div class="text-center">
                            <h4>欢迎使用文件网盘系统</h4>
                            <p class="text-muted">本向导将帮助您完成系统的安装和配置</p>
                            
                            <div class="row mt-4">
                                <div class="col-md-4">
                                    <i class="bi bi-cloud-upload text-primary" style="font-size: 3rem;"></i>
                                    <h6 class="mt-2">文件上传</h6>
                                    <p class="text-muted small">支持多种格式文件上传</p>
                                </div>
                                <div class="col-md-4">
                                    <i class="bi bi-share text-success" style="font-size: 3rem;"></i>
                                    <h6 class="mt-2">安全分享</h6>
                                    <p class="text-muted small">生成分享码安全分享文件</p>
                                </div>
                                <div class="col-md-4">
                                    <i class="bi bi-shield-check text-info" style="font-size: 3rem;"></i>
                                    <h6 class="mt-2">权限管理</h6>
                                    <p class="text-muted small">完善的用户权限控制</p>
                                </div>
                            </div>
                            
                            <form method="post" action="?step=2" class="mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    开始安装 <i class="bi bi-arrow-right ms-2"></i>
                                </button>
                            </form>
                        </div>
                        
                        <?php elseif ($step == 2): ?>
                        <!-- 步骤2: 环境检测 -->
                        <h4>环境检测</h4>
                        <p class="text-muted">检查服务器环境是否满足系统要求</p>
                        
                        <?php $checks = checkEnvironment(); ?>
                        <div class="list-group">
                            <?php foreach ($checks as $name => $result): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <?= $name ?>
                                <?php if ($result): ?>
                                    <span class="badge bg-success rounded-pill">
                                        <i class="bi bi-check"></i> 通过
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger rounded-pill">
                                        <i class="bi bi-x"></i> 失败
                                    </span>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php $allPassed = !in_array(false, $checks); ?>
                        <div class="mt-4">
                            <?php if ($allPassed): ?>
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle me-2"></i>恭喜！您的环境满足系统要求
                                </div>
                                <form method="post" action="?step=3">
                                    <button type="submit" class="btn btn-primary">
                                        下一步 <i class="bi bi-arrow-right ms-2"></i>
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle me-2"></i>请先解决环境问题后再继续安装
                                </div>
                                <button class="btn btn-secondary" onclick="location.reload()">
                                    重新检测
                                </button>
                            <?php endif; ?>
                        </div>
                        
                        <?php elseif ($step == 3): ?>
                        <!-- 步骤3: 数据库配置 -->
                        <h4>数据库配置</h4>
                        <p class="text-muted">请填写MySQL数据库连接信息</p>
                        
                        <form method="post" action="?step=3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_host" class="form-label">数据库主机</label>
                                        <input type="text" class="form-control" id="db_host" name="db_host" 
                                               value="<?= htmlspecialchars($_POST['db_host'] ?? 'localhost') ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_name" class="form-label">数据库名称</label>
                                        <input type="text" class="form-control" id="db_name" name="db_name" 
                                               value="<?= htmlspecialchars($_POST['db_name'] ?? 'filecloud') ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_user" class="form-label">数据库用户名</label>
                                        <input type="text" class="form-control" id="db_user" name="db_user" 
                                               value="<?= htmlspecialchars($_POST['db_user'] ?? 'root') ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="db_pass" class="form-label">数据库密码</label>
                                        <input type="password" class="form-control" id="db_pass" name="db_pass" 
                                               value="<?= htmlspecialchars($_POST['db_pass'] ?? '') ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                系统将自动创建数据库（如果不存在）并导入必要的表结构
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                配置数据库 <i class="bi bi-arrow-right ms-2"></i>
                            </button>
                        </form>
                        
                        <?php elseif ($step == 4): ?>
                        <!-- 步骤4: 完成配置 -->
                        <h4>完成配置</h4>
                        <p class="text-muted">系统配置即将完成，请确认以下信息</p>
                        
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>数据库配置成功
                        </div>
                        
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6>默认登录账户</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>管理员账户:</strong><br>
                                        用户名: <code>admin</code><br>
                                        密码: <code>admin123</code>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>普通用户:</strong><br>
                                        用户名: <code>user</code><br>
                                        密码: <code>user123</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            安装完成后请及时修改默认密码并删除本安装文件
                        </div>
                        
                        <form method="post" action="?step=5">
                            <button type="submit" class="btn btn-success btn-lg">
                                完成安装 <i class="bi bi-check-circle ms-2"></i>
                            </button>
                        </form>
                        
                        <?php elseif ($step == 5): ?>
                        <!-- 步骤5: 安装完成 -->
                        <div class="text-center">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 5rem;"></i>
                            <h4 class="mt-3">安装完成！</h4>
                            <p class="text-muted">文件网盘系统已成功安装</p>
                            
                            <div class="alert alert-info">
                                <strong>安全提示:</strong> 请立即删除本安装文件（install.php）以确保系统安全
                            </div>
                            
                            <div class="d-flex gap-3 justify-content-center">
                                <a href="index.php" class="btn btn-primary btn-lg">
                                    <i class="bi bi-house me-2"></i>进入系统
                                </a>
                                <a href="admin.php" class="btn btn-outline-primary btn-lg">
                                    <i class="bi bi-gear me-2"></i>管理后台
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        &copy; 2025 文件网盘系统. All rights reserved.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
