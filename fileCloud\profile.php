<?php
/**
 * 文件网盘系统 - 个人资料页面
 * 创建时间: 2025-06-23
 */

require_once 'functions.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    redirect('login.php');
}

$currentUserId = $_SESSION['user_id'];
$currentUsername = $_SESSION['username'] ?? '用户';

// 获取用户统计信息
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as file_count, COALESCE(SUM(file_size), 0) as total_size 
        FROM filecloud_info 
        WHERE user_id = ? AND is_deleted = 0
    ");
    $stmt->execute([$currentUserId]);
    $stats = $stmt->fetch();
    
    // 获取总下载量
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(download_count), 0) as total_downloads 
        FROM filecloud_info 
        WHERE user_id = ? AND is_deleted = 0
    ");
    $stmt->execute([$currentUserId]);
    $downloadStats = $stmt->fetch();
    
    // 获取分享统计
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as shared_count 
        FROM filecloud_share 
        WHERE from_user_id = ? AND is_deleted = 0
    ");
    $stmt->execute([$currentUserId]);
    $shareStats = $stmt->fetch();
    
} catch (PDOException $e) {
    $stats = ['file_count' => 0, 'total_size' => 0];
    $downloadStats = ['total_downloads' => 0];
    $shareStats = ['shared_count' => 0];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 个人资料</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-files me-1"></i>我的文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>上传文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="shared.php">
                            <i class="bi bi-share me-1"></i>与我共享
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <?php if (isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin.php">
                            <i class="bi bi-gear me-1"></i>系统管理
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?= h($currentUsername) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="profile.php">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <div class="row">
            <!-- 用户信息卡片 -->
            <div class="col-lg-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <div class="avatar-large mx-auto mb-3">
                                <i class="bi bi-person-circle text-primary" style="font-size: 5rem;"></i>
                            </div>
                            <h4 class="mb-1"><?= h($currentUsername) ?></h4>
                            <p class="text-muted mb-3">
                                <?php if (isAdmin()): ?>
                                    <i class="bi bi-shield-check text-success me-1"></i>系统管理员
                                <?php else: ?>
                                    <i class="bi bi-person text-info me-1"></i>普通用户
                                <?php endif; ?>
                            </p>
                            <p class="text-muted small">
                                用户ID: <?= h($currentUserId) ?><br>
                                加入时间: <?= date('Y年m月d日') ?>
                            </p>
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="mb-0 text-primary"><?= number_format($stats['file_count']) ?></h5>
                                    <small class="text-muted">文件数</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h5 class="mb-0 text-success"><?= number_format($downloadStats['total_downloads']) ?></h5>
                                    <small class="text-muted">下载量</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h5 class="mb-0 text-info"><?= number_format($shareStats['shared_count']) ?></h5>
                                <small class="text-muted">分享数</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 存储使用情况 -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-hdd me-2"></i>存储使用情况
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>已使用</span>
                            <strong><?= formatFileSize($stats['total_size']) ?></strong>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <?php
                            $maxStorage = 1024 * 1024 * 1024; // 1GB 演示用
                            $usagePercent = min(($stats['total_size'] / $maxStorage) * 100, 100);
                            ?>
                            <div class="progress-bar bg-primary" style="width: <?= $usagePercent ?>%"></div>
                        </div>
                        <small class="text-muted">
                            总容量: <?= formatFileSize($maxStorage) ?>
                        </small>
                    </div>
                </div>
            </div>

            <!-- 主要内容区 -->
            <div class="col-lg-8">
                <!-- 账户设置 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear me-2"></i>账户设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="username" value="<?= h($currentUsername) ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">邮箱地址</label>
                                        <input type="email" class="form-control" id="email" placeholder="暂未设置">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unitOrg" class="form-label">编制单位</label>
                                        <input type="text" class="form-control" id="unitOrg" 
                                               value="<?= h($_SESSION['unit_id'][0] ?? '未设置') ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="unitWork" class="form-label">工作单位</label>
                                        <input type="text" class="form-control" id="unitWork" 
                                               value="<?= h($_SESSION['unit_id'][1] ?? '未设置') ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                基本信息由系统管理员统一管理，如需修改请联系管理员。
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 偏好设置 -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-sliders me-2"></i>偏好设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="preferencesForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="theme" class="form-label">界面主题</label>
                                        <select class="form-select" id="theme">
                                            <option value="light">浅色主题</option>
                                            <option value="dark">深色主题</option>
                                            <option value="auto">跟随系统</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">界面语言</label>
                                        <select class="form-select" id="language">
                                            <option value="zh-CN">简体中文</option>
                                            <option value="en-US">English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="pageSize" class="form-label">每页显示文件数</label>
                                        <select class="form-select" id="pageSize">
                                            <option value="10">10个</option>
                                            <option value="20" selected>20个</option>
                                            <option value="50">50个</option>
                                            <option value="100">100个</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="defaultExpire" class="form-label">默认分享有效期</label>
                                        <select class="form-select" id="defaultExpire">
                                            <option value="0">永不过期</option>
                                            <option value="1">1天</option>
                                            <option value="7" selected>7天</option>
                                            <option value="30">30天</option>
                                            <option value="90">90天</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                                <label class="form-check-label" for="autoRefresh">
                                    自动刷新文件列表
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="notifications" checked>
                                <label class="form-check-label" for="notifications">
                                    显示系统通知
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>保存设置
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-shield-lock me-2"></i>安全设置
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>修改密码</h6>
                                <p class="text-muted small">定期修改密码可以提高账户安全性</p>
                                <button class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-key me-1"></i>修改密码
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>登录记录</h6>
                                <p class="text-muted small">查看最近的登录活动记录</p>
                                <button class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-clock-history me-1"></i>查看记录
                                </button>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>数据导出</h6>
                                <p class="text-muted small">导出您的所有文件信息</p>
                                <button class="btn btn-outline-success btn-sm">
                                    <i class="bi bi-download me-1"></i>导出数据
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>账户注销</h6>
                                <p class="text-muted small">永久删除账户和所有数据</p>
                                <button class="btn btn-outline-danger btn-sm">
                                    <i class="bi bi-trash me-1"></i>注销账户
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // 偏好设置表单提交
        document.getElementById('preferencesForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const preferences = {
                theme: formData.get('theme') || document.getElementById('theme').value,
                language: formData.get('language') || document.getElementById('language').value,
                pageSize: formData.get('pageSize') || document.getElementById('pageSize').value,
                defaultExpire: formData.get('defaultExpire') || document.getElementById('defaultExpire').value,
                autoRefresh: document.getElementById('autoRefresh').checked,
                notifications: document.getElementById('notifications').checked
            };
            
            // 保存到本地存储
            if (typeof FileCloud !== 'undefined') {
                FileCloud.saveUserPreferences(preferences);
                FileCloud.showToast('设置已保存', 'success');
            } else {
                showToast('设置已保存', 'success');
            }
            
            // 应用主题变化
            if (preferences.theme && preferences.theme !== 'auto') {
                document.body.className = document.body.className.replace(/theme-\w+/g, '');
                document.body.classList.add(`theme-${preferences.theme}`);
            }
        });
        
        // 加载保存的偏好设置
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const preferences = JSON.parse(localStorage.getItem('filecloud_prefs') || '{}');
                
                if (preferences.theme) {
                    document.getElementById('theme').value = preferences.theme;
                }
                if (preferences.language) {
                    document.getElementById('language').value = preferences.language;
                }
                if (preferences.pageSize) {
                    document.getElementById('pageSize').value = preferences.pageSize;
                }
                if (preferences.defaultExpire !== undefined) {
                    document.getElementById('defaultExpire').value = preferences.defaultExpire;
                }
                if (preferences.autoRefresh !== undefined) {
                    document.getElementById('autoRefresh').checked = preferences.autoRefresh;
                }
                if (preferences.notifications !== undefined) {
                    document.getElementById('notifications').checked = preferences.notifications;
                }
            } catch (e) {
                console.warn('加载偏好设置失败:', e);
            }
        });
        
        // 其他功能按钮事件
        document.querySelectorAll('.btn-outline-primary, .btn-outline-info, .btn-outline-success, .btn-outline-danger').forEach(btn => {
            btn.addEventListener('click', function() {
                const action = this.textContent.trim();
                showToast(`"${action}" 功能开发中...`, 'info');
            });
        });
    </script>
</body>
</html>
