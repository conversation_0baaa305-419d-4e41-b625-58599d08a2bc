<?php
require_once '../../conn_waf.php';
$APP_ID=9;

function isHasPerm(){
    global $conn, $APP_ID;

    // 检查用户登录状态
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('无权限操作');
        exit;
    }

    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }

    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }

    // 获取基于角色授权的应用权限
    $userId = $_SESSION['user_id'];
    $sql = "SELECT DISTINCT a.id FROM 5_application a
            JOIN 3_user_Role ur ON a.id = ur.appId
            WHERE ur.userId = ? AND JSON_CONTAINS(a.roleList, CAST(ur.roleId AS JSON))";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && $result->num_rows > 0) {
        return true;
    }

    // 权限不足，重定向到美观的错误页面
    throw new Exception('无权限操作');
    exit;
}

// 添加排班记录
function addSchedule() {
    global $conn, $APP_ID;

    // 获取参数
    $sched_date = $_POST['sched_date'] ?? '';
    $a_dir = $_POST['a_dir'] ?? '';
    $b_dir = $_POST['b_dir'] ?? null;
    $a_com = $_POST['a_com'] ?? '';
    $b_com = $_POST['b_com'] ?? null;
    $sub = $_POST['sub'] ?? '';
    $op = $_POST['op'] ?? '';
    $tech = $_POST['tech'] ?? '';
    $sec = $_POST['sec'] ?? '';


    $sql = "INSERT INTO 6_duty_sched (sched_date, a_dir, b_dir, a_com, b_com, sub, op, tech, sec) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = mysqli_prepare($conn, $sql);
    
    // 处理整数字段的空值
    $a_dir = empty($a_dir) ? null : (int)$a_dir;
    $b_dir = empty($b_dir) ? null : (int)$b_dir;
    $a_com = empty($a_com) ? null : (int)$a_com;
    $b_com = empty($b_com) ? null : (int)$b_com;
    $sub = empty($sub) ? null : (int)$sub;
    
    mysqli_stmt_bind_param($stmt, 'siiiiisss', 
        $sched_date, 
        $a_dir, 
        $b_dir, 
        $a_com, 
        $b_com, 
        $sub, $op, $tech, $sec);
    $executeResult = mysqli_stmt_execute($stmt);

    if ($executeResult === false) {
        throw new Exception('插入失败: ' . mysqli_error($conn));
    }

    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('插入失败: 没有影响任何行');
    }

    $id = $_POST['id'] ?? '';
    logOperation($conn, $_SESSION['user_id'], $APP_ID, '新增排班表:'.$id);
    return ['status' => 1, 'message' => '插入成功', 'data' => []];
}

// 修改排班记录
function modifySchedule() {
    global $conn, $APP_ID;

    // 获取参数
    $id = $_POST['id'] ?? '';
    $sched_date = $_POST['sched_date'] ?? '';
    $a_dir = $_POST['a_dir'] ?? '';
    $b_dir = $_POST['b_dir'] ?? '';
    $a_com = $_POST['a_com'] ?? '';
    $b_com = $_POST['b_com'] ?? '';
    $sub = $_POST['sub'] ?? '';
    $op = $_POST['op'] ?? '';
    $tech = $_POST['tech'] ?? '';
    $sec = $_POST['sec'] ?? '';


    $sql = "UPDATE 6_duty_sched SET sched_date = ?, a_dir = ?, b_dir = ?, a_com = ?, b_com = ?, sub = ?, op = ?, tech = ?, sec = ? WHERE id = ?";
    $params = [$sched_date, $a_dir, $b_dir, $a_com, $b_com, $sub, $op, $tech, $sec, $id];

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'sssssssssi', $params[0], $params[1], $params[2], $params[3], $params[4], $params[5], $params[6], $params[7], $params[8], $params[9]);
    $executeResult = mysqli_stmt_execute($stmt);

    if ($executeResult === false) {
        throw new Exception('修改失败: ' . mysqli_error($conn));
    }

    $affectedRows = mysqli_stmt_affected_rows($stmt);
    if ($affectedRows <= 0) {
        throw new Exception('修改失败: 没有影响任何行');
    }

    $logContent = '修改排班表ID:'.$id.', 日期='.$sched_date.', a_dir='.$a_dir.', b_dir='.$b_dir.', a_com='.$a_com.', b_com='.$b_com.', sub='.$sub.', op='.$op.', tech='.$tech.', sec='.$sec;
    logOperation($conn, $_SESSION['user_id'], $APP_ID, $logContent);
    return ['status' => 1, 'message' => '修改成功', 'data' => []];
}

// 删除排班记录
function deleteSchedule() {
    global $conn, $APP_ID;

    // 获取参数
    $id = $_POST['id'] ?? '';

    // 检查权限
    if (!isset($_SESSION['user_id']) || !isAdmin()) {
        throw new Exception('无权限操作');
    }

    // 判断是单个ID还是批量ID
    if (strpos($id, ',') !== false) {
        // 批量删除：处理逗号分隔的ID列表
        $idArray = explode(',', $id);
        $validIds = [];

        // 验证并转换每个ID为整数
        foreach ($idArray as $singleId) {
            $singleId = trim($singleId);
            if (is_numeric($singleId) && $singleId > 0) {
                $validIds[] = (int)$singleId;
            }
        }

        if (empty($validIds)) {
            throw new Exception('没有有效的ID可以删除');
        }

        // 构建批量删除SQL
        $placeholders = implode(',', array_fill(0, count($validIds), '?'));
        $sql = "DELETE FROM 6_duty_sched WHERE id IN ($placeholders)";
        $stmt = mysqli_prepare($conn, $sql);

        // 绑定参数
        $types = str_repeat('i', count($validIds));
        mysqli_stmt_bind_param($stmt, $types, ...$validIds);

        $executeResult = mysqli_stmt_execute($stmt);

        if ($executeResult === false) {
            throw new Exception('批量删除失败: ' . mysqli_error($conn));
        }

        $affectedRows = mysqli_stmt_affected_rows($stmt);
        if ($affectedRows <= 0) {
            throw new Exception('批量删除失败: 没有影响任何行');
        }

        // 记录批量删除日志
        $deletedIds = implode(',', $validIds);
        logOperation($conn, $_SESSION['user_id'], $APP_ID, '批量删除排班表:' . $deletedIds . ' (共删除' . $affectedRows . '条记录)');

        return ['status' => 1, 'message' => '批量删除成功，共删除' . $affectedRows . '条记录', 'data' => []];

    } else {
        // 单个删除：处理单个ID
        if (!is_numeric($id) || $id <= 0) {
            throw new Exception('无效的ID参数');
        }

        $id = (int)$id;
        $sql = "DELETE FROM 6_duty_sched WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, 'i', $id);
        $executeResult = mysqli_stmt_execute($stmt);

        if ($executeResult === false) {
            throw new Exception('删除失败: ' . mysqli_error($conn));
        }

        $affectedRows = mysqli_stmt_affected_rows($stmt);
        if ($affectedRows <= 0) {
            throw new Exception('删除失败: 没有影响任何行');
        }

        logOperation($conn, $_SESSION['user_id'], $APP_ID, '删除排班表:' . $id);
        return ['status' => 1, 'message' => '删除成功', 'data' => []];
    }
}

// 后端查询排班记录
function querySchedule() {
    global $conn, $APP_ID;

    // 获取参数
    $sched_date = $_POST['sched_date'] ?? '';


    // 获取分页参数
    $page = isset($_POST['page']) ? max(1, (int)$_POST['page']) : 1;
    $pageSize = isset($_POST['pagesize']) ? max(1, (int)$_POST['pagesize']) : 10;

    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM 6_duty_sched";
    if (!empty($sched_date)) {
        $countSql .= " WHERE sched_date = '" . mysqli_real_escape_string($conn, $sched_date) . "'";
    }
    $countResult = mysqli_query($conn, $countSql);
    $totalCount = (int)mysqli_fetch_assoc($countResult)['total'];
    $pageMax = ceil($totalCount / $pageSize);

    // 构建查询SQL
    $sql = "SELECT * FROM 6_duty_sched";
    if (!empty($sched_date)) {
        $sql .= " WHERE sched_date = '" . mysqli_real_escape_string($conn, $sched_date) . "'";
    } else {
        $sql .= " ORDER BY sched_date DESC";
    }
    $offset = ($page - 1) * $pageSize;
    $sql .= " LIMIT $offset, $pageSize";
    $resultQuery = mysqli_query($conn, $sql);
    $data = [];

    if (mysqli_num_rows($resultQuery) > 0) {
        while ($row = mysqli_fetch_assoc($resultQuery)) {
            // 转换数字字段
            $row['id'] = (int)$row['id'];
            $convertFields = ['a_dir', 'b_dir', 'a_com', 'b_com', 'sub', 'tech'];
            foreach ($convertFields as $field) {
                if (isset($row[$field])) {
                    $row[$field] = (int)$row[$field];
                }
            }
            // 处理数组字段
            foreach (['op', 'sec'] as $arrayField) {
                if (!empty($row[$arrayField])) {
                    $ids = is_array($row[$arrayField]) ? $row[$arrayField] : explode(',', $row[$arrayField]);
                    $row[$arrayField] = array_map('intval', $ids);
                }
            }
            $data[] = $row;
        }
    }

    // 收集所有用户ID
    $userIds = [];
    foreach ($data as $row) {
        $fields = ['a_dir', 'b_dir', 'a_com', 'b_com', 'sub', 'op', 'tech', 'sec'];
        foreach ($fields as $field) {
            if (!empty($row[$field])) {
                $ids = is_array($row[$field]) ? $row[$field] : explode(',', $row[$field]);
                foreach ($ids as $id) {
                    $id = trim($id);
                    if (is_numeric($id)) {
                        $userIds[] = $id;
                    }
                }
            }
        }
    }
    $userIds = array_unique($userIds);

    // 查询用户信息
    $userInfo = [];
    if (!empty($userIds)) {
        $placeholders = implode(',', array_fill(0, count($userIds), '?'));
        $sqlUsers = "SELECT id, name FROM 3_user WHERE id IN ($placeholders)";
        $stmt = mysqli_prepare($conn, $sqlUsers);
        $types = str_repeat('i', count($userIds));
        mysqli_stmt_bind_param($stmt, $types, ...$userIds);
        mysqli_stmt_execute($stmt);
        $resultUsers = mysqli_stmt_get_result($stmt);
        $userMap = [];
        while ($user = mysqli_fetch_assoc($resultUsers)) {
            $userId = $user['id'];
            if (!isset($userMap[$userId])) {
                $userMap[$userId] = [
                    'userId' => $userId,
                    'userName' => $user['name']
                ];
            }
        }
        $userInfo = array_values($userMap);
    }

    // 记录查询日志
    $logContent = '查询排班表，参数: ';
    $logContent .= 'sched_date: ' . ($sched_date ?? '无') . ', ';
    $logContent .= 'page: ' . $page . ', ';
    $logContent .= 'pageSize: ' . $pageSize ;
    logOperation($conn, $_SESSION['user_id'], $APP_ID, $logContent);

    // 为每个用户ID字段添加对应的名称字段
    foreach ($data as &$row) {
        $fields = ['a_dir', 'b_dir', 'a_com', 'b_com', 'sub', 'tech'];
        foreach ($fields as $field) {
            if (!empty($row[$field])) {
                $userId = $row[$field];
                $userStmt = $conn->prepare("SELECT name FROM 3_user WHERE id = ?");
                $userStmt->bind_param("i", $userId);
                $userStmt->execute();
                $userResult = $userStmt->get_result();
                $user = $userResult->fetch_assoc();
                $row[$field.'_name'] = $user['name'] ?? '';
            }
        }

        // 处理数组字段
        $arrayFields = ['op', 'sec'];
        foreach ($arrayFields as $field) {
            if (!empty($row[$field])) {
                $ids = is_array($row[$field]) ? $row[$field] : explode(',', $row[$field]);
                $names = [];
                foreach ($ids as $id) {
                    $userStmt = $conn->prepare("SELECT name FROM 3_user WHERE id = ?");
                    $userStmt->bind_param("i", $id);
                    $userStmt->execute();
                    $userResult = $userStmt->get_result();
                    $user = $userResult->fetch_assoc();
                    $names[] = $user['name'] ?? '';
                }
                $row[$field.'_name'] = implode(',', $names);
            }
        }
    }

    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'page' => $page,
        'pageMax' => $pageMax,
        'totalCount' => $totalCount,
        /*'debug' => [
            'sql' => $sql,
        ],*/
    ];
}

// 前端查询排班记录
function queryScheduleFrontend() {
    global $conn;

    // 获取参数
    $sched_date = $_POST['sched_date'] ?? '';

    // 设置默认日期为今天
    if (empty($sched_date)) {
        $sched_date = date('Y-m-d');
    }

    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM 6_duty_sched";
    if (!empty($sched_date)) {
        $countSql .= " WHERE sched_date = '" . mysqli_real_escape_string($conn, $sched_date) . "'";
    }
    $countResult = mysqli_query($conn, $countSql);
    $totalCount = (int)mysqli_fetch_assoc($countResult)['total'];

    // 构建查询SQL
    $sql = "SELECT * FROM 6_duty_sched";
    if (!empty($sched_date)) {
        $sql .= " WHERE sched_date = '" . mysqli_real_escape_string($conn, $sched_date) . "'";
    } else {
        $sql .= " ORDER BY sched_date DESC";
    }
    $resultQuery = mysqli_query($conn, $sql);
    $data = [];

    if (mysqli_num_rows($resultQuery) > 0) {
        while ($row = mysqli_fetch_assoc($resultQuery)) {
            // 转换数字字段
            $row['id'] = (int)$row['id'];
            $data[] = $row;
        }
    }

    // 收集所有用户ID
    $userIds = [];
    foreach ($data as $row) {
        $fields = ['a_dir', 'b_dir', 'a_com', 'b_com', 'sub', 'op', 'tech', 'sec'];
        foreach ($fields as $field) {
            if (!empty($row[$field])) {
                $ids = is_array($row[$field]) ? $row[$field] : explode(',', $row[$field]);
                foreach ($ids as $id) {
                    $id = trim($id);
                    if (is_numeric($id)) {
                        $userIds[] = $id;
                    }
                }
            }
        }
    }
    $userIds = array_unique($userIds);

    // 查询用户信息
    $userMap = [];
    if (!empty($userIds)) {
        $placeholders = implode(',', array_fill(0, count($userIds), '?'));
        $sqlUsers = "SELECT id, name FROM 3_user WHERE id IN ($placeholders)";
        $stmt = mysqli_prepare($conn, $sqlUsers);
        $types = str_repeat('i', count($userIds));
        mysqli_stmt_bind_param($stmt, $types, ...$userIds);
        mysqli_stmt_execute($stmt);
        $resultUsers = mysqli_stmt_get_result($stmt);
        while ($user = mysqli_fetch_assoc($resultUsers)) {
            $userMap[$user['id']] = $user['name'];
        }
    }

    // 处理用户名替换
    $formattedData = [];
    foreach ($data as $row) {
        // 处理单个用户字段
        $fields = ['a_dir', 'b_dir', 'a_com', 'b_com', 'sub', 'tech'];
        foreach ($fields as $field) {
            $userId = $row[$field];
            $row[$field] = isset($userMap[$userId]) ? $userMap[$userId] : '';
        }

        // 处理数组用户字段
        $arrayFields = ['op', 'sec'];
        foreach ($arrayFields as $field) {
            $ids = is_array($row[$field]) ? $row[$field] : array_map('intval', explode(',', $row[$field]));
            $names = [];
            foreach ($ids as $id) {
                $names[] = isset($userMap[$id]) ? $userMap[$id] : '';
            }
            $row[$field] = $names;
        }

        $formattedData[] = $row;
    }

    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $formattedData,
        'totalCount' => $totalCount
    ];
}

// 获取角色列表
function getRoleList() {
    global $conn;

    // 查询角色列表
    $roleList = [];
    $sqlRoles = "SELECT * FROM 4_Role_List";
    $resultRoles = mysqli_query($conn, $sqlRoles);
    if ($resultRoles && mysqli_num_rows($resultRoles) > 0) {
        while ($role = mysqli_fetch_assoc($resultRoles)) {
            $role['id'] = (int)$role['id'];
            $roleList[] = $role;
        }
    }

    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $roleList,
    ];
}

// 根据角色ID获取用户及单位信息
function getRoleUsers() {
    global $conn;

    $users = [];
    $roleId = $_POST['roleId'];
    $stmt = $conn->prepare("SELECT userId,unitId FROM 3_user_Role WHERE roleId = ?");
    if(!$stmt) {
        $result = [
            'status' => 0,
            'message' => 'SQL准备失败: ' . $conn->error
        ];
        echo json_encode($result);
        exit;
    }
    $stmt->bind_param("i", $roleId);
    if(!$stmt->execute()) {
        $result = [
            'status' => 0,
            'message' => 'SQL执行失败: ' . $stmt->error
        ];
        echo json_encode($result);
        exit;
    }
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        // 查询用户姓名
        $userStmt = $conn->prepare("SELECT name FROM 3_user WHERE id = ?");
        $userStmt->bind_param("i", $row['userId']);
        $userStmt->execute();
        $userResult = $userStmt->get_result();
        $user = $userResult->fetch_assoc();

        // 查询单位名称
        $unitStmt = $conn->prepare("SELECT unit_name FROM 2_unit WHERE id = ?");
        $unitStmt->bind_param("i", $row['unitId']);
        $unitStmt->execute();
        $unitResult = $unitStmt->get_result();
        $unit = $unitResult->fetch_assoc();

        $users[] = [
            'userId' => $row['userId'],
            'name' => isset($user['name']) ? $user['name'] : '',
            'unitName' => isset($unit['unit_name']) ? $unit['unit_name'] : ''
        ];
    }

    return [
        'status' => 1,
        'message' => '查询成功',
        'data' => $users
    ];
}

try {

    // 获取参数
    $controlCode = $_POST['controlCode'] ?? '';
    $id = $_POST['id'] ?? '';
    $sched_date = $_POST['sched_date'] ?? '';
    $a_dir = $_POST['a_dir'] ?? '';
    $b_dir = $_POST['b_dir'] ?? '';
    $a_com = $_POST['a_com'] ?? '';
    $b_com = $_POST['b_com'] ?? '';
    $sub = $_POST['sub'] ?? '';
    $op = $_POST['op'] ?? '';
    $tech = $_POST['tech'] ?? '';
    $sec = $_POST['sec'] ?? '';

    // 检查所有参数是否为空
    if (empty($controlCode) || (in_array($controlCode, ['add', 'modify']) && (empty($sched_date) || empty($a_dir) || empty($a_com) || empty($sub) || empty($op) || empty($tech) || empty($sec))) || (in_array($controlCode, ['modify', 'del']) && empty($id))) {
        throw new Exception('所有参数都不能为空');
    }

    switch ($controlCode) {
        case 'add': // 插入操作
            isHasPerm();
            $result = addSchedule();
            break;

        case 'modify': // 修改操作
            isHasPerm();
            $result = modifySchedule();
            break;

        case 'del': // 删除操作
            isHasPerm();
            $result = deleteSchedule();
            break;

        case 'query': // 后端查询操作
            isHasPerm();
            $result = querySchedule();
            break;

        case 'query1': // 前端查询操作
            $result = queryScheduleFrontend();
            break;

        case 'getRole': // 查询角色列表
            isHasPerm();
            $result = getRoleList();
            break;

        case 'getRoleUser': // 根据roleid获取用户及单位信息
            isHasPerm();
            $result = getRoleUsers();
            break;
        default:
            throw new Exception('无效的控制码');
    }

    echo json_encode($result);
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['status' => 0, 'message' => $e->getMessage(), 'data' => []]);
} finally {
    $conn->close();
}