<?php
/**
 * 文件网盘系统 - 直接下载文件API
 * 创建时间: 2025-07-29
 */

require_once '../functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    http_response_code(401);
    die('请先登录');
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    die('无效的请求方法');
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    http_response_code(400);
    die('无效的请求');
}

$fileId = (int)($_POST['file_id'] ?? 0);
$currentUserId = $_SESSION['user_id'];

// 验证文件ID
if ($fileId <= 0) {
    http_response_code(400);
    die('参数错误：无效的文件ID');
}

try {
    // 检查文件是否存在且属于当前用户
    $stmt = $pdo->prepare("
        SELECT file_id, original_name, stored_name, file_size, file_path, download_count
        FROM filecloud_info 
        WHERE file_id = ? AND user_id = ? AND is_deleted = 0
    ");
    $stmt->execute([$fileId, $currentUserId]);
    $file = $stmt->fetch();
    
    if (!$file) {
        http_response_code(404);
        die('文件不存在或无权限');
    }

    // 构建文件完整路径 - 从api目录调整到项目根目录
    $filePath = '../' . $file['file_path'];
    if (!file_exists($filePath)) {
        http_response_code(404);
        die('文件不存在');
    }

    // 更新下载次数
    $updateStmt = $pdo->prepare("
        UPDATE filecloud_info 
        SET download_count = download_count + 1 
        WHERE file_id = ?
    ");
    $updateStmt->execute([$fileId]);

    // 设置下载头部
    $filename = $file['original_name'];
    $filesize = $file['file_size'];
    
    // 清除输出缓冲区
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    // 设置HTTP头部
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $filesize);
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    
    // 输出文件内容
    readfile($filePath);
    exit;

} catch (Exception $e) {
    http_response_code(500);
    die('下载失败');
}
?>
