<?php
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('X-Accel-Buffering: no');
header('Access-Control-Allow-Origin: http://localhost:5173'); 
// 允许携带凭证
header('Access-Control-Allow-Credentials: true'); 
// 允许的请求方法
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS'); 
// 允许的请求头
header('Access-Control-Allow-Headers: Content-Type, Authorization');
ini_set('output_buffering', 'off');
ini_set('zlib.output_compression', false);

function get_sched() {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://**************/DutySched/api/sched_manage.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, ['controlCode' => 'query1']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    $responseData = json_decode($response);
    return $responseData->data;
}
function get_alarm(){
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://**************/importantAlarm/api/alarm_basic.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, [
        'controlCode' => 'query',
        'is_display' => '1'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
    $responseData = json_decode($response);
    
    if(!empty($responseData->data)) {
        foreach($responseData->data as &$alarm) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://**************/importantAlarm/api/alarm_handling.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, [
                'controlCode' => 'query',
                'alarm_id' => $alarm->id
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $handlingResponse = curl_exec($ch);
            curl_close($ch);
            $handlingData = json_decode($handlingResponse);
            
            $alarm->alarm_handling = !empty($handlingData->data) ? $handlingData->data[0] : null;
        }
    }
    
    return $responseData->data;
}
function get_keyperson(){
    // 获取重点人员基础信息
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://**************/keyPersActivity/api/key_person.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, [
        'controlCode' => 'query',
        'is_display' => '1'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $responseData = json_decode($response);
    curl_close($ch);

    if(!empty($responseData->data)) {
        foreach($responseData->data as &$person) {
            // 获取每个重点人员的最新动态
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://**************/keyPersActivity/api/person_movement.php');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, [
                'controlCode' => 'query',
                'search_idnumber' => $person->id_number
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $movementResponse = curl_exec($ch);
            curl_close($ch);
            $movementData = json_decode($movementResponse);
            
            // 合并动态数据
            $person->person_movement = !empty($movementData->data) ? $movementData->data[0] : null;
        }
    }
    
    return $responseData->data;
}
while(true) {
    try {
        $sched = get_sched();
        $alarm = get_alarm();
        $keyperson = get_keyperson();
        $data = json_encode([
            'sched' => $sched,
            'alarm' => $alarm,
            'keyperson' => $keyperson
        ]);
        echo "data: " . $data . "\n\n";
        ob_flush();
        flush();
        sleep(30); // 30秒间隔
    } catch (Exception $e) {
        break;
    }
}
?>