<?php
require_once '../../conn_waf.php';

/**
 * 构建单位树形结构
 */
function buildUnitTree($units, $parent_id = null) {
    $tree = [];
    foreach ($units as $unit) {
        if ($unit['parent_id'] == $parent_id) {
            $unit['children'] = buildUnitTree($units, $unit['id']);
            $tree[] = $unit;
        }
    }
    return $tree;
}

/**
 * 将树形结构的单位转换为平铺的显示顺序
 */
function flattenUnitTree($units, &$flat_order) {
    foreach ($units as $unit) {
        $flat_order[] = $unit['id'];
        if (isset($unit['children']) && !empty($unit['children'])) {
            flattenUnitTree($unit['children'], $flat_order);
        }
    }
}

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['status' => 0, 'message' => '用户未登录']);
    exit;
}

$type = $_GET['type'] ?? '';
$scenario_id = $_GET['scenario_id'] ?? 0;
$unit_id = $_GET['unit_id'] ?? null;

if (empty($type) || empty($scenario_id)) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['status' => 0, 'message' => '参数不完整']);
    exit;
}

try {
    switch ($type) {
        case 'personnel':
            exportPersonnel($scenario_id, $unit_id);
            break;
        case 'statistics':
            exportStatistics($scenario_id);
            break;
        case 'transfer_logs':
            exportTransferLogs($scenario_id);
            break;
        default:
            throw new Exception('无效的导出类型');
    }
} catch (Exception $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['status' => 0, 'message' => $e->getMessage()]);
}

/**
 * 导出人员信息
 */
function exportPersonnel($scenario_id, $unit_id = null) {
    global $conn;
    
    $where = "WHERE sp.scenario_id = ?";
    $params = [$scenario_id];
    $types = 'i';
    
    if (!empty($unit_id)) {
        $where .= " AND sp.organization_unit = ?";
        $params[] = $unit_id;
        $types .= 'i';
    }
    
    $sql = "SELECT sp.name as '姓名', 
                   sp.id_number as '身份证号', 
                   sp.phone as '手机号码',
                   sp.personnel_type as '人员身份', 
                   su.unit_name as '当前单位',
                   sp.police_number as '警号',
                   sp.position as '职务',
                   sp.job_rank as '职级',
                   sp.transfer_count as '调动次数',
                   sp.last_transfer_time as '最后调动时间'
            FROM sim_personnel sp
            LEFT JOIN sim_units su ON sp.organization_unit = su.id
            $where
            ORDER BY su.unit_name, sp.name";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // 获取方案名称
    $scenarioSql = "SELECT scenario_name FROM sim_scenarios WHERE id = ?";
    $scenarioStmt = $conn->prepare($scenarioSql);
    $scenarioStmt->bind_param('i', $scenario_id);
    $scenarioStmt->execute();
    $scenario_name = $scenarioStmt->get_result()->fetch_assoc()['scenario_name'];
    
    $filename = $scenario_name . '_人员信息_' . date('Y-m-d') . '.csv';
    
    // 设置CSV下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    $output = fopen('php://output', 'w');
    
    // 添加BOM以支持中文
    fwrite($output, "\xEF\xBB\xBF");
    
    // 写入表头
    if ($result->num_rows > 0) {
        $first_row = $result->fetch_assoc();
        fputcsv($output, array_keys($first_row));
        fputcsv($output, array_values($first_row));
        
        // 写入数据
        while ($row = $result->fetch_assoc()) {
            fputcsv($output, array_values($row));
        }
    }
    
    fclose($output);
}

/**
 * 导出统计信息
 */
function exportStatistics($scenario_id) {
    global $conn;

    // 首先获取单位的树形结构信息以确定正确的显示顺序
    $unitOrderSql = "SELECT u.id, u.unit_name, u.parent_id, u.sort_order
                     FROM sim_units u
                     WHERE u.scenario_id = ? AND u.is_deleted = 0
                     ORDER BY u.sort_order, u.unit_name";
    $unitOrderStmt = $conn->prepare($unitOrderSql);
    $unitOrderStmt->bind_param('i', $scenario_id);
    $unitOrderStmt->execute();
    $unitOrderResult = $unitOrderStmt->get_result();

    $all_units = [];
    while ($row = $unitOrderResult->fetch_assoc()) {
        $all_units[] = $row;
    }

    // 构建树形结构并获取平铺的显示顺序
    $unit_tree = buildUnitTree($all_units);
    $flat_order = [];
    flattenUnitTree($unit_tree, $flat_order);

    // 获取基础统计数据（与页面显示使用相同的查询）
    $sql = "SELECT su.id as unit_id,
                   su.unit_name,
                   pu.unit_name as parent_unit_name,
                   su.sort_order,
                   sp.id as personnel_id,
                   sp.name as personnel_name,
                   sp.personnel_type,
                   sp.original_user_id
            FROM sim_units su
            LEFT JOIN sim_units pu ON su.parent_id = pu.id
            LEFT JOIN sim_personnel sp ON su.id = sp.organization_unit AND sp.scenario_id = ?
            WHERE su.scenario_id = ? AND su.is_deleted = 0
            ORDER BY su.sort_order, su.unit_name";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ii', $scenario_id, $scenario_id);
    $stmt->execute();
    $result = $stmt->get_result();

    // 按单位组织数据
    $units_data = [];
    while ($row = $result->fetch_assoc()) {
        $unit_id = $row['unit_id'];

        if (!isset($units_data[$unit_id])) {
            $units_data[$unit_id] = [
                'unit_id' => $unit_id,
                'unit_name' => $row['unit_name'],
                'parent_unit_name' => $row['parent_unit_name'],
                'sort_order' => $row['sort_order'],
                'police_count' => 0,
                'auxiliary_count' => 0,
                'civilian_count' => 0,
                'total_count' => 0,
                'transfer_in_count' => 0,
                'transfer_out_count' => 0,
                'transfer_in_list' => [],
                'transfer_out_list' => []
            ];
        }

        // 统计人员类型
        if ($row['personnel_id']) {
            $units_data[$unit_id]['total_count']++;

            if (strpos($row['personnel_type'], '民警') !== false || strpos($row['personnel_type'], '警察') !== false) {
                $units_data[$unit_id]['police_count']++;
            } elseif (strpos($row['personnel_type'], '辅警') !== false) {
                $units_data[$unit_id]['auxiliary_count']++;
            } else {
                $units_data[$unit_id]['civilian_count']++;
            }
        }
    }

    // 统计调动信息 - 基于原始人员和模拟人员的具体对比（与页面显示逻辑一致）
    foreach ($units_data as $unit_id => &$unit_data) {
        // 获取sim_units对应的原始单位ID（从2_unit表）
        $getOriginalUnitIdSql = "SELECT id FROM 2_unit WHERE unit_name = ?";
        $getOriginalUnitIdStmt = $conn->prepare($getOriginalUnitIdSql);
        $getOriginalUnitIdStmt->bind_param('s', $unit_data['unit_name']);
        $getOriginalUnitIdStmt->execute();
        $originalUnitResult = $getOriginalUnitIdStmt->get_result();

        if ($originalUnitResult->num_rows === 0) {
            // 如果找不到对应的原始单位，跳过统计
            continue;
        }

        $original_unit_id = $originalUnitResult->fetch_assoc()['id'];

        // 获取该原始单位的人员ID列表（从3_user表）
        $originalUsersSql = "SELECT id, name FROM 3_user WHERE organization_unit = ?";
        $originalUsersStmt = $conn->prepare($originalUsersSql);

        $original_user_ids = [];
        if ($originalUsersStmt) {
            $originalUsersStmt->bind_param('i', $original_unit_id);
            $originalUsersStmt->execute();
            $result = $originalUsersStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $original_user_ids[] = intval($row['id']);
            }
        }

        // 获取该单位的模拟人员原始ID列表（从sim_personnel表）
        $simUsersSql = "SELECT original_user_id, name FROM sim_personnel WHERE scenario_id = ? AND organization_unit = ? AND original_user_id IS NOT NULL";
        $simUsersStmt = $conn->prepare($simUsersSql);

        $sim_user_ids = [];
        if ($simUsersStmt) {
            $simUsersStmt->bind_param('ii', $scenario_id, $unit_id);
            $simUsersStmt->execute();
            $result = $simUsersStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                if ($row['original_user_id'] !== null) {
                    $sim_user_ids[] = intval($row['original_user_id']);
                }
            }
        }

        // 计算调入调出
        $transfer_in_ids = array_diff($sim_user_ids, $original_user_ids);
        $unit_data['transfer_in_count'] = count($transfer_in_ids);

        $transfer_out_ids = array_diff($original_user_ids, $sim_user_ids);
        $unit_data['transfer_out_count'] = count($transfer_out_ids);

        // 获取调入人员详细信息
        if (!empty($transfer_in_ids)) {
            $in_ids_str = implode(',', array_map('intval', $transfer_in_ids));
            $transferInDetailSql = "SELECT sp.name FROM sim_personnel sp
                                    WHERE sp.scenario_id = ? AND sp.organization_unit = ?
                                    AND sp.original_user_id IN ($in_ids_str)";
            $transferInDetailStmt = $conn->prepare($transferInDetailSql);
            $transferInDetailStmt->bind_param('ii', $scenario_id, $unit_id);
            $transferInDetailStmt->execute();
            $result = $transferInDetailStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $unit_data['transfer_in_list'][] = $row['name'];
            }
        }

        // 获取调出人员详细信息
        if (!empty($transfer_out_ids)) {
            $out_ids_str = implode(',', array_map('intval', $transfer_out_ids));
            $transferOutDetailSql = "SELECT u.name FROM 3_user u WHERE u.id IN ($out_ids_str)";
            $transferOutDetailStmt = $conn->prepare($transferOutDetailSql);
            $transferOutDetailStmt->execute();
            $result = $transferOutDetailStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $unit_data['transfer_out_list'][] = $row['name'];
            }
        }
    }

    // 按照树形结构的顺序重新排列数据，并格式化为导出格式
    $sorted_data = [];
    foreach ($flat_order as $unit_id) {
        if (isset($units_data[$unit_id])) {
            $unit = $units_data[$unit_id];

            // 格式化调入调出信息（与页面显示格式一致）
            $transfer_in_text = '-';
            if (!empty($unit['transfer_in_list'])) {
                $transfer_in_text = '调入名单：' . implode('，', $unit['transfer_in_list']) . '（' . count($unit['transfer_in_list']) . '人）';
            }

            $transfer_out_text = '-';
            if (!empty($unit['transfer_out_list'])) {
                $transfer_out_text = '调出名单：' . implode('，', $unit['transfer_out_list']) . '（' . count($unit['transfer_out_list']) . '人）';
            }

            $sorted_data[] = [
                '单位名称' => $unit['unit_name'],
                '上级单位' => $unit['parent_unit_name'] ?: '无',
                '民警' => $unit['police_count'],
                '辅警' => $unit['auxiliary_count'],
                '其他' => $unit['civilian_count'],
                '总计' => $unit['total_count'],
                '调入人员' => $transfer_in_text,
                '调出人员' => $transfer_out_text
            ];
        }
    }

    // 获取方案名称
    $scenarioSql = "SELECT scenario_name FROM sim_scenarios WHERE id = ?";
    $scenarioStmt = $conn->prepare($scenarioSql);
    $scenarioStmt->bind_param('i', $scenario_id);
    $scenarioStmt->execute();
    $scenario_name = $scenarioStmt->get_result()->fetch_assoc()['scenario_name'];

    $filename = $scenario_name . '_统计信息_' . date('Y-m-d') . '.csv';

    // 设置CSV下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');

    $output = fopen('php://output', 'w');

    // 添加BOM以支持中文
    fwrite($output, "\xEF\xBB\xBF");

    // 写入表头和数据
    if (!empty($sorted_data)) {
        $first_row = $sorted_data[0];
        fputcsv($output, array_keys($first_row));

        // 写入所有数据
        foreach ($sorted_data as $row) {
            fputcsv($output, array_values($row));
        }
    }

    fclose($output);
}

/**
 * 导出调动日志
 */
function exportTransferLogs($scenario_id) {
    global $conn;
    
    $sql = "SELECT sp.name as '人员姓名',
                   sp.id_number as '身份证号',
                   stl.from_unit_name as '源单位',
                   stl.to_unit_name as '目标单位',
                   stl.transfer_reason as '调动原因',
                   stl.transfer_type as '调动类型',
                   stl.operator_name as '操作者',
                   stl.transfer_time as '调动时间',
                   stl.remarks as '备注'
            FROM sim_transfer_logs stl
            JOIN sim_personnel sp ON stl.personnel_id = sp.id
            WHERE stl.scenario_id = ?
            ORDER BY stl.transfer_time DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $scenario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // 获取方案名称
    $scenarioSql = "SELECT scenario_name FROM sim_scenarios WHERE id = ?";
    $scenarioStmt = $conn->prepare($scenarioSql);
    $scenarioStmt->bind_param('i', $scenario_id);
    $scenarioStmt->execute();
    $scenario_name = $scenarioStmt->get_result()->fetch_assoc()['scenario_name'];
    
    $filename = $scenario_name . '_调动日志_' . date('Y-m-d') . '.csv';
    
    // 设置CSV下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    $output = fopen('php://output', 'w');
    
    // 添加BOM以支持中文
    fwrite($output, "\xEF\xBB\xBF");
    
    // 写入表头
    if ($result->num_rows > 0) {
        $first_row = $result->fetch_assoc();
        fputcsv($output, array_keys($first_row));
        fputcsv($output, array_values($first_row));
        
        // 写入数据
        while ($row = $result->fetch_assoc()) {
            fputcsv($output, array_values($row));
        }
    }
    
    fclose($output);
}
?>
