# 权限管理代码重构说明

## 概述

本次重构将分散在各个文件中的权限判断代码统一整理到 `permission_manager.php` 文件中，提供了一套完整的权限管理函数库，便于统一管理和调用。

## 重构内容

### 1. 创建的文件

- **`permission_manager.php`** - 权限管理函数库
- **`permission_usage_example.php`** - 使用示例文件
- **`PERMISSION_REFACTOR_README.md`** - 本说明文档

### 2. 修改的文件

- **`api/get_user_app.php`** - 重构了权限判断逻辑，使用新的权限管理函数

## 新的权限管理函数

### 基础权限检查函数

1. **`checkUserLogin()`** - 检查用户是否已登录
2. **`isAdmin()`** - 检查是否为系统管理员
3. **`isAppAdmin($appId)`** - 检查是否为应用管理员
4. **`isUnitAdmin($appId, $unitId)`** - 检查是否为单位管理员

### 综合权限检查函数

1. **`hasAppPermission($appId)`** - 检查用户是否有应用权限
2. **`checkPermissionOrExit($appId, $exitOnFail)`** - 权限检查并可选择性退出
3. **`checkLoginOrExit($exitOnFail)`** - 登录检查并可选择性退出

### 用户信息获取函数

1. **`getCurrentUserInfo()`** - 获取当前用户基本信息
2. **`getUserAccessibleAppIds($userId, $personnelType, $isAdminUser, $conn)`** - 获取用户可访问的应用ID列表

### 辅助函数

1. **`logOperation($conn, $userId, $applicationId, $operation)`** - 记录操作日志

## 使用方法

### 替换原有的 isHasPerm() 函数

**旧代码：**
```php
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
        echo json_encode([
            'status' => 0,
            'message' => '当前用户无权限操作',
            'data' => []
        ]);
        exit;
    }
}
```

**新代码：**
```php
require_once 'permission_manager.php';

// 直接调用，权限不足时自动退出
checkPermissionOrExit($APP_ID);

// 或者不自动退出的方式
if (!checkPermissionOrExit($APP_ID, false)) {
    echo json_encode(['status' => 0, 'message' => '权限不足']);
    return;
}
```

### 获取用户信息

**旧代码：**
```php
$userId = $_SESSION['user_id'];
$personnelType = $_SESSION['personnel_type'] ?? 0;
$isAdminUser = isAdmin();
```

**新代码：**
```php
$userInfo = getCurrentUserInfo();
if (!$userInfo) {
    // 处理未登录情况
    return;
}

$userId = $userInfo['user_id'];
$personnelType = $userInfo['personnel_type'];
$isAdminUser = $userInfo['is_admin'];
```

### API接口中的完整使用

```php
<?php
header('Content-Type: application/json');
require_once '../config.php';
require_once '../permission_manager.php';

try {
    // 权限检查
    checkPermissionOrExit($APP_ID);
    
    // 获取用户信息
    $userInfo = getCurrentUserInfo();
    
    // 执行业务逻辑
    $result = performBusinessLogic();
    
    // 记录操作日志
    logOperation($conn, $userInfo['user_id'], $APP_ID, '执行了某项操作');
    
    // 返回结果
    echo json_encode([
        'status' => 1,
        'message' => '操作成功',
        'data' => $result
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '操作失败: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
```

## 优势

1. **代码复用** - 避免在多个文件中重复编写相同的权限判断逻辑
2. **统一管理** - 所有权限相关的逻辑集中在一个文件中，便于维护
3. **灵活性** - 提供了多种使用方式，可以根据需要选择是否自动退出
4. **可扩展性** - 新增权限类型或修改权限逻辑只需要修改一个文件
5. **一致性** - 确保整个系统的权限判断逻辑保持一致

## 迁移建议

1. 在需要使用权限判断的文件中引入 `permission_manager.php`
2. 将原有的 `isHasPerm()` 函数调用替换为 `checkPermissionOrExit()`
3. 将分散的用户信息获取代码替换为 `getCurrentUserInfo()`
4. 逐步将其他文件中的权限判断逻辑迁移到使用新的函数库

## 注意事项

1. 确保在使用权限管理函数之前已经包含了 `config.php` 和数据库连接
2. 全局变量 `$conn` 和 `$APP_ID` 需要在使用前正确设置
3. 新的函数库与原有的 `conn_waf.php` 中的函数兼容，可以逐步迁移

## 测试建议

1. 测试未登录用户的访问情况
2. 测试不同权限级别用户的访问情况
3. 测试权限不足时的错误处理
4. 验证用户可访问应用列表的正确性

详细的使用示例请参考 `permission_usage_example.php` 文件。
