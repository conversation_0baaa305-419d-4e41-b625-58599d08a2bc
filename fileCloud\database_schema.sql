-- 文件网盘系统数据库设计
-- 创建时间: 2025-06-23

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS filecloud CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE filecloud;

-- 文件信息表
CREATE TABLE IF NOT EXISTS filecloud_info (
    file_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '文件ID',
    user_id INT NOT NULL COMMENT '上传用户ID', 
    original_name VARCHAR(255) NOT NULL COMMENT '文件原始名称',
    stored_name VARCHAR(255) NOT NULL COMMENT '服务器存储文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(100) DEFAULT NULL COMMENT '文件类型',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    share_code VARCHAR(6) UNIQUE NOT NULL COMMENT '分享码（6位）',
    expiration_time DATETIME DEFAULT NULL COMMENT '分享码过期时间',
    is_public TINYINT(1) DEFAULT 1 COMMENT '是否公开分享（1:是 0:否）',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除（1:是 0:否）',
    INDEX idx_user_id (user_id),
    INDEX idx_share_code (share_code),
    INDEX idx_upload_time (upload_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件信息表';

-- 文件分享表（用于指定用户分享）
CREATE TABLE IF NOT EXISTS filecloud_share (
    share_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分享ID',
    file_id INT NOT NULL COMMENT '文件ID',
    from_user_id INT NOT NULL COMMENT '分享者用户ID',
    to_user_id INT NOT NULL COMMENT '被分享者用户ID',
    share_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否已删除（1:是 0:否）',
    FOREIGN KEY (file_id) REFERENCES filecloud_info(file_id) ON DELETE CASCADE,
    INDEX idx_file_id (file_id),
    INDEX idx_from_user (from_user_id),
    INDEX idx_to_user (to_user_id),
    UNIQUE KEY unique_share (file_id, from_user_id, to_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分享表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS filecloud_config (
    config_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_desc VARCHAR(255) COMMENT '配置描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO filecloud_config (config_key, config_value, config_desc) VALUES 
('max_file_size', '52428800', '最大文件上传大小（字节，默认50MB）'),
('allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar', '允许上传的文件类型'),
('upload_path', 'uploads/', '文件上传路径'),
('share_code_expire_days', '30', '分享码默认过期天数（0表示永不过期）'),
('site_title', '文件网盘系统', '网站标题'),
('site_description', '安全、便捷的文件分享平台', '网站描述');

-- 创建上传目录索引表（用于优化文件存储）
CREATE TABLE IF NOT EXISTS filecloud_directories (
    dir_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '目录ID',
    dir_path VARCHAR(500) NOT NULL COMMENT '目录路径',
    created_date DATE NOT NULL COMMENT '创建日期',
    file_count INT DEFAULT 0 COMMENT '文件数量',
    total_size BIGINT DEFAULT 0 COMMENT '总大小',
    INDEX idx_created_date (created_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传目录表';
