<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排班表管理测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .operation-select {
            width: 100%;
            padding: 8px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        button:hover {
            background-color: #45a049;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .data-list {
            margin-top: 15px;
            border-collapse: collapse;
            width: 100%;
            display: none;
        }

        .data-list th, .data-list td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        /* 添加A岗和B岗之间的竖线分隔 */
        .data-list th:nth-child(3), .data-list td:nth-child(3),
        .data-list th:nth-child(5), .data-list td:nth-child(5) {
            border-right: 2px solid #ccc;
        }

        .data-list tr:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>排班表管理测试页面</h2>
        
        <select class="operation-select" id="operationSelect">
            <option value="query">查询</option>
            <option value="add">新增</option>
            <option value="modify">修改</option>
            <option value="del">删除</option>
        </select>
        
        <div id="queryForm" style="display: none;">
            <div class="form-group">
                <label for="queryDate">查询日期:</label>
                <input type="date" id="queryDate" placeholder="请输入查询日期">
            </div>
        </div>

        <div id="formContainer" style="display: none;">
            <!-- 新增和修改表单 -->
            <div id="addModifyForm">
                <div class="form-group">
                    <label for="schedDate">值班时间:</label>
                    <input type="date" id="schedDate" placeholder="请输入日期">
                    <div style="display: flex; gap: 20px; margin: 15px 0;">
                        <div style="flex: 1;">
                            <label for="aDir">A岗局领导:</label>
                            <input type="text" id="aDir" placeholder="请输入A岗局领导">
                            <label for="aCom">A岗指挥长:</label>
                            <input type="text" id="aCom" placeholder="A岗指挥长">
                        </div>
                        <div style="width: 2px; background-color: #ccc;"></div>
                        <div style="flex: 1;">
                            <label for="bDir">B岗局领导:</label>
                            <input type="text" id="bDir" placeholder="请输入B岗局领导">
                            <label for="bCom">B岗指挥长:</label>
                            <input type="text" id="bCom" placeholder="B岗指挥长">
                        </div>
                    </div>
                    <label for="sub">值班长:</label>
                    <input type="text" id="sub" placeholder="值班长">
                    <label for="op">值班人员:</label>
                    <input type="text" id="op" placeholder="值班人员">
                    <label for="tech">技术支持人员:</label>
                    <input type="text" id="tech" placeholder="技术支持人员">
                    <label for="sec">内部安全员:</label>
                    <input type="text" id="sec" placeholder="内部安全员">
                </div>
            </div>

            <!-- 修改表单 -->
            <div id="modifyForm">
                <div class="form-group">
                    <label for="id">ID:</label>
                    <input type="text" id="id" placeholder="请输入ID">
                </div>
                <div class="form-group">
                    <label for="newSchedDate">新值班时间:</label>
                    <input type="date" id="newSchedDate" placeholder="请输入新日期">
                    <label for="newADir">新A岗局领导:</label>
                    <input type="text" id="newADir" placeholder="新A岗局领导">
                    <label for="newBDir">新B岗局领导:</label>
                    <input type="text" id="newBDir" placeholder="新B岗局领导">
                    <label for="newACom">新A岗指挥长:</label>
                    <input type="text" id="newACom" placeholder="新A岗指挥长">
                    <label for="newBCom">新B岗指挥长:</label>
                    <input type="text" id="newBCom" placeholder="新B岗指挥长">
                    <label for="newSub">新值班长:</label>
                    <input type="text" id="newSub" placeholder="新值班长">
                    <label for="newOp">新值班人员:</label>
                    <input type="text" id="newOp" placeholder="新值班人员">
                    <label for="newTech">新技术支持人员:</label>
                    <input type="text" id="newTech" placeholder="新技术支持人员">
                    <label for="newSec">新内部安全员:</label>
                    <input type="text" id="newSec" placeholder="新内部安全员">
                </div>
            </div>

            <!-- 删除表单 -->
            <div id="deleteForm">
                <div class="form-group">
                    <label for="deleteId">ID:</label>
                    <input type="text" id="deleteId" placeholder="请输入ID">
                </div>
            </div>
        </div>

        <button onclick="executeOperation()">执行操作</button>

        <div class="result" id="resultArea">
            <!-- 查询结果将显示在这里 -->
        </div>

        <div id="dataList" class="data-list" style="display: none;">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>值班时间</th>
                    <th>A岗局领导</th>
                    <th>B岗局领导</th>
                    <th>A岗指挥长</th>
                    <th>B岗指挥长</th>
                    <th>值班长</th>
                    <th>值班人员</th>
                    <th>技术支持人员</th>
                    <th>内部安全员</th>
                </tr>
            </thead>
            <tbody id="dataBody"></tbody>
        </div>
    </div>

    <script>
        // 绑定下拉框的change事件，显示相应的表单
        document.getElementById('operationSelect').addEventListener('change', showFormBasedOnOperation);

        // 绑定按钮的click事件，负责执行操作
        const button = document.querySelector('button');
        if (!button.dataset.isBound) {
            button.addEventListener('click', executeOperation);
            button.dataset.isBound = 'true';
        }
        
        // 页面加载时立即显示查询表单
        showFormBasedOnOperation();

        let isRequestInProgress = false;

        function showFormBasedOnOperation() {
            const operation = document.getElementById('operationSelect').value;
            const formContainer = document.getElementById('formContainer');
            const addModifyForm = document.getElementById('addModifyForm');
            const modifyForm = document.getElementById('modifyForm');
            const deleteForm = document.getElementById('deleteForm');

            // 初始化时隐藏所有表单
            formContainer.style.display = 'none';
            addModifyForm.style.display = 'none';
            modifyForm.style.display = 'none';
            deleteForm.style.display = 'none';
            // 默认显示查询表单
            if(document.getElementById('operationSelect').value === 'query') {
                formContainer.style.display = 'block';
                document.getElementById('queryForm').style.display = 'block';
            }

            // 根据操作类型显示相应的表单
            switch(operation) {
                case 'add':
                    formContainer.style.display = 'block';
                    addModifyForm.style.display = 'block';
                    document.getElementById('queryForm').style.display = 'none';
                    break;
                case 'modify':
                    formContainer.style.display = 'block';
                    modifyForm.style.display = 'block';
                    document.getElementById('queryForm').style.display = 'none';
                    break;
                case 'del':
                    formContainer.style.display = 'block';
                    deleteForm.style.display = 'block';
                    document.getElementById('queryForm').style.display = 'none';
                    break;
                case 'query':
                    formContainer.style.display = 'block';
                    document.getElementById('queryForm').style.display = 'block';
                    break;
            }
        }

        function executeOperation() {
            const operationSelect = document.getElementById('operationSelect');
            const controlCode = operationSelect.value;
            //console.log('controlCode:', controlCode);
            if (!controlCode || ![ 'add', 'modify', 'del', 'query' ].includes(controlCode)) {
                alert('请选择有效的操作类型');
                return;
            }
            const formData = {
                controlCode: controlCode
            };

            // 根据操作类型获取参数
            switch(controlCode) {
                case 'add':
                    const schedDate = document.getElementById('schedDate');
                    const aDir = document.getElementById('aDir');
                    const bDir = document.getElementById('bDir');
                    const aCom = document.getElementById('aCom');
                    const bCom = document.getElementById('bCom');
                    const sub = document.getElementById('sub');
                    const op = document.getElementById('op');
                    const tech = document.getElementById('tech');
                    const sec = document.getElementById('sec');
                    if (schedDate && schedDate.value) {
                        formData.sched_date = schedDate.value;
                        formData.a_dir = aDir.value;
                        formData.b_dir = bDir.value;
                        formData.a_com = aCom.value;
                        formData.b_com = bCom.value;
                        formData.sub = sub.value;
                        formData.op = op.value;
                        formData.tech = tech.value;
                        formData.sec = sec.value;
                    }
                    break;
                case 'modify':
                    const id = document.getElementById('id');
                    const newSchedDate = document.getElementById('newSchedDate');
                    const newADir = document.getElementById('newADir');
                    const newBDir = document.getElementById('newBDir');
                    const newACom = document.getElementById('newACom');
                    const newBCom = document.getElementById('newBCom');
                    const newSub = document.getElementById('newSub');
                    const newOp = document.getElementById('newOp');
                    const newTech = document.getElementById('newTech');
                    const newSec = document.getElementById('newSec');
                    if (id && newSchedDate && id.value && newSchedDate.value) {
                        formData.id = id.value;
                        formData.sched_date = newSchedDate.value;
                        formData.a_dir = newADir.value;
                        formData.b_dir = newBDir.value;
                        formData.a_com = newACom.value;
                        formData.b_com = newBCom.value;
                        formData.sub = newSub.value;
                        formData.op = newOp.value;
                        formData.tech = newTech.value;
                        formData.sec = newSec.value;
                    }
                    break;
                case 'del':
                    const deleteId = document.getElementById('deleteId');
                    if (deleteId && deleteId.value) {
                        formData.id = deleteId.value;
                    }
                    break;
                case 'query':
                    const queryDate = document.getElementById('queryDate');
                    if (queryDate && queryDate.value) {
                        formData.sched_date = queryDate.value;
                    }
                    break;
            }

            if (isRequestInProgress) return;
            isRequestInProgress = true;

            // 发送请求
            fetch('api/sched_manage.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => {
                //console.log('Server response:', data);
                const resultArea = document.getElementById('resultArea');
                if (!resultArea) {
                    console.error('找不到resultArea元素');
                    return;
                }
                if (!data || !data.status || !data.message) {
                    console.log('服务器返回数据格式错误:', data);
                    return;
                }
                console.log(data.message);
                console.log(data.status === 1 ? '操作成功' : '操作失败');

                if (data.status === 1) {
                    if (data.data && data.data.length > 0) {
                        // 显示纯文本数据
                        let textOutput = '';
                        data.data.forEach(item => {
                            textOutput += `ID: ${item.id}, 值班时间: ${item.sched_date}, A岗局领导: ${item.a_dir}, B岗局领导: ${item.b_dir}, A岗指挥长: ${item.a_com}, B岗指挥长: ${item.b_com}, 值班长: ${item.sub}, 值班人员: ${item.op}, 技术支持人员: ${item.tech}, 内部安全员: ${item.sec}<br>`;
                        });
                        const resultArea = document.getElementById('resultArea');
                        if (resultArea) {
                            resultArea.innerHTML = textOutput;
                        }
                    } else if (controlCode !== 'query') {
                        // 非查询操作的成功提示
                        resultArea.innerHTML += `<p>${data.message}</p>`;
                    }
                }
            })
            .catch(error => {
                //console.error('Error:', error);
                const resultArea = document.getElementById('resultArea');
                resultArea.innerHTML = '<p style="color: red;">操作失败</p>';
                resultArea.innerHTML += `<p>详细错误信息: ${error.message}</p>`;
            })
            .finally(() => {
                isRequestInProgress = false;
            });
        }
    </script>
</body>
</html>