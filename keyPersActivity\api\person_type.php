<?php
require_once '../../conn_waf.php';

// 设置应用ID
$APP_ID = 8;

// 获取控制参数
$controlCode = isset($_POST['controlCode']) ? $_POST['controlCode'] : '';

// 检查必要参数
if (empty($controlCode)) {
    echo json_encode(['status' => 0, 'message' => '缺少控制参数', 'data' => []]);
    exit;
}

/**
 * 添加人员类型
 */
function addPersonType() {
    global $conn,$APP_ID;
    
    if (empty($_POST['type_name'])) {
        throw new Exception('缺少类型名称');
    }

    // 检查类型名称是否已存在
    $sql = "SELECT COUNT(*) as count FROM 8_person_type WHERE type_name = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $_POST['type_name']);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->fetch_assoc()['count'] > 0) {
        throw new Exception('该类型名称已存在');
    }

    // 执行插入操作
    $sql = "INSERT INTO 8_person_type (type_name) VALUES (?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $_POST['type_name']);
    
    if (!$stmt->execute()) {
        throw new Exception('添加失败：' . $stmt->error);
    }
    $insertId = $stmt->insert_id;
    logOperation($conn, $_SESSION['user_id'], $APP_ID, "新增人员类型：{$_POST['type_name']}，ID：{$insertId}");
    echo json_encode(['status' => 1, 'message' => '添加成功', 'data' => [
        'id' => $insertId,
        'type_name' => $_POST['type_name']
    ]]);
    $stmt->close();
}

/**
 * 删除人员类型
 */
function deletePersonType() {
    global $conn,$APP_ID;
    
    if (empty($_POST['id'])) {
        throw new Exception('缺少ID参数');
    }

    // 处理参数
    $ids = $_POST['id'];
    $idList = is_numeric($ids) ? [intval($ids)] : array_filter(array_map('intval', explode(',', $ids)));
    
    if (empty($idList)) {
        throw new Exception('无效的ID参数');
    }

    // 检查是否有关联的重点人员
    $placeholders = implode(',', array_fill(0, count($idList), '?'));
    $sql = "SELECT COUNT(*) as count FROM 8_key_person WHERE type_id IN ($placeholders)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('i', count($idList)), ...$idList);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->fetch_assoc()['count'] > 0) {
        throw new Exception('存在关联的重点人员，无法删除');
    }

    // 执行删除操作
    $sql = "DELETE FROM 8_person_type WHERE id IN ($placeholders)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('i', count($idList)), ...$idList);
    if (!$stmt->execute()) {
        throw new Exception('删除失败：' . $stmt->error);
    }
    logOperation($conn, $_SESSION['user_id'], $APP_ID, "删除人员类型ID：" . implode(',', $idList));
    echo json_encode(['status' => 1, 'message' => '删除成功', 'data' => [
        'ids' => $idList
    ]]);
    $stmt->execute();
}

/**
 * 修改人员类型
 */
function modifyPersonType() {
    global $conn,$APP_ID;
    
    if (empty($_POST['id']) || empty($_POST['type_name'])) {
        throw new Exception('缺少必要参数');
    }

    // 检查新名称是否与其他类型重复（排除自身）
    $sql = "SELECT COUNT(*) as count FROM 8_person_type WHERE type_name = ? AND id != ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("si", $_POST['type_name'], $_POST['id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->fetch_assoc()['count'] > 0) {
        throw new Exception('该类型名称已存在');
    }

    // 执行更新操作
    $sql = "UPDATE 8_person_type SET type_name = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("si", $_POST['type_name'], $_POST['id']);
    
    if (!$stmt->execute()) {
        throw new Exception('更新失败：' . $stmt->error);
    }

    logOperation($conn, $_SESSION['user_id'], $APP_ID, "修改人员类型ID：{$_POST['id']}，新名称：{$_POST['type_name']}");
    echo json_encode(['status' => 1, 'message' => '更新成功', 'data' => [
        'id' => $_POST['id'],
        'type_name' => $_POST['type_name']
    ]]);
}

/**
 * 查询人员类型
 */
function queryPersonType() {
    global $conn;
    
    // 当page和pagesize为空时显示所有数据
    $showAll = empty($_POST['page']) && empty($_POST['pagesize']);
    $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
    $pagesize = isset($_POST['pagesize']) ? max(1, intval($_POST['pagesize'])) : ($showAll ? PHP_INT_MAX : 10);
    $offset = ($page - 1) * $pagesize;

    // 构建查询条件
    $where = "1=1";
    $params = [];
    $types = "";

    if (!empty($_POST['type_name'])) {
        $where .= " AND type_name LIKE ?";
        $params[] = "%{$_POST['type_name']}%";
        $types .= "s";
    }

    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM 8_person_type WHERE $where";
    $stmt = $conn->prepare($countSql);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $total = $result->fetch_assoc()['total'];

    // 获取分页数据
    $sql = "SELECT t.*, 
                  (SELECT COUNT(*) FROM 8_key_person WHERE type_id = t.id) as person_count 
           FROM 8_person_type t 
           WHERE $where 
            ORDER BY t.id 
            " . ($showAll ? "" : "LIMIT ?, ?");

    $stmt = $conn->prepare($sql);
    if (!$showAll) {
        $types .= "ii";
        $params[] = $offset;
        $params[] = $pagesize;
    }
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'pagesize' => $pagesize,
    ]);
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
try {
    switch ($controlCode) {
        case 'add':
            isHasPerm();
            addPersonType();
            break;

        case 'del':
            isHasPerm();
            deletePersonType();
            break;

        case 'modify':
            isHasPerm();
            modifyPersonType();
            break;

        case 'query':
            queryPersonType();
            break;

        default:
            throw new Exception('无效的控制参数');
    }
} catch (Exception $e) {
    echo json_encode(['status' => 0, 'message' => $e->getMessage(), 'data' => []]);
}