<?php
require_once '../../conn_waf.php';

// 设置应用ID
$APP_ID = 8;


// 获取控制参数
$controlCode = isset($_POST['controlCode']) ? $_POST['controlCode'] : '';

// 检查必要参数
if (empty($controlCode)) {
    echo json_encode(['status' => 0, 'message' => '缺少控制参数', 'data' => []]);
    exit;
}

/**
 * 添加人员动向
 */
function addPersonMovement() {
    global $conn,$APP_ID;
    
    if (empty($_POST['person_id']) || empty($_POST['movement_time']) || empty($_POST['description'])) {
        throw new Exception('缺少必要参数');
    }

    // 检查人员ID是否存在
    $sql = "SELECT COUNT(*) as count FROM 8_key_person WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $_POST['person_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->fetch_assoc()['count'] == 0) {
        throw new Exception('指定的重点人员不存在');
    }

    // 执行插入操作
    $sql = "INSERT INTO 8_person_movement (person_id, movement_time, description) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("iss", 
        $_POST['person_id'],
        $_POST['movement_time'],
        $_POST['description']
    );
    
    if (!$stmt->execute()) {
        throw new Exception('添加失败：' . $stmt->error);
    }
    $insert_id = $stmt->insert_id;
    logOperation($conn, $_SESSION['user_id'], $APP_ID, "新增人员动向，ID：{$insert_id} 人员ID：{$_POST['person_id']} 时间：{$_POST['movement_time']} 描述：{$_POST['description']}");
    echo json_encode(['status' => 1, 'message' => '添加成功', 'data' => [
        'id' => $insert_id
    ]]);
    $stmt->close();
}

/**
 * 删除人员动向
 */
function deletePersonMovement() {
    global $conn, $APP_ID;
    
    if (empty($_POST['id'])) {
        throw new Exception('缺少ID参数');
    }

    // 判断是单个ID还是多个ID
    if (is_numeric($_POST['id'])) {
        // 单个ID删除
        $sql = "DELETE FROM 8_person_movement WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $_POST['id']);
        
        if (!$stmt->execute()) {
            throw new Exception('删除失败：' . $stmt->error);
        }

        logOperation($conn,$_SESSION['user_id'], $APP_ID, "删除人员动向ID：{$_POST['id']}");
        echo json_encode(['status' => 1, 'message' => '删除成功', 'data' => [
            'id' => $_POST['id']
        ]]);
    } else {
        // 多个ID删除
        $ids = explode(',', $_POST['id']);
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $types = str_repeat('i', count($ids));
        
        $sql = "DELETE FROM 8_person_movement WHERE id IN ($placeholders)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param($types, ...$ids);
        
        if (!$stmt->execute()) {
            throw new Exception('批量删除失败：' . $stmt->error);
        }

        logOperation($conn,$_SESSION['user_id'], $APP_ID, "批量删除人员动向ID：{$_POST['id']}");
        echo json_encode(['status' => 1, 'message' => '批量删除成功', 'data' => [
            'ids' => $ids
        ]]);
    }
}

/**
 * 修改人员动向
 */
function modifyPersonMovement() {
    global $conn,$APP_ID;
    
    if (empty($_POST['id'])) {
        throw new Exception('缺少ID参数');
    }

    $updateFields = [];
    $types = "";
    $params = [];

    // 动态构建更新字段
    $allowedFields = [
        'movement_time' => 's',
        'description' => 's'
    ];

    $changedData['id'] = $_POST['id'];
    foreach ($allowedFields as $field => $type) {
        if (isset($_POST[$field]) && $_POST[$field] !== '') {
            $updateFields[] = "`$field` = ?";
            $types .= $type;
            $params[] = $_POST[$field];
            $changedData[$field] = $_POST[$field];
        }
    }

    if (empty($updateFields)) {
        echo json_encode(['status' => 1, 'message' => '没有字段需要更新', 'data' => []]);
        return;
    }

    $types .= "i"; // 为WHERE id = ?添加类型
    $params[] = $_POST['id'];

    $sql = "UPDATE 8_person_movement SET " . implode(", ", $updateFields) . " WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    
    if (!$stmt->execute()) {
        throw new Exception('更新失败：' . $stmt->error);
    }

    // 构建日志消息
    $logParts = [];
    foreach ($changedData as $field => $value) {
        $logParts[] = "$field=$value";
    }
    $logMessage = "修改人员动向ID： ".implode(",", $logParts);
    
    logOperation($conn, $_SESSION['user_id'], $APP_ID, $logMessage);
    echo json_encode(['status' => 1, 'message' => '更新成功', 'data' => $changedData]);
}

/**
 * 查询人员动向
 */
function queryPersonMovement() {
    global $conn;
    $person_id = isset($_POST['person_id']) ? $_POST['person_id'] : null;

    // 当page和pagesize都为空时，设置默认值
    if (empty($_POST['page']) && empty($_POST['pagesize'])) {
        $page = 1;
        $pagesize = 10;
    } else {
        $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
        $pagesize = isset($_POST['pagesize']) ? max(1, intval($_POST['pagesize'])) : 10;
    }
    $offset = ($page - 1) * $pagesize;

    // 构建查询条件
    $where = "1=1";
    $params = [];
    $types = "";

    // 添加person_id筛选条件
    if ($person_id !== null) {
        $person_id = intval($person_id);
        $where .= " AND person_id = ?";
        $types .= "i";
        $params[] = $person_id;
    }
    
    // 新增模糊搜索逻辑
    $personIds = [];
    
    if (!empty($_POST['search_name'])) {
        $sql = "SELECT id FROM 8_key_person WHERE name LIKE ?";
        $stmt = $conn->prepare($sql);
        $searchName = "%" . $_POST['search_name'] . "%";
        $stmt->bind_param("s", $searchName);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $personIds[] = $row['id'];
        }
    }

    if (!empty($_POST['search_idnumber'])) {
        $sql = "SELECT id FROM 8_key_person WHERE id_number LIKE ?";
        $stmt = $conn->prepare($sql);
        $searchIdnumber = "%" . $_POST['search_idnumber'] . "%";
        $stmt->bind_param("s", $searchIdnumber);
        $stmt->execute();
        $result = $stmt->get_result();
        while ($row = $result->fetch_assoc()) {
            $personIds[] = $row['id'];
        }
    }

    // 处理模糊搜索结果
    if (!empty($personIds)) {
        $personIds = array_unique($personIds);
        $placeholders = implode(',', array_fill(0, count($personIds), '?'));
        $where .= " AND m.person_id IN ($placeholders)";
        $types .= str_repeat("i", count($personIds));
        $params = array_merge($params, $personIds);
    }

    // 保留原有时间范围查询
    if (!empty($_POST['start_time']) && !empty($_POST['end_time'])) {
        $where .= " AND m.movement_time BETWEEN ? AND ?";
        $params[] = $_POST['start_time'];
        $params[] = $_POST['end_time'];
        $types .= "ss";
    } else if (!empty($_POST['start_time'])) {
        $where .= " AND m.movement_time >= ?";
        $params[] = $_POST['start_time'];
        $types .= "s";
    } else if (!empty($_POST['end_time'])) {
        $where .= " AND m.movement_time <= ?";
        $params[] = $_POST['end_time'];
        $types .= "s";
    }

    // 保留原有描述查询
    if (!empty($_POST['description'])) {
        $where .= " AND m.description LIKE ?";
        $params[] = "%{$_POST['description']}%";
        $types .= "s";
    }

    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM 8_person_movement m WHERE $where";
    $stmt = $conn->prepare($countSql);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $total = $result->fetch_assoc()['total'];

    // 获取分页数据
    $sql = "SELECT m.*, p.name as person_name, p.id_number, t.type_name 
           FROM 8_person_movement m
           LEFT JOIN 8_key_person p ON m.person_id = p.id
           LEFT JOIN 8_person_type t ON p.type_id = t.id
           WHERE $where
           ORDER BY m.movement_time DESC
           LIMIT ?, ?";

    $stmt = $conn->prepare($sql);
    $types .= "ii";
    $params[] = $offset;
    $params[] = $pagesize;
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
        'page' => $page,
        'pagesize' => $pagesize,
    ]);
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
try {
    switch ($controlCode) {
        case 'add':
            isHasPerm();
            addPersonMovement();
            break;

        case 'del':
            isHasPerm();
            deletePersonMovement();
            break;

        case 'modify':
            isHasPerm();
            modifyPersonMovement();
            break;

        case 'query':
            queryPersonMovement();
            break;

        default:
            throw new Exception('无效的控制参数');
    }
} catch (Exception $e) {
    echo json_encode(['status' => 0, 'message' => $e->getMessage(), 'data' => []]);
}