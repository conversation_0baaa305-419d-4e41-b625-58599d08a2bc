<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>警情质量监督查询系统</title>
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-light: #6366f1;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --border-radius: 16px;
            --box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-gradient: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background: var(--gradient-bg);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            z-index: -1;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
            letter-spacing: 0.5px;
        }

        .search-section {
            margin-bottom: 30px;
        }

        .search-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--box-shadow);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .search-container h3 {
            color: var(--dark-color);
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .search-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .search-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
        }

        .search-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .search-item label {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .search-item input,
        .search-item select {
            padding: 12px 16px;
            border: 2px solid rgba(79, 70, 229, 0.2);
            border-radius: 10px;
            font-size: 0.95rem;
            background: rgba(248, 250, 252, 0.8);
            transition: var(--transition);
        }

        .search-item input:focus,
        .search-item select:focus {
            outline: none;
            border-color: var(--primary-color);
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .search-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .search-btn,
        .reset-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .search-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .reset-btn {
            background: linear-gradient(135deg, var(--secondary-color), #475569);
            color: white;
            box-shadow: 0 4px 15px rgba(100, 116, 139, 0.3);
        }

        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(100, 116, 139, 0.4);
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin-bottom: 40px;
        }

        .alarm-card {
            background: var(--card-gradient);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            transition: var(--transition);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
        }

        .alarm-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
        }

        .alarm-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            padding: 20px 25px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
            gap: 20px;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.6s ease;
        }

        .alarm-card:hover .card-header::before {
            left: 100%;
        }

        .card-header .alarm-number {
            font-size: 1.1rem;
            font-weight: 700;
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .alarm-number-label {
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .alarm-number-value {
            font-size: 1rem;
            font-weight: 600;
            word-break: break-all;
            line-height: 1.3;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-shrink: 0;
        }

        .reoptimize-btn {
            padding: 8px 16px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 3px 12px rgba(255, 107, 107, 0.4);
            position: relative;
            overflow: hidden;
        }

        .reoptimize-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .reoptimize-btn:hover::before {
            left: 100%;
        }

        .reoptimize-btn:hover {
            background: linear-gradient(135deg, #ee5a52, #dc3545);
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(255, 107, 107, 0.6);
        }

        .reoptimize-btn::after {
            content: '⟲';
            font-size: 1rem;
            font-weight: bold;
        }

        .card-body {
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .basic-info-section {
            width: 100%;
            margin-bottom: 10px;
        }

        .content-comparison {
            display: flex;
            gap: 25px;
        }

        .original-content, .optimized-content {
            flex: 1;
            position: relative;
        }

        .content-section h4 {
            color: var(--dark-color);
            margin-bottom: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content-section h4::before {
            content: '';
            width: 4px;
            height: 20px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 2px;
        }

        .content-text {
            background: rgba(248, 250, 252, 0.8);
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 20px;
            min-height: 100px;
            border: 1px solid rgba(226, 232, 240, 0.5);
            font-size: 0.95rem;
            line-height: 1.6;
            position: relative;
            overflow: hidden;
        }

        .content-text::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
        }

        .category-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .category-item {
            display: flex;
            flex-direction: column;
            padding: 12px 16px;
            background: rgba(79, 70, 229, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(79, 70, 229, 0.1);
            transition: var(--transition);
        }

        .category-item:hover {
            background: rgba(79, 70, 229, 0.08);
            border-color: rgba(79, 70, 229, 0.2);
        }

        .category-label {
            font-size: 0.85rem;
            color: var(--secondary-color);
            margin-bottom: 4px;
            font-weight: 500;
        }

        .category-value {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.95rem;
        }

        .basic-info-section .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 15px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            margin-top: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 10px;
            border: 1px solid rgba(226, 232, 240, 0.5);
            transition: var(--transition);
        }

        .info-item:hover {
            background: rgba(241, 245, 249, 0.9);
            border-color: rgba(203, 213, 225, 0.7);
        }

        .info-label {
            font-size: 0.9rem;
            color: var(--secondary-color);
            font-weight: 500;
        }

        .info-value {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.95rem;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: var(--transition);
            white-space: nowrap;
        }

        .status-optimized {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
        }

        .status-pending {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
            color: white;
        }

        .status-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: white;
            font-size: 1.2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            margin: 20px 0;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.2);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1.2s linear infinite;
            margin: 0 auto 25px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-more-data {
            text-align: center;
            padding: 30px;
            color: rgba(255,255,255,0.9);
            font-style: italic;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            margin: 20px 0;
        }

        .divider {
            width: 2px;
            background: linear-gradient(to bottom, transparent, var(--primary-color), transparent);
            margin: 0 10px;
            opacity: 0.3;
        }

        @media (max-width: 1200px) {
            .container {
                max-width: 100%;
                padding: 20px 15px;
            }
        }

        @media (max-width: 768px) {
            .search-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .search-actions {
                flex-direction: column;
                gap: 10px;
            }

            .search-btn,
            .reset-btn {
                width: 100%;
            }

            .cards-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .card-body {
                padding: 20px;
            }

            .content-comparison {
                flex-direction: column;
                gap: 20px;
            }

            .divider {
                display: none;
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .header p {
                font-size: 1rem;
            }

            .category-grid,
            .info-grid,
            .basic-info-section .info-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8rem;
            }

            .card-header {
                padding: 15px 20px;
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .header-actions {
                width: 100%;
                justify-content: space-between;
            }

            .reoptimize-btn {
                font-size: 0.85rem;
                padding: 6px 12px;
            }

            .card-body {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>警情质量监督查询系统</h1>
            <p>实时监控警情处理质量，提升服务效率</p>
        </div>

        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-container">
                <h3>🔍 搜索筛选</h3>
                <div class="search-form">
                    <div class="search-row">
                        <div class="search-item">
                            <label for="searchOrg">辖区派出所</label>
                            <select id="searchOrg">
                                <option value="">全部派出所</option>
                            </select>
                        </div>
                        <div class="search-item">
                            <label for="searchOfficer">接警员</label>
                            <input type="text" id="searchOfficer" placeholder="请输入接警员姓名">
                        </div>
                        <div class="search-item">
                            <label for="searchDateStart">开始时间</label>
                            <input type="datetime-local" id="searchDateStart">
                        </div>
                        <div class="search-item">
                            <label for="searchDateEnd">结束时间</label>
                            <input type="datetime-local" id="searchDateEnd">
                        </div>
                    </div>
                    <div class="search-actions">
                        <button id="searchBtn" class="search-btn">🔍 搜索</button>
                        <button id="resetBtn" class="reset-btn">🔄 重置</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="cardsContainer" class="cards-container">
            <!-- 卡片将通过JavaScript动态加载 -->
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            <div class="loading-spinner"></div>
            <div>正在加载更多数据...</div>
        </div>
        
        <div id="noMoreData" class="no-more-data" style="display: none;">
            已加载全部数据
        </div>
    </div>

    <script>
        let currentPage = 1;
        let isLoading = false;
        let hasMoreData = true;
        const pageSize = 10;
        let searchParams = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAlarmData();
            setupScrollListener();
            setupSearchListeners();
            loadOrganizations();
        });

        // 设置滚动监听
        function setupScrollListener() {
            window.addEventListener('scroll', function() {
                if (isLoading || !hasMoreData) return;
                
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const windowHeight = window.innerHeight;
                const documentHeight = document.documentElement.scrollHeight;
                
                // 当滚动到距离底部200px时加载更多数据
                if (scrollTop + windowHeight >= documentHeight - 200) {
                    loadAlarmData();
                }
            });
        }

        // 设置搜索监听器
        function setupSearchListeners() {
            document.getElementById('searchBtn').addEventListener('click', performSearch);
            document.getElementById('resetBtn').addEventListener('click', resetSearch);

            // 回车键搜索
            document.getElementById('searchOfficer').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }

        // 执行搜索
        function performSearch() {
            const org = document.getElementById('searchOrg').value;
            const officer = document.getElementById('searchOfficer').value.trim();
            const dateStart = document.getElementById('searchDateStart').value;
            const dateEnd = document.getElementById('searchDateEnd').value;

            searchParams = {
                org: org,
                officer: officer,
                dateStart: dateStart,
                dateEnd: dateEnd
            };

            // 重置分页
            currentPage = 1;
            hasMoreData = true;

            // 清空现有卡片
            document.getElementById('cardsContainer').innerHTML = '';

            // 重新加载数据
            loadAlarmData();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchOrg').value = '';
            document.getElementById('searchOfficer').value = '';
            document.getElementById('searchDateStart').value = '';
            document.getElementById('searchDateEnd').value = '';

            searchParams = {};
            currentPage = 1;
            hasMoreData = true;

            // 清空现有卡片
            document.getElementById('cardsContainer').innerHTML = '';

            // 重新加载数据
            loadAlarmData();
        }

        // 加载派出所列表
        async function loadOrganizations() {
            try {
                const response = await fetch('api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=getOrganizations'
                });

                const data = await response.json();

                if (data.status === 1 && data.data) {
                    const select = document.getElementById('searchOrg');
                    data.data.forEach(org => {
                        const option = document.createElement('option');
                        option.value = org;
                        option.textContent = org;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载派出所列表失败:', error);
            }
        }

        // 加载警情数据
        async function loadAlarmData() {
            if (isLoading || !hasMoreData) return;

            isLoading = true;
            showLoading();

            try {
                // 构建请求参数
                let requestBody = `action=getAlarmList&page=${currentPage}&pageSize=${pageSize}`;

                if (searchParams.org) {
                    requestBody += `&org=${encodeURIComponent(searchParams.org)}`;
                }
                if (searchParams.officer) {
                    requestBody += `&officer=${encodeURIComponent(searchParams.officer)}`;
                }
                if (searchParams.dateStart) {
                    requestBody += `&dateStart=${encodeURIComponent(searchParams.dateStart)}`;
                }
                if (searchParams.dateEnd) {
                    requestBody += `&dateEnd=${encodeURIComponent(searchParams.dateEnd)}`;
                }

                const response = await fetch('api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: requestBody
                });

                const data = await response.json();

                if (data.status === 1) {
                    if (data.data && data.data.length > 0) {
                        renderAlarmCards(data.data);
                        currentPage++;

                        if (data.data.length < pageSize) {
                            hasMoreData = false;
                            showNoMoreData();
                        }
                    } else {
                        hasMoreData = false;
                        showNoMoreData();
                    }
                } else {
                    console.error('加载数据失败:', data.message);
                }
            } catch (error) {
                console.error('请求失败:', error);
            } finally {
                isLoading = false;
                hideLoading();
            }
        }

        // 渲染警情卡片
        function renderAlarmCards(alarmList) {
            const container = document.getElementById('cardsContainer');
            
            alarmList.forEach(alarm => {
                const card = createAlarmCard(alarm);
                container.appendChild(card);
            });
        }

        // 创建单个警情卡片
        function createAlarmCard(alarm) {
            const card = document.createElement('div');
            card.className = 'alarm-card';

            const statusText = alarm.status == 1 ? 'AI已优化' : '待优化';
            const statusClass = alarm.status == 1 ? 'status-optimized' : 'status-pending';

            // 计算到达时间差
            const arriveTimeDiff = calculateTimeDifference(alarm.receiveTime, alarm.arriveTime);

            card.innerHTML = `
                <div class="card-header">
                    <div class="alarm-number">
                        <span class="alarm-number-label">警情编号:</span>
                        <span class="alarm-number-value">${alarm.alarmNum}</span>
                    </div>
                    <div class="header-actions">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                        ${alarm.status == 1 ? `<button class="reoptimize-btn" onclick="reoptimizeAlarm('${alarm.alarmNum}')">重新优化</button>` : ''}
                    </div>
                </div>
                <div class="card-body">
                    <!-- 基础信息区域 -->
                    <div class="basic-info-section">
                        <h4>📊 基础信息</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">辖区派出所</span>
                                <span class="info-value">${alarm.governOrgName || '-'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">接警员</span>
                                <span class="info-value">${alarm.receiveUserName || '-'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">警情时间</span>
                                <span class="info-value">${formatDateTime(alarm.alarmTime)}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">到达现场时间</span>
                                <span class="info-value">${arriveTimeDiff}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 内容对比区域 -->
                    <div class="content-comparison">
                        <div class="original-content content-section">
                            <h4>📋 原始警情内容</h4>
                            <div class="content-text">${alarm.alarmContent || '暂无内容'}</div>

                            <h4>🏷️ 原始警情分类</h4>
                            <div class="category-grid">
                                <div class="category-item">
                                    <span class="category-label">一级类别</span>
                                    <span class="category-value">${alarm.alarmCategoryName || '-'}</span>
                                </div>
                                <div class="category-item">
                                    <span class="category-label">二级类别</span>
                                    <span class="category-value">${alarm.alarmTypeName || '-'}</span>
                                </div>
                                <div class="category-item">
                                    <span class="category-label">三级类别</span>
                                    <span class="category-value">${alarm.alarmSubclassName || '-'}</span>
                                </div>
                                <div class="category-item">
                                    <span class="category-label">四级类别</span>
                                    <span class="category-value">${alarm.alarmDetailName || '-'}</span>
                                </div>
                            </div>

                            ${alarm.resultDescribe ? `
                            <h4>📋 处理结果</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">处理结果</span>
                                    <span class="info-value">${alarm.resultDescribe}</span>
                                </div>
                            </div>
                            ` : ''}
                        </div>

                        <div class="divider"></div>

                        <div class="optimized-content content-section">
                            <h4>✨ 优化后警情内容</h4>
                            <div class="content-text">${alarm.alarmAdvice || (alarm.status == 1 ? '暂无优化内容' : '待AI优化处理')}</div>

                            <h4>🎯 优化后警情分类</h4>
                            <div class="category-grid">
                                <div class="category-item">
                                    <span class="category-label">一级类别</span>
                                    <span class="category-value">${alarm.adviceCategory1 || (alarm.status == 1 ? '-' : '待优化')}</span>
                                </div>
                                <div class="category-item">
                                    <span class="category-label">二级类别</span>
                                    <span class="category-value">${alarm.adviceCategory2 || (alarm.status == 1 ? '-' : '待优化')}</span>
                                </div>
                                <div class="category-item">
                                    <span class="category-label">三级类别</span>
                                    <span class="category-value">${alarm.adviceCategory3 || (alarm.status == 1 ? '-' : '待优化')}</span>
                                </div>
                                <div class="category-item">
                                    <span class="category-label">四级类别</span>
                                    <span class="category-value">${alarm.adviceCategory4 || (alarm.status == 1 ? '-' : '待优化')}</span>
                                </div>
                            </div>

                            ${alarm.feedbackAdvice ? `
                            <div class="info-grid" style="margin-top: 20px;">
                                <div class="info-item">
                                    <span class="info-label">反馈优化</span>
                                    <span class="info-value">${alarm.feedbackAdvice}</span>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            return card;
        }

        // 计算时间差
        function calculateTimeDifference(receiveTime, arriveTime) {
            if (!receiveTime || !arriveTime) {
                return '-';
            }
            
            const receive = new Date(receiveTime);
            const arrive = new Date(arriveTime);
            const diffMs = arrive - receive;
            
            if (diffMs < 0) return '-';
            
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffSeconds = Math.floor((diffMs % (1000 * 60)) / 1000);
            
            return `${diffMinutes}分${diffSeconds}秒`;
        }

        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 显示无更多数据提示
        function showNoMoreData() {
            document.getElementById('noMoreData').style.display = 'block';
        }

        // 重新优化警情
        async function reoptimizeAlarm(alarmNum) {
            if (!confirm('确定要重新优化这条警情吗？')) {
                return;
            }

            try {
                const response = await fetch('api.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=reoptimizeAlarm&alarmNum=${encodeURIComponent(alarmNum)}`
                });

                const data = await response.json();

                if (data.status === 1) {
                    alert('重新优化请求已提交，状态已更新为待优化');
                    // 刷新当前页面数据
                    refreshCurrentData();
                } else {
                    alert('操作失败: ' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('重新优化请求失败:', error);
                alert('操作失败，请稍后重试');
            }
        }

        // 刷新当前数据
        function refreshCurrentData() {
            // 重置分页
            currentPage = 1;
            hasMoreData = true;

            // 清空现有卡片
            document.getElementById('cardsContainer').innerHTML = '';

            // 重新加载数据
            loadAlarmData();
        }
    </script>
</body>
</html>
