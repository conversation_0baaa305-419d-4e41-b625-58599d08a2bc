<?php
/**
 * 权限管理函数使用示例
 * 展示如何在其他文件中使用新的权限管理函数
 * 创建时间: 2025-07-09
 */

require_once 'config.php';
require_once 'permission_manager.php';

// 示例1: 简单的登录检查
function example1_simple_login_check() {
    // 旧的方式 (不推荐)
    /*
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['status' => 0, 'message' => '用户未登录']);
        exit;
    }
    */
    
    // 新的方式 (推荐)
    checkLoginOrExit(); // 如果未登录会自动输出JSON并退出
    
    // 或者不自动退出的方式
    if (!checkUserLogin()) {
        echo json_encode(['status' => 0, 'message' => '用户未登录']);
        return;
    }
    
    echo "用户已登录，可以继续处理业务逻辑\n";
}

// 示例2: 权限检查 (替换原有的 isHasPerm 函数)
function example2_permission_check() {
    global $APP_ID;
    
    // 旧的方式 (不推荐)
    /*
    function isHasPerm(){
        if (!isset($_SESSION['user_id'])) {
            echo json_encode([
                'status' => 0,
                'message' => '用户未登录',
                'data' => []
            ]);
            exit;
        }
        global $APP_ID;
        if (!isAdmin() && !isAppAdmin($APP_ID)) {
            echo json_encode([
                'status' => 0,
                'message' => '当前用户无权限操作',
                'data' => []
            ]);
            exit;
        }
    }
    */
    
    // 新的方式 (推荐)
    checkPermissionOrExit($APP_ID); // 如果无权限会自动输出JSON并退出
    
    // 或者不自动退出的方式
    if (!checkPermissionOrExit($APP_ID, false)) {
        echo json_encode(['status' => 0, 'message' => '权限不足']);
        return;
    }
    
    echo "权限检查通过，可以执行操作\n";
}

// 示例3: 获取用户信息
function example3_get_user_info() {
    // 旧的方式 (不推荐)
    /*
    $userId = $_SESSION['user_id'];
    $personnelType = $_SESSION['personnel_type'] ?? 0;
    $isAdminUser = isAdmin();
    */
    
    // 新的方式 (推荐)
    $userInfo = getCurrentUserInfo();
    if (!$userInfo) {
        echo "用户未登录\n";
        return;
    }
    
    $userId = $userInfo['user_id'];
    $personnelType = $userInfo['personnel_type'];
    $isAdminUser = $userInfo['is_admin'];
    
    echo "用户ID: $userId, 人员类型: $personnelType, 是否管理员: " . ($isAdminUser ? '是' : '否') . "\n";
}

// 示例4: 检查特定权限
function example4_specific_permission_check() {
    $appId = 123; // 示例应用ID
    
    // 检查是否为系统管理员
    if (isAdmin()) {
        echo "当前用户是系统管理员\n";
    }
    
    // 检查是否为应用管理员
    if (isAppAdmin($appId)) {
        echo "当前用户是应用 $appId 的管理员\n";
    }
    
    // 检查是否有应用权限
    if (hasAppPermission($appId)) {
        echo "当前用户有应用 $appId 的权限\n";
    }
    
    // 检查单位管理员权限
    $unitId = 456; // 示例单位ID
    if (isUnitAdmin($appId, $unitId)) {
        echo "当前用户是应用 $appId 单位 $unitId 的管理员\n";
    }
}

// 示例5: 在API接口中的完整使用
function example5_api_usage() {
    global $conn, $APP_ID;
    
    // 设置响应头
    header('Content-Type: application/json');
    
    try {
        // 权限检查 (会自动处理未登录和权限不足的情况)
        checkPermissionOrExit($APP_ID);
        
        // 获取用户信息
        $userInfo = getCurrentUserInfo();
        
        // 执行业务逻辑
        $result = performBusinessLogic($userInfo);
        
        // 记录操作日志
        logOperation($conn, $userInfo['user_id'], $APP_ID, '执行了某项操作');
        
        // 返回成功结果
        echo json_encode([
            'status' => 1,
            'message' => '操作成功',
            'data' => $result
        ]);
        
    } catch (Exception $e) {
        // 错误处理
        echo json_encode([
            'status' => 0,
            'message' => '操作失败: ' . $e->getMessage(),
            'data' => []
        ]);
    }
}

// 模拟业务逻辑函数
function performBusinessLogic($userInfo) {
    // 这里是具体的业务逻辑
    return ['user_id' => $userInfo['user_id'], 'result' => 'success'];
}

// 示例6: 获取用户可访问的应用
function example6_get_accessible_apps() {
    global $conn;
    
    $userInfo = getCurrentUserInfo();
    if (!$userInfo) {
        echo "用户未登录\n";
        return;
    }
    
    $accessibleAppIds = getUserAccessibleAppIds(
        $userInfo['user_id'],
        $userInfo['personnel_type'],
        $userInfo['is_admin'],
        $conn
    );
    
    echo "用户可访问的应用ID: " . implode(', ', $accessibleAppIds) . "\n";
}

// 运行示例 (仅在直接访问此文件时运行)
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "权限管理函数使用示例:\n\n";
    
    echo "示例1: 简单登录检查\n";
    example1_simple_login_check();
    echo "\n";
    
    echo "示例2: 权限检查\n";
    example2_permission_check();
    echo "\n";
    
    echo "示例3: 获取用户信息\n";
    example3_get_user_info();
    echo "\n";
    
    echo "示例4: 特定权限检查\n";
    example4_specific_permission_check();
    echo "\n";
    
    echo "示例6: 获取可访问应用\n";
    example6_get_accessible_apps();
}

?>
