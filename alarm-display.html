<!DOCTYPE html>
<html>
<head>
    <title>实时报警监控</title>
    <style>
        .alarm {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px;
        }
        .alarm img {
            max-width: 200px;
        }
    </style>
</head>
<body>
    <h1>实时报警监控</h1>
    <div id="alarms"></div>

    <script>
        const eventSource = new EventSource('kafka.php');
        eventSource.addEventListener('status', function(e) {
            const status = JSON.parse(e.data);
            document.getElementById('status').innerHTML = `
                <p>当前时间: ${status.timestamp}</p>
                <p>报警状态: ${status.hasAlarm ? '有报警' : '无报警'}</p>
                <p>最后报警时间: ${status.lastAlarmTime}</p>
            `;
        });
        eventSource.addEventListener('alarm', function(e) {
            const data = JSON.parse(e.data);
            const alarmDiv = document.createElement('div');
            alarmDiv.className = 'alarm';
            alarmDiv.innerHTML = `
                <h3>报警编号: ${data.alarmNo}</h3>
                <p>时间: ${data.time}</p>
                <p>姓名: ${data.name}</p>
                <p>位置: ${data.location}</p>
                <p>相似度: ${data.score}%</p>
                <p>图库: ${data.album}</p>
                <img src="${data.image}" alt="抓拍图片">
            `;
            document.getElementById('alarms').prepend(alarmDiv);
        });
        
        eventSource.onerror = function() {
            console.error('SSE连接错误');
        };
    </script>
</body>
</html>