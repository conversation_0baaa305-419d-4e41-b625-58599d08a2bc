/* 人员调动模拟系统样式表 */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #7f8c8d;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #1abc9c;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --border-color: #ddd;
    --text-color: #333;
    --text-light: #666;
    --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f5f5f5;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: white;
    box-shadow: var(--shadow);
    z-index: 10;
}

.toolbar-left h1 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin: 0;
}

.toolbar-center {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toolbar-right {
    display: flex;
    gap: 10px;
}

.toolbar-user {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.user-avatar {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.user-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.login-prompt {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background-color: #fff3cd;
    border-radius: 6px;
    border: 1px solid #ffeaa7;
}

.login-text {
    font-size: 0.9rem;
    color: #856404;
    font-weight: 500;
}

.scenario-select {
    min-width: 250px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

/* 主内容区样式 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 左侧面板 */
.left-panel {
    width: 300px;
    background-color: white;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.unit-stats {
    font-size: 0.8rem;
    color: var(--text-light);
    display: flex;
    gap: 10px;
}

.unit-tree {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

/* 右侧面板 */
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: white;
}

.personnel-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-input {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 200px;
}

.personnel-filters {
    padding: 12px 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.filter-label {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
    white-space: nowrap;
}

.filter-buttons {
    display: flex;
    gap: 4px;
    background-color: white;
    padding: 3px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.filter-btn {
    padding: 6px 12px;
    border: none;
    background-color: transparent;
    color: var(--text-light);
    font-size: 0.85rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    min-width: 50px;
}

.filter-btn:hover {
    background-color: #f0f9f4;
    color: var(--success-color);
}

.filter-btn.active {
    background-color: var(--success-color);
    color: white;
    box-shadow: 0 1px 3px rgba(46, 204, 113, 0.3);
}

.clear-filter-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(238, 90, 82, 0.2);
}

.clear-filter-btn:hover {
    background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(238, 90, 82, 0.3);
}

.clear-filter-btn:active {
    transform: translateY(0);
}

.clear-icon {
    font-size: 0.9rem;
    font-weight: bold;
}

.personnel-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.personnel-header {
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selected-count {
    font-size: 0.9rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 10px;
}

.personnel-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.pagination {
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    border-top: 1px solid var(--border-color);
}

#pageInfo {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* 单位树样式 */
.unit-item {
    margin: 5px 0;
    cursor: pointer;
}

.unit-content {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    border-radius: 4px;
    transition: var(--transition);
}

.unit-content:hover {
    background-color: #f0f7ff;
}

.unit-content.active {
    background-color: #e3f2fd;
    color: var(--primary-color);
    font-weight: bold;
}

.unit-toggle {
    margin-right: 5px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.unit-name {
    flex: 1;
}

.unit-count {
    font-size: 0.8rem;
    color: var(--text-light);
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
}

.unit-children {
    margin-left: 20px;
    display: none;
}

.unit-children.expanded {
    display: block;
}

/* 人员卡片样式 */
.personnel-card {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    cursor: move;
    user-select: none;
}

.personnel-card:hover {
    background-color: #f9f9f9;
}

.personnel-card.selected {
    background-color: #e3f2fd;
}

.personnel-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.unit-content.drag-over {
    background-color: #e8f5e8;
    border: 2px dashed var(--success-color);
}

.drag-ghost {
    position: fixed;
    pointer-events: none;
    z-index: 1000;
    background-color: white;
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    padding: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    font-size: 0.9rem;
}

.personnel-checkbox {
    margin-right: 15px;
}

.personnel-info {
    flex: 1;
}

.personnel-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.personnel-details {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 0.85rem;
    color: var(--text-light);
}

.personnel-detail {
    display: flex;
    align-items: center;
}

.personnel-detail-label {
    margin-right: 5px;
    color: var(--text-light);
}

.personnel-actions {
    display: flex;
    gap: 5px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #6c7a7d;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #27ae60;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: #16a085;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.text-warning {
    color: var(--warning-color);
    font-size: 0.9rem;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100;
    overflow: auto;
}

.modal-content {
    background-color: white;
    margin: 50px auto;
    width: 500px;
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s;
}

.modal-large {
    width: 800px;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
}

.close {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-light);
    cursor: pointer;
}

.close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input[type="text"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 复选框和单选框样式 */
.checkbox-container, .radio-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 30px;
    margin-bottom: 10px;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input, .radio-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark, .radio-mark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #eee;
    border-radius: 4px;
}

.radio-mark {
    border-radius: 50%;
}

.checkbox-container:hover input ~ .checkmark,
.radio-container:hover input ~ .radio-mark {
    background-color: #ccc;
}

.checkbox-container input:checked ~ .checkmark,
.radio-container input:checked ~ .radio-mark {
    background-color: var(--primary-color);
}

.checkmark:after, .radio-mark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after,
.radio-container input:checked ~ .radio-mark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.radio-container .radio-mark:after {
    top: 6px;
    left: 6px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: var(--text-color);
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.message {
    padding: 12px 20px;
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: messageFadeIn 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

@keyframes messageFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.message-error {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.message-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.message-close {
    margin-left: 10px;
    cursor: pointer;
    font-weight: bold;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--text-light);
    text-align: center;
}

/* 统计报表样式 */
.statistics-container {
    width: 100%;
}

.statistics-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    table-layout: fixed;
}

/* 统计表格列宽设置 - 加宽调入调出列 */
.statistics-table th:nth-child(1), .statistics-table td:nth-child(1) { width: 12%; } /* 单位名称 */
.statistics-table th:nth-child(2), .statistics-table td:nth-child(2) { width: 12%; } /* 上级单位 */
.statistics-table th:nth-child(3), .statistics-table td:nth-child(3) { width: 8%; }  /* 民警 */
.statistics-table th:nth-child(4), .statistics-table td:nth-child(4) { width: 8%; }  /* 辅警 */
.statistics-table th:nth-child(5), .statistics-table td:nth-child(5) { width: 8%; }  /* 其他 */
.statistics-table th:nth-child(6), .statistics-table td:nth-child(6) { width: 8%; }  /* 总计 */
.statistics-table th:nth-child(7), .statistics-table td:nth-child(7) { width: 22%; } /* 调入人员 */
.statistics-table th:nth-child(8), .statistics-table td:nth-child(8) { width: 22%; } /* 调出人员 */

.statistics-table th,
.statistics-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    word-wrap: break-word;
    vertical-align: top;
}

/* 调入调出列特殊样式 */
.statistics-table td:nth-child(7),
.statistics-table td:nth-child(8) {
    line-height: 1.4;
    max-height: none;
    white-space: normal;
}

.statistics-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.statistics-table tr:hover {
    background-color: #f9f9f9;
}

/* 选中人员列表 */
.selected-personnel-list {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
}

.selected-personnel-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

.selected-personnel-item:last-child {
    border-bottom: none;
}

/* 日志查看样式 */
.logs-filters {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.filter-row {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 60px;
}

.filter-item label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
}

.filter-select, .filter-input {
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    min-width: 150px;
    width: 200px;
    flex-shrink: 0;
}

/* 特别针对日志过滤器的样式 */
#logScenarioFilter, #logDateFilter {
    min-width: 180px !important;
    width: 200px !important;
    flex-shrink: 0 !important;
}

.logs-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
}

.logs-table th,
.logs-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.logs-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1;
}

.logs-table tr:hover {
    background-color: #f9f9f9;
}

.logs-pagination {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

/* 单位管理样式 */
.unit-manage-toolbar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.unit-manage-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.unit-manage-table {
    width: 100%;
    border-collapse: collapse;
}

.unit-manage-table th,
.unit-manage-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.unit-manage-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1;
}

.unit-manage-table tr:hover {
    background-color: #f9f9f9;
}

.unit-manage-table tr.selected {
    background-color: #e3f2fd;
}

.unit-level-0 { padding-left: 10px; }
.unit-level-1 { padding-left: 30px; }
.unit-level-2 { padding-left: 50px; }
.unit-level-3 { padding-left: 70px; }

/* 调入调出详细名单样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.transfer-detail-modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.transfer-detail-modal .modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
}

.transfer-detail-modal .modal-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.2rem;
}

.transfer-detail-modal .modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.transfer-detail-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.transfer-detail-table th,
.transfer-detail-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.transfer-detail-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--text-color);
}

.transfer-detail-table tr:hover {
    background-color: #f9f9f9;
}

.transfer-detail-modal .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background-color: #f8f9fa;
    text-align: right;
}

/* 统计表格中的链接样式 */
.statistics-table .text-success a {
    color: var(--success-color);
    text-decoration: none;
    font-weight: 600;
}

.statistics-table .text-success a:hover {
    color: #1e7e34;
    text-decoration: underline;
}

.statistics-table .text-warning a {
    color: var(--warning-color);
    text-decoration: none;
    font-weight: 600;
}

.statistics-table .text-warning a:hover {
    color: #d39e00;
    text-decoration: underline;
}

/* 登录要求提示样式 */
.login-required-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-color: #f8f9fa;
}

.message-content {
    text-align: center;
    padding: 40px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 400px;
}

.message-content h2 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.message-content p {
    color: var(--text-light);
    margin-bottom: 25px;
    font-size: 1rem;
    line-height: 1.5;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .toolbar {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
    
    .toolbar-center, .toolbar-right {
        width: 100%;
        justify-content: center;
    }
    
    .left-panel {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-panel {
        width: 100%;
        height: 300px;
    }
    
    .modal-content {
        width: 90%;
    }
    
    .modal-large {
        width: 95%;
    }
}
