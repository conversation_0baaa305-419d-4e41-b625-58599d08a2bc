<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能测试</title>
    <style>
        .drag-item {
            padding: 10px;
            margin: 5px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            cursor: move;
            user-select: none;
        }
        .drag-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        .drop-zone {
            padding: 20px;
            margin: 10px;
            background-color: #e0e0e0;
            border: 2px dashed #999;
            min-height: 100px;
        }
        .drop-zone.drag-over {
            background-color: #e8f5e8;
            border-color: #4CAF50;
        }
    </style>
</head>
<body>
    <h1>拖拽功能测试</h1>
    
    <div>
        <h3>拖拽源</h3>
        <div class="drag-item" draggable="true" data-id="1">人员1</div>
        <div class="drag-item" draggable="true" data-id="2">人员2</div>
        <div class="drag-item" draggable="true" data-id="3">人员3</div>
    </div>
    
    <div>
        <h3>拖拽目标</h3>
        <div class="drop-zone" data-unit="A">单位A</div>
        <div class="drop-zone" data-unit="B">单位B</div>
    </div>
    
    <div id="log"></div>
    
    <script>
        function log(message) {
            document.getElementById('log').innerHTML += '<p>' + message + '</p>';
        }
        
        // 拖拽源事件
        document.querySelectorAll('.drag-item').forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.target.classList.add('dragging');
                e.dataTransfer.setData('text/plain', e.target.dataset.id);
                log('开始拖拽: ' + e.target.textContent);
            });
            
            item.addEventListener('dragend', (e) => {
                e.target.classList.remove('dragging');
                log('结束拖拽: ' + e.target.textContent);
            });
        });
        
        // 拖拽目标事件
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.classList.add('drag-over');
            });
            
            zone.addEventListener('dragleave', (e) => {
                zone.classList.remove('drag-over');
            });
            
            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.classList.remove('drag-over');
                const id = e.dataTransfer.getData('text/plain');
                log('放置到 ' + zone.dataset.unit + ': 人员' + id);
            });
        });
    </script>
</body>
</html>
