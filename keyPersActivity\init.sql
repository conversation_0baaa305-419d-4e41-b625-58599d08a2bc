-- 创建重点人员基本信息表
CREATE TABLE IF NOT EXISTS `8_key_person` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `id_number` varchar(18) NOT NULL COMMENT '身份证号码',
  `gender` varchar(10) NOT NULL COMMENT '性别',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `type_id` int(11) NOT NULL COMMENT '人员类型ID',
  `status` varchar(20) NOT NULL COMMENT '人员状态',
  `unit_id` int(11) NOT NULL COMMENT '所属辖区',
  `is_display` tinyint(1) DEFAULT 0 COMMENT '是否展示大屏',
  `is_top` tinyint(1) DEFAULT 0 COMMENT '是否置顶',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_id_number` (`id_number`),
  KEY `fk_type_id` (`type_id`),
  KEY `fk_unit_id` (`unit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='重点人员基本信息表';

-- 创建人员类型表
CREATE TABLE IF NOT EXISTS `8_person_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type_name` varchar(50) NOT NULL COMMENT '类型名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员类型表';

-- 创建人员动向表
CREATE TABLE IF NOT EXISTS `8_person_movement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `person_id` int(11) NOT NULL COMMENT '重点人员ID',
  `movement_time` datetime NOT NULL COMMENT '时间',
  `description` text NOT NULL COMMENT '动向描述',
  PRIMARY KEY (`id`),
  KEY `fk_person_id` (`person_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员动向表';

-- 添加外键约束
ALTER TABLE `8_key_person`
  ADD CONSTRAINT `fk_key_person_type` FOREIGN KEY (`type_id`) REFERENCES `8_person_type` (`id`),
  ADD CONSTRAINT `fk_key_person_unit` FOREIGN KEY (`unit_id`) REFERENCES `2_unit` (`id`);

ALTER TABLE `8_person_movement`
  ADD CONSTRAINT `fk_movement_person` FOREIGN KEY (`person_id`) REFERENCES `8_key_person` (`id`);

-- 插入默认人员类型数据
INSERT INTO `8_person_type` (`type_name`) VALUES 
('上访人员'),
('重点关注人员'),
('涉稳人员'),
('刑满释放人员'),
('社区矫正人员'),
('精神障碍患者'),
('吸毒人员'),
('其他');