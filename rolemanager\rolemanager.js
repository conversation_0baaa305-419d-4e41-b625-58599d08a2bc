// 全局变量
let currentPage = 1;
const pageSize = 10;
let totalUsers = 0;
let selectedUserId = null;
let units = [];
let apps = [];
let roles = [];

// DOM元素
const searchBtn = document.getElementById('searchBtn');
const searchKeyword = document.getElementById('searchKeyword');
const organizationUnit = document.getElementById('organizationUnit');
const userTable = document.getElementById('userTable').getElementsByTagName('tbody')[0];
const authTable = document.getElementById('authTable').getElementsByTagName('tbody')[0];
const prevPageBtn = document.getElementById('prevPage');
const nextPageBtn = document.getElementById('nextPage');
const pageInfo = document.getElementById('pageInfo');
const authDialog = document.getElementById('authDialog');
const authForm = document.getElementById('authForm');
const authId = document.getElementById('authId');
const userId = document.getElementById('userId');
const userName = document.getElementById('userName');
const appId = document.getElementById('appId');
const unitId = document.getElementById('unitId');
const roleId = document.getElementById('roleId');
const operationType = document.getElementById('operationType');
const cancelBtn = document.getElementById('cancelBtn');
const confirmBtn = document.getElementById('confirmBtn');
const confirmDialog = document.getElementById('confirmDialog');
const deleteId = document.getElementById('deleteId');
const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
const appSelect = document.getElementById('app_select');

function urlencode(obj) {
    return Object.keys(obj)
        .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]))
        .join('&');
}

function findUnitById(units, id) {
    for (const unit of units) {
        if (unit.id == id) return unit;
        if (unit.children) {
            const found = findUnitById(unit.children, id);
            if (found) return found;
        }
    }
    return null;
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 加载单位数据
    fetchUnits();
    
    // 加载应用数据
    fetchApps();
    
    // 加载用户数据
    fetchUsers();
    
    // 绑定事件
    searchBtn.addEventListener('click', function() {
        currentPage = 1;
        fetchUsers();
    });

    // 新增：应用授权查询按钮事件
    document.getElementById('searchAuthBtn').addEventListener('click', function() {
        currentPage = 1;
        fetchUsersByApp();
    });
    
    // 新增：单位选择变化事件
    organizationUnit.addEventListener('change', function() {
        currentPage = 1;
        fetchUsers();
    });
    
    prevPageBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            fetchUsers();
        }
    });
    
    nextPageBtn.addEventListener('click', function() {
        if (currentPage * pageSize < totalUsers) {
            currentPage++;
            fetchUsers();
        }
    });
    
    cancelBtn.addEventListener('click', function() {
        authDialog.style.display = 'none';
    });
    
    confirmBtn.addEventListener('click', function(e) {
        e.preventDefault();
        if (authForm.checkValidity()) {
            saveAuth();
        } else {
            authForm.reportValidity();
        }
    });
    
    cancelDeleteBtn.addEventListener('click', function() {
        confirmDialog.style.display = 'none';
    });
    
    confirmDeleteBtn.addEventListener('click', function() {
        deleteAuth();
    });
});

// 获取单位数据
function fetchUnits() {
    fetch('/api/get_unit_info.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 1) {
            units = data.data;
            populateUnitDropdowns();
        } else {
            alert('获取单位数据失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('获取单位数据时出错');
    });
}

// 填充单位下拉框
// 填充单位下拉框
function populateUnitDropdowns() {
    // 清空现有选项
    organizationUnit.innerHTML = '<option value="">所有单位</option>';
    unitId.innerHTML = '<option value="">请选择单位</option>';

    // 递归函数添加带缩进的单位选项
    function addUnitsWithIndent(units, level = 0) {
        units.forEach(unit => {
            // 创建带缩进的单位名称
            const indent = '&nbsp;'.repeat(level * 4);
            const displayName = `${indent}${unit.unit_name}`;
            
            // 添加到搜索下拉框
            const option1 = document.createElement('option');
            option1.value = unit.id;
            option1.innerHTML = displayName;
            organizationUnit.appendChild(option1);
            
            // 添加到授权表单下拉框
            const option2 = document.createElement('option');
            option2.value = unit.id;
            option2.innerHTML = displayName;
            unitId.appendChild(option2);
            
            // 递归处理子单位
            if (unit.children && unit.children.length > 0) {
                addUnitsWithIndent(unit.children, level + 1);
            }
        });
    }

    // 从顶级单位开始添加
    addUnitsWithIndent([units],0);
}





// 获取应用数据
function fetchApps() {
// 将对象转换为 URL 编码的字符串


    // 发起 fetch 请求，使用表单格式
    fetch('/api/application_manage.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'  // 关键修改：不再是 JSON
        },
        body:urlencode({ controlCode: 'query' }) // 直接发送 URL 编码后的字符串
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 1) {
            apps = data.data.application;
            roles = data.data.rolelist;
            populateAppDropdown();
        } else {
            alert('获取应用数据失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('获取应用数据时出错');
    });
}

// 填充应用下拉框
function populateAppDropdown() {
    // 填充授权对话框的应用下拉框
    appId.innerHTML = '<option value="">请选择应用</option>';
    // 填充查询授权人员的应用下拉框
    const appSelect = document.getElementById('app_select');
    
    apps.forEach(app => {
        // 填充授权对话框的应用下拉框
        const option1 = document.createElement('option');
        option1.value = app.id;
        option1.textContent = app.application_name;
        appId.appendChild(option1);
        
        // 填充查询授权人员的应用下拉框
        const option2 = document.createElement('option');
        option2.value = app.id;
        option2.textContent = app.application_name;
        appSelect.appendChild(option2);
    });
}

// 获取用户数据
function fetchUsers() {
    const params = {
        controlCode: 'query',
        page: currentPage,
        pagesize: pageSize,
    };
    
    if (searchKeyword.value) {
        params.search_keyword = searchKeyword.value;
    }
    
    if (organizationUnit.value) {
        params.organization_unit = organizationUnit.value;
    }

    fetch('/api/user_manage.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: urlencode(params)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 1) {
            totalUsers = data.total;
            renderUserTable(data.data);
            updatePagination();
        } else {
            alert('获取用户数据失败: ' + data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('获取用户数据时出错');
    });
}

// 渲染用户表格
function renderUserTable(users) {
    userTable.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        
        // 查找单位名称 - 修改后的查找逻辑

        
        const unit = findUnitById([units], user.organization_unit);
        const unitName = unit ? unit.unit_name : '未知单位';
        row.innerHTML = `
            <td>${unitName}</td>
            <td>${user.name}</td>
            <td>${user.police_number || '-'}</td>
            <td>${user.id_number}</td>
            <td>${user.position || '-'}</td>
            <td><button class="add-auth-btn" data-userid="${user.id}" data-username="${user.name}" data-unitid="${user.organization_unit}">添加权限</button><button class="user-auth-btn" data-userid="${user.id}" data-username="${user.name}" data-unitid="${user.organization_unit}">查看权限</button></td>
        `;
        
        userTable.appendChild(row);
    });
    
    // 绑定添加权限按钮事件
    document.querySelectorAll('.add-auth-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-userid');
            const userName = this.getAttribute('data-username');
            const unitId = this.getAttribute('data-unitid');
            
            showAuthDialog('add', {
                userId: userId,
                userName: userName,
                unitId: unitId
            });
        });
    });
    // 绑定查看权限按钮事件
    document.querySelectorAll('.user-auth-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const userId = this.getAttribute('data-userid');
            
            // 查询并显示用户授权信息
            fetchUserAuths(userId);
            
            // 高亮显示当前用户行
            document.querySelectorAll('#userTable tr').forEach(row => {
                row.style.backgroundColor = '';
            });
            this.closest('tr').style.backgroundColor = '#f0f8ff';
        });
    });
}

// 更新分页信息
function updatePagination() {
    const totalPages = Math.ceil(totalUsers / pageSize);
    pageInfo.textContent = `第${currentPage}页/共${totalPages}页`;
    
    prevPageBtn.disabled = currentPage === 1;
    nextPageBtn.disabled = currentPage * pageSize >= totalUsers;
}

// 获取用户授权数据
function fetchUserAuths(userId) {
    fetch('/api/user_Role_manage.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: urlencode({
            controlCode: 'query',
            userId: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 1) {
            renderAuthTable(data.data);
        } else {
            alert('获取授权数据失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('获取授权数据时出错');
    });
}

// 渲染授权表格
function renderAuthTable(auths) {
    authTable.innerHTML = '';
    
    if (auths.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="4" style="text-align: center;">暂无授权记录</td>';
        authTable.appendChild(row);
        return;
    }
    
    auths.forEach(auth => {
        const row = document.createElement('tr');
        
        // 查找应用名称
        const app = apps.find(a => a.id == auth.appId);
        const appName = app ? app.application_name : '未知应用';
        
        // 查找单位名称
        const unit = findUnitById([units], auth.unitId);
        const unitName = unit ? unit.unit_name : '未知单位';
        
        // 查找角色名称
        const role = roles.find(r => r.id == auth.roleId);
        const roleName = role ? role.roleName : '未知角色';
        
        row.innerHTML = `
            <td>${appName}</td>
            <td>${unitName}</td>
            <td>${roleName}</td>
            <td>
                <button class="edit-auth-btn" data-id="${auth.id}" data-userid="${auth.userId}" data-roleid="${auth.roleId}" data-unitid="${auth.unitId}" data-appid="${auth.appId}">修改</button>
                <button class="delete-auth-btn" data-id="${auth.id}">删除</button>
            </td>
        `;
        
        authTable.appendChild(row);
    });
    
    // 绑定修改按钮事件
    document.querySelectorAll('.edit-auth-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const authData = {
                id: this.getAttribute('data-id'),
                userId: this.getAttribute('data-userid'),
                roleId: this.getAttribute('data-roleid'),
                unitId: this.getAttribute('data-unitid'),
                appId: this.getAttribute('data-appid')
            };
            
            // 查找用户名
            const userRow = document.querySelector(`.add-auth-btn[data-userid="${authData.userId}"]`);
            if (userRow) {
                authData.userName = userRow.getAttribute('data-username');
            }
            
            showAuthDialog('modify', authData);
            
            // 自动填充并选中当前角色
            const selectedAppId = authData.appId;
            if (selectedAppId) {
                fetch('/api/application_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: urlencode({
                        controlCode: 'query',
                        id: selectedAppId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 1) {
                        roleId.innerHTML = '<option value="">请选择角色</option>';
                        data.data.rolelist.forEach(role => {
                            const option = document.createElement('option');
                            option.value = role.id;
                            option.textContent = role.roleName;
                            if (role.id == authData.roleId) {
                                option.selected = true;
                            }
                            roleId.appendChild(option);
                        });
                    }
                });
            }
        });
    });
    
    // 绑定删除按钮事件
    document.querySelectorAll('.delete-auth-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            deleteId.value = this.getAttribute('data-id');
            confirmDialog.style.display = 'flex';
        });
    });
}

// 显示授权对话框
function showAuthDialog(type, data) {
    dialogTitle.textContent = type === 'add' ? '添加授权' : '修改授权';
    operationType.value = type;
    
    if (type === 'add') {
        authId.value = '';
        userId.value = data.userId;
        userName.value = data.userName;
        unitId.value = data.unitId;
        appId.value = '';
        roleId.innerHTML = '<option value="">请选择角色</option>';
    } else {
        authId.value = data.id;
        userId.value = data.userId;
        userName.value = data.userName;
        unitId.value = data.unitId;
        appId.value = data.appId;
        
        // 填充角色下拉框
        roleId.innerHTML = '<option value="">请选择角色</option>';
        const selectedApp = apps.find(a => a.id == data.appId);
        if (selectedApp) {
            const appRoles = roles.filter(r => r.applicationId == selectedApp.id);
            appRoles.forEach(role => {
                const option = document.createElement('option');
                option.value = role.id;
                option.textContent = role.roleName;
                if (role.id == data.roleId) {
                    option.selected = true;
                }
                roleId.appendChild(option);
            });
        }
    }
    
    authDialog.style.display = 'flex';
}

// 保存授权
function saveAuth() {
    const params = {
        controlCode: operationType.value,
        userId: userId.value,
        appId: appId.value,
        unitId: unitId.value,
        roleId: roleId.value
    };
    
    if (operationType.value === 'modify') {
        params.id = authId.value;
    }
    
    fetch('/api/user_Role_manage.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: urlencode(params)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 1) {
            alert('授权保存成功');
            authDialog.style.display = 'none';
            fetchUserAuths(userId.value);
        } else {
            alert('授权保存失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('授权保存时出错');
    });
}

// 删除授权
function deleteAuth() {
    fetch('/api/user_Role_manage.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: urlencode({
            controlCode: 'del',
            id: deleteId.value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 1) {
            alert('授权删除成功');
            confirmDialog.style.display = 'none';
            fetchUserAuths(userId.value);
        } else {
            alert('授权删除失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('授权删除时出错');
    });
}

// 应用选择变化时更新角色列表
appId.addEventListener('change', function() {
    const selectedAppId = this.value;
    roleId.innerHTML = '<option value="">请选择角色</option>';
    
    if (selectedAppId) {
        fetch('/api/application_manage.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: urlencode({
                controlCode: 'query',
                id: selectedAppId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 1) {
                data.data.rolelist.forEach(role => {
                    const option = document.createElement('option');
                    option.value = role.id;
                    option.textContent = role.roleName;
                    roleId.appendChild(option);
                });
            } else {
                alert('获取角色列表失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('获取角色列表时出错');
        });
    }
});

// 新增：按应用查询用户授权
function fetchUsersByApp() {
    const appId = document.getElementById('app_select').value;
    if (!appId) {
        alert('请先选择应用');
        return;
    }

    fetch('/api/user_Role_manage.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: urlencode({
            controlCode: 'query',
            appId: appId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 1) {
            // 获取所有授权用户的ID
            const userIds = [...new Set(data.data.map(auth => auth.userId))];
            
            if (userIds.length > 0) {
                // 获取这些用户的详细信息
                fetch('/api/user_manage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: urlencode({
                        controlCode: 'query',
                        userIds: userIds.join(',')
                    })
                })
                .then(response => response.json())
                .then(userData => {
                    if (userData.status === 1) {
                        totalUsers = userData.data.length;
                        renderUserTable(userData.data);
                        updatePagination();
                    } else {
                        alert('获取用户数据失败: ' + userData.msg);
                    }
                });
            } else {
                // 清空用户列表
                totalUsers = 0;
                userTable.innerHTML = '';
                updatePagination();
            }
        } else {
            alert('获取授权数据失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('获取授权数据时出错');
    });
}