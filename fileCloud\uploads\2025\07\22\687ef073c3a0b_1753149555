{
  "mcpServers": {
    "mcp-feedback-collector": {
      "command": "uvx",
      "args": ["mcp-feedback-collector"],
      "env": {
        "PYTHONIOENCODING": "utf-8",
        "MCP_DIALOG_TIMEOUT": "600"
      }
    }
  }
}


Whenever you want to ask a question, always call the MCP .

Whenever you’re about to complete a user request, call the MCP instead of simply ending the process. Keep calling MCP until the user’s feedback is empty, then end the request. mcp-feedback-collector.collect_feedback