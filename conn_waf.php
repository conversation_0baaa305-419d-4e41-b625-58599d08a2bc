<?php
header('Access-Control-Allow-Origin: http://**************:5173'); 
// 允许携带凭证
header('Access-Control-Allow-Credentials: true'); 
// 允许的请求方法
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS'); 
// 允许的请求头
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: text/html; charset=utf-8');
$servername = "localhost";
$username = "root";
$password = "YC@yc110";
$dbname = "application";

// 使用mysqli扩展连接MySQL
$conn = mysqli_connect($servername, $username, $password, $dbname);
// 检查连接
// 在数据库连接后添加
if ($conn->connect_error) {
    die("数据库连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset("utf8mb4");



function waf_check_all_inputs() {
    $request_method = $_SERVER['REQUEST_METHOD'];
    
    // 根据请求方法获取输入数据
    $inputs = [];
    if ($request_method === 'GET') {
        $inputs = $_GET;
    } elseif ($request_method === 'POST') {
        $inputs = $_POST;
    }
    
    // 检查所有输入参数
    foreach ($inputs as $key => $value) {
        if (!waf_check($value)) {
            die(json_encode(['status' => 0, 'message' => '参数['.$key.']包含非法字符']));
        }
    }
    return true;
}

function waf_check($input) {
    $keywords = array(
        "select", "insert", "update", "delete", "drop", 
        "truncate", "union", "sleep", "benchmark", "--", 
        "/*", "*/", "<", ">", "script", "onerror", "onload"
    );
    
    if (is_array($input)) {
        foreach ($input as $item) {
            if (!waf_check($item)) {
                return false;
            }
        }
        return true;
    }
    
    // 将输入和关键字都转换为小写进行比较
    $lowerInput = strtolower($input);
    foreach($keywords as $keyword) {
        if(strpos($lowerInput, strtolower($keyword)) !== false) {
            return false;
        }
    }
    return true;
}

function logOperation($conn, $user_id, $application_id, $operation) {
    $ip = $_SERVER['REMOTE_ADDR'] ;
    $log_stmt = $conn->prepare("INSERT INTO log (user_id, application_id, operation, ip) VALUES (?, ?, ?, ?)");
    $log_stmt->bind_param("iiss", $user_id, $application_id, $operation, $ip);
    $log_stmt->execute();
}

// 系统管理员验证
function isAdmin() {
    global $conn;
    $stmt = $conn->prepare("SELECT roleId FROM 3_user_Role WHERE userId = ?");
    $stmt->bind_param('i', $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    return ($result->num_rows > 0 && $result->fetch_assoc()['roleId'] == 1);
}
// 应用管理员验证
function isAppAdmin($appId) {
    global $conn;
    $stmt = $conn->prepare("SELECT roleId FROM 3_user_Role WHERE userId = ? AND appId = ?");
    $stmt->bind_param('ii', $_SESSION['user_id'], $appId);
    $stmt->execute();
    $result = $stmt->get_result();
    return ($result->num_rows > 0 && $result->fetch_assoc()['roleId'] == 2);
}

// 单位管理员验证
function isUnitAdmin($appId, $unitId) {
    global $conn;
    $stmt = $conn->prepare("SELECT roleId FROM 3_user_Role WHERE userId = ? AND appId = ? AND unitId = ?");
    $stmt->bind_param('iii', $_SESSION['user_id'], $appId, $unitId);
    $stmt->execute();
    $result = $stmt->get_result();
    return ($result->num_rows > 0 && $result->fetch_assoc()['roleId'] ==3);
}


// 自动执行检查
waf_check_all_inputs();
session_start();
ini_set('display_errors',1);
error_reporting(E_ALL);
?>