// 人员调动模拟系统 JavaScript
class PersonnelSimulationSystem {
    constructor() {
        this.currentScenario = null;
        this.currentUnit = null;
        this.selectedPersonnel = new Set();
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalPages = 1;
        this.unitTree = [];
        this.personnelList = [];
        this.currentPersonnelTypeFilter = '';

        this.initDragAndDrop();
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadScenarios();
    }

    bindEvents() {
        // 方案选择
        document.getElementById('scenarioSelect').addEventListener('change', (e) => {
            this.selectScenario(e.target.value);
            this.updateDeleteButtonState();
        });

        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshData();
        });

        // 新建方案
        document.getElementById('createScenarioBtn').addEventListener('click', () => {
            this.showCreateScenarioModal();
        });

        // 删除方案
        document.getElementById('deleteScenarioBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.showDeleteScenarioModal();
        });

        // 单位管理
        document.getElementById('unitManageBtn').addEventListener('click', () => {
            this.showUnitManageModal();
        });

        // 操作日志
        document.getElementById('operationLogsBtn').addEventListener('click', () => {
            this.showOperationLogsModal();
        });

        // 统计报表
        document.getElementById('statisticsBtn').addEventListener('click', () => {
            this.showStatisticsModal();
        });

        // 导出数据
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.showExportModal();
        });

        // 批量调动
        document.getElementById('batchTransferBtn').addEventListener('click', () => {
            this.showTransferModal();
        });

        // 搜索
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.searchPersonnel(e.target.value);
        });

        // 人员身份筛选按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectFilterButton(e.target);
            });
        });

        // 清除筛选
        document.getElementById('clearFiltersBtn').addEventListener('click', () => {
            this.clearFilters();
        });

        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // 全选
        document.getElementById('selectAllCheckbox').addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 分页
        document.getElementById('prevPage').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadPersonnelList();
            }
        });

        document.getElementById('nextPage').addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadPersonnelList();
            }
        });



        // 模态框事件
        this.bindModalEvents();
    }

    bindModalEvents() {
        // 新建方案模态框
        document.getElementById('closeCreateModal').addEventListener('click', () => {
            this.hideModal('createScenarioModal');
        });

        document.getElementById('cancelCreateBtn').addEventListener('click', () => {
            this.hideModal('createScenarioModal');
        });

        document.getElementById('createScenarioForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.createScenario();
        });

        // 删除方案模态框
        document.getElementById('closeDeleteModal').addEventListener('click', () => {
            this.hideModal('deleteScenarioModal');
        });

        document.getElementById('cancelDeleteBtn').addEventListener('click', () => {
            this.hideModal('deleteScenarioModal');
        });

        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.deleteScenario();
        });

        // 操作日志模态框
        document.getElementById('closeLogsModal').addEventListener('click', () => {
            this.hideModal('operationLogsModal');
        });

        document.getElementById('closeLogsBtn').addEventListener('click', () => {
            this.hideModal('operationLogsModal');
        });

        document.getElementById('searchLogsBtn').addEventListener('click', () => {
            this.searchOperationLogs();
        });

        document.getElementById('clearLogFiltersBtn').addEventListener('click', () => {
            this.clearLogFilters();
        });

        document.getElementById('exportLogsBtn').addEventListener('click', () => {
            this.exportOperationLogs();
        });

        // 单位管理模态框
        document.getElementById('closeUnitManageModal').addEventListener('click', () => {
            this.hideModal('unitManageModal');
        });

        document.getElementById('closeUnitManageBtn').addEventListener('click', () => {
            this.hideModal('unitManageModal');
        });

        document.getElementById('addUnitBtn').addEventListener('click', () => {
            this.showUnitEditModal();
        });

        document.getElementById('editUnitBtn').addEventListener('click', () => {
            this.editSelectedUnit();
        });

        document.getElementById('deleteUnitBtn').addEventListener('click', () => {
            this.deleteSelectedUnit();
        });

        // 单位编辑模态框
        document.getElementById('closeUnitEditModal').addEventListener('click', () => {
            this.hideModal('unitEditModal');
        });

        document.getElementById('cancelUnitEditBtn').addEventListener('click', () => {
            this.hideModal('unitEditModal');
        });

        document.getElementById('unitEditForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveUnit();
        });

        // 调动模态框
        document.getElementById('closeTransferModal').addEventListener('click', () => {
            this.hideModal('transferModal');
        });

        document.getElementById('cancelTransferBtn').addEventListener('click', () => {
            this.hideModal('transferModal');
        });

        document.getElementById('transferForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.transferPersonnel();
        });

        // 统计模态框
        document.getElementById('closeStatisticsModal').addEventListener('click', () => {
            this.hideModal('statisticsModal');
        });

        document.getElementById('closeStatisticsBtn').addEventListener('click', () => {
            this.hideModal('statisticsModal');
        });

        document.getElementById('exportStatisticsBtn').addEventListener('click', () => {
            this.exportData('statistics');
        });

        // 导出模态框
        document.getElementById('closeExportModal').addEventListener('click', () => {
            this.hideModal('exportModal');
        });

        document.getElementById('cancelExportBtn').addEventListener('click', () => {
            this.hideModal('exportModal');
        });

        document.getElementById('confirmExportBtn').addEventListener('click', () => {
            this.confirmExport();
        });

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.hideModal(e.target.id);
            }
        });
    }

    // API 请求方法
    async apiRequest(url, data = null, method = 'POST') {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            };

            if (data && method === 'POST') {
                const formData = new URLSearchParams();
                for (const key in data) {
                    if (Array.isArray(data[key])) {
                        data[key].forEach(item => formData.append(key + '[]', item));
                    } else {
                        formData.append(key, data[key]);
                    }
                }
                options.body = formData;
            }

            const response = await fetch(url, options);
            const result = await response.json();
            
            if (result.status === 0) {
                throw new Error(result.message);
            }
            
            return result;
        } catch (error) {
            this.showMessage('请求失败: ' + error.message, 'error');
            throw error;
        }
    }

    // 加载方案列表
    async loadScenarios() {
        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getScenarios');
            const select = document.getElementById('scenarioSelect');
            
            select.innerHTML = '<option value="">选择模拟方案</option>';
            
            result.data.forEach(scenario => {
                const option = document.createElement('option');
                option.value = scenario.id;
                option.textContent = `${scenario.scenario_name} (${scenario.personnel_count}人)`;
                select.appendChild(option);
            });
            
        } catch (error) {
            console.error('加载方案失败:', error);
        }
    }

    // 选择方案
    async selectScenario(scenarioId) {
        if (!scenarioId) {
            this.currentScenario = null;
            this.clearData();
            this.updateDeleteButtonState();
            return;
        }

        this.currentScenario = scenarioId;
        this.updateDeleteButtonState();
        this.showLoading();

        try {
            await this.loadUnitTree();
            this.updateStats();
        } catch (error) {
            console.error('选择方案失败:', error);
        } finally {
            this.hideLoading();
        }
    }

    // 加载单位树
    async loadUnitTree() {
        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getUnitTree', {
                scenario_id: this.currentScenario
            });
            
            this.unitTree = result.data;
            this.renderUnitTree();
            
        } catch (error) {
            console.error('加载单位树失败:', error);
        }
    }

    // 渲染单位树
    renderUnitTree() {
        const container = document.getElementById('unitTree');

        if (this.unitTree.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无单位数据</div>';
            return;
        }

        // 保存当前选中的单位
        const currentActiveUnit = document.querySelector('.unit-content.active');
        const currentActiveUnitId = currentActiveUnit ? currentActiveUnit.dataset.unitId : null;

        container.innerHTML = this.buildUnitTreeHTML(this.unitTree);
        this.bindUnitTreeEvents();
        this.bindUnitDropEvents();

        // 自动展开所有单位
        this.expandAllUnits();

        // 恢复选中状态
        if (currentActiveUnitId) {
            const unitToReactivate = document.querySelector(`[data-unit-id="${currentActiveUnitId}"]`);
            if (unitToReactivate) {
                unitToReactivate.classList.add('active');
            }
        }
    }

    // 构建单位树HTML
    buildUnitTreeHTML(units) {
        return units.map(unit => {
            const hasChildren = unit.children && unit.children.length > 0;
            const childrenHTML = hasChildren ? this.buildUnitTreeHTML(unit.children) : '';

            return `
                <div class="unit-item" data-unit-id="${unit.id}">
                    <div class="unit-content" data-unit-id="${unit.id}" onclick="personnelSystem.selectUnit(${unit.id}, '${unit.unit_name}', event)">
                        <span class="unit-toggle" onclick="event.stopPropagation(); personnelSystem.toggleUnit(this)">
                            ${hasChildren ? '▶' : ''}
                        </span>
                        <span class="unit-name">${unit.unit_name}</span>
                        <span class="unit-count">${unit.personnel_count || 0}</span>
                    </div>
                    ${hasChildren ? `<div class="unit-children">${childrenHTML}</div>` : ''}
                </div>
            `;
        }).join('');
    }

    // 绑定单位树事件
    bindUnitTreeEvents() {
        // 事件已在HTML中内联绑定
    }

    // 切换单位展开/折叠
    toggleUnit(element) {
        const unitItem = element.closest('.unit-item');
        const children = unitItem.querySelector('.unit-children');

        if (children) {
            const isExpanded = children.classList.contains('expanded');
            children.classList.toggle('expanded');
            element.textContent = isExpanded ? '▶' : '▼';
        }
    }

    // 自动展开所有单位
    expandAllUnits() {
        // 展开所有有子单位的节点
        const allChildren = document.querySelectorAll('.unit-children');
        allChildren.forEach(children => {
            children.classList.add('expanded');
        });

        // 更新所有展开按钮的图标
        const allToggles = document.querySelectorAll('.unit-toggle');
        allToggles.forEach(toggle => {
            if (toggle.textContent.trim() === '▶') {
                toggle.textContent = '▼';
            }
        });

        // 更新按钮文本
        const toggleBtn = document.getElementById('toggleAllUnitsBtn');
        if (toggleBtn) {
            toggleBtn.textContent = '折叠全部';
        }
    }

    // 切换全部展开/折叠
    toggleAllUnits() {
        const allChildren = document.querySelectorAll('.unit-children');
        const toggleBtn = document.getElementById('toggleAllUnitsBtn');

        if (allChildren.length === 0) {
            return;
        }

        // 检查当前状态（如果第一个是展开的，就认为是展开状态）
        const isExpanded = allChildren[0].classList.contains('expanded');

        if (isExpanded) {
            // 折叠所有
            allChildren.forEach(children => {
                children.classList.remove('expanded');
            });

            const allToggles = document.querySelectorAll('.unit-toggle');
            allToggles.forEach(toggle => {
                if (toggle.textContent.trim() === '▼') {
                    toggle.textContent = '▶';
                }
            });

            toggleBtn.textContent = '展开全部';
        } else {
            // 展开所有
            this.expandAllUnits();
        }
    }

    // 选择单位
    selectUnit(unitId, unitName, event) {
        // 更新选中状态
        document.querySelectorAll('.unit-content').forEach(el => {
            el.classList.remove('active');
        });

        event.target.closest('.unit-content').classList.add('active');
        
        this.currentUnit = unitId;
        document.getElementById('currentUnitName').textContent = unitName;
        
        // 重置分页和选择
        this.currentPage = 1;
        this.selectedPersonnel.clear();
        this.updateSelectedCount();
        
        this.loadPersonnelList();
    }

    // 加载人员列表
    async loadPersonnelList() {
        if (!this.currentScenario || !this.currentUnit) {
            return;
        }

        try {
            const searchKeyword = document.getElementById('searchInput').value;

            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getPersonnelList', {
                scenario_id: this.currentScenario,
                unit_id: this.currentUnit,
                page: this.currentPage,
                pagesize: this.pageSize,
                search_keyword: searchKeyword,
                personnel_type_filter: this.currentPersonnelTypeFilter
            });
            
            this.personnelList = result.data;
            this.totalPages = Math.ceil(result.total / this.pageSize);
            
            this.renderPersonnelList();
            this.updatePagination();
            this.updatePersonnelTypeFilter();
            
        } catch (error) {
            console.error('加载人员列表失败:', error);
        }
    }

    // 渲染人员列表
    renderPersonnelList() {
        const container = document.getElementById('personnelList');
        
        if (this.personnelList.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无人员数据</div>';
            document.getElementById('pagination').style.display = 'none';
            return;
        }
        
        const html = this.personnelList.map(person => {
            const isSelected = this.selectedPersonnel.has(person.id);
            return `
                <div class="personnel-card ${isSelected ? 'selected' : ''}" data-person-id="${person.id}" draggable="true">
                    <div class="personnel-checkbox">
                        <label class="checkbox-container">
                            <input type="checkbox" ${isSelected ? 'checked' : ''} 
                                   onchange="personnelSystem.togglePersonnelSelection(${person.id}, this.checked)">
                            <span class="checkmark"></span>
                        </label>
                    </div>
                    <div class="personnel-info">
                        <div class="personnel-name">${person.name}</div>
                        <div class="personnel-details">
                            <div class="personnel-detail">
                                <span class="personnel-detail-label">身份证:</span>
                                ${person.id_number || '未填写'}
                            </div>
                            <div class="personnel-detail">
                                <span class="personnel-detail-label">类型:</span>
                                ${person.personnel_type || '未分类'}
                            </div>
                            <div class="personnel-detail">
                                <span class="personnel-detail-label">警号:</span>
                                ${person.police_number || '无'}
                            </div>
                            <div class="personnel-detail">
                                <span class="personnel-detail-label">职务:</span>
                                ${person.position || '无'}
                            </div>
                            <div class="personnel-detail">
                                <span class="personnel-detail-label">调动次数:</span>
                                ${person.transfer_count || 0}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
        
        container.innerHTML = html;
        document.getElementById('pagination').style.display = 'flex';

        // 绑定拖拽事件
        this.bindPersonnelDragEvents();
    }

    // 切换人员选择
    togglePersonnelSelection(personId, selected) {
        if (selected) {
            this.selectedPersonnel.add(personId);
        } else {
            this.selectedPersonnel.delete(personId);
        }
        
        this.updateSelectedCount();
        this.updateBatchTransferButton();
        
        // 更新卡片样式
        const card = document.querySelector(`[data-person-id="${personId}"]`);
        if (card) {
            card.classList.toggle('selected', selected);
        }
    }

    // 全选/取消全选
    toggleSelectAll(selectAll) {
        this.personnelList.forEach(person => {
            if (selectAll) {
                this.selectedPersonnel.add(person.id);
            } else {
                this.selectedPersonnel.delete(person.id);
            }
        });
        
        // 更新复选框状态
        document.querySelectorAll('.personnel-card input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = selectAll;
        });
        
        // 更新卡片样式
        document.querySelectorAll('.personnel-card').forEach(card => {
            card.classList.toggle('selected', selectAll);
        });
        
        this.updateSelectedCount();
        this.updateBatchTransferButton();
    }

    // 更新选中计数
    updateSelectedCount() {
        const count = this.selectedPersonnel.size;
        document.getElementById('selectedCount').textContent = `已选择: ${count}`;
        
        // 更新全选复选框状态
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const currentPagePersonnelIds = this.personnelList.map(p => p.id);
        const currentPageSelectedCount = currentPagePersonnelIds.filter(id => this.selectedPersonnel.has(id)).length;
        
        if (currentPageSelectedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (currentPageSelectedCount === currentPagePersonnelIds.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    // 更新批量调动按钮状态
    updateBatchTransferButton() {
        const button = document.getElementById('batchTransferBtn');
        button.disabled = this.selectedPersonnel.size === 0;
    }

    // 更新分页信息
    updatePagination() {
        document.getElementById('pageInfo').textContent = `第 ${this.currentPage} 页，共 ${this.totalPages} 页`;
        document.getElementById('prevPage').disabled = this.currentPage <= 1;
        document.getElementById('nextPage').disabled = this.currentPage >= this.totalPages;
    }

    // 搜索人员
    searchPersonnel(keyword) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.currentPage = 1;
            this.loadPersonnelList();
        }, 500);
    }

    // 刷新数据
    refreshData() {
        if (this.currentScenario) {
            this.selectScenario(this.currentScenario);
        } else {
            this.loadScenarios();
        }
    }

    // 更新统计信息
    updateStats() {
        if (!this.currentScenario) {
            document.getElementById('unitCount').textContent = '单位: 0';
            document.getElementById('personnelCount').textContent = '人员: 0';
            return;
        }
        
        // 计算单位和人员总数
        const countUnits = (units) => {
            let count = units.length;
            units.forEach(unit => {
                if (unit.children) {
                    count += countUnits(unit.children);
                }
            });
            return count;
        };
        
        const totalUnits = countUnits(this.unitTree);
        const totalPersonnel = this.unitTree.reduce((sum, unit) => {
            return sum + this.countPersonnelInUnit(unit);
        }, 0);
        
        document.getElementById('unitCount').textContent = `单位: ${totalUnits}`;
        document.getElementById('personnelCount').textContent = `人员: ${totalPersonnel}`;
    }

    // 递归计算单位人员数
    countPersonnelInUnit(unit) {
        let count = unit.personnel_count || 0;
        if (unit.children) {
            unit.children.forEach(child => {
                count += this.countPersonnelInUnit(child);
            });
        }
        return count;
    }

    // 清空数据
    clearData() {
        this.unitTree = [];
        this.personnelList = [];
        this.selectedPersonnel.clear();
        this.currentUnit = null;
        
        document.getElementById('unitTree').innerHTML = '<div class="empty-state">请选择模拟方案</div>';
        document.getElementById('personnelList').innerHTML = '<div class="empty-state">请选择单位查看人员</div>';
        document.getElementById('currentUnitName').textContent = '请选择单位';
        document.getElementById('pagination').style.display = 'none';
        
        this.updateSelectedCount();
        this.updateBatchTransferButton();
        this.updateStats();
    }

    // 显示模态框
    showModal(modalId) {
        document.getElementById(modalId).style.display = 'block';
    }

    // 隐藏模态框
    hideModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    // 显示加载遮罩
    showLoading(text = '处理中...') {
        const overlay = document.getElementById('loadingOverlay');
        const textElement = overlay.querySelector('.loading-text');
        textElement.textContent = text;
        overlay.style.display = 'flex';
    }

    // 隐藏加载遮罩
    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    // 显示消息
    showMessage(message, type = 'info', duration = 3000) {
        const container = document.getElementById('messageContainer');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.innerHTML = `
            <span>${message}</span>
            <span class="message-close" onclick="this.parentElement.remove()">&times;</span>
        `;

        container.appendChild(messageDiv);

        // 自动移除
        setTimeout(() => {
            if (messageDiv.parentElement) {
                messageDiv.remove();
            }
        }, duration);
    }

    // 显示新建方案模态框
    showCreateScenarioModal() {
        document.getElementById('createScenarioForm').reset();
        this.showModal('createScenarioModal');
    }

    // 创建方案
    async createScenario() {
        const form = document.getElementById('createScenarioForm');
        const formData = new FormData(form);

        const data = {
            scenario_name: formData.get('scenario_name'),
            description: formData.get('description'),
            copy_data: formData.get('copy_data') === 'on'
        };

        this.showLoading('正在创建方案...');

        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=createScenario', data);

            this.showMessage('方案创建成功', 'success');
            this.hideModal('createScenarioModal');

            // 重新加载方案列表并选择新方案
            await this.loadScenarios();
            document.getElementById('scenarioSelect').value = result.data.scenario_id;
            await this.selectScenario(result.data.scenario_id);

        } catch (error) {
            console.error('创建方案失败:', error);
        } finally {
            this.hideLoading();
        }
    }

    // 显示调动模态框
    async showTransferModal() {
        if (this.selectedPersonnel.size === 0) {
            this.showMessage('请先选择要调动的人员', 'error');
            return;
        }

        // 显示选中的人员
        this.renderSelectedPersonnelList();

        // 加载目标单位选项
        await this.loadTargetUnits();

        this.showModal('transferModal');
    }

    // 渲染选中人员列表
    renderSelectedPersonnelList() {
        const container = document.getElementById('selectedPersonnelList');
        const selectedPersonnelData = this.personnelList.filter(p => this.selectedPersonnel.has(p.id));

        const html = selectedPersonnelData.map(person => `
            <div class="selected-personnel-item">
                <span>${person.name} (${person.personnel_type || '未分类'})</span>
                <small>${person.organization_unit_name || '未知单位'}</small>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    // 加载目标单位选项
    async loadTargetUnits() {
        const select = document.getElementById('targetUnit');
        select.innerHTML = '<option value="">请选择目标单位</option>';

        const addUnitsToSelect = (units, prefix = '') => {
            units.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.id;
                option.textContent = prefix + unit.unit_name;
                select.appendChild(option);

                if (unit.children && unit.children.length > 0) {
                    addUnitsToSelect(unit.children, prefix + '　');
                }
            });
        };

        addUnitsToSelect(this.unitTree);
    }

    // 执行人员调动
    async transferPersonnel() {
        const form = document.getElementById('transferForm');
        const formData = new FormData(form);

        const data = {
            scenario_id: this.currentScenario,
            personnel_ids: Array.from(this.selectedPersonnel),
            target_unit_id: formData.get('target_unit_id'),
            transfer_reason: formData.get('transfer_reason'),
            remarks: formData.get('remarks')
        };

        if (!data.target_unit_id) {
            this.showMessage('请选择目标单位', 'error');
            return;
        }

        this.showLoading('正在执行调动...');

        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=transferPersonnel', data);

            this.showMessage(`调动完成：成功 ${result.data.transferred_count} 人，失败 ${result.data.failed_count} 人`, 'success');
            this.hideModal('transferModal');

            // 刷新数据 - 确保完整刷新
            await this.refreshAfterTransfer();

        } catch (error) {
            console.error('调动失败:', error);
        } finally {
            this.hideLoading();
        }
    }

    // 显示统计模态框
    async showStatisticsModal() {
        if (!this.currentScenario) {
            this.showMessage('请先选择模拟方案', 'error');
            return;
        }

        this.showModal('statisticsModal');
        this.showLoading('正在加载统计数据...');

        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getUnitStatistics', {
                scenario_id: this.currentScenario
            });

            this.renderStatistics(result.data);

        } catch (error) {
            console.error('加载统计数据失败:', error);
            document.getElementById('statisticsContainer').innerHTML = '<div class="empty-state">加载统计数据失败</div>';
        } finally {
            this.hideLoading();
        }
    }

    // 渲染统计数据
    renderStatistics(data) {
        const container = document.getElementById('statisticsContainer');

        if (data.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无统计数据</div>';
            return;
        }

        const totalTransferIn = data.reduce((sum, unit) => sum + (parseInt(unit.transfer_in_count) || 0), 0);
        const totalTransferOut = data.reduce((sum, unit) => sum + (parseInt(unit.transfer_out_count) || 0), 0);

        const html = `
            <table class="statistics-table">
                <thead>
                    <tr>
                        <th>单位名称</th>
                        <th>上级单位</th>
                        <th>民警</th>
                        <th>辅警</th>
                        <th>其他</th>
                        <th>总计</th>
                        <th>调入人员（${totalTransferIn}人）</th>
                        <th>调出人员（${totalTransferOut}人）</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(unit => `
                        <tr>
                            <td>${unit.unit_name}</td>
                            <td>${unit.parent_unit_name || '-'}</td>
                            <td>${unit.police_count}</td>
                            <td>${unit.auxiliary_count}</td>
                            <td>${unit.civilian_count}</td>
                            <td><strong>${unit.total_count}</strong></td>
                            <td class="text-success">
                                ${unit.transfer_in_list && unit.transfer_in_list.length > 0 ?
                                    `调入名单：${unit.transfer_in_list.join('，')}（${unit.transfer_in_list.length}人）` : '-'}
                            </td>
                            <td class="text-warning">
                                ${unit.transfer_out_list && unit.transfer_out_list.length > 0 ?
                                    `调出名单：${unit.transfer_out_list.join('，')}（${unit.transfer_out_list.length}人）` : '-'}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = html;
    }

    // 显示导出模态框
    showExportModal() {
        if (!this.currentScenario) {
            this.showMessage('请先选择模拟方案', 'error');
            return;
        }

        this.showModal('exportModal');
    }

    // 加载导出单位筛选
    loadExportUnitFilter() {
        const select = document.getElementById('exportUnitFilter');
        select.innerHTML = '<option value="">全部单位</option>';

        const addUnitsToSelect = (units, prefix = '') => {
            units.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.id;
                option.textContent = prefix + unit.unit_name;
                select.appendChild(option);

                if (unit.children && unit.children.length > 0) {
                    addUnitsToSelect(unit.children, prefix + '　');
                }
            });
        };

        addUnitsToSelect(this.unitTree);
    }

    // 确认导出
    confirmExport() {
        const exportType = document.querySelector('input[name="export_type"]:checked').value;

        this.exportData(exportType);
        this.hideModal('exportModal');
    }

    // 导出数据
    exportData(type, unitId = null) {
        const params = new URLSearchParams({
            type: type,
            scenario_id: this.currentScenario
        });

        if (unitId) {
            params.append('unit_id', unitId);
        }

        const url = `./api/simulation_export.php?${params.toString()}`;

        // 创建隐藏的下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = '';
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showMessage('导出已开始，请稍候...', 'info');
    }

    // 调动后刷新数据
    async refreshAfterTransfer() {
        try {
            // 1. 重新加载单位树（更新人员统计数）
            await this.loadUnitTree();

            // 2. 重新加载当前单位的人员列表
            if (this.currentUnit) {
                await this.loadPersonnelList();
            }

            // 3. 更新总体统计信息
            this.updateStats();

            // 4. 清除选择状态（因为人员可能已经调走）
            this.selectedPersonnel.clear();
            this.updateSelectedCount();
            this.updateBatchTransferButton();

        } catch (error) {
            console.error('刷新数据失败:', error);
            this.showMessage('数据刷新失败，请手动刷新页面', 'error');
        }
    }

    // 选择筛选按钮
    selectFilterButton(button) {
        // 移除所有按钮的active状态
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // 激活当前按钮
        button.classList.add('active');

        // 获取筛选类型
        const filterType = button.dataset.type;
        this.filterPersonnelByType(filterType);
    }

    // 更新人员身份筛选按钮状态
    updatePersonnelTypeFilter() {
        // 这个方法现在主要用于保持筛选状态
        // 按钮式筛选不需要动态更新选项
    }

    // 按人员身份筛选
    filterPersonnelByType(personnelType) {
        this.currentPersonnelTypeFilter = personnelType;
        this.currentPage = 1;
        this.selectedPersonnel.clear();
        this.updateSelectedCount();
        this.loadPersonnelList();
    }

    // 清除筛选
    clearFilters() {
        // 重置筛选按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector('.filter-btn[data-type=""]').classList.add('active');

        // 清除搜索框
        document.getElementById('searchInput').value = '';

        // 重置筛选状态
        this.currentPersonnelTypeFilter = '';
        this.currentPage = 1;
        this.selectedPersonnel.clear();
        this.updateSelectedCount();
        this.loadPersonnelList();
    }

    // 更新删除按钮状态
    updateDeleteButtonState() {
        const deleteBtn = document.getElementById('deleteScenarioBtn');
        deleteBtn.disabled = !this.currentScenario;
    }

    // 显示删除方案模态框
    showDeleteScenarioModal() {
        if (!this.currentScenario) {
            this.showMessage('请先选择要删除的方案', 'error');
            return;
        }

        const select = document.getElementById('scenarioSelect');
        const selectedOption = select.options[select.selectedIndex];
        const scenarioName = selectedOption.textContent;

        document.getElementById('deleteScenarioName').textContent = scenarioName;
        this.showModal('deleteScenarioModal');
    }

    // 删除方案
    async deleteScenario() {
        if (!this.currentScenario) {
            return;
        }

        this.showLoading('正在删除方案...');

        try {
            await this.apiRequest('./api/personnel_simulation_manage.php?operation=deleteScenario', {
                scenario_id: this.currentScenario
            });

            this.showMessage('方案删除成功', 'success');
            this.hideModal('deleteScenarioModal');

            // 重新加载方案列表
            this.currentScenario = null;
            document.getElementById('scenarioSelect').value = '';
            this.updateDeleteButtonState();
            this.clearData();
            await this.loadScenarios();

        } catch (error) {
            console.error('删除方案失败:', error);
        } finally {
            this.hideLoading();
        }
    }

    // 初始化拖拽功能
    initDragAndDrop() {
        this.draggedPersonnel = null;
        this.dragGhost = null;
    }

    // 绑定人员卡片拖拽事件
    bindPersonnelDragEvents() {
        const personnelCards = document.querySelectorAll('.personnel-card');

        personnelCards.forEach(card => {
            // 移除之前的事件监听器（如果有）
            card.removeEventListener('dragstart', this.handleDragStart);
            card.removeEventListener('dragend', this.handleDragEnd);

            // 绑定新的事件监听器
            card.addEventListener('dragstart', (e) => {
                this.handleDragStart(e);
            });

            card.addEventListener('dragend', (e) => {
                this.handleDragEnd(e);
            });
        });
    }

    // 绑定单位拖拽接收事件
    bindUnitDropEvents() {
        const unitContents = document.querySelectorAll('.unit-content');

        unitContents.forEach(unitContent => {
            unitContent.addEventListener('dragover', (e) => {
                e.preventDefault();
                unitContent.classList.add('drag-over');
            });

            unitContent.addEventListener('dragleave', (e) => {
                unitContent.classList.remove('drag-over');
            });

            unitContent.addEventListener('drop', (e) => {
                e.preventDefault();
                this.handleDrop(e);
            });
        });
    }

    // 处理拖拽开始
    handleDragStart(e) {
        const card = e.target.closest('.personnel-card');
        const personId = card.dataset.personId;

        this.draggedPersonnel = this.personnelList.find(p => p.id == personId);

        if (this.draggedPersonnel) {
            card.classList.add('dragging');

            // 创建拖拽幽灵元素
            this.createDragGhost(this.draggedPersonnel.name);

            // 设置拖拽数据
            e.dataTransfer.setData('text/plain', personId);
            e.dataTransfer.effectAllowed = 'move';
        }
    }

    // 处理拖拽结束
    handleDragEnd(e) {
        const card = e.target.closest('.personnel-card');
        card.classList.remove('dragging');

        // 清除所有拖拽状态
        document.querySelectorAll('.unit-content').forEach(unit => {
            unit.classList.remove('drag-over');
        });

        // 移除拖拽幽灵元素
        this.removeDragGhost();

        this.draggedPersonnel = null;
    }

    // 处理放置
    async handleDrop(e) {
        e.preventDefault();

        const unitContent = e.target.closest('.unit-content');
        unitContent.classList.remove('drag-over');

        // 从拖拽数据中获取人员ID
        const personnelId = e.dataTransfer.getData('text/plain');
        if (!personnelId) {
            return;
        }

        // 重新查找人员信息
        const draggedPersonnel = this.personnelList.find(p => p.id == personnelId);
        if (!draggedPersonnel) {
            return;
        }

        // 获取目标单位ID
        const targetUnitId = unitContent.dataset.unitId;

        if (!targetUnitId) {
            this.showMessage('无法获取目标单位信息', 'error');
            return;
        }

        // 检查是否是同一个单位
        if (draggedPersonnel.organization_unit == targetUnitId) {
            this.showMessage('人员已在该单位中', 'info');
            return;
        }

        // 获取目标单位名称
        const unitName = unitContent.querySelector('.unit-name').textContent;

        // 确认调动
        const confirmed = confirm(`确定要将 ${draggedPersonnel.name} 调动到 ${unitName} 吗？`);

        if (confirmed) {
            await this.performDragTransfer(draggedPersonnel.id, targetUnitId, unitName, draggedPersonnel.name);
        }
    }

    // 执行拖拽调动
    async performDragTransfer(personnelId, targetUnitId, targetUnitName, personnelName) {
        this.showLoading('正在执行调动...');

        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=transferPersonnel', {
                scenario_id: this.currentScenario,
                personnel_ids: [personnelId],
                target_unit_id: targetUnitId,
                transfer_reason: '拖拽调动',
                remarks: `通过拖拽操作调动到${targetUnitName}`
            });

            this.showMessage(`调动成功：${personnelName} 已调动到 ${targetUnitName}`, 'success');

            // 刷新数据 - 确保完整刷新
            await this.refreshAfterTransfer();

        } catch (error) {
            console.error('拖拽调动失败:', error);
        } finally {
            this.hideLoading();
        }
    }

    // 创建拖拽幽灵元素
    createDragGhost(personnelName) {
        this.dragGhost = document.createElement('div');
        this.dragGhost.className = 'drag-ghost';
        this.dragGhost.textContent = `拖拽调动: ${personnelName}`;
        this.dragGhost.style.display = 'none';
        document.body.appendChild(this.dragGhost);

        // 跟随鼠标移动
        document.addEventListener('dragover', this.updateDragGhostPosition.bind(this));
    }

    // 更新拖拽幽灵元素位置
    updateDragGhostPosition(e) {
        if (this.dragGhost) {
            this.dragGhost.style.display = 'block';
            this.dragGhost.style.left = (e.clientX + 10) + 'px';
            this.dragGhost.style.top = (e.clientY + 10) + 'px';
        }
    }

    // 移除拖拽幽灵元素
    removeDragGhost() {
        if (this.dragGhost) {
            document.removeEventListener('dragover', this.updateDragGhostPosition.bind(this));
            this.dragGhost.remove();
            this.dragGhost = null;
        }
    }

    // ==================== 操作日志功能 ====================

    // 显示操作日志模态框
    async showOperationLogsModal() {
        this.showModal('operationLogsModal');
        await this.loadLogScenarios();
        await this.loadOperationLogs();
    }

    // 加载日志筛选的方案列表
    async loadLogScenarios() {
        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getScenarios');
            const select = document.getElementById('logScenarioFilter');

            select.innerHTML = '<option value="">全部方案</option>';

            result.data.forEach(scenario => {
                const option = document.createElement('option');
                option.value = scenario.id;
                option.textContent = scenario.scenario_name;
                select.appendChild(option);
            });

        } catch (error) {
            console.error('加载方案列表失败:', error);
        }
    }

    // 加载操作日志
    async loadOperationLogs() {
        try {
            const scenarioId = document.getElementById('logScenarioFilter').value;
            const dateFilter = document.getElementById('logDateFilter').value;

            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getOperationLogs', {
                scenario_id: scenarioId,
                date_filter: dateFilter,
                page: this.currentLogsPage || 1,
                pagesize: 20
            });

            this.renderOperationLogs(result.data);
            this.updateLogsPagination(result.total, result.page, result.pagesize);

        } catch (error) {
            console.error('加载操作日志失败:', error);
            document.getElementById('logsContainer').innerHTML = '<div class="empty-state">加载日志失败</div>';
        }
    }

    // 渲染操作日志
    renderOperationLogs(logs) {
        const container = document.getElementById('logsContainer');

        if (logs.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无操作日志</div>';
            return;
        }

        const html = `
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>调动时间</th>
                        <th>调动类型</th>
                        <th>人员姓名</th>
                        <th>调动详情</th>
                        <th>操作者</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    ${logs.map(log => `
                        <tr>
                            <td>${log.transfer_time}</td>
                            <td>${this.getTransferTypeText(log.transfer_type)}</td>
                            <td>${log.personnel_name || `人员ID: ${log.personnel_id}`}</td>
                            <td>${log.from_unit_name || '未知单位'} → ${log.to_unit_name || '未知单位'}</td>
                            <td>${log.operator_user_name || log.operator_name || '未知操作者'}</td>
                            <td>${log.transfer_reason || log.remarks || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = html;
    }

    // 获取调动类型文本
    getTransferTypeText(type) {
        const typeMap = {
            'manual': '手动调动',
            'batch': '批量调动',
            'import': '导入调动'
        };
        return typeMap[type] || type || '未知类型';
    }

    // 更新日志分页
    updateLogsPagination(total, page, pagesize) {
        const totalPages = Math.ceil(total / pagesize);
        document.getElementById('logsPageInfo').textContent = `第 ${page} 页，共 ${totalPages} 页`;

        const prevBtn = document.getElementById('prevLogsPage');
        const nextBtn = document.getElementById('nextLogsPage');

        prevBtn.disabled = page <= 1;
        nextBtn.disabled = page >= totalPages;

        document.getElementById('logsPagination').style.display = totalPages > 1 ? 'flex' : 'none';
    }

    // 搜索操作日志
    async searchOperationLogs() {
        this.currentLogsPage = 1;
        await this.loadOperationLogs();
    }

    // 清除日志筛选
    clearLogFilters() {
        document.getElementById('logScenarioFilter').value = '';
        document.getElementById('logDateFilter').value = '';
        this.currentLogsPage = 1;
        this.loadOperationLogs();
    }

    // 导出操作日志
    async exportOperationLogs() {
        try {
            const scenarioId = document.getElementById('logScenarioFilter').value;
            const dateFilter = document.getElementById('logDateFilter').value;

            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=exportOperationLogs', {
                scenario_id: scenarioId,
                date_filter: dateFilter
            });

            if (result.data && result.data.download_url) {
                window.open(result.data.download_url, '_blank');
                this.showMessage('日志导出已开始，请稍候...', 'info');
            }

        } catch (error) {
            console.error('导出日志失败:', error);
        }
    }

    // ==================== 单位管理功能 ====================

    // 显示单位管理模态框
    async showUnitManageModal() {
        // 检查是否已选择方案
        if (!this.currentScenario) {
            alert('请先选择一个模拟方案');
            return;
        }

        this.showModal('unitManageModal');
        await this.loadUnitsForManage();
    }

    // 加载单位管理列表
    async loadUnitsForManage() {
        try {
            // 传递当前方案ID
            const data = {
                scenario_id: this.currentScenario
            };

            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getAllUnits', data);
            this.renderUnitsForManage(result.data);

        } catch (error) {
            console.error('加载单位列表失败:', error);
            document.getElementById('unitManageContainer').innerHTML = '<div class="empty-state">加载单位失败</div>';
        }
    }

    // 渲染单位管理列表
    renderUnitsForManage(units) {
        const container = document.getElementById('unitManageContainer');

        if (units.length === 0) {
            container.innerHTML = '<div class="empty-state">暂无单位数据</div>';
            return;
        }

        const html = `
            <table class="unit-manage-table">
                <thead>
                    <tr>
                        <th>单位名称</th>
                        <th>单位代码</th>
                        <th>上级单位</th>
                        <th>排序号</th>
                        <th>人员数量</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.buildUnitManageRows(units)}
                </tbody>
            </table>
        `;

        container.innerHTML = html;
        this.bindUnitManageEvents();
    }

    // 构建单位管理行（支持层级显示）
    buildUnitManageRows(units, level = 0) {
        return units.map(unit => {
            const levelClass = `unit-level-${Math.min(level, 3)}`;
            let html = `
                <tr class="unit-row" data-unit-id="${unit.id}" onclick="personnelSystem.selectUnitRow(this)">
                    <td class="${levelClass}">${unit.unit_name}</td>
                    <td>${unit.code || ''}</td>
                    <td>${unit.parent_name || '无'}</td>
                    <td>${unit.sort_order || 0}</td>
                    <td>${unit.personnel_count || 0}</td>
                </tr>
            `;

            if (unit.children && unit.children.length > 0) {
                html += this.buildUnitManageRows(unit.children, level + 1);
            }

            return html;
        }).join('');
    }

    // 绑定单位管理事件
    bindUnitManageEvents() {
        // 事件已在HTML中内联绑定
    }

    // 选择单位行
    selectUnitRow(row) {
        // 清除其他选中状态
        document.querySelectorAll('.unit-row').forEach(r => r.classList.remove('selected'));

        // 选中当前行
        row.classList.add('selected');

        // 更新按钮状态
        const unitId = row.dataset.unitId;
        document.getElementById('editUnitBtn').disabled = !unitId;
        document.getElementById('deleteUnitBtn').disabled = !unitId;

        this.selectedUnitId = unitId;
    }

    // 显示单位编辑模态框
    async showUnitEditModal(unitId = null) {
        this.showModal('unitEditModal');

        if (unitId) {
            // 编辑模式
            document.getElementById('unitEditTitle').textContent = '编辑单位';
            await this.loadUnitForEdit(unitId);
        } else {
            // 新增模式
            document.getElementById('unitEditTitle').textContent = '新增单位';
            this.clearUnitEditForm();
        }

        await this.loadParentUnits();
    }

    // 加载单位信息用于编辑
    async loadUnitForEdit(unitId) {
        try {
            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getUnitInfo', {
                unit_id: unitId
            });

            const unit = result.data;
            document.getElementById('editUnitId').value = unit.id;
            document.getElementById('unitName').value = unit.unit_name;
            document.getElementById('parentUnit').value = unit.parent_id || '';
            document.getElementById('unitCode').value = unit.code || '';
            document.getElementById('sortOrder').value = unit.sort_order || 0;

        } catch (error) {
            console.error('加载单位信息失败:', error);
        }
    }

    // 清空单位编辑表单
    clearUnitEditForm() {
        document.getElementById('editUnitId').value = '';
        document.getElementById('unitName').value = '';
        document.getElementById('parentUnit').value = '';
        document.getElementById('unitCode').value = '';
        document.getElementById('sortOrder').value = 0;
    }

    // 加载上级单位列表
    async loadParentUnits() {
        try {
            const data = {
                scenario_id: this.currentScenario
            };

            const result = await this.apiRequest('./api/personnel_simulation_manage.php?operation=getAllUnits', data);
            const select = document.getElementById('parentUnit');

            select.innerHTML = '<option value="">无上级单位</option>';

            this.buildParentUnitOptions(result.data, select);

        } catch (error) {
            console.error('加载上级单位失败:', error);
        }
    }

    // 构建上级单位选项（递归）
    buildParentUnitOptions(units, select, level = 0) {
        units.forEach(unit => {
            const option = document.createElement('option');
            option.value = unit.id;
            option.textContent = '　'.repeat(level) + unit.unit_name;
            select.appendChild(option);

            if (unit.children && unit.children.length > 0) {
                this.buildParentUnitOptions(unit.children, select, level + 1);
            }
        });
    }

    // 编辑选中的单位
    editSelectedUnit() {
        if (this.selectedUnitId) {
            this.showUnitEditModal(this.selectedUnitId);
        }
    }

    // 删除选中的单位
    async deleteSelectedUnit() {
        if (!this.selectedUnitId) {
            return;
        }

        const confirmed = confirm('确定要删除选中的单位吗？删除后不可恢复！');
        if (!confirmed) {
            return;
        }

        try {
            await this.apiRequest('./api/personnel_simulation_manage.php?operation=deleteUnit', {
                unit_id: this.selectedUnitId
            });

            this.showMessage('单位删除成功', 'success');
            await this.loadUnitsForManage();

            // 重置选择状态
            this.selectedUnitId = null;
            document.getElementById('editUnitBtn').disabled = true;
            document.getElementById('deleteUnitBtn').disabled = true;

        } catch (error) {
            console.error('删除单位失败:', error);
        }
    }

    // 保存单位
    async saveUnit() {
        const formData = new FormData(document.getElementById('unitEditForm'));
        const unitId = formData.get('unit_id');

        try {
            const operation = unitId ? 'updateUnit' : 'createUnit';

            const requestData = {
                unit_id: unitId,
                unit_name: formData.get('unit_name'),
                parent_id: formData.get('parent_id') || null,
                code: formData.get('code'),
                sort_order: formData.get('sort_order') || 0
            };

            // 新建单位时需要传递scenario_id
            if (!unitId) {
                requestData.scenario_id = this.currentScenario;
            }

            await this.apiRequest('./api/personnel_simulation_manage.php?operation=' + operation, requestData);

            this.showMessage(unitId ? '单位更新成功' : '单位创建成功', 'success');
            this.hideModal('unitEditModal');
            await this.loadUnitsForManage();

            // 如果当前有选中的方案，刷新单位树
            if (this.currentScenario) {
                await this.loadUnitTree();
            }

        } catch (error) {
            console.error('保存单位失败:', error);
        }
    }

    // 退出登录
    async logout() {
        const confirmed = confirm('确定要退出登录吗？');
        if (!confirmed) {
            return;
        }

        try {
            await this.apiRequest('./api/personnel_simulation_manage.php?operation=logout');
            this.showMessage('已成功退出登录', 'info');

            // 延迟跳转，让用户看到提示信息
            setTimeout(() => {
                window.location.href = '../login.html';
            }, 1000);

        } catch (error) {
            console.error('退出登录失败:', error);
            // 即使API调用失败，也强制跳转到登录页面
            window.location.href = '../login.html';
        }
    }

}

// 初始化系统
let personnelSystem;
document.addEventListener('DOMContentLoaded', () => {
    personnelSystem = new PersonnelSimulationSystem();
});
