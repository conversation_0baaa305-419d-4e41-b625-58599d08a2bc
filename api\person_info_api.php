<?php
// 设置响应头为 JSON 格式
header('Content-Type: application/json');

require_once '../conn_waf.php';
// 定义人员身份、状态和职级数组
$identities = ['民警', '事业', '机关工勤', '辅警', '三方合作人员'];
$statuses = ['在职', '退休', '调离', '开除', '抽调出局'];
$ranks = ['正科级', '副科级', '股级'];
// 获取查询参数
$type = isset($_POST['type']) ? $_POST['type'] : '';

// 根据不同类型返回相应数据
switch ($type) {
    case 'identity':
        echo json_encode(['status' => 1, 'message'=>"查询成功", 'data' => $identities]);
        break;
    case 'status':
        echo json_encode(['status' => 1,'message'=>"查询成功", 'data' => $statuses]);

        break;
    case 'rank':
        echo json_encode(['status' => 1,'message'=>"查询成功", 'data' => $ranks]);

        break;
    default:
        echo json_encode(['status' => 1,'message'=>"无效的查询类型"]);

}
?>