<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PerSimTS 开发者工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #3498db;
            text-align: center;
        }
        .tool-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .tool-section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .danger {
            background-color: #e74c3c;
        }
        .danger:hover {
            background-color: #c0392b;
        }
        .success {
            background-color: #27ae60;
        }
        .success:hover {
            background-color: #229954;
        }
        .info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PerSimTS 开发者工具</h1>
        
        <div class="tool-section">
            <h3>🔧 缓存管理</h3>
            <p>解决浏览器缓存导致的更新不生效问题</p>
            <button onclick="clearAllCache()">清除所有缓存</button>
            <button onclick="clearBrowserCache()" class="danger">强制刷新页面</button>
            <button onclick="checkCacheStatus()">检查缓存状态</button>
        </div>
        
        <div class="tool-section">
            <h3>📊 版本信息</h3>
            <div class="info" id="versionInfo">
                <p><strong>当前版本:</strong> <span id="currentVersion">检测中...</span></p>
                <p><strong>构建时间:</strong> <span id="buildTime">检测中...</span></p>
                <p><strong>浏览器:</strong> <span id="browserInfo"></span></p>
            </div>
            <button onclick="updateVersion()">更新版本号</button>
        </div>
        
        <div class="tool-section">
            <h3>🔗 快速链接</h3>
            <button onclick="openMainSystem()" class="success">打开主系统</button>
            <button onclick="openTestPage()">打开拖拽测试</button>
            <button onclick="openApiTest()">API测试</button>
        </div>
        
        <div class="tool-section">
            <h3>📝 操作日志</h3>
            <div class="log" id="logContainer">等待操作...</div>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        // 日志功能
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.textContent += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清除所有缓存
        function clearAllCache() {
            log('开始清除缓存...');
            
            // 清除localStorage
            const localStorageKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                localStorageKeys.push(localStorage.key(i));
            }
            localStorageKeys.forEach(key => localStorage.removeItem(key));
            log(`清除localStorage: ${localStorageKeys.length} 项`);
            
            // 清除sessionStorage
            const sessionStorageKeys = [];
            for (let i = 0; i < sessionStorage.length; i++) {
                sessionStorageKeys.push(sessionStorage.key(i));
            }
            sessionStorageKeys.forEach(key => sessionStorage.removeItem(key));
            log(`清除sessionStorage: ${sessionStorageKeys.length} 项`);
            
            // 清除Service Worker缓存
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                        log(`清除缓存: ${name}`);
                    });
                });
            }
            
            log('缓存清除完成！');
        }

        // 强制刷新浏览器缓存
        function clearBrowserCache() {
            log('强制刷新页面...');
            window.location.reload(true);
        }

        // 检查缓存状态
        function checkCacheStatus() {
            log('检查缓存状态...');
            log(`localStorage项目数: ${localStorage.length}`);
            log(`sessionStorage项目数: ${sessionStorage.length}`);
            
            if ('caches' in window) {
                caches.keys().then(names => {
                    log(`Service Worker缓存数: ${names.length}`);
                    names.forEach(name => log(`- ${name}`));
                });
            }
        }

        // 更新版本号
        function updateVersion() {
            const newVersion = prompt('请输入新版本号:', '1.0.2');
            if (newVersion) {
                localStorage.setItem('persimts_version', newVersion);
                localStorage.setItem('persimts_build_time', new Date().toLocaleString());
                log(`版本号已更新为: ${newVersion}`);
                updateVersionDisplay();
            }
        }

        // 更新版本显示
        function updateVersionDisplay() {
            const version = localStorage.getItem('persimts_version') || '未知';
            const buildTime = localStorage.getItem('persimts_build_time') || '未知';
            
            document.getElementById('currentVersion').textContent = version;
            document.getElementById('buildTime').textContent = buildTime;
            document.getElementById('browserInfo').textContent = navigator.userAgent;
        }

        // 打开主系统
        function openMainSystem() {
            const url = './index.html?v=' + Date.now();
            window.open(url, '_blank');
            log('打开主系统: ' + url);
        }

        // 打开测试页面
        function openTestPage() {
            const url = './test_drag.html?v=' + Date.now();
            window.open(url, '_blank');
            log('打开拖拽测试: ' + url);
        }

        // API测试
        function openApiTest() {
            const url = './api/test.php?v=' + Date.now();
            window.open(url, '_blank');
            log('打开API测试: ' + url);
        }

        // 清除日志
        function clearLog() {
            document.getElementById('logContainer').textContent = '日志已清除\n';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateVersionDisplay();
            log('开发者工具已加载');
        });
    </script>
</body>
</html>
