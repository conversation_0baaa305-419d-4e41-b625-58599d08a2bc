<?php
require '../conn_waf.php';




// 添加POST参数获取
$id_number = $_POST['id_number'] ?? '';
$password = $_POST['password'] ?? '';



// 修改验证条件（原验证代码保持不变）
if (empty($id_number) || empty($password)) {
    echo json_encode(['status' =>0, 'message' => '身份证号和密码不能为空']);
    exit;
}

// 查询用户


$sql = "SELECT id, name, work_unit ,organization_unit, password, id_number,personnel_type FROM 3_user WHERE id_number = ?";

$stmt = $conn->prepare($sql);


// 添加prepare错误检查
if (!$stmt) {
    error_log("预处理失败: " . $conn->error);
    echo json_encode(['status' => 0, 'message' => '系统错误']);
    exit;
}

$stmt->bind_param('i', $id_number);  // 第24行
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();


// 修改后
if (!$user) {
    error_log("登录失败 - 身份证号不存在: " . $id_number);
    logOperation($conn, 1, 1, "登录失败: 身份证号不存在");
    echo json_encode(['status' => 0, 'message' => '身份证号不存在']);
    exit;
}

// 验证密码
// 在文件开头添加
$ip = $_SERVER['REMOTE_ADDR'];

// 检查IP是否被锁定
$lock_check = $conn->prepare("SELECT locked_until FROM login_attempts WHERE ip_address = ? AND locked_until > NOW()");
$lock_check->bind_param('s', $ip);
$lock_check->execute();
$lock_result = $lock_check->get_result();

if ($lock_result->num_rows > 0) {
    echo json_encode(['status' => 0, 'message' => '您的IP已被锁定，请5分钟后再试']);
    exit;
}

// 在密码验证失败后添加
if (!password_verify($password, $user['password'])) {
    // 更新失败尝试次数
    $stmt = $conn->prepare("INSERT INTO login_attempts (ip_address, attempts) VALUES (?, 1) ON DUPLICATE KEY UPDATE attempts = attempts + 1, last_attempt = NOW()");
    $stmt->bind_param('s', $ip);
    $stmt->execute();
    
    // 检查是否达到5次失败
    $check = $conn->prepare("SELECT attempts FROM login_attempts WHERE ip_address = ?");
    $check->bind_param('s', $ip);
    $check->execute();
    $result = $check->get_result();
    $data = $result->fetch_assoc();
    
    if ($data['attempts'] >= 5) {
        // 锁定IP 5分钟
        $lock = $conn->prepare("UPDATE login_attempts SET locked_until = DATE_ADD(NOW(), INTERVAL 5 MINUTE) WHERE ip_address = ?");
        $lock->bind_param('s', $ip);
        $lock->execute();
    }
    
    echo json_encode(['status' => 0, 'message' => '密码错误']);
    exit;
}

// 登录成功时清除失败记录
$clear = $conn->prepare("DELETE FROM login_attempts WHERE ip_address = ?");
$clear->bind_param('s', $ip);
$clear->execute();

// 设置Session
$_SESSION['user_id'] = $user['id'];
$_SESSION['username'] = $user['name'];
$_SESSION['unit_id'][0] = $user['organization_unit'];
if($user['work_unit'])
{
   $_SESSION['unit_id'][1] = $user['work_unit']; 
}

$_SESSION['personnel_type'] = $user['personnel_type'];//人员身份
$_SESSION['last_activity'] = time();



// 从新数据库结构查询用户权限信息，返回所有应用及用户权限状态
$newPermissionSql = "SELECT
    a.id AS application_id,
    a.application_name,
    CASE WHEN ur.userId IS NOT NULL THEN 1 ELSE 0 END AS has_permission
FROM
    5_application a
LEFT JOIN
    3_user_Role ur ON a.id = ur.appId AND ur.userId = ?
ORDER BY
    a.id;"; // 修改查询语句，返回所有应用及权限状态
$newPermissionStmt = $conn->prepare($newPermissionSql);

// 检查 prepare 是否成功
if ($newPermissionStmt === false) {
    // 记录错误日志
    error_log("SQL 准备失败: " . $conn->error);
    // 可以根据需要进行其他处理，例如返回错误信息给用户
    // 这里简单终止脚本
    die("数据库查询出错，请稍后重试。1");
}

$newPermissionStmt->bind_param('i', $user['id']);
$newPermissionStmt->execute();
$newPermissionResult = $newPermissionStmt->get_result();

// 查询用户的所有角色
$roleSql = "SELECT r.roleName FROM 3_user_Role ur JOIN 4_Role_List r ON ur.roleId = r.id WHERE ur.userId = ?"; // 假设角色表为 4_role
$roleStmt = $conn->prepare($roleSql);
if ($roleStmt === false) {
    error_log("SQL 准备失败: " . $conn->error);
    die("数据库查询出错，请稍后重试。");
}
$roleStmt->bind_param('i', $user['id']);
$roleStmt->execute();
$roleResult = $roleStmt->get_result();

$_SESSION['is_admin'] = false;
// 检查是否有系统管理员角色
while ($roleRow = $roleResult->fetch_assoc()) {
    if ($roleRow['roleName'] == '系统管理员') {
        $_SESSION['is_admin'] = true;
        break;
    }
}

// 将新的权限信息存入 session 字典
$_SESSION['permissions'] = array();
while ($row = $newPermissionResult->fetch_assoc()) {
    $_SESSION['permissions'][] = $row;
}

  
logOperation($conn, $user['id'], 1, "登录成功");
echo json_encode([
    'status' => 1,
    'message' => '登录成功',
    'user' => [
        'id' => $_SESSION['user_id'],
        'name' => $_SESSION['username'],
        'unit_id' => $_SESSION['unit_id'],
        'is_admin' => $_SESSION['is_admin']
    ]
]);


?>
