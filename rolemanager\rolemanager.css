body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
}
.search-panel {
    height: 60px;
    width: 100%;
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
}
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}
.user-list {
    width: 50%;
    overflow-y: auto;
    padding: 10px;
    border-right: 1px solid #ddd;
}
.auth-list {
    width: 50%;
    overflow-y: auto;
    padding: 10px;
}
table {
    width: 100%;
    border-collapse: collapse;
}
th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}
th {
    background-color: #f2f2f2;
}
tr:nth-child(even) {
    background-color: #f9f9f9;
}
button {
    padding: 5px 10px;
    cursor: pointer;
    margin: 2px;
}
.dialog {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}
.dialog-content {
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    width: 400px;
}
.dialog-footer {
    margin-top: 20px;
    text-align: right;
}
.form-group {
    margin-bottom: 15px;
}
label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}
select, input {
    width: 20%;
    padding: 8px;
    box-sizing: border-box;
}
.pagination {
    margin-top: 10px;
    display: flex;
    justify-content: center;
}
.pagination button {
    margin: 0 5px;
}