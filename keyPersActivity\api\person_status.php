<?php
header('Content-Type: application/json; charset=utf-8');
require_once('../../conn_waf.php');
$APP_ID = 8; // 设置应用ID


function addStatus() {
    global $conn, $user_id, $APP_ID;
    
    $status_name = trim($_POST['status_name'] ?? '');
    if (empty($status_name)) {
        throw new Exception('状态名称不能为空');
    }
    
    $stmt = $conn->prepare("INSERT INTO 8_person_status (status_name) VALUES (?)");
    $stmt->bind_param("s", $status_name);
    $stmt->execute();
    $new_id = $stmt->insert_id;
    $stmt->close();
    
    logOperation($conn, $_SESSION['user_id'] , $APP_ID, "新增人员状态ID: ".$new_id." 状态: ".$status_name);
    
    echo json_encode([
        'status' => 1,
        'message' => '新增成功',
        'data' => [
            'id' => $new_id,
            'name' => $status_name
        ]
    ]);
}

function deleteStatus() {
    global $conn, $user_id, $APP_ID;
    
    $ids = $_POST['id'] ?? '';
    $idList = is_numeric($ids) ? [intval($ids)] : array_filter(array_map('intval', explode(',', $ids)));
    
    if (empty($idList)) {
        throw new Exception('ID参数无效');
    }

    // 检查是否有关联的重点人员
    $placeholders = implode(',', array_fill(0, count($idList), '?'));
    $sql = "SELECT COUNT(*) as count FROM 8_key_person WHERE status_id IN ($placeholders)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('i', count($idList)), ...$idList);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['count'];
    if ($count > 0) {
        throw new Exception('存在关联的重点人员，无法删除');
    }
    
    // 执行删除操作
    $sql = "DELETE FROM 8_person_status WHERE id IN ($placeholders)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(str_repeat('i', count($idList)), ...$idList);
    $stmt->execute();
    $affected_rows = $stmt->affected_rows;
    $stmt->close();
    
    if ($affected_rows > 0) {
        logOperation($conn, $_SESSION['user_id'], $APP_ID, '删除人员状态ID: '.implode(',', $idList));
        echo json_encode([
            'status' => 1,
            'message' => '删除成功',
            'data' => [
                'ids' => $idList
            ]
        ]);
    } else {
        throw new Exception('未找到要删除的记录');
    }
}

function modifyStatus() {
    global $conn, $user_id, $APP_ID;
    
    $id = intval($_POST['id'] ?? 0);
    $status_name = trim($_POST['status_name'] ?? '');
    
    if ($id <= 0) {
        throw new Exception('ID参数无效');
    }
    if (empty($status_name)) {
        throw new Exception('状态名称不能为空');
    }
    
    $stmt = $conn->prepare("UPDATE 8_person_status SET status_name = ? WHERE id = ?");
    $stmt->bind_param("si", $status_name, $id);
    $stmt->execute();
    $affected_rows = $stmt->affected_rows;
    $stmt->close();
    
    if ($affected_rows > 0) {
        logOperation($conn, $_SESSION['user_id'], $APP_ID, '修改人员状态ID '.$id.' 为: '.$status_name);
        echo json_encode([
            'status' => 1,
            'message' => '修改成功'
        ]);
    } else {
        throw new Exception('未找到要修改的记录或数据未变化');
    }
}

function queryStatus() {
    global $conn;
    
    $page = max(1, intval($_POST['page'] ?? 1));
    $page = intval($_POST['page'] ?? 0);
    $pagesize = intval($_POST['pagesize'] ?? 0);
    
    if (empty($page) || $page <= 0) {
        $page = 1;
    }
    if (empty($pagesize) || $pagesize <= 0) {
        $pagesize = 10;
    }
    $pagesize = max(1, min(100, $pagesize));
    $offset = ($page - 1) * $pagesize;
    
    $total = $conn->query("SELECT COUNT(*) FROM 8_person_status")->fetch_row()[0];
    
    $stmt = $conn->prepare("SELECT * FROM 8_person_status LIMIT ? OFFSET ?");
    $stmt->bind_param("ii", $pagesize, $offset);
    $stmt->execute();
    $result = $stmt->get_result();
    $data = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
    
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $data,
        'total' => $total,
        'total_page' => ceil($total / $pagesize),
        'page' => $page,
        'pagesize' => $pagesize,
        
    ]);
}
function isHasPerm(){
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
            'data' => []
        ]);
        exit;
    }
    global $APP_ID;
    if (!isAdmin() && !isAppAdmin($APP_ID)) {
    echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
        'data' => []
    ]);
    exit;
    }
}
try {
    // 获取POST参数
    $controlCode = $_POST['controlCode'] ?? '';
    
    
    // 根据controlCode执行不同操作
    switch($controlCode) {
        case 'add': 
            isHasPerm();
            addStatus(); 
            break;
        case 'del': 
            isHasPerm();
            deleteStatus(); 
            break;
        case 'modify': 
            isHasPerm();
            modifyStatus(); 
            break;
        case 'query': 
            queryStatus(); 
            break;
        default: throw new Exception('无效的操作类型');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => $e->getMessage(),
        'data' => []
    ]);
}
?>