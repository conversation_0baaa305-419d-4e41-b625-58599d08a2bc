<?php
require_once '../conn_waf.php';
$APP_ID=30;
$personnelType = $_SESSION['personnel_type'] ?? 0;

function isHasPerm($APP_ID){
    global $conn;
    // 系统管理员拥有所有权限
    if (isset($_SESSION['user_id'])) {
        return true;
    }
    else {
        echo json_encode([
            'status' => 0,
            'message' => '用户未登录',
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    // 获取用户作为应用管理员的应用
    if (isAdmin() && isAppAdmin($APP_ID)) {
        return true;
    }
    else {
        echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作',
    ], JSON_UNESCAPED_UNICODE);
    exit;
    }
    // 获取基于角色授权的应用
    $userId = $_SESSION['user_id'];
    $sql = "SELECT DISTINCT a.id FROM 5_application a
            JOIN 3_user_Role ur ON a.id = ur.appId
            WHERE ur.userId = ? AND JSON_CONTAINS(a.roleList, CAST(ur.roleId AS JSON))";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $userId);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    if ($result && $result->num_rows > 0) {
        return true;
    }
    else {
        echo json_encode([
        'status' => 0,
        'message' => '当前用户无权限操作该应用',
    ], JSON_UNESCAPED_UNICODE);
    exit;
    }
}

if($personnelType!='民警'){
    isHasPerm($APP_ID);
}

// 处理AJAX请求
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_all':
            try {
                $page = intval($_POST['page'] ?? 1);
                $pageSize = intval($_POST['page_size'] ?? 10);
                $offset = ($page - 1) * $pageSize;

                // 确保page参数有效
                if ($page < 1) $page = 1;

                // 获取总数
                $countStmt = $conn->prepare("SELECT COUNT(*) as total FROM 30_schInfo");
                $countStmt->execute();
                $totalResult = $countStmt->get_result();
                $total = $totalResult->fetch_assoc()['total'];

                // 获取分页数据
                $stmt = $conn->prepare("SELECT * FROM 30_schInfo ORDER BY created_at DESC LIMIT ? OFFSET ?");
                if (!$stmt) {
                    throw new Exception("预处理语句创建失败: " . $conn->error);
                }

                $stmt->bind_param('ii', $pageSize, $offset);
                $stmt->execute();
                $result = $stmt->get_result();

                $students = [];
                if ($result && $result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $students[] = $row;
                    }
                }

                echo json_encode([
                    'status' => 'success',
                    'data' => $students,
                    'count' => count($students),
                    'total' => $total,
                    'page' => $page,
                    'page_size' => $pageSize,
                    'total_pages' => ceil($total / $pageSize)
                ]);

            } catch (Exception $e) {
                echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
            }
            break;
            
        case 'search':
            try {
                $searchType = $_POST['search_type'] ?? 'fuzzy';

                if ($searchType === 'fuzzy') {
                    $searchTerm = $_POST['search_term'] ?? '';
                    if (empty($searchTerm)) {
                        echo json_encode(['status' => 'error', 'message' => '搜索关键词不能为空']);
                        exit;
                    }

                    $page = intval($_POST['page'] ?? 1);
                    $pageSize = intval($_POST['page_size'] ?? 10);
                    $offset = ($page - 1) * $pageSize;

                    // 确保page参数有效
                    if ($page < 1) $page = 1;

                    $searchTerm = '%' . $searchTerm . '%';

                    // 获取搜索结果总数
                    $countSql = "SELECT COUNT(*) as total FROM 30_schInfo WHERE
                            school LIKE ? OR
                            class LIKE ? OR
                            name LIKE ? OR
                            id_card LIKE ? OR
                            phone LIKE ? OR
                            address LIKE ? OR
                            parent1_name LIKE ? OR
                            parent1_relation LIKE ? OR
                            parent1_phone LIKE ? OR
                            parent1_id_card LIKE ? OR
                            parent2_name LIKE ? OR
                            parent2_relation LIKE ? OR
                            parent2_phone LIKE ? OR
                            parent2_id_card LIKE ?";

                    $countStmt = $conn->prepare($countSql);
                    $countStmt->bind_param(str_repeat('s', 14),
                        $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm,
                        $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm
                    );
                    $countStmt->execute();
                    $totalResult = $countStmt->get_result();
                    $total = $totalResult->fetch_assoc()['total'];

                    // 获取分页搜索结果
                    $sql = "SELECT * FROM 30_schInfo WHERE
                            school LIKE ? OR
                            class LIKE ? OR
                            name LIKE ? OR
                            id_card LIKE ? OR
                            phone LIKE ? OR
                            address LIKE ? OR
                            parent1_name LIKE ? OR
                            parent1_relation LIKE ? OR
                            parent1_phone LIKE ? OR
                            parent1_id_card LIKE ? OR
                            parent2_name LIKE ? OR
                            parent2_relation LIKE ? OR
                            parent2_phone LIKE ? OR
                            parent2_id_card LIKE ?
                            ORDER BY created_at DESC LIMIT ? OFFSET ?";

                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("预处理语句创建失败: " . $conn->error);
                    }

                    $stmt->bind_param(str_repeat('s', 14) . 'ii',
                        $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm,
                        $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm,
                        $pageSize, $offset
                    );

                } else if ($searchType === 'advanced') {
                    $conditionsJson = $_POST['conditions'] ?? '';
                    if (empty($conditionsJson)) {
                        echo json_encode(['status' => 'error', 'message' => '请添加搜索条件']);
                        exit;
                    }

                    $conditions = json_decode($conditionsJson, true);
                    if (!is_array($conditions) || empty($conditions)) {
                        echo json_encode(['status' => 'error', 'message' => '搜索条件格式错误']);
                        exit;
                    }

                    $page = intval($_POST['page'] ?? 1);
                    $pageSize = intval($_POST['page_size'] ?? 10);
                    $offset = ($page - 1) * $pageSize;

                    // 确保page参数有效
                    if ($page < 1) $page = 1;

                    $whereClauses = [];
                    $params = [];
                    $types = '';

                    foreach ($conditions as $condition) {
                        $field = $condition['field'] ?? '';
                        $operator = $condition['operator'] ?? '';
                        $value = $condition['value'] ?? '';

                        if (empty($field) || empty($operator) || empty($value)) {
                            continue;
                        }

                        switch ($operator) {
                            case 'equals':
                                $whereClauses[] = "$field = ?";
                                $params[] = $value;
                                $types .= 's';
                                break;
                            case 'not_equals':
                                $whereClauses[] = "$field != ?";
                                $params[] = $value;
                                $types .= 's';
                                break;
                            case 'contains':
                                $whereClauses[] = "$field LIKE ?";
                                $params[] = '%' . $value . '%';
                                $types .= 's';
                                break;
                        }
                    }

                    if (empty($whereClauses)) {
                        echo json_encode(['status' => 'error', 'message' => '无有效的搜索条件']);
                        exit;
                    }

                    // 获取高级搜索结果总数
                    $countSql = "SELECT COUNT(*) as total FROM 30_schInfo WHERE " . implode(' AND ', $whereClauses);
                    $countStmt = $conn->prepare($countSql);
                    if (!empty($params)) {
                        $countStmt->bind_param($types, ...$params);
                    }
                    $countStmt->execute();
                    $totalResult = $countStmt->get_result();
                    $total = $totalResult->fetch_assoc()['total'];

                    // 获取分页的高级搜索结果
                    $sql = "SELECT * FROM 30_schInfo WHERE " . implode(' AND ', $whereClauses) . " ORDER BY created_at DESC LIMIT ? OFFSET ?";
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("预处理语句创建失败: " . $conn->error);
                    }

                    if (!empty($params)) {
                        $stmt->bind_param($types . 'ii', ...array_merge($params, [$pageSize, $offset]));
                    } else {
                        $stmt->bind_param('ii', $pageSize, $offset);
                    }
                }

                $stmt->execute();
                $result = $stmt->get_result();

                $students = [];
                if ($result && $result->num_rows > 0) {
                    while($row = $result->fetch_assoc()) {
                        $students[] = $row;
                    }
                }

                echo json_encode([
                    'status' => 'success',
                    'data' => $students,
                    'count' => count($students),
                    'total' => $total,
                    'page' => $page,
                    'page_size' => $pageSize,
                    'total_pages' => ceil($total / $pageSize)
                ]);

            } catch (Exception $e) {
                echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
            }
            break;
            
        default:
            echo json_encode(['status' => 'error', 'message' => '无效的操作']);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生信息查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        /* 动态背景装饰元素 */
        .bg-decoration {
            position: fixed;
            pointer-events: none;
            z-index: 1;
        }

        .bg-circle {
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .bg-circle:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .bg-circle:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }

        .bg-circle:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .bg-circle:nth-child(4) {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 10%;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            position: relative;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% { text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3); }
            100% { text-shadow: 2px 2px 15px rgba(255, 255, 255, 0.5); }
        }

        /* 统计面板样式 */
        .stats-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            padding: 20px;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            min-width: 120px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            animation: statFadeIn 0.6s ease forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .stat-item:nth-child(1) { animation-delay: 0.1s; }
        .stat-item:nth-child(2) { animation-delay: 0.2s; }
        .stat-item:nth-child(3) { animation-delay: 0.3s; }
        .stat-item:nth-child(4) { animation-delay: 0.4s; }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.9);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        @keyframes statFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .search-section {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .search-section:hover {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 16px 50px rgba(0, 0, 0, 0.2);
        }

        .search-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input-container {
            position: relative;
            flex: 1;
            min-width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 20px; /* 右侧留出空间给清空按钮 */
            border: none;
            border-radius: 25px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.9);
        }

        .clear-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(0, 0, 0, 0.4);
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: rgba(0, 0, 0, 0.1);
            color: rgba(0, 0, 0, 0.7);
        }

        .clear-btn:active {
            transform: translateY(-50%) scale(0.95);
        }

        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 30px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-weight: 600;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4CAF50, #45a049, #66BB6A);
            color: white;
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
        }

        .btn-primary:active {
            transform: translateY(-1px) scale(1.02);
        }

        .btn-secondary {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:active {
            transform: translateY(-1px) scale(1.02);
        }

        .results-section {
            background: rgba(255, 255, 255, 0.12);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .results-section:hover {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 16px 50px rgba(0, 0, 0, 0.2);
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            color: white;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .student-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .student-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .student-card:hover::before {
            left: 100%;
        }

        .student-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .photo-container {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
        }

        .student-info h3 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .school-class {
            color: #666;
            font-size: 0.9rem;
        }

        .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .info-label {
            font-weight: 500;
            color: #555;
            min-width: 80px;
        }

        .info-value {
            color: #333;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            padding: 40px;
        }

        .error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .empty-state {
            text-align: center;
            color: white;
            padding: 40px;
        }

        .hidden {
            display: none;
        }

        /* 优雅的加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(102, 126, 234, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(10px);
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .loading-text {
            font-size: 1.2rem;
            font-weight: 500;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 照片切换容器样式 */
        .photo-toggle-container {
            text-align: center;
            margin-bottom: 20px;
        }

        /* 可点击照片样式 */
        .clickable-photo {
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .clickable-photo:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* 高级搜索样式 */
        .advanced-search-area {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .condition-row {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .condition-select, .condition-input {
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.9);
        }

        .remove-condition {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
        }

        /* 视图控制样式 */
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            color: white;
            flex-wrap: wrap;
            gap: 15px;
        }

        .view-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .view-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            overflow: hidden;
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-btn.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .view-btn:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }

        .data-table {
            width: 100%;
            min-width: 1400px; /* 确保表格有足够宽度显示所有列 */
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            white-space: nowrap; /* 防止文字换行 */
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 设置各列的最小宽度 */
        .data-table th:nth-child(1), .data-table td:nth-child(1) { min-width: 60px; } /* 头像 */
        .data-table th:nth-child(2), .data-table td:nth-child(2) { min-width: 80px; } /* 姓名 */
        .data-table th:nth-child(3), .data-table td:nth-child(3) { min-width: 150px; } /* 身份证号 */
        .data-table th:nth-child(4), .data-table td:nth-child(4) { min-width: 120px; } /* 电话 */
        .data-table th:nth-child(5), .data-table td:nth-child(5) { min-width: 100px; } /* 学校 */
        .data-table th:nth-child(6), .data-table td:nth-child(6) { min-width: 80px; } /* 班级 */
        .data-table th:nth-child(7), .data-table td:nth-child(7) { min-width: 200px; } /* 地址 */
        .data-table th:nth-child(8), .data-table td:nth-child(8) { min-width: 80px; } /* 监护人1 */
        .data-table th:nth-child(9), .data-table td:nth-child(9) { min-width: 120px; } /* 监护人1电话 */
        .data-table th:nth-child(10), .data-table td:nth-child(10) { min-width: 150px; } /* 监护人1身份证 */
        .data-table th:nth-child(11), .data-table td:nth-child(11) { min-width: 80px; } /* 监护人2 */
        .data-table th:nth-child(12), .data-table td:nth-child(12) { min-width: 120px; } /* 监护人2电话 */

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .data-table tr:hover {
            background: rgba(0, 123, 255, 0.05);
        }

        .photo-cell {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .photo-cell img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: #333;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .modal-body {
            padding: 25px;
        }

        .modal-info-row {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .modal-info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .modal-info-label {
            font-weight: 600;
            color: #555;
            min-width: 120px;
            margin-right: 15px;
        }

        .modal-info-value {
            color: #333;
            flex: 1;
        }

        /* 优化的分页样式 */
        .pagination-container {
            margin-top: 30px;
            padding: 25px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .pagination-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 12px;
            color: white;
            font-weight: 500;
        }

        .page-size-selector select {
            padding: 10px 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            backdrop-filter: blur(10px);
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .page-size-selector select:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.2);
        }

        .page-size-selector select:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.8);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

        .page-size-selector select option {
            background: #2a2a2a;
            color: white;
            padding: 8px;
        }

        .pagination-stats {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .pagination-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .page-numbers {
            display: flex;
            gap: 8px;
        }

        .page-number {
            padding: 12px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 48px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .page-number::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .page-number:hover::before {
            left: 100%;
        }

        .page-number:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .page-number.active {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
            border-color: rgba(102, 126, 234, 1);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .page-number.active:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.5);
        }

        .pagination-controls .btn {
            padding: 12px 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .pagination-controls .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .pagination-controls .btn:hover::before {
            left: 100%;
        }

        .pagination-controls .btn:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .pagination-controls .btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .pagination-controls .btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .pagination-controls .btn:disabled:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            transform: none;
            box-shadow: none;
        }

        .pagination-controls .btn:disabled::before {
            display: none;
        }

        .page-jump {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: 25px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 500;
        }

        .page-jump-input {
            width: 95px;
            padding: 10px 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .page-jump-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
            font-weight: 400;
        }

        .page-jump-input:hover {
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .page-jump-input:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.8);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2), 0 6px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        /* 按钮图标样式 */

        #advancedSearchBtn::after {
            content: "▼";
            font-size: 0.7em;
            margin-left: 5px;
            transition: transform 0.3s ease;
            display: inline-block;
        }

        #advancedSearchBtn.expanded::after {
            transform: rotate(180deg);
        }



        #addConditionBtn::before {
            content: "+";
            font-weight: bold;
        }

        #firstPageBtn::before {
            content: "";
            margin-right: 3px;
        }

        #prevPageBtn::before {
            content: "";
            margin-right: 3px;
            font-size: 1.2em;
        }

        /* 移除下一页和末页按钮的符号 */
        #nextPageBtn::after, #lastPageBtn::after {
            content: "";
        }

        @media (max-width: 768px) {
            .search-controls {
                flex-direction: column;
            }

            .search-input-container {
                min-width: 100%;
            }

            .cards-container {
                grid-template-columns: 1fr;
            }

            .pagination-info {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }

            .pagination-controls {
                flex-direction: column;
                gap: 15px;
            }

            .page-jump {
                margin-left: 0;
                justify-content: center;
            }

            .page-numbers {
                justify-content: center;
            }

            .condition-row {
                flex-direction: column;
                align-items: stretch;
            }

            .view-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .results-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .modal-info-row {
                flex-direction: column;
            }

            .modal-info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <!-- 加载覆盖层 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载学生信息...</div>
        </div>
    </div>

    <!-- 动态背景装饰 -->
    <div class="bg-decoration bg-circle"></div>
    <div class="bg-decoration bg-circle"></div>
    <div class="bg-decoration bg-circle"></div>
    <div class="bg-decoration bg-circle"></div>

    <div class="container">
        <div class="header">
            <h1>🎓 学生信息查询系统</h1>
            <p>快速查找和管理学生信息数据</p>
            <p>数据库最后更新时间：2025-07-01</p>
        </div>

        <div class="search-section">
            <div class="search-controls">
                <div class="search-input-container">
                    <input type="text" id="searchInput" class="search-input" placeholder="输入学生姓名、学校、班级等关键词进行搜索...">
                    <button type="button" id="clearBtn" class="clear-btn" title="清空搜索">✕</button>
                </div>
                <button type="button" id="searchBtn" class="btn btn-primary">搜索</button>
                <button type="button" id="advancedSearchBtn" class="btn btn-secondary">高级搜索</button>
            </div>

            <!-- 高级搜索区域 -->
            <div id="advancedSearchArea" class="advanced-search-area hidden">
                <h3 style="color: white; margin-bottom: 15px;">高级条件搜索</h3>
                <div id="conditionsContainer"></div>
                <div style="margin-top: 15px;">
                    <button type="button" id="addConditionBtn" class="btn btn-secondary">添加条件</button>
                    <button type="button" id="advancedSearchExecuteBtn" class="btn btn-primary">执行搜索</button>
                </div>
            </div>
        </div>

        <div class="results-section">
            <!-- 数据统计面板 -->
            <div class="stats-panel hidden" id="statsPanel">
                <div class="stat-item">
                    <div class="stat-number" id="totalStudents">-</div>
                    <div class="stat-label">当前结果</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalSchools">-</div>
                    <div class="stat-label">学校数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalClasses">-</div>
                    <div class="stat-label">班级数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="searchResults">-</div>
                    <div class="stat-label">页面显示</div>
                </div>
            </div>

            <div class="results-header">
                <h2>查询结果</h2>
                <div class="view-controls">
                    <div class="view-toggle">
                        <button type="button" id="cardViewBtn" class="view-btn active">⊞ 卡片视图</button>
                        <button type="button" id="tableViewBtn" class="view-btn">☰ 表格视图</button>
                    </div>
                </div>
            </div>

            <div id="loadingState" class="loading hidden">
                <div>⟳ 正在加载数据...</div>
            </div>

            <div id="errorState" class="error hidden"></div>

            <div id="emptyState" class="empty-state hidden">
                <div style="font-size: 3rem; margin-bottom: 20px; color: rgba(255,255,255,0.6);">∅</div>
                <h3>未找到相关数据</h3>
                <p>请尝试调整搜索条件或检查输入的关键词</p>
            </div>

            <div id="cardsContainer" class="cards-container"></div>
            <div id="tableContainer" class="table-container hidden"></div>

            <!-- 分页控件 -->
            <div id="paginationContainer" class="pagination-container hidden">
                <div class="pagination-info">
                    <div class="page-size-selector">
                        <label>每页显示：</label>
                        <select id="pageSizeSelect">
                            <option value="10" selected>10条</option>
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                        </select>
                    </div>
                    <div class="pagination-stats">
                        <span id="paginationStats">第1页，共1页，总计0条记录</span>
                    </div>
                </div>
                <div class="pagination-controls">
                    <button type="button" id="firstPageBtn" class="btn btn-secondary" disabled>首页</button>
                    <button type="button" id="prevPageBtn" class="btn btn-secondary" disabled>上一页</button>
                    <div id="pageNumbers" class="page-numbers"></div>
                    <button type="button" id="nextPageBtn" class="btn btn-secondary" disabled>下一页</button>
                    <button type="button" id="lastPageBtn" class="btn btn-secondary" disabled>末页</button>
                    <div class="page-jump">
                        <span>跳转到</span>
                        <input type="number" id="pageJumpInput" class="page-jump-input" min="1" placeholder="页码">
                        <button type="button" id="pageJumpBtn" class="btn btn-secondary">跳转</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 照片查看器已移除，使用新窗口方式 -->

    <!-- 学生详情模态框 -->
    <div id="studentModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>学生详细信息</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body" id="modalBody"></div>
        </div>
    </div>

    <script>
        // 页面元素
        const elements = {
            searchInput: document.getElementById('searchInput'),
            searchBtn: document.getElementById('searchBtn'),
            advancedSearchBtn: document.getElementById('advancedSearchBtn'),
            clearBtn: document.getElementById('clearBtn'),
            advancedSearchArea: document.getElementById('advancedSearchArea'),
            conditionsContainer: document.getElementById('conditionsContainer'),
            addConditionBtn: document.getElementById('addConditionBtn'),
            advancedSearchExecuteBtn: document.getElementById('advancedSearchExecuteBtn'),
            cardViewBtn: document.getElementById('cardViewBtn'),
            tableViewBtn: document.getElementById('tableViewBtn'),
            loadingState: document.getElementById('loadingState'),
            errorState: document.getElementById('errorState'),
            emptyState: document.getElementById('emptyState'),
            cardsContainer: document.getElementById('cardsContainer'),
            tableContainer: document.getElementById('tableContainer'),

            studentModal: document.getElementById('studentModal'),
            modalBody: document.getElementById('modalBody'),
            // 分页相关元素
            paginationContainer: document.getElementById('paginationContainer'),
            pageSizeSelect: document.getElementById('pageSizeSelect'),
            paginationStats: document.getElementById('paginationStats'),
            firstPageBtn: document.getElementById('firstPageBtn'),
            prevPageBtn: document.getElementById('prevPageBtn'),
            nextPageBtn: document.getElementById('nextPageBtn'),
            lastPageBtn: document.getElementById('lastPageBtn'),
            pageNumbers: document.getElementById('pageNumbers')
        };

        // 当前数据和状态
        let currentData = [];
        let currentView = 'cards';
        let advancedSearchVisible = false;

        // 分页相关变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;
        let totalRecords = 0;

        // 搜索字段选项
        const searchFields = [
            { value: 'name', label: '姓名' },
            { value: 'id_card', label: '身份证号' },
            { value: 'phone', label: '电话' },
            { value: 'school', label: '学校' },
            { value: 'class', label: '班级' },
            { value: 'address', label: '地址' },
            { value: 'parent1_name', label: '监护人1姓名' },
            { value: 'parent1_relation', label: '监护人1关系' },
            { value: 'parent1_phone', label: '监护人1电话' },
            { value: 'parent1_id_card', label: '监护人1身份证' },
            { value: 'parent2_name', label: '监护人2姓名' },
            { value: 'parent2_relation', label: '监护人2关系' },
            { value: 'parent2_phone', label: '监护人2电话' },
            { value: 'parent2_id_card', label: '监护人2身份证' }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示加载动画
            showLoadingOverlay();

            // 延迟加载数据以显示动画效果
            setTimeout(() => {
                loadAllData();
                initializeEventListeners();
                // 移除初始统计更新，只在有数据时更新
            }, 1000);
        });

        // 初始化事件监听器
        function initializeEventListeners() {
            elements.searchBtn.addEventListener('click', () => performFuzzySearch(1));
            elements.advancedSearchBtn.addEventListener('click', toggleAdvancedSearch);
            elements.clearBtn.addEventListener('click', clearSearch);
            elements.addConditionBtn.addEventListener('click', addCondition);
            elements.advancedSearchExecuteBtn.addEventListener('click', () => performAdvancedSearch(1));
            elements.cardViewBtn.addEventListener('click', () => switchView('cards'));
            elements.tableViewBtn.addEventListener('click', () => switchView('table'));

            elements.searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performFuzzySearch(1);
                }
            });

            // 模态框关闭事件
            elements.studentModal.addEventListener('click', function(e) {
                if (e.target === elements.studentModal || e.target.classList.contains('close')) {
                    closeModal();
                }
            });

            // 照片查看器相关代码已移除

            // 分页相关事件监听器
            elements.pageSizeSelect.addEventListener('change', changePageSize);
            elements.firstPageBtn.addEventListener('click', () => goToPage(1));
            elements.prevPageBtn.addEventListener('click', () => goToPage(currentPage - 1));
            elements.nextPageBtn.addEventListener('click', () => goToPage(currentPage + 1));
            elements.lastPageBtn.addEventListener('click', () => goToPage(totalPages));

            // 页面跳转功能
            const pageJumpBtn = document.getElementById('pageJumpBtn');
            const pageJumpInput = document.getElementById('pageJumpInput');

            pageJumpBtn.addEventListener('click', performPageJump);
            pageJumpInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performPageJump();
                }
            });

            // 键盘事件监听 - 照片查看器相关代码已移除

            // 照片点击事件委托 - 暂时禁用
            // document.addEventListener('click', function(e) {
            //     // 只处理在结果区域内的照片点击
            //     const resultsSection = e.target.closest('.results-section');
            //     const modalBody = e.target.closest('.modal-body');
            //
            //     if (resultsSection || modalBody) {
            //         if (e.target.classList.contains('clickable-photo') || e.target.closest('.clickable-photo')) {
            //             e.preventDefault();
            //             e.stopPropagation();
            //
            //             const photoElement = e.target.classList.contains('clickable-photo') ? e.target : e.target.closest('.clickable-photo');
            //             const photoSrc = photoElement.getAttribute('data-photo');
            //             const studentName = photoElement.getAttribute('data-name');
            //
            //             if (photoSrc && studentName) {
            //                 showPhotoModal(photoSrc, studentName);
            //             }
            //         }
            //     }
            // });
        }

        // 加载所有数据
        function loadAllData(page = 1) {
            currentPage = page;
            showLoading();

            const formData = new URLSearchParams();
            formData.append('action', 'get_all');
            formData.append('page', currentPage);
            formData.append('page_size', pageSize);

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                hideLoadingOverlay(); // 隐藏页面加载覆盖层
                if (data.status === 'success') {
                    currentData = data.data;
                    totalRecords = data.total;
                    totalPages = data.total_pages;
                    displayData(currentData);

                    updatePagination();
                    // 初始加载时不显示统计面板，只在搜索时显示
                    hideStatisticsPanel();
                } else {
                    showError('加载数据失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showError('网络错误，请检查连接');
            });
        }

        // 执行模糊搜索
        function performFuzzySearch(page = 1) {
            const searchTerm = elements.searchInput.value.trim();

            if (!searchTerm) {
                loadAllData(page);
                return;
            }

            currentPage = page;
            showLoading();

            const formData = new URLSearchParams();
            formData.append('action', 'search');
            formData.append('search_type', 'fuzzy');
            formData.append('search_term', searchTerm);
            formData.append('page', currentPage);
            formData.append('page_size', pageSize);

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.status === 'success') {
                    currentData = data.data;
                    totalRecords = data.total;
                    totalPages = data.total_pages;
                    displayData(currentData);

                    updatePagination();
                    showStatisticsPanel(); // 显示并更新统计数据
                } else {
                    showError('搜索失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showError('搜索时发生网络错误');
            });
        }

        // 执行高级搜索
        function performAdvancedSearch(page = 1) {
            const conditions = [];
            const conditionRows = elements.conditionsContainer.querySelectorAll('.condition-row');

            conditionRows.forEach(row => {
                const field = row.querySelector('.field-select').value;
                const operator = row.querySelector('.operator-select').value;
                const value = row.querySelector('.value-input').value.trim();

                if (field && operator && value) {
                    conditions.push({ field, operator, value });
                }
            });

            if (conditions.length === 0) {
                showError('请添加至少一个搜索条件');
                return;
            }

            currentPage = page;
            showLoading();

            const formData = new URLSearchParams();
            formData.append('action', 'search');
            formData.append('search_type', 'advanced');
            formData.append('conditions', JSON.stringify(conditions));
            formData.append('page', currentPage);
            formData.append('page_size', pageSize);

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.status === 'success') {
                    currentData = data.data;
                    totalRecords = data.total;
                    totalPages = data.total_pages;
                    displayData(currentData);

                    updatePagination();
                    showStatisticsPanel(); // 显示并更新统计数据
                } else {
                    showError('高级搜索失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                showError('高级搜索时发生网络错误');
            });
        }

        // 清空搜索
        function clearSearch() {
            elements.searchInput.value = '';
            elements.conditionsContainer.innerHTML = '';
            if (advancedSearchVisible) {
                toggleAdvancedSearch();
            }
            hideStatisticsPanel(); // 隐藏统计面板
            loadAllData();
        }

        // 切换高级搜索显示
        function toggleAdvancedSearch() {
            advancedSearchVisible = !advancedSearchVisible;
            if (advancedSearchVisible) {
                elements.advancedSearchArea.classList.remove('hidden');
                elements.advancedSearchBtn.textContent = '高级搜索';//收起高级搜索
                elements.advancedSearchBtn.classList.add('expanded');
                if (elements.conditionsContainer.children.length === 0) {
                    addCondition();
                }
            } else {
                elements.advancedSearchArea.classList.add('hidden');
                elements.advancedSearchBtn.textContent = '高级搜索';
                elements.advancedSearchBtn.classList.remove('expanded');
            }
        }

        // 添加搜索条件
        function addCondition() {
            const conditionRow = document.createElement('div');
            conditionRow.className = 'condition-row';

            conditionRow.innerHTML = `
                <select class="condition-select field-select">
                    <option value="">选择条件</option>
                    ${searchFields.map(field => `<option value="${field.value}">${field.label}</option>`).join('')}
                </select>
                <select class="condition-select operator-select">
                    <option value="equals">等于</option>
                    <option value="not_equals">不等于</option>
                    <option value="contains">包含</option>
                </select>
                <input type="text" class="condition-input value-input" placeholder="输入值">
                <button type="button" class="remove-condition" onclick="removeCondition(this)">×</button>
            `;

            elements.conditionsContainer.appendChild(conditionRow);
        }

        // 移除搜索条件
        function removeCondition(button) {
            button.parentElement.remove();
        }

        // 切换视图
        function switchView(viewType) {
            currentView = viewType;

            if (viewType === 'cards') {
                elements.cardViewBtn.classList.add('active');
                elements.tableViewBtn.classList.remove('active');
                elements.cardsContainer.classList.remove('hidden');
                elements.tableContainer.classList.add('hidden');
            } else {
                elements.tableViewBtn.classList.add('active');
                elements.cardViewBtn.classList.remove('active');
                elements.cardsContainer.classList.add('hidden');
                elements.tableContainer.classList.remove('hidden');
            }

            displayData(currentData);
        }

        // 显示数据
        function displayData(students) {
            hideAllStates();

            if (students.length === 0) {
                // 清空所有容器
                elements.cardsContainer.innerHTML = '';
                elements.tableContainer.innerHTML = '';
                // 显示空状态
                elements.emptyState.classList.remove('hidden');
                return;
            }

            if (currentView === 'cards') {
                displayCardsView(students);
            } else {
                displayTableView(students);
            }
        }

        // 显示卡片视图
        function displayCardsView(students) {
            let html = '';
            students.forEach(student => {
                html += createStudentCard(student);
            });
            elements.cardsContainer.innerHTML = html;
        }

        // 显示表格视图
        function displayTableView(students) {
            let html = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>头像</th>
                            <th>姓名</th>
                            <th>身份证号</th>
                            <th>电话</th>
                            <th>学校</th>
                            <th>班级</th>
                            <th>地址</th>
                            <th>监护人1</th>
                            <th>监护人1电话</th>
                            <th>监护人1身份证</th>
                            <th>监护人2</th>
                            <th>监护人2电话</th>
                            <th>监护人2身份证</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            students.forEach(student => {
                const photoHtml = student.photo_path ?
                    `<img src="http://127.0.0.1/${student.photo_path}" alt="${student.name}" onerror="this.parentElement.innerHTML='👤'">` :
                    '👤';

                const photoCellContent = student.photo_path ?
                    `<div class="photo-cell clickable-photo" onclick="viewPhoto('http://127.0.0.1/${student.photo_path}', '${student.name}')" title="点击查看大图">${photoHtml}</div>` :
                    `<div class="photo-cell">${photoHtml}</div>`;

                html += `
                    <tr>
                        <td>${photoCellContent}</td>
                        <td>${escapeHtml(student.name || '未知')}</td>
                        <td>${escapeHtml(student.id_card || '未填写')}</td>
                        <td>${escapeHtml(student.phone || '未填写')}</td>
                        <td>${escapeHtml(student.school || '未填写')}</td>
                        <td>${escapeHtml(student.class || '未填写')}</td>
                        <td>${escapeHtml(student.address || '未填写')}</td>
                        <td>${escapeHtml(student.parent1_name || '未填写')} (${escapeHtml(student.parent1_relation || '未知')})</td>
                        <td>${escapeHtml(student.parent1_phone || '未填写')}</td>
                        <td>${escapeHtml(student.parent1_id_card || '未填写')}</td>
                        <td>${escapeHtml(student.parent2_name || '未填写')} ${student.parent2_relation ? '(' + escapeHtml(student.parent2_relation) + ')' : ''}</td>
                        <td>${escapeHtml(student.parent2_phone || '未填写')}</td>
                        <td>${escapeHtml(student.parent2_id_card || '未填写')}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            elements.tableContainer.innerHTML = html;
        }

        // 创建学生卡片（简化版本）
        function createStudentCard(student) {
            const photoHtml = student.photo_path ?
                `<img src="http://127.0.0.1/${student.photo_path}" alt="${student.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;" onerror="this.parentElement.innerHTML='👤'">` :
                '👤';

            const photoClickable = `<div class="photo-container">${photoHtml}</div>`;

            return `
                <div class="student-card" onclick="showStudentDetails(${student.id})" style="cursor: pointer;">
                    <div class="card-header">
                        ${photoClickable}
                        <div class="student-info">
                            <h3>${escapeHtml(student.name || '未知')}</h3>
                            <div class="school-class">${escapeHtml(student.school || '未填写')} - ${escapeHtml(student.class || '未填写')}</div>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="info-row">
                            <span class="info-label">身份证号:</span>
                            <span class="info-value">${escapeHtml(student.id_card || '未填写')}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">电话:</span>
                            <span class="info-value">${escapeHtml(student.phone || '未填写')}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">家庭住址:</span>
                            <span class="info-value">${escapeHtml(student.address || '未填写')}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示学生详情
        function showStudentDetails(studentId) {
            const student = currentData.find(s => s.id == studentId);
            if (!student) return;

            const photoHtml = student.photo_path ?
                `<div class="photo-toggle-container">
                    <img id="studentPhoto_${student.id}" src="http://127.0.0.1/${student.photo_path}" alt="${student.name}"
                         style="width: 80px; height: 80px; object-fit: cover; border-radius: 50%; margin-bottom: 20px; cursor: pointer; transition: all 0.3s ease;"
                         class="clickable-photo" onclick="togglePhotoSize(${student.id})" title="点击放大/缩小照片" onerror="this.style.display='none'">
                    <div style="font-size: 12px; color: #666; text-align: center;">点击照片放大</div>
                </div>` :
                '<div style="width: 80px; height: 80px; background: #f0f0f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 32px; margin-bottom: 20px;">👤</div>';

            elements.modalBody.innerHTML = `
                <div style="text-align: center; margin-bottom: 25px;">
                    ${photoHtml}
                    <h3 style="margin: 0; color: #333;">${escapeHtml(student.name || '未知')}</h3>
                </div>

                <div class="modal-info-row">
                    <div class="modal-info-label">身份证号:</div>
                    <div class="modal-info-value">${escapeHtml(student.id_card || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">电话:</div>
                    <div class="modal-info-value">${escapeHtml(student.phone || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">学校:</div>
                    <div class="modal-info-value">${escapeHtml(student.school || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">班级:</div>
                    <div class="modal-info-value">${escapeHtml(student.class || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">地址:</div>
                    <div class="modal-info-value">${escapeHtml(student.address || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人1姓名:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent1_name || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人1关系:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent1_relation || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人1电话:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent1_phone || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人1身份证:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent1_id_card || '未填写')}</div>
                </div>
                ${student.parent2_name ? `
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人2姓名:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent2_name)}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人2关系:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent2_relation || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人2电话:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent2_phone || '未填写')}</div>
                </div>
                <div class="modal-info-row">
                    <div class="modal-info-label">监护人2身份证:</div>
                    <div class="modal-info-value">${escapeHtml(student.parent2_id_card || '未填写')}</div>
                </div>
                ` : ''}
            `;

            elements.studentModal.classList.remove('hidden');
        }

        // 关闭模态框
        function closeModal() {
            elements.studentModal.classList.add('hidden');
        }

        // 照片大小切换功能
        let photoEnlarged = {};

        function togglePhotoSize(studentId) {
            const photo = document.getElementById(`studentPhoto_${studentId}`);
            if (!photo) return;

            const isEnlarged = photoEnlarged[studentId] || false;

            if (isEnlarged) {
                // 缩小照片
                photo.style.width = '80px';
                photo.style.height = '80px';
                photo.style.borderRadius = '50%';
                photo.style.objectFit = 'cover';
                photo.title = '点击放大照片';
                photoEnlarged[studentId] = false;
            } else {
                // 放大照片
                photo.style.width = '300px';
                photo.style.height = 'auto';
                photo.style.borderRadius = '10px';
                photo.style.objectFit = 'contain';
                photo.title = '点击缩小照片';
                photoEnlarged[studentId] = true;
            }
        }

        // 保留新窗口方式作为备用
        function viewPhoto(imageSrc, studentName) {
            // 计算窗口居中位置
            const width = 800;
            const height = 600;
            const left = (screen.width - width) / 2;
            const top = (screen.height - height) / 2;

            const newWindow = window.open('', '_blank', `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`);
            newWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${studentName} 的照片</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 20px;
                            background: #000;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                            font-family: Arial, sans-serif;
                        }
                        .photo-container {
                            text-align: center;
                            color: white;
                        }
                        img {
                            max-width: 100%;
                            max-height: 80vh;
                            border-radius: 10px;
                            box-shadow: 0 10px 30px rgba(255,255,255,0.1);
                        }
                        h2 {
                            margin-top: 20px;
                            color: white;
                        }
                    </style>
                </head>
                <body>
                    <div class="photo-container">
                        <img src="${imageSrc}" alt="${studentName}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleaYvuekuuWbvueJhzwvdGV4dD48L3N2Zz4=';">
                        <h2>${studentName} 的照片</h2>
                    </div>
                </body>
                </html>
            `);
            newWindow.document.close();
        }

        // 工具函数
        function showLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'flex';
            }
        }

        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // 标记是否有活跃的搜索
        let isSearchActive = false;

        function updateStatistics() {
            const statsPanel = document.getElementById('statsPanel');

            // 只在有搜索活动且有数据时显示统计面板
            if (isSearchActive && currentData && currentData.length > 0) {
                // 获取全部搜索结果进行准确统计
                fetchAllSearchResultsForStats();

                // 显示统计面板
                statsPanel.classList.remove('hidden');
            } else {
                // 隐藏统计面板
                statsPanel.classList.add('hidden');
            }
        }

        function hideStatisticsPanel() {
            const statsPanel = document.getElementById('statsPanel');
            isSearchActive = false;
            statsPanel.classList.add('hidden');
        }

        function showStatisticsPanel() {
            isSearchActive = true;
            updateStatistics();
        }

        // 获取全部搜索结果用于准确统计
        function fetchAllSearchResultsForStats() {
            const searchTerm = elements.searchInput.value.trim();
            const isAdvancedSearch = !elements.advancedSearchArea.classList.contains('hidden') &&
                                   elements.conditionsContainer.children.length > 0;

            const formData = new URLSearchParams();

            if (isAdvancedSearch) {
                // 高级搜索 - 复制当前的搜索条件
                formData.append('action', 'search');
                formData.append('search_type', 'advanced');
                const conditions = [];
                const conditionRows = elements.conditionsContainer.querySelectorAll('.condition-row');

                conditionRows.forEach(row => {
                    const field = row.querySelector('.field-select').value;
                    const operator = row.querySelector('.operator-select').value;
                    const value = row.querySelector('.value-input').value.trim();

                    if (field && operator && value) {
                        conditions.push({ field, operator, value });
                    }
                });

                formData.append('conditions', JSON.stringify(conditions));
            } else if (searchTerm) {
                // 模糊搜索
                formData.append('action', 'search');
                formData.append('search_type', 'fuzzy');
                formData.append('search_term', searchTerm);
            } else {
                // 如果没有搜索条件，直接使用当前数据
                calculateStatsFromCurrentData();
                return;
            }

            // 获取数据用于统计 - 使用大page_size获取尽可能多的数据
            formData.append('page', 1);
            formData.append('page_size', 10000); // 大幅增加page_size以获取完整数据

            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('=== 统计API响应 ===');
                console.log('响应状态:', data.status);
                console.log('响应数据量:', data.data ? data.data.length : 0);
                console.log('总记录数:', data.total);
                console.log('总页数:', data.total_pages);

                if (data.status === 'success' && data.data) {
                    console.log('✅ API请求成功，使用全部数据进行统计');
                    console.log('获取到的数据量:', data.data.length, '总记录数:', data.total);

                    // 检查是否获取了完整数据
                    if (data.data.length < data.total) {
                        console.warn('⚠️ 警告：只获取了部分数据！', `${data.data.length}/${data.total}`);
                    }

                    // 使用获取到的全部数据进行统计
                    calculateStatsFromAllData(data.data, data.total);
                } else {
                    console.log('❌ API请求失败，使用当前页面数据');
                    console.log('错误信息:', data.message || '未知错误');
                    // 如果获取失败，使用当前数据
                    calculateStatsFromCurrentData();
                }
            })
            .catch(error => {
                console.error('获取统计数据失败:', error);
                // 如果请求失败，使用当前数据
                calculateStatsFromCurrentData();
            });
        }



        // 基于全部搜索结果数据进行统计
        function calculateStatsFromAllData(allData, totalCount) {
            // 学校统计 - 基于全部搜索结果
            const validSchools = allData
                .map(s => s.school)
                .filter(school => school && school.trim() !== '' &&
                                 school !== 'undefined' && school !== 'null');
            const schools = new Set(validSchools);

            // 班级统计 - 修复逻辑
            let classes;
            if (schools.size === 1) {
                // 如果只有一个学校，只统计班级名称
                const validClasses = allData
                    .filter(s => s.class &&
                               s.class.trim() !== '' &&
                               s.class !== 'undefined' &&
                               s.class !== 'null')
                    .map(s => s.class.trim());
                classes = new Set(validClasses);
            } else {
                // 如果有多个学校，使用学校-班级组合
                const validClasses = allData
                    .filter(s => s.school && s.class &&
                               s.school.trim() !== '' && s.class.trim() !== '' &&
                               s.school !== 'undefined' && s.class !== 'undefined' &&
                               s.school !== 'null' && s.class !== 'null')
                    .map(s => `${s.school.trim()}-${s.class.trim()}`);
                classes = new Set(validClasses);
            }

            // 当前页面显示数量
            const currentPageCount = currentData.length;

            // 详细调试信息
            console.log('=== 统计调试信息 ===');
            console.log('数据总量:', allData.length);
            console.log('学校数量:', schools.size);
            console.log('所有学校列表:', Array.from(schools));
            console.log('班级数量:', classes.size);
            console.log('统计模式:', schools.size === 1 ? '单学校模式' : '多学校模式');

            if (schools.size === 1) {
                console.log('单学校班级列表:', Array.from(classes));
            } else {
                console.log('多学校班级组合（前20个）:', Array.from(classes).slice(0, 20));
            }

            // 显示每个学校的班级分布
            schools.forEach(school => {
                const schoolClasses = allData
                    .filter(s => s.school && s.school.trim() === school && s.class && s.class.trim() !== '')
                    .map(s => s.class.trim());
                const uniqueSchoolClasses = new Set(schoolClasses);
                console.log(`${school} 的班级数量:`, uniqueSchoolClasses.size, '班级列表:', Array.from(uniqueSchoolClasses));
            });

            // 更新显示
            document.getElementById('totalStudents').textContent = totalCount || allData.length;
            document.getElementById('totalSchools').textContent = schools.size;
            document.getElementById('totalClasses').textContent = classes.size;
            document.getElementById('searchResults').textContent = currentPageCount;
        }

        // 基于当前页面数据进行统计（备用方案）
        function calculateStatsFromCurrentData() {
            // 学校统计 - 基于当前页面数据
            const validSchools = currentData
                .map(s => s.school)
                .filter(school => school && school.trim() !== '' &&
                                 school !== 'undefined' && school !== 'null');
            const schools = new Set(validSchools);

            // 班级统计 - 修复逻辑（与主统计函数保持一致）
            let classes;
            if (schools.size === 1) {
                // 如果只有一个学校，只统计班级名称
                const validClasses = currentData
                    .filter(s => s.class &&
                               s.class.trim() !== '' &&
                               s.class !== 'undefined' &&
                               s.class !== 'null')
                    .map(s => s.class.trim());
                classes = new Set(validClasses);
            } else {
                // 如果有多个学校，使用学校-班级组合
                const validClasses = currentData
                    .filter(s => s.school && s.class &&
                               s.school.trim() !== '' && s.class.trim() !== '' &&
                               s.school !== 'undefined' && s.class !== 'undefined' &&
                               s.school !== 'null' && s.class !== 'null')
                    .map(s => `${s.school.trim()}-${s.class.trim()}`);
                classes = new Set(validClasses);
            }

            // 当前页面显示数量
            const currentPageCount = currentData.length;

            // 调试信息（备用方案）
            console.log('备用统计调试信息:', {
                学校数量: schools.size,
                学校列表: Array.from(schools),
                班级数量: classes.size,
                班级列表: Array.from(classes).slice(0, 10),
                数据来源: '当前页面数据（备用方案）'
            });

            // 更新显示（注明这是基于当前页面的估算）
            document.getElementById('totalStudents').textContent = totalRecords || currentPageCount;
            document.getElementById('totalSchools').textContent = schools.size + '*';
            document.getElementById('totalClasses').textContent = classes.size + '*';
            document.getElementById('searchResults').textContent = currentPageCount;
        }

        function showLoading() {
            hideAllStates();
            elements.loadingState.classList.remove('hidden');
        }

        function hideLoading() {
            elements.loadingState.classList.add('hidden');
        }

        function showError(message) {
            hideAllStates();
            elements.errorState.textContent = message;
            elements.errorState.classList.remove('hidden');
        }

        function hideAllStates() {
            elements.loadingState.classList.add('hidden');
            elements.errorState.classList.add('hidden');
            elements.emptyState.classList.add('hidden');
        }



        // 更新分页控件
        function updatePagination() {
            if (totalPages <= 1) {
                elements.paginationContainer.classList.add('hidden');
                return;
            }

            elements.paginationContainer.classList.remove('hidden');

            // 更新分页统计信息
            elements.paginationStats.textContent = `第${currentPage}页，共${totalPages}页，总计${totalRecords}条记录`;

            // 更新按钮状态
            elements.firstPageBtn.disabled = currentPage === 1;
            elements.prevPageBtn.disabled = currentPage === 1;
            elements.nextPageBtn.disabled = currentPage === totalPages;
            elements.lastPageBtn.disabled = currentPage === totalPages;

            // 更新跳转输入框的最大值
            const pageJumpInput = document.getElementById('pageJumpInput');
            if (pageJumpInput) {
                pageJumpInput.max = totalPages;
                pageJumpInput.placeholder = `1-${totalPages}`;
            }

            // 生成页码按钮
            generatePageNumbers();
        }

        // 生成页码按钮
        function generatePageNumbers() {
            elements.pageNumbers.innerHTML = '';

            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);

            // 确保显示至少5个页码（如果总页数允许）
            if (endPage - startPage < 4) {
                if (startPage === 1) {
                    endPage = Math.min(totalPages, startPage + 4);
                } else if (endPage === totalPages) {
                    startPage = Math.max(1, endPage - 4);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('div');
                pageBtn.className = `page-number ${i === currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => goToPage(i);
                elements.pageNumbers.appendChild(pageBtn);
            }
        }

        // 跳转到指定页面
        function goToPage(page) {
            if (page === currentPage || page < 1 || page > totalPages) return;

            // 根据当前搜索状态调用相应的函数
            const searchTerm = elements.searchInput.value.trim();
            const isAdvancedSearch = !elements.advancedSearchArea.classList.contains('hidden') &&
                                   elements.conditionsContainer.children.length > 0;

            if (isAdvancedSearch) {
                performAdvancedSearch(page);
            } else if (searchTerm) {
                performFuzzySearch(page);
            } else {
                loadAllData(page);
            }
        }

        // 执行页面跳转
        function performPageJump() {
            const pageJumpInput = document.getElementById('pageJumpInput');
            const targetPage = parseInt(pageJumpInput.value);

            // 验证输入
            if (isNaN(targetPage)) {
                showError('请输入有效的页码数字');
                pageJumpInput.focus();
                return;
            }

            if (targetPage < 1) {
                showError('页码不能小于1');
                pageJumpInput.focus();
                return;
            }

            if (targetPage > totalPages) {
                showError(`页码不能大于${totalPages}`);
                pageJumpInput.focus();
                return;
            }

            // 清空输入框
            pageJumpInput.value = '';

            // 跳转到指定页面
            goToPage(targetPage);
        }

        // 更改每页显示数量
        function changePageSize() {
            pageSize = parseInt(elements.pageSizeSelect.value);
            currentPage = 1; // 重置到第一页

            // 重新执行当前搜索
            const searchTerm = elements.searchInput.value.trim();
            const isAdvancedSearch = !elements.advancedSearchArea.classList.contains('hidden') &&
                                   elements.conditionsContainer.children.length > 0;

            if (isAdvancedSearch) {
                performAdvancedSearch(1);
            } else if (searchTerm) {
                performFuzzySearch(1);
            } else {
                loadAllData(1);
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
