<?php
/**
 * 数据库配置文件
 * 适用于openEuler 24操作系统
 */

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'taskmgr');
define('DB_USER', 'taskmgr_user');
define('DB_PASS', 'your_password_here');
define('DB_CHARSET', 'utf8mb4');

// 数据库连接选项
define('DB_OPTIONS', [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);

// 应用配置
define('APP_NAME', 'TaskMgr - 计划任务管理器');
define('APP_VERSION', '1.0.0');
define('APP_DEBUG', false);

// 安全配置
define('SESSION_NAME', 'TASKMGR_SESSION');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_HASH_ALGO', PASSWORD_ARGON2ID);

// 日志配置
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('LOG_MAX_FILES', 10);

// 任务配置
define('TASK_MAX_EXECUTION_TIME', 3600); // 1小时
define('TASK_LOG_RETENTION_DAYS', 30);
define('CRON_LOCK_FILE', __DIR__ . '/../logs/cron.lock');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

/**
 * 获取数据库连接
 */
function getDbConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS, DB_OPTIONS);
        } catch (PDOException $e) {
            error_log("数据库连接失败: " . $e->getMessage());
            throw new Exception("数据库连接失败");
        }
    }
    
    return $pdo;
}

/**
 * 检查数据库连接
 */
function checkDbConnection() {
    try {
        $pdo = getDbConnection();
        $pdo->query("SELECT 1");
        return true;
    } catch (Exception $e) {
        return false;
    }
}
