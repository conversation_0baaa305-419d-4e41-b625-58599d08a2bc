<?php
require_once '../conn_waf.php';
//引入waf并检测输入参数是否有注入风险

// 设置响应头为JSON格式
header('Content-Type: application/json; charset=utf-8');

try {
    // 查询最大的值班时间
    $sql = "SELECT MAX(sched_date) as latest_time FROM `6_duty_sched`";
    $stmt = $conn->prepare($sql);

    // 检查prepare是否成功
    if (!$stmt) {
        error_log("SQL准备失败: " . $conn->error . " SQL: " . $sql);
        echo json_encode([
            'status' => 0, 
            'message' => '数据库准备失败: ' . $conn->error
        ]);
        exit;
    }

    // 执行查询
    if (!$stmt->execute()) {
        error_log("SQL执行失败: " . $stmt->error);
        echo json_encode([
            'status' => 0, 
            'message' => '数据库查询失败'
        ]);
        exit;
    }

    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    // 检查是否有数据
    if (!$row || $row['latest_time'] === null) {
        echo json_encode([
            'status' => 0, 
            'message' => '暂无值班数据'
        ]);
        exit;
    }

    // 返回成功结果
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'latestTime' => $row['latest_time']
    ]);

} catch (Exception $e) {
    // 捕获异常并记录日志
    error_log("获取最大值班时间异常: " . $e->getMessage());
    echo json_encode([
        'status' => 0,
        'message' => '系统异常，请稍后重试'
    ]);
} finally {
    // 关闭数据库连接
    if (isset($stmt)) {
        $stmt->close();
    }
    if (isset($conn)) {
        $conn->close();
    }
}
?>
