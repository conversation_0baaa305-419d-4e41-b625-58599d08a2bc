<?php
/**
 * 文件网盘系统 - 管理员批量删除文件API
 * 创建时间: 2025-06-27
 * 更新时间: 2025-07-08
 * 功能：实现完整的文件删除（数据库+物理文件+相关数据清理）
 */

require_once '../functions.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => '仅支持POST请求'], 405);
}

// 检查用户是否已登录且为管理员
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => '请先登录'], 401);
}

if (!isAdmin()) {
    jsonResponse(['success' => false, 'message' => '访问被拒绝：您没有管理员权限'], 403);
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => '无效的请求令牌'], 400);
}

// 检查是否有确认参数（安全机制）
// 支持两种模式：兼容模式（与现有界面兼容）和增强模式（需要明确确认）
$confirmed = isset($_POST['confirmed']) && $_POST['confirmed'] === 'true';
$compatibilityMode = isset($_POST['compatibility_mode']) && $_POST['compatibility_mode'] === 'true';

// 兼容模式：为了与现有管理界面兼容，自动视为已确认（仅软删除）
if (!$confirmed && !$compatibilityMode) {
    jsonResponse(['success' => false, 'message' => '此操作需要确认。请确认您要永久删除这些文件。', 'require_confirmation' => true], 400);
}

// 获取文件ID列表
$fileIdsString = trim($_POST['file_ids'] ?? '');
if (empty($fileIdsString)) {
    jsonResponse(['success' => false, 'message' => '请选择要删除的文件'], 400);
}

// 解析文件ID
$fileIdArray = array_filter(array_map('intval', explode(',', $fileIdsString)));
if (empty($fileIdArray)) {
    jsonResponse(['success' => false, 'message' => '无效的文件ID'], 400);
}

// 限制批量操作数量
if (count($fileIdArray) > 50) {
    jsonResponse(['success' => false, 'message' => '单次最多只能删除50个文件'], 400);
}

// 获取删除类型（软删除或硬删除）
$deleteType = $_POST['delete_type'] ?? 'soft';
if (!in_array($deleteType, ['soft', 'hard'])) {
    jsonResponse(['success' => false, 'message' => '无效的删除类型'], 400);
}

// 兼容模式下强制使用软删除，确保与现有界面兼容
if ($compatibilityMode && $deleteType === 'hard') {
    $deleteType = 'soft';
}

// 硬删除需要额外确认（兼容模式下不会执行硬删除）
if ($deleteType === 'hard') {
    $hardConfirmed = isset($_POST['hard_confirmed']) && $_POST['hard_confirmed'] === 'true';
    if (!$hardConfirmed) {
        jsonResponse([
            'success' => false,
            'message' => '硬删除将永久删除文件，无法恢复。请再次确认。',
            'require_hard_confirmation' => true
        ], 400);
    }
}

try {
    $pdo->beginTransaction();

    $deletedCount = 0;
    $errors = [];
    $deletedFiles = [];
    $fileDeleteErrors = [];
    $dataCleanupErrors = [];

    foreach ($fileIdArray as $fileId) {
        try {
            // 检查文件是否存在
            $checkCondition = $deleteType === 'soft' ? 'f.is_deleted = 0' : '1=1';
            $checkStmt = $pdo->prepare("
                SELECT f.file_id, f.original_name, f.file_path, f.file_size, f.is_deleted, u.name as username
                FROM filecloud_info f
                LEFT JOIN 3_user u ON f.user_id = u.id
                WHERE f.file_id = ? AND {$checkCondition}
            ");
            $checkStmt->execute([$fileId]);
            $file = $checkStmt->fetch();

            if (!$file) {
                $errors[] = "文件 ID {$fileId} 不存在" . ($deleteType === 'soft' ? '或已被删除' : '');
                continue;
            }

            $fileInfo = [
                'file_id' => $fileId,
                'original_name' => $file['original_name'],
                'username' => $file['username'],
                'file_path' => $file['file_path'],
                'file_size' => $file['file_size'],
                'was_soft_deleted' => $file['is_deleted'] == 1
            ];

            if ($deleteType === 'soft') {
                // 软删除：只标记为已删除
                $deleteStmt = $pdo->prepare("
                    UPDATE filecloud_info
                    SET is_deleted = 1
                    WHERE file_id = ?
                ");

                if ($deleteStmt->execute([$fileId])) {
                    $deletedCount++;
                    $deletedFiles[] = $fileInfo;
                } else {
                    $errors[] = "软删除文件 {$file['original_name']} 失败";
                }

            } else {
                // 硬删除：删除物理文件和数据库记录
                $physicalDeleteSuccess = true;
                $dataDeleteSuccess = true;

                // 1. 先尝试删除物理文件
                if (!empty($file['file_path']) && file_exists($file['file_path'])) {
                    $deleteResult = safeDeleteFile($file['file_path']);
                    if (!$deleteResult['success']) {
                        $physicalDeleteSuccess = false;
                        $fileDeleteErrors[] = "文件 {$file['original_name']}: " . $deleteResult['message'];
                    }
                }

                // 2. 如果物理文件删除成功（或文件不存在），则删除数据库记录
                if ($physicalDeleteSuccess) {
                    $cleanupResult = cleanupFileData($pdo, $fileId);
                    if ($cleanupResult['success']) {
                        $deletedCount++;
                        $fileInfo['cleaned_tables'] = $cleanupResult['cleaned_tables'];
                        $deletedFiles[] = $fileInfo;
                    } else {
                        $dataDeleteSuccess = false;
                        $dataCleanupErrors[] = "文件 {$file['original_name']}: " . $cleanupResult['message'];
                    }
                }

                // 如果任一步骤失败，记录错误
                if (!$physicalDeleteSuccess || !$dataDeleteSuccess) {
                    $errors[] = "硬删除文件 {$file['original_name']} 失败";
                }
            }

        } catch (Exception $e) {
            $errors[] = "处理文件 ID {$fileId} 时出错: " . $e->getMessage();
        }
    }

    if ($deletedCount > 0) {
        $pdo->commit();

        // 记录操作日志
        $currentUserId = $_SESSION['user_id'];
        $currentUsername = $_SESSION['username'] ?? '管理员';
        $currentIp = getClientRealIP();

        try {
            $logDetails = [
                'delete_type' => $deleteType,
                'deleted_count' => $deletedCount,
                'total_requested' => count($fileIdArray),
                'deleted_files' => array_map(function($file) {
                    return [
                        'file_id' => $file['file_id'],
                        'original_name' => $file['original_name'],
                        'username' => $file['username']
                    ];
                }, $deletedFiles),
                'errors_count' => count($errors),
                'operator' => $currentUsername
            ];

            $operationType = $deleteType === 'soft' ? 'admin_batch_soft_delete_files' : 'admin_batch_hard_delete_files';
            logAdminAction($currentUserId, $operationType, $logDetails, $currentIp);

        } catch (Exception $e) {
            // 日志记录失败不影响主要操作，但记录到错误日志
            error_log("Admin action log failed: " . $e->getMessage());
        }

        $message = "成功" . ($deleteType === 'soft' ? '软删除' : '硬删除') . " {$deletedCount} 个文件";

        if (!empty($errors)) {
            $message .= "，但有 " . count($errors) . " 个操作失败";
        }

        $response = [
            'success' => true,
            'message' => $message,
            'delete_type' => $deleteType,
            'deleted_count' => $deletedCount,
            'total_requested' => count($fileIdArray),
            'deleted_files' => $deletedFiles,
            'errors' => $errors
        ];

        // 添加详细的错误信息
        if (!empty($fileDeleteErrors)) {
            $response['file_delete_errors'] = $fileDeleteErrors;
        }
        if (!empty($dataCleanupErrors)) {
            $response['data_cleanup_errors'] = $dataCleanupErrors;
        }

        jsonResponse($response);

    } else {
        $pdo->rollback();
        $errorMessage = !empty($errors) ? implode('; ', $errors) : '没有文件被删除';
        jsonResponse(['success' => false, 'message' => $errorMessage], 400);
    }

} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }

    // 记录错误日志
    error_log("Admin batch delete files error: " . $e->getMessage());

    // 记录失败的操作日志
    try {
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $currentIp = getClientRealIP();
        $errorDetails = [
            'delete_type' => $deleteType ?? 'unknown',
            'requested_files' => $fileIdArray ?? [],
            'error_message' => $e->getMessage(),
            'operator' => $_SESSION['username'] ?? '未知用户'
        ];
        logAdminAction($currentUserId, 'admin_batch_delete_files_error', $errorDetails, $currentIp);
    } catch (Exception $logError) {
        error_log("Failed to log admin action error: " . $logError->getMessage());
    }

    jsonResponse(['success' => false, 'message' => '操作失败: 系统错误'], 500);
}
?>
