# 文件网盘系统 (FileCloud)

一个功能完整、界面美观的PHP文件网盘系统，支持文件上传、分享、下载等核心功能。

## 🌟 主要特性

### 核心功能
- **文件上传** - 支持拖拽上传，多种文件格式，实时上传进度
- **分享码下载** - 6位随机分享码，支持过期时间设置
- **用户间分享** - 指定用户分享，权限控制
- **管理员后台** - 系统配置、用户管理、数据统计

### 技术特点
- **响应式设计** - Bootstrap 5，适配各种设备
- **安全可靠** - SQL注入防护、XSS防护、CSRF令牌
- **用户友好** - 直观的操作界面，完善的提示信息
- **高性能** - 优化的数据库查询，高效的文件存储

## 🚀 快速开始

### 系统要求
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx Web服务器
- 支持文件上传的PHP配置

### 安装步骤

1. **下载源码**
```bash
git clone [repository-url] filecloud
cd filecloud
```

2. **配置数据库**
```sql
-- 导入数据库结构
mysql -u root -p < database_schema.sql
```

3. **配置文件**
编辑 `config.php` 文件，修改数据库连接信息：
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'filecloud');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

4. **设置目录权限**
```bash
chmod 755 uploads/
chmod 755 assets/
```

5. **访问系统**
在浏览器中访问：`http://your-domain.com/filecloud/`

### 默认账户
- **管理员**: `admin` / `admin123`
- **普通用户**: `user` / `user123`

## 📁 目录结构

```
filecloud/
├── index.php              # 主界面
├── upload.php             # 文件上传
├── download.php           # 分享码下载
├── shared.php             # 与我共享
├── admin.php              # 管理员后台
├── login.php              # 登录页面
├── logout.php             # 退出登录
├── profile.php            # 个人资料
├── config.php             # 配置文件
├── functions.php          # 公共函数
├── api/                   # API接口
│   ├── share.php         # 文件分享
│   ├── file_info.php     # 文件信息
│   └── remove_share.php  # 取消分享
├── assets/               # 静态资源
│   ├── css/
│   │   └── style.css    # 主样式表
│   └── js/
│       └── main.js      # 主JS文件
└── uploads/             # 文件上传目录
```

## 🔧 配置说明

### 基本配置 (config.php)
```php
// 文件上传限制
define('MAX_FILE_SIZE', 52428800);  // 50MB
define('ALLOWED_EXTENSIONS', [
    'jpg', 'jpeg', 'png', 'gif',   // 图片
    'pdf', 'doc', 'docx',          // 文档
    'xls', 'xlsx', 'ppt', 'pptx',  // Office
    'txt', 'zip', 'rar'            // 其他
]);

// 分享设置
define('SHARE_CODE_LENGTH', 6);     // 分享码长度
define('DEFAULT_EXPIRE_DAYS', 30);  // 默认过期天数
```

### PHP配置建议
```ini
; php.ini
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 256M
```

## 🗄️ 数据库表结构

### filecloud_info (文件信息表)
- `file_id` - 文件ID (主键)
- `user_id` - 上传用户ID
- `original_name` - 原始文件名
- `stored_name` - 存储文件名
- `file_size` - 文件大小
- `share_code` - 分享码
- `expiration_time` - 过期时间

### filecloud_share (文件分享表)
- `share_id` - 分享ID (主键)
- `file_id` - 文件ID
- `from_user_id` - 分享者ID
- `to_user_id` - 接收者ID
- `share_time` - 分享时间

### filecloud_config (系统配置表)
- `config_id` - 配置ID (主键)
- `config_key` - 配置键
- `config_value` - 配置值
- `config_desc` - 配置描述

## 🔐 安全特性

### 文件安全
- 文件类型白名单验证
- 文件大小限制
- 安全的文件名生成
- 防止目录遍历攻击

### 用户安全
- CSRF令牌验证
- SQL注入防护 (PDO预处理)
- XSS过滤 (htmlspecialchars)
- 会话安全管理

### 系统安全
- 权限验证
- 错误日志记录
- 安全的文件下载
- 数据库连接加密

## 🎨 UI特性

### 响应式设计
- Bootstrap 5框架
- 移动端友好
- 现代化界面
- 流畅动画效果

### 用户体验
- 拖拽上传
- 实时进度显示
- 智能提示信息
- 键盘快捷键

## 🔌 API接口

### 文件分享 API
```
POST /api/share.php
参数: file_id, user_id, csrf_token
返回: JSON格式结果
```

### 文件信息 API
```
GET /api/file_info.php?file_id=123
返回: 文件详细信息
```

## 🛠️ 自定义开发

### 添加新的文件类型
1. 修改 `config.php` 中的 `ALLOWED_EXTENSIONS`
2. 在 `assets/css/style.css` 中添加对应图标样式
3. 更新 `functions.php` 中的 `getMimeType()` 函数

### 集成现有用户系统
1. 修改 `login.php` 中的认证逻辑
2. 调整 `functions.php` 中的 `isLoggedIn()` 和 `isAdmin()` 函数
3. 更新会话变量的使用

### 添加新功能
1. 在对应页面添加UI界面
2. 创建API处理文件
3. 更新数据库表结构（如需要）
4. 添加相应的权限检查

## 📊 性能优化

### 数据库优化
- 合理的索引设计
- 分页查询减少内存占用
- 预处理语句提高安全性

### 文件存储优化
- 按日期分目录存储
- 唯一文件名避免冲突
- 大文件分块上传（可扩展）

### 前端优化
- CDN加速静态资源
- 图片懒加载
- JavaScript代码压缩

## 🚨 常见问题

### 上传失败
1. 检查PHP文件上传限制
2. 确认目录写入权限
3. 查看错误日志

### 分享码无效
1. 检查分享码格式
2. 确认文件是否被删除
3. 验证分享码是否过期

### 权限错误
1. 检查用户登录状态
2. 确认管理员权限设置
3. 验证CSRF令牌

## 📝 更新日志

### v1.0.0 (2025-06-23)
- ✨ 首次发布
- 🎉 完整的文件上传下载功能
- 🔒 安全的用户权限系统
- 📱 响应式用户界面
- 🛡️ 完善的安全防护

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进这个项目。

## 📞 支持

如果您在使用过程中遇到问题，请：
1. 查看本文档的常见问题部分
2. 搜索已有的 Issue
3. 创建新的 Issue 描述问题

---

**开发团队** | 2025年6月23日
