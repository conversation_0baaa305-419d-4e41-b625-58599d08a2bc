<?php
/**
 * 文件网盘系统 - 文件分享API（支持多用户）
 * 创建时间: 2025-06-23
 * 更新时间: 2025-07-15
 */

require_once '../functions.php';

header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => '请先登录'], 401);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => '无效的请求方法'], 405);
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => '无效的请求'], 400);
}

$fileId = (int)($_POST['file_id'] ?? 0);
$toUserIds = $_POST['user_ids'] ?? [];
$fromUserId = $_SESSION['user_id'];

// 验证文件ID
if ($fileId <= 0) {
    jsonResponse(['success' => false, 'message' => '参数错误：无效的文件ID'], 400);
}

// 验证用户ID数组
if (empty($toUserIds)) {
    jsonResponse(['success' => false, 'message' => '请选择至少一个用户进行分享'], 400);
}

try {
    // 检查文件是否存在且属于当前用户
    $stmt = $pdo->prepare("
        SELECT file_id FROM filecloud_info 
        WHERE file_id = ? AND user_id = ? AND is_deleted = 0
    ");
    $stmt->execute([$fileId, $fromUserId]);
    
    if (!$stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => '文件不存在或无权限'], 404);
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    $successCount = 0;
    $skipCount = 0;
    $errorMessages = [];
    
    // 准备查询语句
    $checkStmt = $pdo->prepare("
        SELECT share_id FROM filecloud_share 
        WHERE file_id = ? AND from_user_id = ? AND to_user_id = ? AND is_deleted = 0
    ");
    
    $insertStmt = $pdo->prepare("
        INSERT INTO filecloud_share (file_id, from_user_id, to_user_id) 
        VALUES (?, ?, ?)
    ");
    
    // 处理每个用户
    foreach ($toUserIds as $toUserId) {
        $toUserId = (int)$toUserId;
        
        // 跳过无效的用户ID
        if ($toUserId <= 0) {
            continue;
        }
        
        // 不能分享给自己
        if ($fromUserId === $toUserId) {
            $errorMessages[] = "不能分享给自己";
            continue;
        }
        
        // 检查是否已经分享过
        $checkStmt->execute([$fileId, $fromUserId, $toUserId]);
        if ($checkStmt->fetch()) {
            $skipCount++;
            continue;
        }
        
        // 创建分享记录
        $insertStmt->execute([$fileId, $fromUserId, $toUserId]);
        $successCount++;
    }
    
    // 提交事务
    $pdo->commit();
    
    // 构建响应消息
    $message = "文件分享成功";
    if ($successCount > 0) {
        $message = "成功分享给 {$successCount} 位用户";
    }
    if ($skipCount > 0) {
        $message .= "，{$skipCount} 位用户已存在分享";
    }
    if (!empty($errorMessages)) {
        $message .= "，部分用户分享失败：" . implode(", ", $errorMessages);
    }
    
    jsonResponse(['success' => true, 'message' => $message]);
    
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    jsonResponse(['success' => false, 'message' => '分享失败：' . $e->getMessage()], 500);
}
?>