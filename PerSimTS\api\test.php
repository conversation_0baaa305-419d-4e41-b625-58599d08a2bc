<?php
header('Content-Type: application/json');

try {
    require_once '../../conn_waf.php';
    
    echo json_encode([
        'status' => 1,
        'message' => 'API测试成功',
        'data' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'session_status' => isset($_SESSION['user_id']) ? 'logged_in' : 'not_logged_in',
            'user_id' => $_SESSION['user_id'] ?? null
        ]
    ]);
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => 'API测试失败: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
