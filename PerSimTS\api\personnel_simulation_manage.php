<?php
header('Content-Type: application/json');
require_once '../../conn_waf.php';

$APP_ID = 10; // 人员调动模拟系统应用ID

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'status' => 0,
        'message' => '用户未登录',
        'data' => []
    ]);
    exit;
}

$operation = $_GET['operation'] ?? $_POST['operation'] ?? '';

try {
    switch ($operation) {
        case 'createScenario':
            createScenario();
            break;
        case 'getScenarios':
            getScenarios();
            break;
        case 'initScenarioData':
            initScenarioData();
            break;
        case 'getPersonnelList':
            getPersonnelList();
            break;
        case 'getUnitTree':
            getUnitTree();
            break;
        case 'transferPersonnel':
            transferPersonnel();
            break;
        case 'getUnitStatistics':
            getUnitStatistics();
            break;
        case 'deleteScenario':
            deleteScenario();
            break;
        case 'getOperationLogs':
            getOperationLogs();
            break;
        case 'exportOperationLogs':
            exportOperationLogs();
            break;
        case 'getAllUnits':
            getAllUnits();
            break;
        case 'getUnitInfo':
            getUnitInfo();
            break;
        case 'createUnit':
            createUnit();
            break;
        case 'updateUnit':
            updateUnit();
            break;
        case 'deleteUnit':
            deleteUnit();
            break;
        case 'getUserInfo':
            getUserInfo();
            break;
        case 'logout':
            logout();
            break;
        default:
            echo json_encode([
                'status' => 0,
                'message' => '无效的操作类型',
                'data' => []
            ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 0,
        'message' => '操作失败: ' . $e->getMessage(),
        'data' => []
    ]);
}

/**
 * 创建模拟方案
 */
function createScenario() {
    global $conn;
    
    $scenario_name = $_POST['scenario_name'] ?? '';
    $description = $_POST['description'] ?? '';
    $copy_data = $_POST['copy_data'] ?? true;
    
    if (empty($scenario_name)) {
        throw new Exception('方案名称不能为空');
    }
    
    // 检查方案名称是否已存在
    $checkSql = "SELECT id FROM sim_scenarios WHERE scenario_name = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('s', $scenario_name);
    $checkStmt->execute();
    if ($checkStmt->get_result()->num_rows > 0) {
        throw new Exception('方案名称已存在');
    }
    
    // 创建方案
    $sql = "INSERT INTO sim_scenarios (scenario_name, description, created_by, data_source_date) VALUES (?, ?, ?, CURDATE())";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ssi', $scenario_name, $description, $_SESSION['user_id']);
    
    if (!$stmt->execute()) {
        throw new Exception('创建方案失败: ' . $stmt->error);
    }
    
    $scenario_id = $conn->insert_id;
    
    $result = [
        'scenario_id' => $scenario_id,
        'units_count' => 0,
        'personnel_count' => 0
    ];
    
    // 如果需要复制数据
    if ($copy_data) {
        $result = copyDataToScenario($scenario_id);
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '方案创建成功',
        'data' => $result
    ]);
}

/**
 * 复制原始数据到模拟方案
 */
function copyDataToScenario($scenario_id) {
    global $conn;
    
    // 复制单位数据
    $unitSql = "INSERT INTO sim_units (scenario_id, original_unit_id, unit_name, parent_id, sort_order, code, unit_level)
                SELECT ?, id, unit_name, parent_id, sort_order, code, 1 FROM 2_unit";
    $unitStmt = $conn->prepare($unitSql);
    $unitStmt->bind_param('i', $scenario_id);
    $unitStmt->execute();
    $units_count = $unitStmt->affected_rows;
    
    // 更新单位层级和父子关系
    updateUnitHierarchy($scenario_id);
    
    // 复制人员数据
    $personnelSql = "INSERT INTO sim_personnel (scenario_id, original_user_id, name, id_number, phone, personnel_type, organization_unit, police_number, position, job_rank)
                     SELECT ?, u.id, u.name, IFNULL(u.id_number, ''), u.phone, u.personnel_type, su.id, u.police_number, u.position, u.job_rank
                     FROM 3_user u
                     LEFT JOIN sim_units su ON su.original_unit_id = u.organization_unit AND su.scenario_id = ?";
    $personnelStmt = $conn->prepare($personnelSql);
    $personnelStmt->bind_param('ii', $scenario_id, $scenario_id);
    $personnelStmt->execute();
    $personnel_count = $personnelStmt->affected_rows;
    
    return [
        'scenario_id' => $scenario_id,
        'units_count' => $units_count,
        'personnel_count' => $personnel_count
    ];
}

/**
 * 更新单位层级关系
 */
function updateUnitHierarchy($scenario_id) {
    global $conn;
    
    // 更新父子关系映射
    $updateSql = "UPDATE sim_units su1 
                  JOIN sim_units su2 ON su2.original_unit_id = (
                      SELECT parent_id FROM 2_unit WHERE id = su1.original_unit_id
                  ) AND su2.scenario_id = su1.scenario_id
                  SET su1.parent_id = su2.id 
                  WHERE su1.scenario_id = ?";
    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bind_param('i', $scenario_id);
    $updateStmt->execute();
}

/**
 * 获取方案列表（只返回当前用户创建的方案）
 */
function getScenarios() {
    global $conn;

    $user_id = $_SESSION['user_id'];

    $sql = "SELECT s.*,
                   (SELECT COUNT(*) FROM sim_units WHERE scenario_id = s.id) as units_count,
                   (SELECT COUNT(*) FROM sim_personnel WHERE scenario_id = s.id) as personnel_count,
                   u.name as creator_name
            FROM sim_scenarios s
            LEFT JOIN 3_user u ON s.created_by = u.id
            WHERE s.is_active = 1 AND s.created_by = ?
            ORDER BY s.created_time DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $scenarios = [];
    while ($row = $result->fetch_assoc()) {
        $scenarios[] = $row;
    }

    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $scenarios
    ]);
}

/**
 * 获取单位树形结构
 */
function getUnitTree() {
    global $conn;

    $scenario_id = $_POST['scenario_id'] ?? 0;
    $user_id = $_SESSION['user_id'];

    if (empty($scenario_id)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 0,
            'message' => '方案ID不能为空',
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    // 验证用户是否有权限访问该方案
    $checkSql = "SELECT created_by FROM sim_scenarios WHERE id = ? AND is_active = 1";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('i', $scenario_id);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows === 0) {
        throw new Exception('方案不存在或已被删除');
    }

    $scenario = $checkResult->fetch_assoc();
    if ($scenario['created_by'] != $user_id) {
        throw new Exception('您没有权限访问该方案');
    }

    $sql = "SELECT id, unit_name, parent_id, sort_order,
                   (SELECT COUNT(*) FROM sim_personnel WHERE organization_unit = su.id) as personnel_count
            FROM sim_units su
            WHERE scenario_id = ? AND is_deleted = 0
            ORDER BY sort_order, unit_name";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $scenario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $units = [];
    while ($row = $result->fetch_assoc()) {
        $units[] = $row;
    }
    
    // 构建树形结构
    $tree = buildTree($units);
    
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $tree
    ]);
}

/**
 * 构建树形结构
 */
function buildTree($units, $parent_id = null) {
    $tree = [];
    
    foreach ($units as $unit) {
        if ($unit['parent_id'] == $parent_id) {
            $unit['children'] = buildTree($units, $unit['id']);
            $tree[] = $unit;
        }
    }
    
    return $tree;
}

/**
 * 获取人员列表
 */
function getPersonnelList() {
    global $conn;

    $scenario_id = $_POST['scenario_id'] ?? 0;
    $unit_id = $_POST['unit_id'] ?? 0;
    $page = $_POST['page'] ?? 1;
    $pagesize = $_POST['pagesize'] ?? 20;
    $search_keyword = $_POST['search_keyword'] ?? '';
    $personnel_type_filter = $_POST['personnel_type_filter'] ?? '';
    $user_id = $_SESSION['user_id'];

    if (empty($scenario_id)) {
        throw new Exception('方案ID不能为空');
    }

    // 验证用户是否有权限访问该方案
    $checkSql = "SELECT created_by FROM sim_scenarios WHERE id = ? AND is_active = 1";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('i', $scenario_id);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows === 0) {
        throw new Exception('方案不存在或已被删除');
    }

    $scenario = $checkResult->fetch_assoc();
    if ($scenario['created_by'] != $user_id) {
        throw new Exception('您没有权限访问该方案');
    }

    $offset = ($page - 1) * $pagesize;

    $where = "WHERE sp.scenario_id = ?";
    $params = [$scenario_id];
    $types = 'i';

    if (!empty($unit_id)) {
        $where .= " AND sp.organization_unit = ?";
        $params[] = $unit_id;
        $types .= 'i';
    }

    if (!empty($search_keyword)) {
        $where .= " AND (sp.name LIKE ? OR sp.id_number LIKE ? OR sp.police_number LIKE ?)";
        $keyword = "%$search_keyword%";
        $params[] = $keyword;
        $params[] = $keyword;
        $params[] = $keyword;
        $types .= 'sss';
    }

    if (!empty($personnel_type_filter)) {
        if ($personnel_type_filter === '其他') {
            // 筛选"其他"类型：不是民警也不是辅警的人员
            $where .= " AND sp.personnel_type NOT LIKE '%民警%' AND sp.personnel_type NOT LIKE '%辅警%'";
        } else {
            // 筛选特定类型
            $where .= " AND sp.personnel_type LIKE ?";
            $params[] = "%$personnel_type_filter%";
            $types .= 's';
        }
    }
    
    // 查询总数
    $countSql = "SELECT COUNT(*) as total FROM sim_personnel sp $where";
    $countStmt = $conn->prepare($countSql);
    $countStmt->bind_param($types, ...$params);
    $countStmt->execute();
    $total = $countStmt->get_result()->fetch_assoc()['total'];
    
    // 查询数据
    $sql = "SELECT sp.*, su.unit_name as organization_unit_name
            FROM sim_personnel sp
            LEFT JOIN sim_units su ON sp.organization_unit = su.id
            $where
            ORDER BY sp.name
            LIMIT $offset, $pagesize";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $personnel = [];
    while ($row = $result->fetch_assoc()) {
        $personnel[] = $row;
    }
    
    echo json_encode([
        'status' => 1,
        'message' => '查询成功',
        'data' => $personnel,
        'total' => $total,
        'page' => $page,
        'pagesize' => $pagesize
    ]);
}

/**
 * 人员调动操作
 */
function transferPersonnel() {
    global $conn;

    $scenario_id = $_POST['scenario_id'] ?? 0;
    $personnel_ids = $_POST['personnel_ids'] ?? [];
    $target_unit_id = $_POST['target_unit_id'] ?? 0;
    $transfer_reason = $_POST['transfer_reason'] ?? '';
    $remarks = $_POST['remarks'] ?? '';
    $user_id = $_SESSION['user_id'];

    if (empty($scenario_id) || empty($personnel_ids) || empty($target_unit_id)) {
        throw new Exception('参数不完整');
    }

    // 验证用户是否有权限操作该方案
    $checkSql = "SELECT created_by FROM sim_scenarios WHERE id = ? AND is_active = 1";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('i', $scenario_id);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows === 0) {
        throw new Exception('方案不存在或已被删除');
    }

    $scenario = $checkResult->fetch_assoc();
    if ($scenario['created_by'] != $user_id) {
        throw new Exception('您没有权限操作该方案');
    }

    if (!is_array($personnel_ids)) {
        $personnel_ids = [$personnel_ids];
    }

    // 验证目标单位是否存在
    $unitSql = "SELECT unit_name FROM sim_units WHERE id = ? AND scenario_id = ?";
    $unitStmt = $conn->prepare($unitSql);
    $unitStmt->bind_param('ii', $target_unit_id, $scenario_id);
    $unitStmt->execute();
    $unitResult = $unitStmt->get_result();

    if ($unitResult->num_rows === 0) {
        throw new Exception('目标单位不存在');
    }

    $target_unit_name = $unitResult->fetch_assoc()['unit_name'];

    $transferred_count = 0;
    $failed_count = 0;
    $transfer_log_ids = [];

    $conn->begin_transaction();

    try {
        foreach ($personnel_ids as $personnel_id) {
            // 获取人员当前信息
            $personnelSql = "SELECT sp.*, su.unit_name as current_unit_name
                           FROM sim_personnel sp
                           LEFT JOIN sim_units su ON sp.organization_unit = su.id
                           WHERE sp.id = ? AND sp.scenario_id = ?";
            $personnelStmt = $conn->prepare($personnelSql);
            $personnelStmt->bind_param('ii', $personnel_id, $scenario_id);
            $personnelStmt->execute();
            $personnelResult = $personnelStmt->get_result();

            if ($personnelResult->num_rows === 0) {
                $failed_count++;
                continue;
            }

            $personnel = $personnelResult->fetch_assoc();
            $from_unit_id = $personnel['organization_unit'];
            $from_unit_name = $personnel['current_unit_name'];

            // 如果已经在目标单位，跳过
            if ($from_unit_id == $target_unit_id) {
                $failed_count++;
                continue;
            }

            // 更新人员单位
            $updateSql = "UPDATE sim_personnel
                         SET organization_unit = ?,
                             transfer_count = transfer_count + 1,
                             last_transfer_time = NOW()
                         WHERE id = ?";
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param('ii', $target_unit_id, $personnel_id);

            if ($updateStmt->execute()) {
                // 记录调动日志
                $logSql = "INSERT INTO sim_transfer_logs
                          (scenario_id, personnel_id, from_unit_id, to_unit_id, from_unit_name, to_unit_name,
                           transfer_type, transfer_reason, operator_id, operator_name, remarks)
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $logStmt = $conn->prepare($logSql);
                $operator_name = $_SESSION['user_name'] ?? '';
                $transfer_type = 'manual';
                $logStmt->bind_param('iiissssisss',
                    $scenario_id, $personnel_id, $from_unit_id, $target_unit_id,
                    $from_unit_name, $target_unit_name, $transfer_type, $transfer_reason,
                    $_SESSION['user_id'], $operator_name, $remarks);

                if ($logStmt->execute()) {
                    $transfer_log_ids[] = $conn->insert_id;
                    $transferred_count++;
                } else {
                    $failed_count++;
                }
            } else {
                $failed_count++;
            }
        }

        $conn->commit();

        echo json_encode([
            'status' => 1,
            'message' => '调动操作完成',
            'data' => [
                'transferred_count' => $transferred_count,
                'failed_count' => $failed_count,
                'transfer_log_ids' => $transfer_log_ids
            ]
        ]);

    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
}

/**
 * 获取单位统计信息
 */
function getUnitStatistics() {
    global $conn;

    $scenario_id = $_POST['scenario_id'] ?? 0;
    $unit_id = $_POST['unit_id'] ?? null;

    if (empty($scenario_id)) {
        throw new Exception('方案ID不能为空');
    }

    $where = "WHERE sp.scenario_id = ?";
    $params = [$scenario_id];
    $types = 'i';

    if (!empty($unit_id)) {
        $where .= " AND sp.organization_unit = ?";
        $params[] = $unit_id;
        $types .= 'i';
    }

    $sql = "SELECT su.id as unit_id, su.unit_name, su.sort_order,
                   pu.unit_name as parent_unit_name,
                   sp.name, sp.police_number, sp.position, sp.personnel_type, sp.transfer_count
            FROM sim_personnel sp
            JOIN sim_units su ON sp.organization_unit = su.id
            LEFT JOIN sim_units pu ON su.parent_id = pu.id
            $where
            ORDER BY su.sort_order, su.unit_name, sp.name";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $units_data = [];

    while ($row = $result->fetch_assoc()) {
        $unit_id = $row['unit_id'];

        if (!isset($units_data[$unit_id])) {
            $units_data[$unit_id] = [
                'unit_id' => $unit_id,
                'unit_name' => $row['unit_name'],
                'parent_unit_name' => $row['parent_unit_name'],
                'sort_order' => $row['sort_order'],
                'police_list' => [],
                'auxiliary_list' => [],
                'civilian_list' => [],
                'police_count' => 0,
                'auxiliary_count' => 0,
                'civilian_count' => 0,
                'total_count' => 0,
                'transfer_in_count' => 0,
                'transfer_out_count' => 0
            ];
        }

        $person = [
            'name' => $row['name'],
            'police_number' => $row['police_number'],
            'position' => $row['position'],
            'personnel_type' => $row['personnel_type']
        ];

        // 根据人员类型分类
        if (($row['personnel_type'] !== null && strpos($row['personnel_type'], '民警') !== false) || ($row['personnel_type'] !== null && strpos($row['personnel_type'], '警察') !== false)) {
            $units_data[$unit_id]['police_list'][] = $person;
            $units_data[$unit_id]['police_count']++;
        } elseif ($row['personnel_type'] !== null && strpos($row['personnel_type'], '辅警') !== false) {
            $units_data[$unit_id]['auxiliary_list'][] = $person;
            $units_data[$unit_id]['auxiliary_count']++;
        } else {
            $units_data[$unit_id]['civilian_list'][] = $person;
            $units_data[$unit_id]['civilian_count']++;
        }

        $units_data[$unit_id]['total_count']++;
    }

    // 统计调动信息 - 基于原始人员和模拟人员的具体对比
    foreach ($units_data as $unit_id => &$unit_data) {
        // 获取sim_units对应的原始单位ID（从2_unit表）
        // 假设sim_units.unit_name对应2_unit.unit_name
        $getOriginalUnitIdSql = "SELECT id FROM 2_unit WHERE unit_name = ?";
        $getOriginalUnitIdStmt = $conn->prepare($getOriginalUnitIdSql);
        $getOriginalUnitIdStmt->bind_param('s', $unit_data['unit_name']);
        $getOriginalUnitIdStmt->execute();
        $originalUnitResult = $getOriginalUnitIdStmt->get_result();

        if ($originalUnitResult->num_rows === 0) {
            // 如果找不到对应的原始单位，跳过统计
            $unit_data['transfer_in_count'] = 0;
            $unit_data['transfer_out_count'] = 0;
            $unit_data['original_count'] = 0;
            $unit_data['transfer_in_list'] = [];
            $unit_data['transfer_out_list'] = [];
            continue;
        }

        $original_unit_id = $originalUnitResult->fetch_assoc()['id'];

        // 获取该原始单位的人员ID列表（从3_user表）
        $originalUsersSql = "SELECT id, name FROM 3_user WHERE organization_unit = ?";
        $originalUsersStmt = $conn->prepare($originalUsersSql);

        $original_user_ids = [];
        $original_user_names = [];
        if ($originalUsersStmt) {
            $originalUsersStmt->bind_param('i', $original_unit_id);
            $originalUsersStmt->execute();
            $result = $originalUsersStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $original_user_ids[] = intval($row['id']);
                $original_user_names[] = $row['name'];
            }
        }

        // 获取该单位的模拟人员原始ID列表（从sim_personnel表）
        $simUsersSql = "SELECT original_user_id, name FROM sim_personnel WHERE scenario_id = ? AND organization_unit = ? AND original_user_id IS NOT NULL";
        $simUsersStmt = $conn->prepare($simUsersSql);

        $sim_user_ids = [];
        $sim_user_names = [];
        if ($simUsersStmt) {
            $simUsersStmt->bind_param('ii', $scenario_id, $unit_id);
            $simUsersStmt->execute();
            $result = $simUsersStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                if ($row['original_user_id'] !== null) {
                    $sim_user_ids[] = intval($row['original_user_id']);
                    $sim_user_names[] = $row['name'];
                }
            }
        }

        error_log("Sim users: " . implode(', ', $sim_user_names));



        // 计算调入调出
        // 调入 = 模拟数据中有但原始数据中没有的人员
        $transfer_in_ids = array_diff($sim_user_ids, $original_user_ids);
        $unit_data['transfer_in_count'] = count($transfer_in_ids);

        // 调出 = 原始数据中有但模拟数据中没有的人员
        $transfer_out_ids = array_diff($original_user_ids, $sim_user_ids);
        $unit_data['transfer_out_count'] = count($transfer_out_ids);



        // 获取调入人员详细信息（模拟数据中有但原始数据中没有的人员）
        $transfer_in_list = [];
        if (!empty($transfer_in_ids)) {
            $in_ids_str = implode(',', array_map('intval', $transfer_in_ids));
            $transferInDetailSql = "SELECT sp.name FROM sim_personnel sp
                                    WHERE sp.scenario_id = ? AND sp.organization_unit = ?
                                    AND sp.original_user_id IN ($in_ids_str)";
            $transferInDetailStmt = $conn->prepare($transferInDetailSql);
            $transferInDetailStmt->bind_param('ii', $scenario_id, $unit_id);
            $transferInDetailStmt->execute();
            $result = $transferInDetailStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $transfer_in_list[] = $row['name'];
            }
        }

        // 获取调出人员详细信息（原始数据中有但模拟数据中没有的人员）
        $transfer_out_list = [];
        if (!empty($transfer_out_ids)) {
            $out_ids_str = implode(',', array_map('intval', $transfer_out_ids));
            $transferOutDetailSql = "SELECT u.name FROM 3_user u WHERE u.id IN ($out_ids_str)";
            $transferOutDetailStmt = $conn->prepare($transferOutDetailSql);
            $transferOutDetailStmt->execute();
            $result = $transferOutDetailStmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $transfer_out_list[] = $row['name'];
            }
        }

        // 添加详细信息到返回数据
        $unit_data['original_count'] = count($original_user_ids);
        $unit_data['transfer_in_list'] = $transfer_in_list;
        $unit_data['transfer_out_list'] = $transfer_out_list;
    }

    // 获取单位的树形结构信息以确定正确的显示顺序
    $unitOrderSql = "SELECT u.id, u.unit_name, u.parent_id, u.sort_order
                     FROM sim_units u
                     WHERE u.scenario_id = ? AND u.is_deleted = 0
                     ORDER BY u.sort_order, u.unit_name";
    $unitOrderStmt = $conn->prepare($unitOrderSql);
    $unitOrderStmt->bind_param('i', $scenario_id);
    $unitOrderStmt->execute();
    $unitOrderResult = $unitOrderStmt->get_result();

    $all_units = [];
    while ($row = $unitOrderResult->fetch_assoc()) {
        $all_units[] = $row;
    }

    // 构建树形结构并获取平铺的显示顺序
    $unit_tree = buildUnitTree($all_units);
    $flat_order = [];
    flattenUnitTree($unit_tree, $flat_order);

    // 按照树形结构的顺序重新排列统计数据
    $sorted_units = [];
    foreach ($flat_order as $unit_id) {
        if (isset($units_data[$unit_id])) {
            $sorted_units[] = $units_data[$unit_id];
        }
    }

    $response = [
        'status' => 1,
        'message' => '获取单位统计信息成功',
        'data' => $sorted_units
    ];
    
    header('Content-Type: application/json');
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

/**
 * 将树形结构的单位转换为平铺的显示顺序
 */
function flattenUnitTree($units, &$flat_order) {
    foreach ($units as $unit) {
        $flat_order[] = $unit['id'];
        if (isset($unit['children']) && !empty($unit['children'])) {
            flattenUnitTree($unit['children'], $flat_order);
        }
    }
}

/**
 * 删除模拟方案（硬删除）
 */
function deleteScenario() {
    global $conn;

    $scenario_id = $_POST['scenario_id'] ?? 0;

    if (empty($scenario_id)) {
        throw new Exception('方案ID不能为空');
    }

    // 检查权限（只有创建者可以删除）
    $checkSql = "SELECT created_by FROM sim_scenarios WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('i', $scenario_id);
    $checkStmt->execute();
    $result = $checkStmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('方案不存在');
    }

    $scenario = $result->fetch_assoc();
    if ($scenario['created_by'] != $_SESSION['user_id']) {
        throw new Exception('只有创建者可以删除方案');
    }

    // 开始事务
    $conn->begin_transaction();

    try {
        // 删除调动日志
        $deleteLogsSql = "DELETE FROM sim_transfer_logs WHERE scenario_id = ?";
        $deleteLogsStmt = $conn->prepare($deleteLogsSql);
        $deleteLogsStmt->bind_param('i', $scenario_id);
        $deleteLogsStmt->execute();

        // 删除人员数据
        $deletePersonnelSql = "DELETE FROM sim_personnel WHERE scenario_id = ?";
        $deletePersonnelStmt = $conn->prepare($deletePersonnelSql);
        $deletePersonnelStmt->bind_param('i', $scenario_id);
        $deletePersonnelStmt->execute();

        // 删除单位数据
        $deleteUnitsSql = "DELETE FROM sim_units WHERE scenario_id = ?";
        $deleteUnitsStmt = $conn->prepare($deleteUnitsSql);
        $deleteUnitsStmt->bind_param('i', $scenario_id);
        $deleteUnitsStmt->execute();

        // 删除方案
        $deleteScenarioSql = "DELETE FROM sim_scenarios WHERE id = ?";
        $deleteScenarioStmt = $conn->prepare($deleteScenarioSql);
        $deleteScenarioStmt->bind_param('i', $scenario_id);
        $deleteScenarioStmt->execute();

        // 提交事务
        $conn->commit();

        echo json_encode([
            'status' => 1,
            'message' => '方案删除成功',
            'data' => []
        ]);

    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        throw new Exception('删除失败: ' . $e->getMessage());
    }
}

/**
 * 获取操作日志
 */
function getOperationLogs() {
    global $conn;

    $page = $_POST['page'] ?? 1;
    $pagesize = $_POST['pagesize'] ?? 20;

    // 先测试表是否存在
    $testSql = "SELECT COUNT(*) as total FROM sim_transfer_logs LIMIT 1";
    $testResult = $conn->query($testSql);

    if (!$testResult) {
        // 表不存在，返回空数据
        echo json_encode([
            'status' => 1,
            'message' => '获取成功',
            'data' => [],
            'total' => 0,
            'page' => $page,
            'pagesize' => $pagesize
        ]);
        return;
    }

    $user_id = $_SESSION['user_id'];
    $scenario_id = $_POST['scenario_id'] ?? '';
    $date_filter = $_POST['date_filter'] ?? '';
    $offset = ($page - 1) * $pagesize;

    // 构建查询条件 - 用户只能查看自己创建的方案的日志
    if (!empty($scenario_id)) {
        // 指定方案：检查用户是否有权限访问该方案
        $checkSql = "SELECT id FROM sim_scenarios WHERE id = ? AND created_by = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param('ii', $scenario_id, $user_id);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();

        if ($checkResult->num_rows === 0) {
            throw new Exception('无权限访问该方案的日志');
        }

        $where = "WHERE tl.scenario_id = ?";
        $whereCount = "WHERE scenario_id = ?";
        $params = [$scenario_id];
        $types = 'i';
    } else {
        // 全部方案：查看用户创建的所有方案的日志
        $where = "WHERE tl.scenario_id IN (SELECT id FROM sim_scenarios WHERE created_by = ?)";
        $whereCount = "WHERE scenario_id IN (SELECT id FROM sim_scenarios WHERE created_by = ?)";
        $params = [$user_id];
        $types = 'i';
    }

    if (!empty($date_filter)) {
        $where .= " AND DATE(tl.transfer_time) = ?";
        $whereCount .= " AND DATE(transfer_time) = ?";
        $params[] = $date_filter;
        $types .= 's';
    }

    // 查询日志数据，关联人员表和用户表获取姓名
    $sql = "SELECT tl.*,
                   p.name as personnel_name,
                   u.name as operator_user_name,
                   s.scenario_name
            FROM sim_transfer_logs tl
            LEFT JOIN sim_personnel p ON tl.personnel_id = p.id
            LEFT JOIN 3_user u ON tl.operator_id = u.id
            LEFT JOIN sim_scenarios s ON tl.scenario_id = s.id
            $where
            ORDER BY tl.transfer_time DESC
            LIMIT ? OFFSET ?";

    $params[] = $pagesize;
    $params[] = $offset;
    $types .= 'ii';

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('查询日志失败: ' . $conn->error);
    }

    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $logs = [];
    while ($row = $result->fetch_assoc()) {
        $logs[] = $row;
    }

    // 获取总数
    $countSql = "SELECT COUNT(*) as total FROM sim_transfer_logs $whereCount";
    $countStmt = $conn->prepare($countSql);

    // 移除分页参数
    $countParams = array_slice($params, 0, -2);
    $countTypes = substr($types, 0, -2);

    if (!empty($countParams)) {
        $countStmt->bind_param($countTypes, ...$countParams);
    }
    $countStmt->execute();
    $total = $countStmt->get_result()->fetch_assoc()['total'];

    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => $logs,
        'total' => $total,
        'page' => $page,
        'pagesize' => $pagesize
    ]);
}

/**
 * 导出操作日志
 */
function exportOperationLogs() {
    global $conn;

    $user_id = $_SESSION['user_id'];
    $scenario_id = $_POST['scenario_id'] ?? '';
    $date_filter = $_POST['date_filter'] ?? '';

    $where = "WHERE ol.user_id = ?";
    $params = [$user_id];
    $types = 'i';

    if (!empty($scenario_id)) {
        $where .= " AND ol.scenario_id = ?";
        $params[] = $scenario_id;
        $types .= 'i';
    }

    if (!empty($date_filter)) {
        $where .= " AND DATE(ol.created_at) = ?";
        $params[] = $date_filter;
        $types .= 's';
    }

    // 获取日志数据
    $sql = "SELECT ol.created_at, ol.operation_type, ss.scenario_name, ol.operation_content, ol.status
            FROM operation_logs ol
            LEFT JOIN sim_scenarios ss ON ol.scenario_id = ss.id
            $where
            ORDER BY ol.created_at DESC";

    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    // 生成CSV文件
    $filename = 'operation_logs_' . date('Y-m-d_H-i-s') . '.csv';
    $filepath = '../exports/' . $filename;

    // 确保导出目录存在
    if (!file_exists('../exports/')) {
        mkdir('../exports/', 0777, true);
    }

    $file = fopen($filepath, 'w');

    // 写入BOM以支持中文
    fwrite($file, "\xEF\xBB\xBF");

    // 写入表头
    fputcsv($file, ['操作时间', '操作类型', '方案名称', '操作内容', '操作结果']);

    // 写入数据
    while ($row = $result->fetch_assoc()) {
        fputcsv($file, [
            $row['created_at'],
            $row['operation_type'],
            $row['scenario_name'] ?: '未知方案',
            $row['operation_content'],
            $row['status'] === 'success' ? '成功' : '失败'
        ]);
    }

    fclose($file);

    echo json_encode([
        'status' => 1,
        'message' => '导出成功',
        'data' => [
            'download_url' => './exports/' . $filename
        ]
    ]);
}

/**
 * 获取所有单位（用于管理）
 */
function getAllUnits() {
    global $conn;

    $user_id = $_SESSION['user_id'];
    $scenario_id = $_POST['scenario_id'] ?? 0;

    if (empty($scenario_id)) {
        throw new Exception('方案ID不能为空');
    }

    // 验证用户是否有权限访问该方案
    $checkSql = "SELECT id FROM sim_scenarios WHERE id = ? AND created_by = ? AND is_active = 1";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('ii', $scenario_id, $user_id);
    $checkStmt->execute();

    if ($checkStmt->get_result()->num_rows === 0) {
        throw new Exception('方案不存在或您没有权限访问');
    }

    $sql = "SELECT u.*, pu.unit_name as parent_name,
                   COUNT(p.id) as personnel_count
            FROM sim_units u
            LEFT JOIN sim_units pu ON u.parent_id = pu.id
            LEFT JOIN sim_personnel p ON u.id = p.organization_unit
            WHERE u.scenario_id = ? AND u.is_deleted = 0
            GROUP BY u.id
            ORDER BY u.sort_order, u.unit_name";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $scenario_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        throw new Exception('查询单位失败: ' . $conn->error);
    }

    $units = [];
    while ($row = $result->fetch_assoc()) {
        $units[] = $row;
    }

    // 构建树形结构
    $tree = buildUnitTree($units);

    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => $tree
    ]);
}

/**
 * 获取单位信息
 */
function getUnitInfo() {
    global $conn;

    $unit_id = $_POST['unit_id'] ?? 0;
    $user_id = $_SESSION['user_id'];

    if (empty($unit_id)) {
        throw new Exception('单位ID不能为空');
    }

    $sql = "SELECT u.*, s.scenario_name
            FROM sim_units u
            LEFT JOIN sim_scenarios s ON u.scenario_id = s.id
            WHERE u.id = ? AND s.created_by = ? AND s.is_active = 1 AND u.is_deleted = 0";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ii', $unit_id, $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('单位不存在或您没有权限访问');
    }

    $unit = $result->fetch_assoc();

    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => $unit
    ]);
}

/**
 * 创建单位
 */
function createUnit() {
    global $conn;

    $unit_name = $_POST['unit_name'] ?? '';
    $parent_id = $_POST['parent_id'] ?? null;
    $code = $_POST['code'] ?? '';
    $sort_order = $_POST['sort_order'] ?? 0;
    $scenario_id = $_POST['scenario_id'] ?? 0;
    $user_id = $_SESSION['user_id'];

    if (empty($unit_name)) {
        throw new Exception('单位名称不能为空');
    }

    if (empty($scenario_id)) {
        throw new Exception('方案ID不能为空');
    }

    // 验证用户是否有权限访问该方案
    $checkScenarioSql = "SELECT id FROM sim_scenarios WHERE id = ? AND created_by = ? AND is_active = 1";
    $checkScenarioStmt = $conn->prepare($checkScenarioSql);
    $checkScenarioStmt->bind_param('ii', $scenario_id, $user_id);
    $checkScenarioStmt->execute();

    if ($checkScenarioStmt->get_result()->num_rows === 0) {
        throw new Exception('方案不存在或您没有权限访问');
    }

    // 检查单位名称在该方案中是否已存在
    $checkSql = "SELECT id FROM sim_units WHERE unit_name = ? AND scenario_id = ? AND is_deleted = 0";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('si', $unit_name, $scenario_id);
    $checkStmt->execute();

    if ($checkStmt->get_result()->num_rows > 0) {
        throw new Exception('该方案中单位名称已存在');
    }

    $sql = "INSERT INTO sim_units (unit_name, parent_id, code, sort_order, scenario_id, is_deleted)
            VALUES (?, ?, ?, ?, ?, 0)";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('SQL准备失败: ' . $conn->error);
    }

    $stmt->bind_param('sisii', $unit_name, $parent_id, $code, $sort_order, $scenario_id);

    if (!$stmt->execute()) {
        throw new Exception('创建单位失败');
    }

    // 记录操作日志
    logSystemOperation('单位管理', '创建单位: ' . $unit_name, 'success');

    echo json_encode([
        'status' => 1,
        'message' => '创建成功',
        'data' => ['unit_id' => $conn->insert_id]
    ]);
}

/**
 * 更新单位
 */
function updateUnit() {
    global $conn;

    $unit_id = $_POST['unit_id'] ?? 0;
    $unit_name = $_POST['unit_name'] ?? '';
    $parent_id = $_POST['parent_id'] ?? null;
    $code = $_POST['code'] ?? '';
    $sort_order = $_POST['sort_order'] ?? 0;

    if (empty($unit_id)) {
        throw new Exception('单位ID不能为空');
    }

    if (empty($unit_name)) {
        throw new Exception('单位名称不能为空');
    }

    // 检查单位是否存在
    $checkSql = "SELECT unit_name FROM sim_units WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('i', $unit_id);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows === 0) {
        throw new Exception('单位不存在');
    }

    $oldName = $checkResult->fetch_assoc()['unit_name'];

    // 检查新名称是否与其他单位重复
    $duplicateSql = "SELECT id FROM sim_units WHERE unit_name = ? AND id != ?";
    $duplicateStmt = $conn->prepare($duplicateSql);
    $duplicateStmt->bind_param('si', $unit_name, $unit_id);
    $duplicateStmt->execute();

    if ($duplicateStmt->get_result()->num_rows > 0) {
        throw new Exception('单位名称已存在');
    }

    $sql = "UPDATE sim_units
            SET unit_name = ?, parent_id = ?, code = ?, sort_order = ?
            WHERE id = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception('SQL准备失败: ' . $conn->error);
    }

    $stmt->bind_param('sisii', $unit_name, $parent_id, $code, $sort_order, $unit_id);

    if (!$stmt->execute()) {
        throw new Exception('更新单位失败');
    }

    // 记录操作日志
    logSystemOperation('单位管理', '更新单位: ' . $oldName . ' -> ' . $unit_name, 'success');

    echo json_encode([
        'status' => 1,
        'message' => '更新成功',
        'data' => []
    ]);
}

/**
 * 删除单位
 */
function deleteUnit() {
    global $conn;

    $unit_id = $_POST['unit_id'] ?? 0;

    if (empty($unit_id)) {
        throw new Exception('单位ID不能为空');
    }

    // 检查单位是否存在
    $checkSql = "SELECT unit_name FROM sim_units WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param('i', $unit_id);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows === 0) {
        throw new Exception('单位不存在');
    }

    $unitName = $checkResult->fetch_assoc()['unit_name'];

    // 检查是否有子单位
    $childSql = "SELECT COUNT(*) as count FROM sim_units WHERE parent_id = ?";
    $childStmt = $conn->prepare($childSql);
    $childStmt->bind_param('i', $unit_id);
    $childStmt->execute();
    $childCount = $childStmt->get_result()->fetch_assoc()['count'];

    if ($childCount > 0) {
        throw new Exception('该单位下还有子单位，无法删除');
    }

    // 检查是否有人员
    $personnelSql = "SELECT COUNT(*) as count FROM sim_personnel WHERE organization_unit = ?";
    $personnelStmt = $conn->prepare($personnelSql);
    $personnelStmt->bind_param('i', $unit_id);
    $personnelStmt->execute();
    $personnelCount = $personnelStmt->get_result()->fetch_assoc()['count'];

    if ($personnelCount > 0) {
        throw new Exception('该单位下还有人员，无法删除');
    }

    // 删除单位
    $deleteSql = "DELETE FROM sim_units WHERE id = ?";
    $deleteStmt = $conn->prepare($deleteSql);
    $deleteStmt->bind_param('i', $unit_id);

    if (!$deleteStmt->execute()) {
        throw new Exception('删除单位失败');
    }

    // 记录操作日志
    logSystemOperation('单位管理', '删除单位: ' . $unitName, 'success');

    echo json_encode([
        'status' => 1,
        'message' => '删除成功',
        'data' => []
    ]);
}

/**
 * 记录系统操作日志
 */
function logSystemOperation($operation_type, $operation_content, $status = 'success', $scenario_id = null) {
    global $conn;

    $user_id = $_SESSION['user_id'];

    $sql = "INSERT INTO operation_logs (user_id, scenario_id, operation_type, operation_content, status)
            VALUES (?, ?, ?, ?, ?)";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log('logSystemOperation SQL准备失败: ' . $conn->error);
        return; // 日志记录失败不应该影响主要功能
    }

    $stmt->bind_param('iisss', $user_id, $scenario_id, $operation_type, $operation_content, $status);
    $stmt->execute();
}

/**
 * 获取用户信息
 */
function getUserInfo() {
    global $conn;

    $user_id = $_SESSION['user_id'];

    // 获取用户基本信息
    $sql = "SELECT u.id, u.name, u.username, ur.role_id
            FROM 3_user u
            LEFT JOIN 3_user_Role ur ON u.id = ur.user_id
            WHERE u.id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        throw new Exception('用户信息不存在');
    }

    $userInfo = $result->fetch_assoc();

    echo json_encode([
        'status' => 1,
        'message' => '获取成功',
        'data' => $userInfo
    ]);
}

/**
 * 用户退出登录
 */
function logout() {
    // 清除session
    session_destroy();

    echo json_encode([
        'status' => 1,
        'message' => '退出登录成功',
        'data' => []
    ]);
}

/**
 * 构建单位树形结构
 */
function buildUnitTree($units, $parentId = null) {
    $tree = [];

    foreach ($units as $unit) {
        if ($unit['parent_id'] == $parentId) {
            $children = buildUnitTree($units, $unit['id']);
            if (!empty($children)) {
                $unit['children'] = $children;
            }
            $tree[] = $unit;
        }
    }

    return $tree;
}
?>
