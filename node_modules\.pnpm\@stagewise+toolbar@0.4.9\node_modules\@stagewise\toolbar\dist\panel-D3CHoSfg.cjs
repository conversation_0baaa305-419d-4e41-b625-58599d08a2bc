"use strict";const pluginUi_jsxRuntime=require("./jsx-runtime-r6LeVNNi.cjs");var t,r$1,u,i,o=0,f=[],c=pluginUi_jsxRuntime.l,e=c.__b,a=c.__r,v=c.diffed,l=c.__c,m=c.unmount,s=c.__;function p(n,t2){c.__h&&c.__h(r$1,n,o||t2),o=0;var u2=r$1.__H||(r$1.__H={__:[],__h:[]});return n>=u2.__.length&&u2.__.push({}),u2.__[n]}function d(n){return o=1,h(D$1,n)}function h(n,u2,i2){var o2=p(t++,2);if(o2.t=n,!o2.__c&&(o2.__=[i2?i2(u2):D$1(void 0,u2),function(n2){var t2=o2.__N?o2.__N[0]:o2.__[0],r2=o2.t(t2,n2);t2!==r2&&(o2.__N=[r2,o2.__[1]],o2.__c.setState({}))}],o2.__c=r$1,!r$1.__f)){var f2=function(n2,t2,r2){if(!o2.__c.__H)return!0;var u3=o2.__c.__H.__.filter(function(n3){return!!n3.__c});if(u3.every(function(n3){return!n3.__N}))return!c2||c2.call(this,n2,t2,r2);var i3=o2.__c.props!==n2;return u3.forEach(function(n3){if(n3.__N){var t3=n3.__[0];n3.__=n3.__N,n3.__N=void 0,t3!==n3.__[0]&&(i3=!0)}}),c2&&c2.call(this,n2,t2,r2)||i3};r$1.__f=!0;var c2=r$1.shouldComponentUpdate,e2=r$1.componentWillUpdate;r$1.componentWillUpdate=function(n2,t2,r2){if(this.__e){var u3=c2;c2=void 0,f2(n2,t2,r2),c2=u3}e2&&e2.call(this,n2,t2,r2)},r$1.shouldComponentUpdate=f2}return o2.__N||o2.__}function y(n,u2){var i2=p(t++,3);!c.__s&&C$1(i2.__H,u2)&&(i2.__=n,i2.u=u2,r$1.__H.__h.push(i2))}function _(n,u2){var i2=p(t++,4);!c.__s&&C$1(i2.__H,u2)&&(i2.__=n,i2.u=u2,r$1.__h.push(i2))}function A$1(n){return o=5,T$1(function(){return{current:n}},[])}function F$1(n,t2,r2){o=6,_(function(){if(typeof n=="function"){var r3=n(t2());return function(){n(null),r3&&typeof r3=="function"&&r3()}}if(n)return n.current=t2(),function(){return n.current=null}},r2==null?r2:r2.concat(n))}function T$1(n,r2){var u2=p(t++,7);return C$1(u2.__H,r2)&&(u2.__=n(),u2.__H=r2,u2.__h=n),u2.__}function q$1(n,t2){return o=8,T$1(function(){return n},t2)}function x$1(n){var u2=r$1.context[n.__c],i2=p(t++,9);return i2.c=n,u2?(i2.__==null&&(i2.__=!0,u2.sub(r$1)),u2.props.value):n.__}function P$1(n,t2){c.useDebugValue&&c.useDebugValue(t2?t2(n):n)}function g$1(){var n=p(t++,11);if(!n.__){for(var u2=r$1.__v;u2!==null&&!u2.__m&&u2.__!==null;)u2=u2.__;var i2=u2.__m||(u2.__m=[0,0]);n.__="P"+i2[0]+"-"+i2[1]++}return n.__}function j$1(){for(var n;n=f.shift();)if(n.__P&&n.__H)try{n.__H.__h.forEach(z$2),n.__H.__h.forEach(B$1),n.__H.__h=[]}catch(t2){n.__H.__h=[],c.__e(t2,n.__v)}}c.__b=function(n){r$1=null,e&&e(n)},c.__=function(n,t2){n&&t2.__k&&t2.__k.__m&&(n.__m=t2.__k.__m),s&&s(n,t2)},c.__r=function(n){a&&a(n),t=0;var i2=(r$1=n.__c).__H;i2&&(u===r$1?(i2.__h=[],r$1.__h=[],i2.__.forEach(function(n2){n2.__N&&(n2.__=n2.__N),n2.u=n2.__N=void 0})):(i2.__h.forEach(z$2),i2.__h.forEach(B$1),i2.__h=[],t=0)),u=r$1},c.diffed=function(n){v&&v(n);var t2=n.__c;t2&&t2.__H&&(t2.__H.__h.length&&(f.push(t2)!==1&&i===c.requestAnimationFrame||((i=c.requestAnimationFrame)||w$1)(j$1)),t2.__H.__.forEach(function(n2){n2.u&&(n2.__H=n2.u),n2.u=void 0})),u=r$1=null},c.__c=function(n,t2){t2.some(function(n2){try{n2.__h.forEach(z$2),n2.__h=n2.__h.filter(function(n3){return!n3.__||B$1(n3)})}catch(r2){t2.some(function(n3){n3.__h&&(n3.__h=[])}),t2=[],c.__e(r2,n2.__v)}}),l&&l(n,t2)},c.unmount=function(n){m&&m(n);var t2,r2=n.__c;r2&&r2.__H&&(r2.__H.__.forEach(function(n2){try{z$2(n2)}catch(n3){t2=n3}}),r2.__H=void 0,t2&&c.__e(t2,r2.__v))};var k$1=typeof requestAnimationFrame=="function";function w$1(n){var t2,r2=function(){clearTimeout(u2),k$1&&cancelAnimationFrame(t2),setTimeout(n)},u2=setTimeout(r2,100);k$1&&(t2=requestAnimationFrame(r2))}function z$2(n){var t2=r$1,u2=n.__c;typeof u2=="function"&&(n.__c=void 0,u2()),r$1=t2}function B$1(n){var t2=r$1;n.__c=n.__(),r$1=t2}function C$1(n,t2){return!n||n.length!==t2.length||t2.some(function(t3,r2){return t3!==n[r2]})}function D$1(n,t2){return typeof t2=="function"?t2(n):t2}function g(n,t2){for(var e2 in t2)n[e2]=t2[e2];return n}function E(n,t2){for(var e2 in n)if(e2!=="__source"&&!(e2 in t2))return!0;for(var r2 in t2)if(r2!=="__source"&&n[r2]!==t2[r2])return!0;return!1}function C(n,t2){var e2=t2(),r2=d({t:{__:e2,u:t2}}),u2=r2[0].t,o2=r2[1];return _(function(){u2.__=e2,u2.u=t2,x(u2)&&o2({t:u2})},[n,e2,t2]),y(function(){return x(u2)&&o2({t:u2}),n(function(){x(u2)&&o2({t:u2})})},[n]),e2}function x(n){var t2,e2,r2=n.u,u2=n.__;try{var o2=r2();return!((t2=u2)===(e2=o2)&&(t2!==0||1/t2==1/e2)||t2!=t2&&e2!=e2)}catch{return!0}}function R(n){n()}function w(n){return n}function k(){return[!1,R]}var I=_;function N(n,t2){this.props=n,this.context=t2}function M(n,e2){function r2(n2){var t2=this.props.ref,r3=t2==n2.ref;return!r3&&t2&&(t2.call?t2(null):t2.current=null),e2?!e2(this.props,n2)||!r3:E(this.props,n2)}function u2(e3){return this.shouldComponentUpdate=r2,pluginUi_jsxRuntime._(n,e3)}return u2.displayName="Memo("+(n.displayName||n.name)+")",u2.prototype.isReactComponent=!0,u2.__f=!0,u2}(N.prototype=new pluginUi_jsxRuntime.x).isPureReactComponent=!0,N.prototype.shouldComponentUpdate=function(n,t2){return E(this.props,n)||E(this.state,t2)};var T=pluginUi_jsxRuntime.l.__b;pluginUi_jsxRuntime.l.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),T&&T(n)};var A=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function D(n){function t2(t3){var e2=g({},t3);return delete e2.ref,n(e2,t3.ref||null)}return t2.$$typeof=A,t2.render=t2,t2.prototype.isReactComponent=t2.__f=!0,t2.displayName="ForwardRef("+(n.displayName||n.name)+")",t2}var L=function(n,t2){return n==null?null:pluginUi_jsxRuntime.H(pluginUi_jsxRuntime.H(n).map(t2))},O={map:L,forEach:L,count:function(n){return n?pluginUi_jsxRuntime.H(n).length:0},only:function(n){var t2=pluginUi_jsxRuntime.H(n);if(t2.length!==1)throw"Children.only";return t2[0]},toArray:pluginUi_jsxRuntime.H},F=pluginUi_jsxRuntime.l.__e;pluginUi_jsxRuntime.l.__e=function(n,t2,e2,r2){if(n.then){for(var u2,o2=t2;o2=o2.__;)if((u2=o2.__c)&&u2.__c)return t2.__e==null&&(t2.__e=e2.__e,t2.__k=e2.__k),u2.__c(n,t2)}F(n,t2,e2,r2)};var U=pluginUi_jsxRuntime.l.unmount;function V(n,t2,e2){return n&&(n.__c&&n.__c.__H&&(n.__c.__H.__.forEach(function(n2){typeof n2.__c=="function"&&n2.__c()}),n.__c.__H=null),(n=g({},n)).__c!=null&&(n.__c.__P===e2&&(n.__c.__P=t2),n.__c.__e=!0,n.__c=null),n.__k=n.__k&&n.__k.map(function(n2){return V(n2,t2,e2)})),n}function W(n,t2,e2){return n&&e2&&(n.__v=null,n.__k=n.__k&&n.__k.map(function(n2){return W(n2,t2,e2)}),n.__c&&n.__c.__P===t2&&(n.__e&&e2.appendChild(n.__e),n.__c.__e=!0,n.__c.__P=e2)),n}function P(){this.__u=0,this.o=null,this.__b=null}function j(n){var t2=n.__.__c;return t2&&t2.__a&&t2.__a(n)}function z$1(n){var e2,r2,u2;function o2(o3){if(e2||(e2=n()).then(function(n2){r2=n2.default||n2},function(n2){u2=n2}),u2)throw u2;if(!r2)throw e2;return pluginUi_jsxRuntime._(r2,o3)}return o2.displayName="Lazy",o2.__f=!0,o2}function B(){this.i=null,this.l=null}pluginUi_jsxRuntime.l.unmount=function(n){var t2=n.__c;t2&&t2.__R&&t2.__R(),t2&&32&n.__u&&(n.type=null),U&&U(n)},(P.prototype=new pluginUi_jsxRuntime.x).__c=function(n,t2){var e2=t2.__c,r2=this;r2.o==null&&(r2.o=[]),r2.o.push(e2);var u2=j(r2.__v),o2=!1,i2=function(){o2||(o2=!0,e2.__R=null,u2?u2(l2):l2())};e2.__R=i2;var l2=function(){if(!--r2.__u){if(r2.state.__a){var n2=r2.state.__a;r2.__v.__k[0]=W(n2,n2.__c.__P,n2.__c.__O)}var t3;for(r2.setState({__a:r2.__b=null});t3=r2.o.pop();)t3.forceUpdate()}};r2.__u++||32&t2.__u||r2.setState({__a:r2.__b=r2.__v.__k[0]}),n.then(i2,i2)},P.prototype.componentWillUnmount=function(){this.o=[]},P.prototype.render=function(n,e2){if(this.__b){if(this.__v.__k){var r2=document.createElement("div"),o2=this.__v.__k[0].__c;this.__v.__k[0]=V(this.__b,r2,o2.__O=o2.__P)}this.__b=null}var i2=e2.__a&&pluginUi_jsxRuntime._(pluginUi_jsxRuntime.k,null,n.fallback);return i2&&(i2.__u&=-33),[pluginUi_jsxRuntime._(pluginUi_jsxRuntime.k,null,e2.__a?null:n.children),i2]};var H=function(n,t2,e2){if(++e2[1]===e2[0]&&n.l.delete(t2),n.props.revealOrder&&(n.props.revealOrder[0]!=="t"||!n.l.size))for(e2=n.i;e2;){for(;e2.length>3;)e2.pop()();if(e2[1]<e2[0])break;n.i=e2=e2[2]}};function Z(n){return this.getChildContext=function(){return n.context},n.children}function Y(n){var e2=this,r2=n.h;if(e2.componentWillUnmount=function(){pluginUi_jsxRuntime.E(null,e2.v),e2.v=null,e2.h=null},e2.h&&e2.h!==r2&&e2.componentWillUnmount(),!e2.v){for(var u2=e2.__v;u2!==null&&!u2.__m&&u2.__!==null;)u2=u2.__;e2.h=r2,e2.v={nodeType:1,parentNode:r2,childNodes:[],__k:{__m:u2.__m},contains:function(){return!0},appendChild:function(n2){this.childNodes.push(n2),e2.h.appendChild(n2)},insertBefore:function(n2,t2){this.childNodes.push(n2),e2.h.insertBefore(n2,t2)},removeChild:function(n2){this.childNodes.splice(this.childNodes.indexOf(n2)>>>1,1),e2.h.removeChild(n2)}}}pluginUi_jsxRuntime.E(pluginUi_jsxRuntime._(Z,{context:e2.context},n.__v),e2.v)}function $(n,e2){var r2=pluginUi_jsxRuntime._(Y,{__v:n,h:e2});return r2.containerInfo=e2,r2}(B.prototype=new pluginUi_jsxRuntime.x).__a=function(n){var t2=this,e2=j(t2.__v),r2=t2.l.get(n);return r2[0]++,function(u2){var o2=function(){t2.props.revealOrder?(r2.push(u2),H(t2,n,r2)):u2()};e2?e2(o2):o2()}},B.prototype.render=function(n){this.i=null,this.l=new Map;var t2=pluginUi_jsxRuntime.H(n.children);n.revealOrder&&n.revealOrder[0]==="b"&&t2.reverse();for(var e2=t2.length;e2--;)this.l.set(t2[e2],this.i=[1,0,this.i]);return n.children},B.prototype.componentDidUpdate=B.prototype.componentDidMount=function(){var n=this;this.l.forEach(function(t2,e2){H(n,e2,t2)})};var q=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,G=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,J=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,K=/[A-Z0-9]/g,Q=typeof document<"u",X=function(n){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(n)};function nn(n,t2,e2){return t2.__k==null&&(t2.textContent=""),pluginUi_jsxRuntime.E(n,t2),typeof e2=="function"&&e2(),n?n.__c:null}function tn(n,t2,e2){return pluginUi_jsxRuntime.G(n,t2),typeof e2=="function"&&e2(),n?n.__c:null}pluginUi_jsxRuntime.x.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t2){Object.defineProperty(pluginUi_jsxRuntime.x.prototype,t2,{configurable:!0,get:function(){return this["UNSAFE_"+t2]},set:function(n){Object.defineProperty(this,t2,{configurable:!0,writable:!0,value:n})}})});var en=pluginUi_jsxRuntime.l.event;function rn(){}function un(){return this.cancelBubble}function on(){return this.defaultPrevented}pluginUi_jsxRuntime.l.event=function(n){return en&&(n=en(n)),n.persist=rn,n.isPropagationStopped=un,n.isDefaultPrevented=on,n.nativeEvent=n};var ln,cn$1={enumerable:!1,configurable:!0,get:function(){return this.class}},fn=pluginUi_jsxRuntime.l.vnode;pluginUi_jsxRuntime.l.vnode=function(n){typeof n.type=="string"&&function(n2){var t2=n2.props,e2=n2.type,u2={},o2=e2.indexOf("-")===-1;for(var i2 in t2){var l2=t2[i2];if(!(i2==="value"&&"defaultValue"in t2&&l2==null||Q&&i2==="children"&&e2==="noscript"||i2==="class"||i2==="className")){var c2=i2.toLowerCase();i2==="defaultValue"&&"value"in t2&&t2.value==null?i2="value":i2==="download"&&l2===!0?l2="":c2==="translate"&&l2==="no"?l2=!1:c2[0]==="o"&&c2[1]==="n"?c2==="ondoubleclick"?i2="ondblclick":c2!=="onchange"||e2!=="input"&&e2!=="textarea"||X(t2.type)?c2==="onfocus"?i2="onfocusin":c2==="onblur"?i2="onfocusout":J.test(i2)&&(i2=c2):c2=i2="oninput":o2&&G.test(i2)?i2=i2.replace(K,"-$&").toLowerCase():l2===null&&(l2=void 0),c2==="oninput"&&u2[i2=c2]&&(i2="oninputCapture"),u2[i2]=l2}}e2=="select"&&u2.multiple&&Array.isArray(u2.value)&&(u2.value=pluginUi_jsxRuntime.H(t2.children).forEach(function(n3){n3.props.selected=u2.value.indexOf(n3.props.value)!=-1})),e2=="select"&&u2.defaultValue!=null&&(u2.value=pluginUi_jsxRuntime.H(t2.children).forEach(function(n3){n3.props.selected=u2.multiple?u2.defaultValue.indexOf(n3.props.value)!=-1:u2.defaultValue==n3.props.value})),t2.class&&!t2.className?(u2.class=t2.class,Object.defineProperty(u2,"className",cn$1)):(t2.className&&!t2.class||t2.class&&t2.className)&&(u2.class=u2.className=t2.className),n2.props=u2}(n),n.$$typeof=q,fn&&fn(n)};var an=pluginUi_jsxRuntime.l.__r;pluginUi_jsxRuntime.l.__r=function(n){an&&an(n),ln=n.__c};var sn=pluginUi_jsxRuntime.l.diffed;pluginUi_jsxRuntime.l.diffed=function(n){sn&&sn(n);var t2=n.props,e2=n.__e;e2!=null&&n.type==="textarea"&&"value"in t2&&t2.value!==e2.value&&(e2.value=t2.value==null?"":t2.value),ln=null};var hn={ReactCurrentDispatcher:{current:{readContext:function(n){return ln.__n[n.__c].props.value},useCallback:q$1,useContext:x$1,useDebugValue:P$1,useDeferredValue:w,useEffect:y,useId:g$1,useImperativeHandle:F$1,useInsertionEffect:I,useLayoutEffect:_,useMemo:T$1,useReducer:h,useRef:A$1,useState:d,useSyncExternalStore:C,useTransition:k}}};function dn(n){return pluginUi_jsxRuntime._.bind(null,n)}function mn(n){return!!n&&n.$$typeof===q}function pn(n){return mn(n)&&n.type===pluginUi_jsxRuntime.k}function yn(n){return!!n&&!!n.displayName&&(typeof n.displayName=="string"||n.displayName instanceof String)&&n.displayName.startsWith("Memo(")}function _n(n){return mn(n)?pluginUi_jsxRuntime.J.apply(null,arguments):n}function bn(n){return!!n.__k&&(pluginUi_jsxRuntime.E(null,n),!0)}function Sn(n){return n&&(n.base||n.nodeType===1&&n)||null}var gn=function(n,t2){return n(t2)},En=function(n,t2){return n(t2)},Cn=pluginUi_jsxRuntime.k,xn=mn,Rn={useState:d,useId:g$1,useReducer:h,useEffect:y,useLayoutEffect:_,useInsertionEffect:I,useTransition:k,useDeferredValue:w,useSyncExternalStore:C,startTransition:R,useRef:A$1,useImperativeHandle:F$1,useMemo:T$1,useCallback:q$1,useContext:x$1,useDebugValue:P$1,version:"18.3.1",Children:O,render:nn,hydrate:tn,unmountComponentAtNode:bn,createPortal:$,createElement:pluginUi_jsxRuntime._,createContext:pluginUi_jsxRuntime.K,createFactory:dn,cloneElement:_n,createRef:pluginUi_jsxRuntime.b,Fragment:pluginUi_jsxRuntime.k,isValidElement:mn,isElement:xn,isFragment:pn,isMemo:yn,findDOMNode:Sn,Component:pluginUi_jsxRuntime.x,PureComponent:N,memo:M,forwardRef:D,flushSync:En,unstable_batchedUpdates:gn,StrictMode:Cn,Suspense:P,SuspenseList:B,lazy:z$1,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:hn},generateId$1=(length=16)=>Math.random().toString(36).substring(2,length+2),DEFAULT_OPTIONS={maxReconnectAttempts:5,reconnectDelay:1e3,requestTimeout:3e4},WebSocketRpcBridge=class{constructor(options={}){this.ws=null,this.pendingRequests=new Map,this.reconnectAttempts=0,this.methods={},this.isIntentionalClose=!1,this.options={...DEFAULT_OPTIONS,...options}}register(methodHandlers){Object.entries(methodHandlers).forEach(([methodName,handler])=>{this.methods[methodName]={handler}})}callMethod(method,payload,onUpdate){if(!this.ws)throw new Error("WebSocket is not connected");const id=generateId$1(),requestMessage={id,messageType:"request",method,payload};return new Promise((resolve,reject)=>{var _a;const timeout=setTimeout(()=>{this.pendingRequests.delete(id),reject(new Error(`Request timed out: ${method}`))},this.options.requestTimeout);this.pendingRequests.set(id,{resolve,reject,timeout,onUpdate}),(_a=this.ws)==null||_a.send(JSON.stringify(requestMessage))})}setupWebSocketHandlers(ws){ws.onmessage=event=>{try{const message=JSON.parse(event.data);this.handleMessage(message)}catch(error){console.error("Error handling WebSocket message:",error)}},ws.onclose=()=>{this.handleDisconnect()},ws.onerror=event=>{console.error("WebSocket error:",event)}}handleMessage(message){const{messageType,id}=message;switch(messageType){case"request":this.handleRequest(message);break;case"response":this.handleResponse(id,message.payload);break;case"update":this.handleUpdate(id,message.payload);break;case"error":this.handleError(id,message.error.message);break;default:console.warn(`Unknown message type: ${messageType}`)}}async handleRequest(message){const{id,method,payload}=message;if(!method){this.sendError(id,"Method name is required");return}const methodDef=this.methods[method];if(!methodDef){this.sendError(id,`Method not found: ${method}`);return}try{const sendUpdate=update=>{this.sendUpdate(id,method,update)},result=await methodDef.handler(payload,sendUpdate);this.sendResponse(id,method,result)}catch(error){this.sendError(id,error instanceof Error?error.message:String(error))}}handleResponse(id,payload){const pendingRequest=this.pendingRequests.get(id);if(!pendingRequest){console.warn(`Received response for unknown request ID: ${id}`);return}clearTimeout(pendingRequest.timeout),this.pendingRequests.delete(id),pendingRequest.resolve(payload)}handleUpdate(id,payload){const pendingRequest=this.pendingRequests.get(id);if(!pendingRequest||!pendingRequest.onUpdate){console.warn(`Received update for unknown request ID: ${id}`);return}pendingRequest.onUpdate(payload)}handleError(id,error){const pendingRequest=this.pendingRequests.get(id);if(!pendingRequest){console.warn(`Received error for unknown request ID: ${id}`);return}clearTimeout(pendingRequest.timeout),this.pendingRequests.delete(id),pendingRequest.reject(new Error(error))}sendResponse(id,method,payload){if(!this.ws)throw new Error("WebSocket is not connected");const responseMessage={id,messageType:"response",method,payload};this.ws.send(JSON.stringify(responseMessage))}sendUpdate(id,method,payload){if(!this.ws)throw new Error("WebSocket is not connected");const updateMessage={id,messageType:"update",method,payload};this.ws.send(JSON.stringify(updateMessage))}sendError(id,errorMessage){if(!this.ws)throw new Error("WebSocket is not connected");const errorResponse={id,messageType:"error",error:{message:errorMessage}};this.ws.send(JSON.stringify(errorResponse))}handleDisconnect(){if(this.isIntentionalClose){console.log("WebSocket closed intentionally, not attempting to reconnect"),this.clearPendingRequests(new Error("Connection closed by user"));return}this.reconnectAttempts<this.options.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`),setTimeout(()=>this.reconnect(),this.options.reconnectDelay*this.reconnectAttempts)):(console.error("Max reconnection attempts reached"),this.clearPendingRequests(new Error("Connection closed")))}clearPendingRequests(error){this.pendingRequests.forEach(({reject})=>{reject(error)}),this.pendingRequests.clear()}async close(){this.isIntentionalClose=!0,this.ws&&(this.ws.close(),this.ws=null),this.clearPendingRequests(new Error("Connection closed by user"))}};function validateWithZod(schema,data,context,silent=!1){const result=schema.safeParse(data);if(!result.success){const error=new Error(`Validation failed for ${context}: ${result.error.message}`);if(silent)return console.error(error),data;throw error}return result.data}var ZodTypedBridge=class{constructor(bridge,contract2){this.bridge=bridge,this.contract=contract2,this.call=new Proxy({},{get:(target,prop)=>(request,options)=>this.callMethod(prop,request,options)})}async callMethod(method,request,options){const methodContract=this.contract.consumes[method];if(!methodContract)throw new Error(`Method ${String(method)} not found in contract`);const validatedRequest=validateWithZod(methodContract.request,request,`request for method ${String(method)}`),onUpdate=options!=null&&options.onUpdate&&methodContract.update?update=>{var _a;if(methodContract.update)try{const validatedUpdate=validateWithZod(methodContract.update,update,`update for method ${String(method)}`,!0);(_a=options.onUpdate)==null||_a.call(options,validatedUpdate)}catch(error){console.error("Update validation failed:",error)}}:void 0,response=await this.bridge.callMethod(method,validatedRequest,onUpdate);return validateWithZod(methodContract.response,response,`response for method ${String(method)}`)}register(implementations){const wrappedImplementations={};for(const[method,implementation]of Object.entries(implementations)){const methodContract=this.contract.serves[method];if(!methodContract)throw new Error(`Method ${method} not found in contract`);wrappedImplementations[method]=async(request,sendUpdate)=>{const validatedRequest=validateWithZod(methodContract.request,request,`request for method ${method}`),wrappedSendUpdate=methodContract.update&&sendUpdate?update=>{if(methodContract.update)try{const validatedUpdate=validateWithZod(methodContract.update,update,`update for method ${method}`,!0);sendUpdate(validatedUpdate)}catch(error){console.error("Update validation failed:",error)}}:void 0,response=await implementation(validatedRequest,{sendUpdate:wrappedSendUpdate});return validateWithZod(methodContract.response,response,`response for method ${method}`)}}this.bridge.register(wrappedImplementations)}async close(){await this.bridge.close()}},ClientBridge=class extends WebSocketRpcBridge{constructor(url,options){super(options),this.reconnectTimer=null,this.url=url}call(method,payload,onUpdate){return this.callMethod(method,payload,onUpdate)}reconnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=setTimeout(async()=>{try{await this.connect()}catch{this.reconnect()}},this.options.reconnectDelay)}connect(){return new Promise((resolve,reject)=>{try{const ws=new window.WebSocket(this.url);ws.onopen=()=>{this.ws=ws,this.setupWebSocketHandlers(ws),resolve()},ws.onerror=()=>{reject(new Error("Failed to connect to WebSocket server"))}}catch(error){reject(error)}})}},ZodClient=class extends ZodTypedBridge{constructor(url,contract2,options){super(new ClientBridge(url,options),{serves:contract2.client||{},consumes:contract2.server||{}})}connect(){return this.bridge.connect()}};function createSRPCClientBridge(url,contract2,options){return new ZodClient(url,contract2,options)}var util;(function(util2){util2.assertEqual=val=>val;function assertIs(_arg){}util2.assertIs=assertIs;function assertNever(_x){throw new Error}util2.assertNever=assertNever,util2.arrayToEnum=items=>{const obj={};for(const item of items)obj[item]=item;return obj},util2.getValidEnumValues=obj=>{const validKeys=util2.objectKeys(obj).filter(k2=>typeof obj[obj[k2]]!="number"),filtered={};for(const k2 of validKeys)filtered[k2]=obj[k2];return util2.objectValues(filtered)},util2.objectValues=obj=>util2.objectKeys(obj).map(function(e2){return obj[e2]}),util2.objectKeys=typeof Object.keys=="function"?obj=>Object.keys(obj):object=>{const keys=[];for(const key in object)Object.prototype.hasOwnProperty.call(object,key)&&keys.push(key);return keys},util2.find=(arr,checker)=>{for(const item of arr)if(checker(item))return item},util2.isInteger=typeof Number.isInteger=="function"?val=>Number.isInteger(val):val=>typeof val=="number"&&isFinite(val)&&Math.floor(val)===val;function joinValues(array,separator=" | "){return array.map(val=>typeof val=="string"?`'${val}'`:val).join(separator)}util2.joinValues=joinValues,util2.jsonStringifyReplacer=(_2,value)=>typeof value=="bigint"?value.toString():value})(util||(util={}));var objectUtil;(function(objectUtil2){objectUtil2.mergeShapes=(first,second)=>({...first,...second})})(objectUtil||(objectUtil={}));const ZodParsedType=util.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),getParsedType=data=>{switch(typeof data){case"undefined":return ZodParsedType.undefined;case"string":return ZodParsedType.string;case"number":return isNaN(data)?ZodParsedType.nan:ZodParsedType.number;case"boolean":return ZodParsedType.boolean;case"function":return ZodParsedType.function;case"bigint":return ZodParsedType.bigint;case"symbol":return ZodParsedType.symbol;case"object":return Array.isArray(data)?ZodParsedType.array:data===null?ZodParsedType.null:data.then&&typeof data.then=="function"&&data.catch&&typeof data.catch=="function"?ZodParsedType.promise:typeof Map<"u"&&data instanceof Map?ZodParsedType.map:typeof Set<"u"&&data instanceof Set?ZodParsedType.set:typeof Date<"u"&&data instanceof Date?ZodParsedType.date:ZodParsedType.object;default:return ZodParsedType.unknown}},ZodIssueCode=util.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),quotelessJson=obj=>JSON.stringify(obj,null,2).replace(/"([^"]+)":/g,"$1:");class ZodError extends Error{get errors(){return this.issues}constructor(issues){super(),this.issues=[],this.addIssue=sub=>{this.issues=[...this.issues,sub]},this.addIssues=(subs=[])=>{this.issues=[...this.issues,...subs]};const actualProto=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,actualProto):this.__proto__=actualProto,this.name="ZodError",this.issues=issues}format(_mapper){const mapper=_mapper||function(issue){return issue.message},fieldErrors={_errors:[]},processError=error=>{for(const issue of error.issues)if(issue.code==="invalid_union")issue.unionErrors.map(processError);else if(issue.code==="invalid_return_type")processError(issue.returnTypeError);else if(issue.code==="invalid_arguments")processError(issue.argumentsError);else if(issue.path.length===0)fieldErrors._errors.push(mapper(issue));else{let curr=fieldErrors,i2=0;for(;i2<issue.path.length;){const el=issue.path[i2];i2===issue.path.length-1?(curr[el]=curr[el]||{_errors:[]},curr[el]._errors.push(mapper(issue))):curr[el]=curr[el]||{_errors:[]},curr=curr[el],i2++}}};return processError(this),fieldErrors}static assert(value){if(!(value instanceof ZodError))throw new Error(`Not a ZodError: ${value}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,util.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(mapper=issue=>issue.message){const fieldErrors={},formErrors=[];for(const sub of this.issues)sub.path.length>0?(fieldErrors[sub.path[0]]=fieldErrors[sub.path[0]]||[],fieldErrors[sub.path[0]].push(mapper(sub))):formErrors.push(mapper(sub));return{formErrors,fieldErrors}}get formErrors(){return this.flatten()}}ZodError.create=issues=>new ZodError(issues);const errorMap=(issue,_ctx)=>{let message;switch(issue.code){case ZodIssueCode.invalid_type:issue.received===ZodParsedType.undefined?message="Required":message=`Expected ${issue.expected}, received ${issue.received}`;break;case ZodIssueCode.invalid_literal:message=`Invalid literal value, expected ${JSON.stringify(issue.expected,util.jsonStringifyReplacer)}`;break;case ZodIssueCode.unrecognized_keys:message=`Unrecognized key(s) in object: ${util.joinValues(issue.keys,", ")}`;break;case ZodIssueCode.invalid_union:message="Invalid input";break;case ZodIssueCode.invalid_union_discriminator:message=`Invalid discriminator value. Expected ${util.joinValues(issue.options)}`;break;case ZodIssueCode.invalid_enum_value:message=`Invalid enum value. Expected ${util.joinValues(issue.options)}, received '${issue.received}'`;break;case ZodIssueCode.invalid_arguments:message="Invalid function arguments";break;case ZodIssueCode.invalid_return_type:message="Invalid function return type";break;case ZodIssueCode.invalid_date:message="Invalid date";break;case ZodIssueCode.invalid_string:typeof issue.validation=="object"?"includes"in issue.validation?(message=`Invalid input: must include "${issue.validation.includes}"`,typeof issue.validation.position=="number"&&(message=`${message} at one or more positions greater than or equal to ${issue.validation.position}`)):"startsWith"in issue.validation?message=`Invalid input: must start with "${issue.validation.startsWith}"`:"endsWith"in issue.validation?message=`Invalid input: must end with "${issue.validation.endsWith}"`:util.assertNever(issue.validation):issue.validation!=="regex"?message=`Invalid ${issue.validation}`:message="Invalid";break;case ZodIssueCode.too_small:issue.type==="array"?message=`Array must contain ${issue.exact?"exactly":issue.inclusive?"at least":"more than"} ${issue.minimum} element(s)`:issue.type==="string"?message=`String must contain ${issue.exact?"exactly":issue.inclusive?"at least":"over"} ${issue.minimum} character(s)`:issue.type==="number"?message=`Number must be ${issue.exact?"exactly equal to ":issue.inclusive?"greater than or equal to ":"greater than "}${issue.minimum}`:issue.type==="date"?message=`Date must be ${issue.exact?"exactly equal to ":issue.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(issue.minimum))}`:message="Invalid input";break;case ZodIssueCode.too_big:issue.type==="array"?message=`Array must contain ${issue.exact?"exactly":issue.inclusive?"at most":"less than"} ${issue.maximum} element(s)`:issue.type==="string"?message=`String must contain ${issue.exact?"exactly":issue.inclusive?"at most":"under"} ${issue.maximum} character(s)`:issue.type==="number"?message=`Number must be ${issue.exact?"exactly":issue.inclusive?"less than or equal to":"less than"} ${issue.maximum}`:issue.type==="bigint"?message=`BigInt must be ${issue.exact?"exactly":issue.inclusive?"less than or equal to":"less than"} ${issue.maximum}`:issue.type==="date"?message=`Date must be ${issue.exact?"exactly":issue.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(issue.maximum))}`:message="Invalid input";break;case ZodIssueCode.custom:message="Invalid input";break;case ZodIssueCode.invalid_intersection_types:message="Intersection results could not be merged";break;case ZodIssueCode.not_multiple_of:message=`Number must be a multiple of ${issue.multipleOf}`;break;case ZodIssueCode.not_finite:message="Number must be finite";break;default:message=_ctx.defaultError,util.assertNever(issue)}return{message}};let overrideErrorMap=errorMap;function setErrorMap(map){overrideErrorMap=map}function getErrorMap(){return overrideErrorMap}const makeIssue=params=>{const{data,path,errorMaps,issueData}=params,fullPath=[...path,...issueData.path||[]],fullIssue={...issueData,path:fullPath};if(issueData.message!==void 0)return{...issueData,path:fullPath,message:issueData.message};let errorMessage="";const maps=errorMaps.filter(m2=>!!m2).slice().reverse();for(const map of maps)errorMessage=map(fullIssue,{data,defaultError:errorMessage}).message;return{...issueData,path:fullPath,message:errorMessage}},EMPTY_PATH=[];function addIssueToContext(ctx,issueData){const overrideMap=getErrorMap(),issue=makeIssue({issueData,data:ctx.data,path:ctx.path,errorMaps:[ctx.common.contextualErrorMap,ctx.schemaErrorMap,overrideMap,overrideMap===errorMap?void 0:errorMap].filter(x2=>!!x2)});ctx.common.issues.push(issue)}class ParseStatus{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(status,results){const arrayValue=[];for(const s2 of results){if(s2.status==="aborted")return INVALID;s2.status==="dirty"&&status.dirty(),arrayValue.push(s2.value)}return{status:status.value,value:arrayValue}}static async mergeObjectAsync(status,pairs){const syncPairs=[];for(const pair of pairs){const key=await pair.key,value=await pair.value;syncPairs.push({key,value})}return ParseStatus.mergeObjectSync(status,syncPairs)}static mergeObjectSync(status,pairs){const finalObject={};for(const pair of pairs){const{key,value}=pair;if(key.status==="aborted"||value.status==="aborted")return INVALID;key.status==="dirty"&&status.dirty(),value.status==="dirty"&&status.dirty(),key.value!=="__proto__"&&(typeof value.value<"u"||pair.alwaysSet)&&(finalObject[key.value]=value.value)}return{status:status.value,value:finalObject}}}const INVALID=Object.freeze({status:"aborted"}),DIRTY=value=>({status:"dirty",value}),OK=value=>({status:"valid",value}),isAborted=x2=>x2.status==="aborted",isDirty=x2=>x2.status==="dirty",isValid=x2=>x2.status==="valid",isAsync=x2=>typeof Promise<"u"&&x2 instanceof Promise;function __classPrivateFieldGet(receiver,state,kind,f2){if(typeof state=="function"?receiver!==state||!0:!state.has(receiver))throw new TypeError("Cannot read private member from an object whose class did not declare it");return state.get(receiver)}function __classPrivateFieldSet(receiver,state,value,kind,f2){if(typeof state=="function"?receiver!==state||!0:!state.has(receiver))throw new TypeError("Cannot write private member to an object whose class did not declare it");return state.set(receiver,value),value}var errorUtil;(function(errorUtil2){errorUtil2.errToObj=message=>typeof message=="string"?{message}:message||{},errorUtil2.toString=message=>typeof message=="string"?message:message==null?void 0:message.message})(errorUtil||(errorUtil={}));var _ZodEnum_cache,_ZodNativeEnum_cache;class ParseInputLazyPath{constructor(parent,value,path,key){this._cachedPath=[],this.parent=parent,this.data=value,this._path=path,this._key=key}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const handleResult=(ctx,result)=>{if(isValid(result))return{success:!0,data:result.value};if(!ctx.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const error=new ZodError(ctx.common.issues);return this._error=error,this._error}}};function processCreateParams(params){if(!params)return{};const{errorMap:errorMap2,invalid_type_error,required_error,description}=params;if(errorMap2&&(invalid_type_error||required_error))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return errorMap2?{errorMap:errorMap2,description}:{errorMap:(iss,ctx)=>{var _a,_b;const{message}=params;return iss.code==="invalid_enum_value"?{message:message??ctx.defaultError}:typeof ctx.data>"u"?{message:(_a=message??required_error)!==null&&_a!==void 0?_a:ctx.defaultError}:iss.code!=="invalid_type"?{message:ctx.defaultError}:{message:(_b=message??invalid_type_error)!==null&&_b!==void 0?_b:ctx.defaultError}},description}}class ZodType{get description(){return this._def.description}_getType(input){return getParsedType(input.data)}_getOrReturnCtx(input,ctx){return ctx||{common:input.parent.common,data:input.data,parsedType:getParsedType(input.data),schemaErrorMap:this._def.errorMap,path:input.path,parent:input.parent}}_processInputParams(input){return{status:new ParseStatus,ctx:{common:input.parent.common,data:input.data,parsedType:getParsedType(input.data),schemaErrorMap:this._def.errorMap,path:input.path,parent:input.parent}}}_parseSync(input){const result=this._parse(input);if(isAsync(result))throw new Error("Synchronous parse encountered promise.");return result}_parseAsync(input){const result=this._parse(input);return Promise.resolve(result)}parse(data,params){const result=this.safeParse(data,params);if(result.success)return result.data;throw result.error}safeParse(data,params){var _a;const ctx={common:{issues:[],async:(_a=params==null?void 0:params.async)!==null&&_a!==void 0?_a:!1,contextualErrorMap:params==null?void 0:params.errorMap},path:(params==null?void 0:params.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data,parsedType:getParsedType(data)},result=this._parseSync({data,path:ctx.path,parent:ctx});return handleResult(ctx,result)}"~validate"(data){var _a,_b;const ctx={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data,parsedType:getParsedType(data)};if(!this["~standard"].async)try{const result=this._parseSync({data,path:[],parent:ctx});return isValid(result)?{value:result.value}:{issues:ctx.common.issues}}catch(err){!((_b=(_a=err==null?void 0:err.message)===null||_a===void 0?void 0:_a.toLowerCase())===null||_b===void 0)&&_b.includes("encountered")&&(this["~standard"].async=!0),ctx.common={issues:[],async:!0}}return this._parseAsync({data,path:[],parent:ctx}).then(result=>isValid(result)?{value:result.value}:{issues:ctx.common.issues})}async parseAsync(data,params){const result=await this.safeParseAsync(data,params);if(result.success)return result.data;throw result.error}async safeParseAsync(data,params){const ctx={common:{issues:[],contextualErrorMap:params==null?void 0:params.errorMap,async:!0},path:(params==null?void 0:params.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data,parsedType:getParsedType(data)},maybeAsyncResult=this._parse({data,path:ctx.path,parent:ctx}),result=await(isAsync(maybeAsyncResult)?maybeAsyncResult:Promise.resolve(maybeAsyncResult));return handleResult(ctx,result)}refine(check,message){const getIssueProperties=val=>typeof message=="string"||typeof message>"u"?{message}:typeof message=="function"?message(val):message;return this._refinement((val,ctx)=>{const result=check(val),setError=()=>ctx.addIssue({code:ZodIssueCode.custom,...getIssueProperties(val)});return typeof Promise<"u"&&result instanceof Promise?result.then(data=>data?!0:(setError(),!1)):result?!0:(setError(),!1)})}refinement(check,refinementData){return this._refinement((val,ctx)=>check(val)?!0:(ctx.addIssue(typeof refinementData=="function"?refinementData(val,ctx):refinementData),!1))}_refinement(refinement){return new ZodEffects({schema:this,typeName:ZodFirstPartyTypeKind.ZodEffects,effect:{type:"refinement",refinement}})}superRefine(refinement){return this._refinement(refinement)}constructor(def){this.spa=this.safeParseAsync,this._def=def,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:data=>this["~validate"](data)}}optional(){return ZodOptional.create(this,this._def)}nullable(){return ZodNullable.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ZodArray.create(this)}promise(){return ZodPromise.create(this,this._def)}or(option){return ZodUnion.create([this,option],this._def)}and(incoming){return ZodIntersection.create(this,incoming,this._def)}transform(transform){return new ZodEffects({...processCreateParams(this._def),schema:this,typeName:ZodFirstPartyTypeKind.ZodEffects,effect:{type:"transform",transform}})}default(def){const defaultValueFunc=typeof def=="function"?def:()=>def;return new ZodDefault({...processCreateParams(this._def),innerType:this,defaultValue:defaultValueFunc,typeName:ZodFirstPartyTypeKind.ZodDefault})}brand(){return new ZodBranded({typeName:ZodFirstPartyTypeKind.ZodBranded,type:this,...processCreateParams(this._def)})}catch(def){const catchValueFunc=typeof def=="function"?def:()=>def;return new ZodCatch({...processCreateParams(this._def),innerType:this,catchValue:catchValueFunc,typeName:ZodFirstPartyTypeKind.ZodCatch})}describe(description){const This=this.constructor;return new This({...this._def,description})}pipe(target){return ZodPipeline.create(this,target)}readonly(){return ZodReadonly.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const cuidRegex=/^c[^\s-]{8,}$/i,cuid2Regex=/^[0-9a-z]+$/,ulidRegex=/^[0-9A-HJKMNP-TV-Z]{26}$/i,uuidRegex=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,nanoidRegex=/^[a-z0-9_-]{21}$/i,jwtRegex=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,durationRegex=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,emailRegex=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,_emojiRegex="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let emojiRegex;const ipv4Regex=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4CidrRegex=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Regex=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ipv6CidrRegex=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64Regex=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64urlRegex=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,dateRegexSource="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",dateRegex=new RegExp(`^${dateRegexSource}$`);function timeRegexSource(args){let secondsRegexSource="[0-5]\\d";args.precision?secondsRegexSource=`${secondsRegexSource}\\.\\d{${args.precision}}`:args.precision==null&&(secondsRegexSource=`${secondsRegexSource}(\\.\\d+)?`);const secondsQuantifier=args.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${secondsRegexSource})${secondsQuantifier}`}function timeRegex(args){return new RegExp(`^${timeRegexSource(args)}$`)}function datetimeRegex(args){let regex=`${dateRegexSource}T${timeRegexSource(args)}`;const opts=[];return opts.push(args.local?"Z?":"Z"),args.offset&&opts.push("([+-]\\d{2}:?\\d{2})"),regex=`${regex}(${opts.join("|")})`,new RegExp(`^${regex}$`)}function isValidIP(ip,version){return!!((version==="v4"||!version)&&ipv4Regex.test(ip)||(version==="v6"||!version)&&ipv6Regex.test(ip))}function isValidJWT(jwt,alg){if(!jwtRegex.test(jwt))return!1;try{const[header]=jwt.split("."),base64=header.replace(/-/g,"+").replace(/_/g,"/").padEnd(header.length+(4-header.length%4)%4,"="),decoded=JSON.parse(atob(base64));return!(typeof decoded!="object"||decoded===null||!decoded.typ||!decoded.alg||alg&&decoded.alg!==alg)}catch{return!1}}function isValidCidr(ip,version){return!!((version==="v4"||!version)&&ipv4CidrRegex.test(ip)||(version==="v6"||!version)&&ipv6CidrRegex.test(ip))}class ZodString extends ZodType{_parse(input){if(this._def.coerce&&(input.data=String(input.data)),this._getType(input)!==ZodParsedType.string){const ctx2=this._getOrReturnCtx(input);return addIssueToContext(ctx2,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.string,received:ctx2.parsedType}),INVALID}const status=new ParseStatus;let ctx;for(const check of this._def.checks)if(check.kind==="min")input.data.length<check.value&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_small,minimum:check.value,type:"string",inclusive:!0,exact:!1,message:check.message}),status.dirty());else if(check.kind==="max")input.data.length>check.value&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_big,maximum:check.value,type:"string",inclusive:!0,exact:!1,message:check.message}),status.dirty());else if(check.kind==="length"){const tooBig=input.data.length>check.value,tooSmall=input.data.length<check.value;(tooBig||tooSmall)&&(ctx=this._getOrReturnCtx(input,ctx),tooBig?addIssueToContext(ctx,{code:ZodIssueCode.too_big,maximum:check.value,type:"string",inclusive:!0,exact:!0,message:check.message}):tooSmall&&addIssueToContext(ctx,{code:ZodIssueCode.too_small,minimum:check.value,type:"string",inclusive:!0,exact:!0,message:check.message}),status.dirty())}else if(check.kind==="email")emailRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"email",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty());else if(check.kind==="emoji")emojiRegex||(emojiRegex=new RegExp(_emojiRegex,"u")),emojiRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"emoji",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty());else if(check.kind==="uuid")uuidRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"uuid",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty());else if(check.kind==="nanoid")nanoidRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"nanoid",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty());else if(check.kind==="cuid")cuidRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"cuid",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty());else if(check.kind==="cuid2")cuid2Regex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"cuid2",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty());else if(check.kind==="ulid")ulidRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"ulid",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty());else if(check.kind==="url")try{new URL(input.data)}catch{ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"url",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty()}else check.kind==="regex"?(check.regex.lastIndex=0,check.regex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"regex",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty())):check.kind==="trim"?input.data=input.data.trim():check.kind==="includes"?input.data.includes(check.value,check.position)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.invalid_string,validation:{includes:check.value,position:check.position},message:check.message}),status.dirty()):check.kind==="toLowerCase"?input.data=input.data.toLowerCase():check.kind==="toUpperCase"?input.data=input.data.toUpperCase():check.kind==="startsWith"?input.data.startsWith(check.value)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.invalid_string,validation:{startsWith:check.value},message:check.message}),status.dirty()):check.kind==="endsWith"?input.data.endsWith(check.value)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.invalid_string,validation:{endsWith:check.value},message:check.message}),status.dirty()):check.kind==="datetime"?datetimeRegex(check).test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.invalid_string,validation:"datetime",message:check.message}),status.dirty()):check.kind==="date"?dateRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.invalid_string,validation:"date",message:check.message}),status.dirty()):check.kind==="time"?timeRegex(check).test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.invalid_string,validation:"time",message:check.message}),status.dirty()):check.kind==="duration"?durationRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"duration",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty()):check.kind==="ip"?isValidIP(input.data,check.version)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"ip",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty()):check.kind==="jwt"?isValidJWT(input.data,check.alg)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"jwt",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty()):check.kind==="cidr"?isValidCidr(input.data,check.version)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"cidr",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty()):check.kind==="base64"?base64Regex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"base64",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty()):check.kind==="base64url"?base64urlRegex.test(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{validation:"base64url",code:ZodIssueCode.invalid_string,message:check.message}),status.dirty()):util.assertNever(check);return{status:status.value,value:input.data}}_regex(regex,validation,message){return this.refinement(data=>regex.test(data),{validation,code:ZodIssueCode.invalid_string,...errorUtil.errToObj(message)})}_addCheck(check){return new ZodString({...this._def,checks:[...this._def.checks,check]})}email(message){return this._addCheck({kind:"email",...errorUtil.errToObj(message)})}url(message){return this._addCheck({kind:"url",...errorUtil.errToObj(message)})}emoji(message){return this._addCheck({kind:"emoji",...errorUtil.errToObj(message)})}uuid(message){return this._addCheck({kind:"uuid",...errorUtil.errToObj(message)})}nanoid(message){return this._addCheck({kind:"nanoid",...errorUtil.errToObj(message)})}cuid(message){return this._addCheck({kind:"cuid",...errorUtil.errToObj(message)})}cuid2(message){return this._addCheck({kind:"cuid2",...errorUtil.errToObj(message)})}ulid(message){return this._addCheck({kind:"ulid",...errorUtil.errToObj(message)})}base64(message){return this._addCheck({kind:"base64",...errorUtil.errToObj(message)})}base64url(message){return this._addCheck({kind:"base64url",...errorUtil.errToObj(message)})}jwt(options){return this._addCheck({kind:"jwt",...errorUtil.errToObj(options)})}ip(options){return this._addCheck({kind:"ip",...errorUtil.errToObj(options)})}cidr(options){return this._addCheck({kind:"cidr",...errorUtil.errToObj(options)})}datetime(options){var _a,_b;return typeof options=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:options}):this._addCheck({kind:"datetime",precision:typeof(options==null?void 0:options.precision)>"u"?null:options==null?void 0:options.precision,offset:(_a=options==null?void 0:options.offset)!==null&&_a!==void 0?_a:!1,local:(_b=options==null?void 0:options.local)!==null&&_b!==void 0?_b:!1,...errorUtil.errToObj(options==null?void 0:options.message)})}date(message){return this._addCheck({kind:"date",message})}time(options){return typeof options=="string"?this._addCheck({kind:"time",precision:null,message:options}):this._addCheck({kind:"time",precision:typeof(options==null?void 0:options.precision)>"u"?null:options==null?void 0:options.precision,...errorUtil.errToObj(options==null?void 0:options.message)})}duration(message){return this._addCheck({kind:"duration",...errorUtil.errToObj(message)})}regex(regex,message){return this._addCheck({kind:"regex",regex,...errorUtil.errToObj(message)})}includes(value,options){return this._addCheck({kind:"includes",value,position:options==null?void 0:options.position,...errorUtil.errToObj(options==null?void 0:options.message)})}startsWith(value,message){return this._addCheck({kind:"startsWith",value,...errorUtil.errToObj(message)})}endsWith(value,message){return this._addCheck({kind:"endsWith",value,...errorUtil.errToObj(message)})}min(minLength,message){return this._addCheck({kind:"min",value:minLength,...errorUtil.errToObj(message)})}max(maxLength,message){return this._addCheck({kind:"max",value:maxLength,...errorUtil.errToObj(message)})}length(len,message){return this._addCheck({kind:"length",value:len,...errorUtil.errToObj(message)})}nonempty(message){return this.min(1,errorUtil.errToObj(message))}trim(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(ch=>ch.kind==="datetime")}get isDate(){return!!this._def.checks.find(ch=>ch.kind==="date")}get isTime(){return!!this._def.checks.find(ch=>ch.kind==="time")}get isDuration(){return!!this._def.checks.find(ch=>ch.kind==="duration")}get isEmail(){return!!this._def.checks.find(ch=>ch.kind==="email")}get isURL(){return!!this._def.checks.find(ch=>ch.kind==="url")}get isEmoji(){return!!this._def.checks.find(ch=>ch.kind==="emoji")}get isUUID(){return!!this._def.checks.find(ch=>ch.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(ch=>ch.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(ch=>ch.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(ch=>ch.kind==="cuid2")}get isULID(){return!!this._def.checks.find(ch=>ch.kind==="ulid")}get isIP(){return!!this._def.checks.find(ch=>ch.kind==="ip")}get isCIDR(){return!!this._def.checks.find(ch=>ch.kind==="cidr")}get isBase64(){return!!this._def.checks.find(ch=>ch.kind==="base64")}get isBase64url(){return!!this._def.checks.find(ch=>ch.kind==="base64url")}get minLength(){let min=null;for(const ch of this._def.checks)ch.kind==="min"&&(min===null||ch.value>min)&&(min=ch.value);return min}get maxLength(){let max=null;for(const ch of this._def.checks)ch.kind==="max"&&(max===null||ch.value<max)&&(max=ch.value);return max}}ZodString.create=params=>{var _a;return new ZodString({checks:[],typeName:ZodFirstPartyTypeKind.ZodString,coerce:(_a=params==null?void 0:params.coerce)!==null&&_a!==void 0?_a:!1,...processCreateParams(params)})};function floatSafeRemainder(val,step){const valDecCount=(val.toString().split(".")[1]||"").length,stepDecCount=(step.toString().split(".")[1]||"").length,decCount=valDecCount>stepDecCount?valDecCount:stepDecCount,valInt=parseInt(val.toFixed(decCount).replace(".","")),stepInt=parseInt(step.toFixed(decCount).replace(".",""));return valInt%stepInt/Math.pow(10,decCount)}class ZodNumber extends ZodType{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(input){if(this._def.coerce&&(input.data=Number(input.data)),this._getType(input)!==ZodParsedType.number){const ctx2=this._getOrReturnCtx(input);return addIssueToContext(ctx2,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.number,received:ctx2.parsedType}),INVALID}let ctx;const status=new ParseStatus;for(const check of this._def.checks)check.kind==="int"?util.isInteger(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:"integer",received:"float",message:check.message}),status.dirty()):check.kind==="min"?(check.inclusive?input.data<check.value:input.data<=check.value)&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_small,minimum:check.value,type:"number",inclusive:check.inclusive,exact:!1,message:check.message}),status.dirty()):check.kind==="max"?(check.inclusive?input.data>check.value:input.data>=check.value)&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_big,maximum:check.value,type:"number",inclusive:check.inclusive,exact:!1,message:check.message}),status.dirty()):check.kind==="multipleOf"?floatSafeRemainder(input.data,check.value)!==0&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.not_multiple_of,multipleOf:check.value,message:check.message}),status.dirty()):check.kind==="finite"?Number.isFinite(input.data)||(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.not_finite,message:check.message}),status.dirty()):util.assertNever(check);return{status:status.value,value:input.data}}gte(value,message){return this.setLimit("min",value,!0,errorUtil.toString(message))}gt(value,message){return this.setLimit("min",value,!1,errorUtil.toString(message))}lte(value,message){return this.setLimit("max",value,!0,errorUtil.toString(message))}lt(value,message){return this.setLimit("max",value,!1,errorUtil.toString(message))}setLimit(kind,value,inclusive,message){return new ZodNumber({...this._def,checks:[...this._def.checks,{kind,value,inclusive,message:errorUtil.toString(message)}]})}_addCheck(check){return new ZodNumber({...this._def,checks:[...this._def.checks,check]})}int(message){return this._addCheck({kind:"int",message:errorUtil.toString(message)})}positive(message){return this._addCheck({kind:"min",value:0,inclusive:!1,message:errorUtil.toString(message)})}negative(message){return this._addCheck({kind:"max",value:0,inclusive:!1,message:errorUtil.toString(message)})}nonpositive(message){return this._addCheck({kind:"max",value:0,inclusive:!0,message:errorUtil.toString(message)})}nonnegative(message){return this._addCheck({kind:"min",value:0,inclusive:!0,message:errorUtil.toString(message)})}multipleOf(value,message){return this._addCheck({kind:"multipleOf",value,message:errorUtil.toString(message)})}finite(message){return this._addCheck({kind:"finite",message:errorUtil.toString(message)})}safe(message){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:errorUtil.toString(message)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:errorUtil.toString(message)})}get minValue(){let min=null;for(const ch of this._def.checks)ch.kind==="min"&&(min===null||ch.value>min)&&(min=ch.value);return min}get maxValue(){let max=null;for(const ch of this._def.checks)ch.kind==="max"&&(max===null||ch.value<max)&&(max=ch.value);return max}get isInt(){return!!this._def.checks.find(ch=>ch.kind==="int"||ch.kind==="multipleOf"&&util.isInteger(ch.value))}get isFinite(){let max=null,min=null;for(const ch of this._def.checks){if(ch.kind==="finite"||ch.kind==="int"||ch.kind==="multipleOf")return!0;ch.kind==="min"?(min===null||ch.value>min)&&(min=ch.value):ch.kind==="max"&&(max===null||ch.value<max)&&(max=ch.value)}return Number.isFinite(min)&&Number.isFinite(max)}}ZodNumber.create=params=>new ZodNumber({checks:[],typeName:ZodFirstPartyTypeKind.ZodNumber,coerce:(params==null?void 0:params.coerce)||!1,...processCreateParams(params)});class ZodBigInt extends ZodType{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(input){if(this._def.coerce)try{input.data=BigInt(input.data)}catch{return this._getInvalidInput(input)}if(this._getType(input)!==ZodParsedType.bigint)return this._getInvalidInput(input);let ctx;const status=new ParseStatus;for(const check of this._def.checks)check.kind==="min"?(check.inclusive?input.data<check.value:input.data<=check.value)&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_small,type:"bigint",minimum:check.value,inclusive:check.inclusive,message:check.message}),status.dirty()):check.kind==="max"?(check.inclusive?input.data>check.value:input.data>=check.value)&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_big,type:"bigint",maximum:check.value,inclusive:check.inclusive,message:check.message}),status.dirty()):check.kind==="multipleOf"?input.data%check.value!==BigInt(0)&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.not_multiple_of,multipleOf:check.value,message:check.message}),status.dirty()):util.assertNever(check);return{status:status.value,value:input.data}}_getInvalidInput(input){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.bigint,received:ctx.parsedType}),INVALID}gte(value,message){return this.setLimit("min",value,!0,errorUtil.toString(message))}gt(value,message){return this.setLimit("min",value,!1,errorUtil.toString(message))}lte(value,message){return this.setLimit("max",value,!0,errorUtil.toString(message))}lt(value,message){return this.setLimit("max",value,!1,errorUtil.toString(message))}setLimit(kind,value,inclusive,message){return new ZodBigInt({...this._def,checks:[...this._def.checks,{kind,value,inclusive,message:errorUtil.toString(message)}]})}_addCheck(check){return new ZodBigInt({...this._def,checks:[...this._def.checks,check]})}positive(message){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:errorUtil.toString(message)})}negative(message){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:errorUtil.toString(message)})}nonpositive(message){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:errorUtil.toString(message)})}nonnegative(message){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:errorUtil.toString(message)})}multipleOf(value,message){return this._addCheck({kind:"multipleOf",value,message:errorUtil.toString(message)})}get minValue(){let min=null;for(const ch of this._def.checks)ch.kind==="min"&&(min===null||ch.value>min)&&(min=ch.value);return min}get maxValue(){let max=null;for(const ch of this._def.checks)ch.kind==="max"&&(max===null||ch.value<max)&&(max=ch.value);return max}}ZodBigInt.create=params=>{var _a;return new ZodBigInt({checks:[],typeName:ZodFirstPartyTypeKind.ZodBigInt,coerce:(_a=params==null?void 0:params.coerce)!==null&&_a!==void 0?_a:!1,...processCreateParams(params)})};class ZodBoolean extends ZodType{_parse(input){if(this._def.coerce&&(input.data=!!input.data),this._getType(input)!==ZodParsedType.boolean){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.boolean,received:ctx.parsedType}),INVALID}return OK(input.data)}}ZodBoolean.create=params=>new ZodBoolean({typeName:ZodFirstPartyTypeKind.ZodBoolean,coerce:(params==null?void 0:params.coerce)||!1,...processCreateParams(params)});class ZodDate extends ZodType{_parse(input){if(this._def.coerce&&(input.data=new Date(input.data)),this._getType(input)!==ZodParsedType.date){const ctx2=this._getOrReturnCtx(input);return addIssueToContext(ctx2,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.date,received:ctx2.parsedType}),INVALID}if(isNaN(input.data.getTime())){const ctx2=this._getOrReturnCtx(input);return addIssueToContext(ctx2,{code:ZodIssueCode.invalid_date}),INVALID}const status=new ParseStatus;let ctx;for(const check of this._def.checks)check.kind==="min"?input.data.getTime()<check.value&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_small,message:check.message,inclusive:!0,exact:!1,minimum:check.value,type:"date"}),status.dirty()):check.kind==="max"?input.data.getTime()>check.value&&(ctx=this._getOrReturnCtx(input,ctx),addIssueToContext(ctx,{code:ZodIssueCode.too_big,message:check.message,inclusive:!0,exact:!1,maximum:check.value,type:"date"}),status.dirty()):util.assertNever(check);return{status:status.value,value:new Date(input.data.getTime())}}_addCheck(check){return new ZodDate({...this._def,checks:[...this._def.checks,check]})}min(minDate,message){return this._addCheck({kind:"min",value:minDate.getTime(),message:errorUtil.toString(message)})}max(maxDate,message){return this._addCheck({kind:"max",value:maxDate.getTime(),message:errorUtil.toString(message)})}get minDate(){let min=null;for(const ch of this._def.checks)ch.kind==="min"&&(min===null||ch.value>min)&&(min=ch.value);return min!=null?new Date(min):null}get maxDate(){let max=null;for(const ch of this._def.checks)ch.kind==="max"&&(max===null||ch.value<max)&&(max=ch.value);return max!=null?new Date(max):null}}ZodDate.create=params=>new ZodDate({checks:[],coerce:(params==null?void 0:params.coerce)||!1,typeName:ZodFirstPartyTypeKind.ZodDate,...processCreateParams(params)});class ZodSymbol extends ZodType{_parse(input){if(this._getType(input)!==ZodParsedType.symbol){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.symbol,received:ctx.parsedType}),INVALID}return OK(input.data)}}ZodSymbol.create=params=>new ZodSymbol({typeName:ZodFirstPartyTypeKind.ZodSymbol,...processCreateParams(params)});class ZodUndefined extends ZodType{_parse(input){if(this._getType(input)!==ZodParsedType.undefined){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.undefined,received:ctx.parsedType}),INVALID}return OK(input.data)}}ZodUndefined.create=params=>new ZodUndefined({typeName:ZodFirstPartyTypeKind.ZodUndefined,...processCreateParams(params)});class ZodNull extends ZodType{_parse(input){if(this._getType(input)!==ZodParsedType.null){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.null,received:ctx.parsedType}),INVALID}return OK(input.data)}}ZodNull.create=params=>new ZodNull({typeName:ZodFirstPartyTypeKind.ZodNull,...processCreateParams(params)});class ZodAny extends ZodType{constructor(){super(...arguments),this._any=!0}_parse(input){return OK(input.data)}}ZodAny.create=params=>new ZodAny({typeName:ZodFirstPartyTypeKind.ZodAny,...processCreateParams(params)});class ZodUnknown extends ZodType{constructor(){super(...arguments),this._unknown=!0}_parse(input){return OK(input.data)}}ZodUnknown.create=params=>new ZodUnknown({typeName:ZodFirstPartyTypeKind.ZodUnknown,...processCreateParams(params)});class ZodNever extends ZodType{_parse(input){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.never,received:ctx.parsedType}),INVALID}}ZodNever.create=params=>new ZodNever({typeName:ZodFirstPartyTypeKind.ZodNever,...processCreateParams(params)});class ZodVoid extends ZodType{_parse(input){if(this._getType(input)!==ZodParsedType.undefined){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.void,received:ctx.parsedType}),INVALID}return OK(input.data)}}ZodVoid.create=params=>new ZodVoid({typeName:ZodFirstPartyTypeKind.ZodVoid,...processCreateParams(params)});class ZodArray extends ZodType{_parse(input){const{ctx,status}=this._processInputParams(input),def=this._def;if(ctx.parsedType!==ZodParsedType.array)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.array,received:ctx.parsedType}),INVALID;if(def.exactLength!==null){const tooBig=ctx.data.length>def.exactLength.value,tooSmall=ctx.data.length<def.exactLength.value;(tooBig||tooSmall)&&(addIssueToContext(ctx,{code:tooBig?ZodIssueCode.too_big:ZodIssueCode.too_small,minimum:tooSmall?def.exactLength.value:void 0,maximum:tooBig?def.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:def.exactLength.message}),status.dirty())}if(def.minLength!==null&&ctx.data.length<def.minLength.value&&(addIssueToContext(ctx,{code:ZodIssueCode.too_small,minimum:def.minLength.value,type:"array",inclusive:!0,exact:!1,message:def.minLength.message}),status.dirty()),def.maxLength!==null&&ctx.data.length>def.maxLength.value&&(addIssueToContext(ctx,{code:ZodIssueCode.too_big,maximum:def.maxLength.value,type:"array",inclusive:!0,exact:!1,message:def.maxLength.message}),status.dirty()),ctx.common.async)return Promise.all([...ctx.data].map((item,i2)=>def.type._parseAsync(new ParseInputLazyPath(ctx,item,ctx.path,i2)))).then(result2=>ParseStatus.mergeArray(status,result2));const result=[...ctx.data].map((item,i2)=>def.type._parseSync(new ParseInputLazyPath(ctx,item,ctx.path,i2)));return ParseStatus.mergeArray(status,result)}get element(){return this._def.type}min(minLength,message){return new ZodArray({...this._def,minLength:{value:minLength,message:errorUtil.toString(message)}})}max(maxLength,message){return new ZodArray({...this._def,maxLength:{value:maxLength,message:errorUtil.toString(message)}})}length(len,message){return new ZodArray({...this._def,exactLength:{value:len,message:errorUtil.toString(message)}})}nonempty(message){return this.min(1,message)}}ZodArray.create=(schema,params)=>new ZodArray({type:schema,minLength:null,maxLength:null,exactLength:null,typeName:ZodFirstPartyTypeKind.ZodArray,...processCreateParams(params)});function deepPartialify(schema){if(schema instanceof ZodObject){const newShape={};for(const key in schema.shape){const fieldSchema=schema.shape[key];newShape[key]=ZodOptional.create(deepPartialify(fieldSchema))}return new ZodObject({...schema._def,shape:()=>newShape})}else return schema instanceof ZodArray?new ZodArray({...schema._def,type:deepPartialify(schema.element)}):schema instanceof ZodOptional?ZodOptional.create(deepPartialify(schema.unwrap())):schema instanceof ZodNullable?ZodNullable.create(deepPartialify(schema.unwrap())):schema instanceof ZodTuple?ZodTuple.create(schema.items.map(item=>deepPartialify(item))):schema}class ZodObject extends ZodType{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const shape=this._def.shape(),keys=util.objectKeys(shape);return this._cached={shape,keys}}_parse(input){if(this._getType(input)!==ZodParsedType.object){const ctx2=this._getOrReturnCtx(input);return addIssueToContext(ctx2,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.object,received:ctx2.parsedType}),INVALID}const{status,ctx}=this._processInputParams(input),{shape,keys:shapeKeys}=this._getCached(),extraKeys=[];if(!(this._def.catchall instanceof ZodNever&&this._def.unknownKeys==="strip"))for(const key in ctx.data)shapeKeys.includes(key)||extraKeys.push(key);const pairs=[];for(const key of shapeKeys){const keyValidator=shape[key],value=ctx.data[key];pairs.push({key:{status:"valid",value:key},value:keyValidator._parse(new ParseInputLazyPath(ctx,value,ctx.path,key)),alwaysSet:key in ctx.data})}if(this._def.catchall instanceof ZodNever){const unknownKeys=this._def.unknownKeys;if(unknownKeys==="passthrough")for(const key of extraKeys)pairs.push({key:{status:"valid",value:key},value:{status:"valid",value:ctx.data[key]}});else if(unknownKeys==="strict")extraKeys.length>0&&(addIssueToContext(ctx,{code:ZodIssueCode.unrecognized_keys,keys:extraKeys}),status.dirty());else if(unknownKeys!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const catchall=this._def.catchall;for(const key of extraKeys){const value=ctx.data[key];pairs.push({key:{status:"valid",value:key},value:catchall._parse(new ParseInputLazyPath(ctx,value,ctx.path,key)),alwaysSet:key in ctx.data})}}return ctx.common.async?Promise.resolve().then(async()=>{const syncPairs=[];for(const pair of pairs){const key=await pair.key,value=await pair.value;syncPairs.push({key,value,alwaysSet:pair.alwaysSet})}return syncPairs}).then(syncPairs=>ParseStatus.mergeObjectSync(status,syncPairs)):ParseStatus.mergeObjectSync(status,pairs)}get shape(){return this._def.shape()}strict(message){return errorUtil.errToObj,new ZodObject({...this._def,unknownKeys:"strict",...message!==void 0?{errorMap:(issue,ctx)=>{var _a,_b,_c,_d;const defaultError=(_c=(_b=(_a=this._def).errorMap)===null||_b===void 0?void 0:_b.call(_a,issue,ctx).message)!==null&&_c!==void 0?_c:ctx.defaultError;return issue.code==="unrecognized_keys"?{message:(_d=errorUtil.errToObj(message).message)!==null&&_d!==void 0?_d:defaultError}:{message:defaultError}}}:{}})}strip(){return new ZodObject({...this._def,unknownKeys:"strip"})}passthrough(){return new ZodObject({...this._def,unknownKeys:"passthrough"})}extend(augmentation){return new ZodObject({...this._def,shape:()=>({...this._def.shape(),...augmentation})})}merge(merging){return new ZodObject({unknownKeys:merging._def.unknownKeys,catchall:merging._def.catchall,shape:()=>({...this._def.shape(),...merging._def.shape()}),typeName:ZodFirstPartyTypeKind.ZodObject})}setKey(key,schema){return this.augment({[key]:schema})}catchall(index){return new ZodObject({...this._def,catchall:index})}pick(mask){const shape={};return util.objectKeys(mask).forEach(key=>{mask[key]&&this.shape[key]&&(shape[key]=this.shape[key])}),new ZodObject({...this._def,shape:()=>shape})}omit(mask){const shape={};return util.objectKeys(this.shape).forEach(key=>{mask[key]||(shape[key]=this.shape[key])}),new ZodObject({...this._def,shape:()=>shape})}deepPartial(){return deepPartialify(this)}partial(mask){const newShape={};return util.objectKeys(this.shape).forEach(key=>{const fieldSchema=this.shape[key];mask&&!mask[key]?newShape[key]=fieldSchema:newShape[key]=fieldSchema.optional()}),new ZodObject({...this._def,shape:()=>newShape})}required(mask){const newShape={};return util.objectKeys(this.shape).forEach(key=>{if(mask&&!mask[key])newShape[key]=this.shape[key];else{let newField=this.shape[key];for(;newField instanceof ZodOptional;)newField=newField._def.innerType;newShape[key]=newField}}),new ZodObject({...this._def,shape:()=>newShape})}keyof(){return createZodEnum(util.objectKeys(this.shape))}}ZodObject.create=(shape,params)=>new ZodObject({shape:()=>shape,unknownKeys:"strip",catchall:ZodNever.create(),typeName:ZodFirstPartyTypeKind.ZodObject,...processCreateParams(params)});ZodObject.strictCreate=(shape,params)=>new ZodObject({shape:()=>shape,unknownKeys:"strict",catchall:ZodNever.create(),typeName:ZodFirstPartyTypeKind.ZodObject,...processCreateParams(params)});ZodObject.lazycreate=(shape,params)=>new ZodObject({shape,unknownKeys:"strip",catchall:ZodNever.create(),typeName:ZodFirstPartyTypeKind.ZodObject,...processCreateParams(params)});class ZodUnion extends ZodType{_parse(input){const{ctx}=this._processInputParams(input),options=this._def.options;function handleResults(results){for(const result of results)if(result.result.status==="valid")return result.result;for(const result of results)if(result.result.status==="dirty")return ctx.common.issues.push(...result.ctx.common.issues),result.result;const unionErrors=results.map(result=>new ZodError(result.ctx.common.issues));return addIssueToContext(ctx,{code:ZodIssueCode.invalid_union,unionErrors}),INVALID}if(ctx.common.async)return Promise.all(options.map(async option=>{const childCtx={...ctx,common:{...ctx.common,issues:[]},parent:null};return{result:await option._parseAsync({data:ctx.data,path:ctx.path,parent:childCtx}),ctx:childCtx}})).then(handleResults);{let dirty;const issues=[];for(const option of options){const childCtx={...ctx,common:{...ctx.common,issues:[]},parent:null},result=option._parseSync({data:ctx.data,path:ctx.path,parent:childCtx});if(result.status==="valid")return result;result.status==="dirty"&&!dirty&&(dirty={result,ctx:childCtx}),childCtx.common.issues.length&&issues.push(childCtx.common.issues)}if(dirty)return ctx.common.issues.push(...dirty.ctx.common.issues),dirty.result;const unionErrors=issues.map(issues2=>new ZodError(issues2));return addIssueToContext(ctx,{code:ZodIssueCode.invalid_union,unionErrors}),INVALID}}get options(){return this._def.options}}ZodUnion.create=(types,params)=>new ZodUnion({options:types,typeName:ZodFirstPartyTypeKind.ZodUnion,...processCreateParams(params)});const getDiscriminator=type=>type instanceof ZodLazy?getDiscriminator(type.schema):type instanceof ZodEffects?getDiscriminator(type.innerType()):type instanceof ZodLiteral?[type.value]:type instanceof ZodEnum?type.options:type instanceof ZodNativeEnum?util.objectValues(type.enum):type instanceof ZodDefault?getDiscriminator(type._def.innerType):type instanceof ZodUndefined?[void 0]:type instanceof ZodNull?[null]:type instanceof ZodOptional?[void 0,...getDiscriminator(type.unwrap())]:type instanceof ZodNullable?[null,...getDiscriminator(type.unwrap())]:type instanceof ZodBranded||type instanceof ZodReadonly?getDiscriminator(type.unwrap()):type instanceof ZodCatch?getDiscriminator(type._def.innerType):[];class ZodDiscriminatedUnion extends ZodType{_parse(input){const{ctx}=this._processInputParams(input);if(ctx.parsedType!==ZodParsedType.object)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.object,received:ctx.parsedType}),INVALID;const discriminator=this.discriminator,discriminatorValue=ctx.data[discriminator],option=this.optionsMap.get(discriminatorValue);return option?ctx.common.async?option._parseAsync({data:ctx.data,path:ctx.path,parent:ctx}):option._parseSync({data:ctx.data,path:ctx.path,parent:ctx}):(addIssueToContext(ctx,{code:ZodIssueCode.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[discriminator]}),INVALID)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(discriminator,options,params){const optionsMap=new Map;for(const type of options){const discriminatorValues=getDiscriminator(type.shape[discriminator]);if(!discriminatorValues.length)throw new Error(`A discriminator value for key \`${discriminator}\` could not be extracted from all schema options`);for(const value of discriminatorValues){if(optionsMap.has(value))throw new Error(`Discriminator property ${String(discriminator)} has duplicate value ${String(value)}`);optionsMap.set(value,type)}}return new ZodDiscriminatedUnion({typeName:ZodFirstPartyTypeKind.ZodDiscriminatedUnion,discriminator,options,optionsMap,...processCreateParams(params)})}}function mergeValues(a2,b){const aType=getParsedType(a2),bType=getParsedType(b);if(a2===b)return{valid:!0,data:a2};if(aType===ZodParsedType.object&&bType===ZodParsedType.object){const bKeys=util.objectKeys(b),sharedKeys=util.objectKeys(a2).filter(key=>bKeys.indexOf(key)!==-1),newObj={...a2,...b};for(const key of sharedKeys){const sharedValue=mergeValues(a2[key],b[key]);if(!sharedValue.valid)return{valid:!1};newObj[key]=sharedValue.data}return{valid:!0,data:newObj}}else if(aType===ZodParsedType.array&&bType===ZodParsedType.array){if(a2.length!==b.length)return{valid:!1};const newArray=[];for(let index=0;index<a2.length;index++){const itemA=a2[index],itemB=b[index],sharedValue=mergeValues(itemA,itemB);if(!sharedValue.valid)return{valid:!1};newArray.push(sharedValue.data)}return{valid:!0,data:newArray}}else return aType===ZodParsedType.date&&bType===ZodParsedType.date&&+a2==+b?{valid:!0,data:a2}:{valid:!1}}class ZodIntersection extends ZodType{_parse(input){const{status,ctx}=this._processInputParams(input),handleParsed=(parsedLeft,parsedRight)=>{if(isAborted(parsedLeft)||isAborted(parsedRight))return INVALID;const merged=mergeValues(parsedLeft.value,parsedRight.value);return merged.valid?((isDirty(parsedLeft)||isDirty(parsedRight))&&status.dirty(),{status:status.value,value:merged.data}):(addIssueToContext(ctx,{code:ZodIssueCode.invalid_intersection_types}),INVALID)};return ctx.common.async?Promise.all([this._def.left._parseAsync({data:ctx.data,path:ctx.path,parent:ctx}),this._def.right._parseAsync({data:ctx.data,path:ctx.path,parent:ctx})]).then(([left,right])=>handleParsed(left,right)):handleParsed(this._def.left._parseSync({data:ctx.data,path:ctx.path,parent:ctx}),this._def.right._parseSync({data:ctx.data,path:ctx.path,parent:ctx}))}}ZodIntersection.create=(left,right,params)=>new ZodIntersection({left,right,typeName:ZodFirstPartyTypeKind.ZodIntersection,...processCreateParams(params)});class ZodTuple extends ZodType{_parse(input){const{status,ctx}=this._processInputParams(input);if(ctx.parsedType!==ZodParsedType.array)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.array,received:ctx.parsedType}),INVALID;if(ctx.data.length<this._def.items.length)return addIssueToContext(ctx,{code:ZodIssueCode.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),INVALID;!this._def.rest&&ctx.data.length>this._def.items.length&&(addIssueToContext(ctx,{code:ZodIssueCode.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),status.dirty());const items=[...ctx.data].map((item,itemIndex)=>{const schema=this._def.items[itemIndex]||this._def.rest;return schema?schema._parse(new ParseInputLazyPath(ctx,item,ctx.path,itemIndex)):null}).filter(x2=>!!x2);return ctx.common.async?Promise.all(items).then(results=>ParseStatus.mergeArray(status,results)):ParseStatus.mergeArray(status,items)}get items(){return this._def.items}rest(rest){return new ZodTuple({...this._def,rest})}}ZodTuple.create=(schemas,params)=>{if(!Array.isArray(schemas))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ZodTuple({items:schemas,typeName:ZodFirstPartyTypeKind.ZodTuple,rest:null,...processCreateParams(params)})};class ZodRecord extends ZodType{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(input){const{status,ctx}=this._processInputParams(input);if(ctx.parsedType!==ZodParsedType.object)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.object,received:ctx.parsedType}),INVALID;const pairs=[],keyType=this._def.keyType,valueType=this._def.valueType;for(const key in ctx.data)pairs.push({key:keyType._parse(new ParseInputLazyPath(ctx,key,ctx.path,key)),value:valueType._parse(new ParseInputLazyPath(ctx,ctx.data[key],ctx.path,key)),alwaysSet:key in ctx.data});return ctx.common.async?ParseStatus.mergeObjectAsync(status,pairs):ParseStatus.mergeObjectSync(status,pairs)}get element(){return this._def.valueType}static create(first,second,third){return second instanceof ZodType?new ZodRecord({keyType:first,valueType:second,typeName:ZodFirstPartyTypeKind.ZodRecord,...processCreateParams(third)}):new ZodRecord({keyType:ZodString.create(),valueType:first,typeName:ZodFirstPartyTypeKind.ZodRecord,...processCreateParams(second)})}}class ZodMap extends ZodType{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(input){const{status,ctx}=this._processInputParams(input);if(ctx.parsedType!==ZodParsedType.map)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.map,received:ctx.parsedType}),INVALID;const keyType=this._def.keyType,valueType=this._def.valueType,pairs=[...ctx.data.entries()].map(([key,value],index)=>({key:keyType._parse(new ParseInputLazyPath(ctx,key,ctx.path,[index,"key"])),value:valueType._parse(new ParseInputLazyPath(ctx,value,ctx.path,[index,"value"]))}));if(ctx.common.async){const finalMap=new Map;return Promise.resolve().then(async()=>{for(const pair of pairs){const key=await pair.key,value=await pair.value;if(key.status==="aborted"||value.status==="aborted")return INVALID;(key.status==="dirty"||value.status==="dirty")&&status.dirty(),finalMap.set(key.value,value.value)}return{status:status.value,value:finalMap}})}else{const finalMap=new Map;for(const pair of pairs){const key=pair.key,value=pair.value;if(key.status==="aborted"||value.status==="aborted")return INVALID;(key.status==="dirty"||value.status==="dirty")&&status.dirty(),finalMap.set(key.value,value.value)}return{status:status.value,value:finalMap}}}}ZodMap.create=(keyType,valueType,params)=>new ZodMap({valueType,keyType,typeName:ZodFirstPartyTypeKind.ZodMap,...processCreateParams(params)});class ZodSet extends ZodType{_parse(input){const{status,ctx}=this._processInputParams(input);if(ctx.parsedType!==ZodParsedType.set)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.set,received:ctx.parsedType}),INVALID;const def=this._def;def.minSize!==null&&ctx.data.size<def.minSize.value&&(addIssueToContext(ctx,{code:ZodIssueCode.too_small,minimum:def.minSize.value,type:"set",inclusive:!0,exact:!1,message:def.minSize.message}),status.dirty()),def.maxSize!==null&&ctx.data.size>def.maxSize.value&&(addIssueToContext(ctx,{code:ZodIssueCode.too_big,maximum:def.maxSize.value,type:"set",inclusive:!0,exact:!1,message:def.maxSize.message}),status.dirty());const valueType=this._def.valueType;function finalizeSet(elements2){const parsedSet=new Set;for(const element of elements2){if(element.status==="aborted")return INVALID;element.status==="dirty"&&status.dirty(),parsedSet.add(element.value)}return{status:status.value,value:parsedSet}}const elements=[...ctx.data.values()].map((item,i2)=>valueType._parse(new ParseInputLazyPath(ctx,item,ctx.path,i2)));return ctx.common.async?Promise.all(elements).then(elements2=>finalizeSet(elements2)):finalizeSet(elements)}min(minSize,message){return new ZodSet({...this._def,minSize:{value:minSize,message:errorUtil.toString(message)}})}max(maxSize,message){return new ZodSet({...this._def,maxSize:{value:maxSize,message:errorUtil.toString(message)}})}size(size,message){return this.min(size,message).max(size,message)}nonempty(message){return this.min(1,message)}}ZodSet.create=(valueType,params)=>new ZodSet({valueType,minSize:null,maxSize:null,typeName:ZodFirstPartyTypeKind.ZodSet,...processCreateParams(params)});class ZodFunction extends ZodType{constructor(){super(...arguments),this.validate=this.implement}_parse(input){const{ctx}=this._processInputParams(input);if(ctx.parsedType!==ZodParsedType.function)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.function,received:ctx.parsedType}),INVALID;function makeArgsIssue(args,error){return makeIssue({data:args,path:ctx.path,errorMaps:[ctx.common.contextualErrorMap,ctx.schemaErrorMap,getErrorMap(),errorMap].filter(x2=>!!x2),issueData:{code:ZodIssueCode.invalid_arguments,argumentsError:error}})}function makeReturnsIssue(returns,error){return makeIssue({data:returns,path:ctx.path,errorMaps:[ctx.common.contextualErrorMap,ctx.schemaErrorMap,getErrorMap(),errorMap].filter(x2=>!!x2),issueData:{code:ZodIssueCode.invalid_return_type,returnTypeError:error}})}const params={errorMap:ctx.common.contextualErrorMap},fn2=ctx.data;if(this._def.returns instanceof ZodPromise){const me=this;return OK(async function(...args){const error=new ZodError([]),parsedArgs=await me._def.args.parseAsync(args,params).catch(e2=>{throw error.addIssue(makeArgsIssue(args,e2)),error}),result=await Reflect.apply(fn2,this,parsedArgs);return await me._def.returns._def.type.parseAsync(result,params).catch(e2=>{throw error.addIssue(makeReturnsIssue(result,e2)),error})})}else{const me=this;return OK(function(...args){const parsedArgs=me._def.args.safeParse(args,params);if(!parsedArgs.success)throw new ZodError([makeArgsIssue(args,parsedArgs.error)]);const result=Reflect.apply(fn2,this,parsedArgs.data),parsedReturns=me._def.returns.safeParse(result,params);if(!parsedReturns.success)throw new ZodError([makeReturnsIssue(result,parsedReturns.error)]);return parsedReturns.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...items){return new ZodFunction({...this._def,args:ZodTuple.create(items).rest(ZodUnknown.create())})}returns(returnType){return new ZodFunction({...this._def,returns:returnType})}implement(func){return this.parse(func)}strictImplement(func){return this.parse(func)}static create(args,returns,params){return new ZodFunction({args:args||ZodTuple.create([]).rest(ZodUnknown.create()),returns:returns||ZodUnknown.create(),typeName:ZodFirstPartyTypeKind.ZodFunction,...processCreateParams(params)})}}class ZodLazy extends ZodType{get schema(){return this._def.getter()}_parse(input){const{ctx}=this._processInputParams(input);return this._def.getter()._parse({data:ctx.data,path:ctx.path,parent:ctx})}}ZodLazy.create=(getter,params)=>new ZodLazy({getter,typeName:ZodFirstPartyTypeKind.ZodLazy,...processCreateParams(params)});class ZodLiteral extends ZodType{_parse(input){if(input.data!==this._def.value){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{received:ctx.data,code:ZodIssueCode.invalid_literal,expected:this._def.value}),INVALID}return{status:"valid",value:input.data}}get value(){return this._def.value}}ZodLiteral.create=(value,params)=>new ZodLiteral({value,typeName:ZodFirstPartyTypeKind.ZodLiteral,...processCreateParams(params)});function createZodEnum(values,params){return new ZodEnum({values,typeName:ZodFirstPartyTypeKind.ZodEnum,...processCreateParams(params)})}class ZodEnum extends ZodType{constructor(){super(...arguments),_ZodEnum_cache.set(this,void 0)}_parse(input){if(typeof input.data!="string"){const ctx=this._getOrReturnCtx(input),expectedValues=this._def.values;return addIssueToContext(ctx,{expected:util.joinValues(expectedValues),received:ctx.parsedType,code:ZodIssueCode.invalid_type}),INVALID}if(__classPrivateFieldGet(this,_ZodEnum_cache)||__classPrivateFieldSet(this,_ZodEnum_cache,new Set(this._def.values)),!__classPrivateFieldGet(this,_ZodEnum_cache).has(input.data)){const ctx=this._getOrReturnCtx(input),expectedValues=this._def.values;return addIssueToContext(ctx,{received:ctx.data,code:ZodIssueCode.invalid_enum_value,options:expectedValues}),INVALID}return OK(input.data)}get options(){return this._def.values}get enum(){const enumValues={};for(const val of this._def.values)enumValues[val]=val;return enumValues}get Values(){const enumValues={};for(const val of this._def.values)enumValues[val]=val;return enumValues}get Enum(){const enumValues={};for(const val of this._def.values)enumValues[val]=val;return enumValues}extract(values,newDef=this._def){return ZodEnum.create(values,{...this._def,...newDef})}exclude(values,newDef=this._def){return ZodEnum.create(this.options.filter(opt=>!values.includes(opt)),{...this._def,...newDef})}}_ZodEnum_cache=new WeakMap;ZodEnum.create=createZodEnum;class ZodNativeEnum extends ZodType{constructor(){super(...arguments),_ZodNativeEnum_cache.set(this,void 0)}_parse(input){const nativeEnumValues=util.getValidEnumValues(this._def.values),ctx=this._getOrReturnCtx(input);if(ctx.parsedType!==ZodParsedType.string&&ctx.parsedType!==ZodParsedType.number){const expectedValues=util.objectValues(nativeEnumValues);return addIssueToContext(ctx,{expected:util.joinValues(expectedValues),received:ctx.parsedType,code:ZodIssueCode.invalid_type}),INVALID}if(__classPrivateFieldGet(this,_ZodNativeEnum_cache)||__classPrivateFieldSet(this,_ZodNativeEnum_cache,new Set(util.getValidEnumValues(this._def.values))),!__classPrivateFieldGet(this,_ZodNativeEnum_cache).has(input.data)){const expectedValues=util.objectValues(nativeEnumValues);return addIssueToContext(ctx,{received:ctx.data,code:ZodIssueCode.invalid_enum_value,options:expectedValues}),INVALID}return OK(input.data)}get enum(){return this._def.values}}_ZodNativeEnum_cache=new WeakMap;ZodNativeEnum.create=(values,params)=>new ZodNativeEnum({values,typeName:ZodFirstPartyTypeKind.ZodNativeEnum,...processCreateParams(params)});class ZodPromise extends ZodType{unwrap(){return this._def.type}_parse(input){const{ctx}=this._processInputParams(input);if(ctx.parsedType!==ZodParsedType.promise&&ctx.common.async===!1)return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.promise,received:ctx.parsedType}),INVALID;const promisified=ctx.parsedType===ZodParsedType.promise?ctx.data:Promise.resolve(ctx.data);return OK(promisified.then(data=>this._def.type.parseAsync(data,{path:ctx.path,errorMap:ctx.common.contextualErrorMap})))}}ZodPromise.create=(schema,params)=>new ZodPromise({type:schema,typeName:ZodFirstPartyTypeKind.ZodPromise,...processCreateParams(params)});class ZodEffects extends ZodType{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ZodFirstPartyTypeKind.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(input){const{status,ctx}=this._processInputParams(input),effect=this._def.effect||null,checkCtx={addIssue:arg=>{addIssueToContext(ctx,arg),arg.fatal?status.abort():status.dirty()},get path(){return ctx.path}};if(checkCtx.addIssue=checkCtx.addIssue.bind(checkCtx),effect.type==="preprocess"){const processed=effect.transform(ctx.data,checkCtx);if(ctx.common.async)return Promise.resolve(processed).then(async processed2=>{if(status.value==="aborted")return INVALID;const result=await this._def.schema._parseAsync({data:processed2,path:ctx.path,parent:ctx});return result.status==="aborted"?INVALID:result.status==="dirty"||status.value==="dirty"?DIRTY(result.value):result});{if(status.value==="aborted")return INVALID;const result=this._def.schema._parseSync({data:processed,path:ctx.path,parent:ctx});return result.status==="aborted"?INVALID:result.status==="dirty"||status.value==="dirty"?DIRTY(result.value):result}}if(effect.type==="refinement"){const executeRefinement=acc=>{const result=effect.refinement(acc,checkCtx);if(ctx.common.async)return Promise.resolve(result);if(result instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return acc};if(ctx.common.async===!1){const inner=this._def.schema._parseSync({data:ctx.data,path:ctx.path,parent:ctx});return inner.status==="aborted"?INVALID:(inner.status==="dirty"&&status.dirty(),executeRefinement(inner.value),{status:status.value,value:inner.value})}else return this._def.schema._parseAsync({data:ctx.data,path:ctx.path,parent:ctx}).then(inner=>inner.status==="aborted"?INVALID:(inner.status==="dirty"&&status.dirty(),executeRefinement(inner.value).then(()=>({status:status.value,value:inner.value}))))}if(effect.type==="transform")if(ctx.common.async===!1){const base=this._def.schema._parseSync({data:ctx.data,path:ctx.path,parent:ctx});if(!isValid(base))return base;const result=effect.transform(base.value,checkCtx);if(result instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:status.value,value:result}}else return this._def.schema._parseAsync({data:ctx.data,path:ctx.path,parent:ctx}).then(base=>isValid(base)?Promise.resolve(effect.transform(base.value,checkCtx)).then(result=>({status:status.value,value:result})):base);util.assertNever(effect)}}ZodEffects.create=(schema,effect,params)=>new ZodEffects({schema,typeName:ZodFirstPartyTypeKind.ZodEffects,effect,...processCreateParams(params)});ZodEffects.createWithPreprocess=(preprocess,schema,params)=>new ZodEffects({schema,effect:{type:"preprocess",transform:preprocess},typeName:ZodFirstPartyTypeKind.ZodEffects,...processCreateParams(params)});class ZodOptional extends ZodType{_parse(input){return this._getType(input)===ZodParsedType.undefined?OK(void 0):this._def.innerType._parse(input)}unwrap(){return this._def.innerType}}ZodOptional.create=(type,params)=>new ZodOptional({innerType:type,typeName:ZodFirstPartyTypeKind.ZodOptional,...processCreateParams(params)});class ZodNullable extends ZodType{_parse(input){return this._getType(input)===ZodParsedType.null?OK(null):this._def.innerType._parse(input)}unwrap(){return this._def.innerType}}ZodNullable.create=(type,params)=>new ZodNullable({innerType:type,typeName:ZodFirstPartyTypeKind.ZodNullable,...processCreateParams(params)});class ZodDefault extends ZodType{_parse(input){const{ctx}=this._processInputParams(input);let data=ctx.data;return ctx.parsedType===ZodParsedType.undefined&&(data=this._def.defaultValue()),this._def.innerType._parse({data,path:ctx.path,parent:ctx})}removeDefault(){return this._def.innerType}}ZodDefault.create=(type,params)=>new ZodDefault({innerType:type,typeName:ZodFirstPartyTypeKind.ZodDefault,defaultValue:typeof params.default=="function"?params.default:()=>params.default,...processCreateParams(params)});class ZodCatch extends ZodType{_parse(input){const{ctx}=this._processInputParams(input),newCtx={...ctx,common:{...ctx.common,issues:[]}},result=this._def.innerType._parse({data:newCtx.data,path:newCtx.path,parent:{...newCtx}});return isAsync(result)?result.then(result2=>({status:"valid",value:result2.status==="valid"?result2.value:this._def.catchValue({get error(){return new ZodError(newCtx.common.issues)},input:newCtx.data})})):{status:"valid",value:result.status==="valid"?result.value:this._def.catchValue({get error(){return new ZodError(newCtx.common.issues)},input:newCtx.data})}}removeCatch(){return this._def.innerType}}ZodCatch.create=(type,params)=>new ZodCatch({innerType:type,typeName:ZodFirstPartyTypeKind.ZodCatch,catchValue:typeof params.catch=="function"?params.catch:()=>params.catch,...processCreateParams(params)});class ZodNaN extends ZodType{_parse(input){if(this._getType(input)!==ZodParsedType.nan){const ctx=this._getOrReturnCtx(input);return addIssueToContext(ctx,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.nan,received:ctx.parsedType}),INVALID}return{status:"valid",value:input.data}}}ZodNaN.create=params=>new ZodNaN({typeName:ZodFirstPartyTypeKind.ZodNaN,...processCreateParams(params)});const BRAND=Symbol("zod_brand");class ZodBranded extends ZodType{_parse(input){const{ctx}=this._processInputParams(input),data=ctx.data;return this._def.type._parse({data,path:ctx.path,parent:ctx})}unwrap(){return this._def.type}}class ZodPipeline extends ZodType{_parse(input){const{status,ctx}=this._processInputParams(input);if(ctx.common.async)return(async()=>{const inResult=await this._def.in._parseAsync({data:ctx.data,path:ctx.path,parent:ctx});return inResult.status==="aborted"?INVALID:inResult.status==="dirty"?(status.dirty(),DIRTY(inResult.value)):this._def.out._parseAsync({data:inResult.value,path:ctx.path,parent:ctx})})();{const inResult=this._def.in._parseSync({data:ctx.data,path:ctx.path,parent:ctx});return inResult.status==="aborted"?INVALID:inResult.status==="dirty"?(status.dirty(),{status:"dirty",value:inResult.value}):this._def.out._parseSync({data:inResult.value,path:ctx.path,parent:ctx})}}static create(a2,b){return new ZodPipeline({in:a2,out:b,typeName:ZodFirstPartyTypeKind.ZodPipeline})}}class ZodReadonly extends ZodType{_parse(input){const result=this._def.innerType._parse(input),freeze=data=>(isValid(data)&&(data.value=Object.freeze(data.value)),data);return isAsync(result)?result.then(data=>freeze(data)):freeze(result)}unwrap(){return this._def.innerType}}ZodReadonly.create=(type,params)=>new ZodReadonly({innerType:type,typeName:ZodFirstPartyTypeKind.ZodReadonly,...processCreateParams(params)});function cleanParams(params,data){const p2=typeof params=="function"?params(data):typeof params=="string"?{message:params}:params;return typeof p2=="string"?{message:p2}:p2}function custom(check,_params={},fatal){return check?ZodAny.create().superRefine((data,ctx)=>{var _a,_b;const r2=check(data);if(r2 instanceof Promise)return r2.then(r3=>{var _a2,_b2;if(!r3){const params=cleanParams(_params,data),_fatal=(_b2=(_a2=params.fatal)!==null&&_a2!==void 0?_a2:fatal)!==null&&_b2!==void 0?_b2:!0;ctx.addIssue({code:"custom",...params,fatal:_fatal})}});if(!r2){const params=cleanParams(_params,data),_fatal=(_b=(_a=params.fatal)!==null&&_a!==void 0?_a:fatal)!==null&&_b!==void 0?_b:!0;ctx.addIssue({code:"custom",...params,fatal:_fatal})}}):ZodAny.create()}const late={object:ZodObject.lazycreate};var ZodFirstPartyTypeKind;(function(ZodFirstPartyTypeKind2){ZodFirstPartyTypeKind2.ZodString="ZodString",ZodFirstPartyTypeKind2.ZodNumber="ZodNumber",ZodFirstPartyTypeKind2.ZodNaN="ZodNaN",ZodFirstPartyTypeKind2.ZodBigInt="ZodBigInt",ZodFirstPartyTypeKind2.ZodBoolean="ZodBoolean",ZodFirstPartyTypeKind2.ZodDate="ZodDate",ZodFirstPartyTypeKind2.ZodSymbol="ZodSymbol",ZodFirstPartyTypeKind2.ZodUndefined="ZodUndefined",ZodFirstPartyTypeKind2.ZodNull="ZodNull",ZodFirstPartyTypeKind2.ZodAny="ZodAny",ZodFirstPartyTypeKind2.ZodUnknown="ZodUnknown",ZodFirstPartyTypeKind2.ZodNever="ZodNever",ZodFirstPartyTypeKind2.ZodVoid="ZodVoid",ZodFirstPartyTypeKind2.ZodArray="ZodArray",ZodFirstPartyTypeKind2.ZodObject="ZodObject",ZodFirstPartyTypeKind2.ZodUnion="ZodUnion",ZodFirstPartyTypeKind2.ZodDiscriminatedUnion="ZodDiscriminatedUnion",ZodFirstPartyTypeKind2.ZodIntersection="ZodIntersection",ZodFirstPartyTypeKind2.ZodTuple="ZodTuple",ZodFirstPartyTypeKind2.ZodRecord="ZodRecord",ZodFirstPartyTypeKind2.ZodMap="ZodMap",ZodFirstPartyTypeKind2.ZodSet="ZodSet",ZodFirstPartyTypeKind2.ZodFunction="ZodFunction",ZodFirstPartyTypeKind2.ZodLazy="ZodLazy",ZodFirstPartyTypeKind2.ZodLiteral="ZodLiteral",ZodFirstPartyTypeKind2.ZodEnum="ZodEnum",ZodFirstPartyTypeKind2.ZodEffects="ZodEffects",ZodFirstPartyTypeKind2.ZodNativeEnum="ZodNativeEnum",ZodFirstPartyTypeKind2.ZodOptional="ZodOptional",ZodFirstPartyTypeKind2.ZodNullable="ZodNullable",ZodFirstPartyTypeKind2.ZodDefault="ZodDefault",ZodFirstPartyTypeKind2.ZodCatch="ZodCatch",ZodFirstPartyTypeKind2.ZodPromise="ZodPromise",ZodFirstPartyTypeKind2.ZodBranded="ZodBranded",ZodFirstPartyTypeKind2.ZodPipeline="ZodPipeline",ZodFirstPartyTypeKind2.ZodReadonly="ZodReadonly"})(ZodFirstPartyTypeKind||(ZodFirstPartyTypeKind={}));const instanceOfType=(cls,params={message:`Input not instance of ${cls.name}`})=>custom(data=>data instanceof cls,params),stringType=ZodString.create,numberType=ZodNumber.create,nanType=ZodNaN.create,bigIntType=ZodBigInt.create,booleanType=ZodBoolean.create,dateType=ZodDate.create,symbolType=ZodSymbol.create,undefinedType=ZodUndefined.create,nullType=ZodNull.create,anyType=ZodAny.create,unknownType=ZodUnknown.create,neverType=ZodNever.create,voidType=ZodVoid.create,arrayType=ZodArray.create,objectType=ZodObject.create,strictObjectType=ZodObject.strictCreate,unionType=ZodUnion.create,discriminatedUnionType=ZodDiscriminatedUnion.create,intersectionType=ZodIntersection.create,tupleType=ZodTuple.create,recordType=ZodRecord.create,mapType=ZodMap.create,setType=ZodSet.create,functionType=ZodFunction.create,lazyType=ZodLazy.create,literalType=ZodLiteral.create,enumType=ZodEnum.create,nativeEnumType=ZodNativeEnum.create,promiseType=ZodPromise.create,effectsType=ZodEffects.create,optionalType=ZodOptional.create,nullableType=ZodNullable.create,preprocessType=ZodEffects.createWithPreprocess,pipelineType=ZodPipeline.create,ostring=()=>stringType().optional(),onumber=()=>numberType().optional(),oboolean=()=>booleanType().optional(),coerce={string:arg=>ZodString.create({...arg,coerce:!0}),number:arg=>ZodNumber.create({...arg,coerce:!0}),boolean:arg=>ZodBoolean.create({...arg,coerce:!0}),bigint:arg=>ZodBigInt.create({...arg,coerce:!0}),date:arg=>ZodDate.create({...arg,coerce:!0})},NEVER=INVALID;var z=Object.freeze({__proto__:null,defaultErrorMap:errorMap,setErrorMap,getErrorMap,makeIssue,EMPTY_PATH,addIssueToContext,ParseStatus,INVALID,DIRTY,OK,isAborted,isDirty,isValid,isAsync,get util(){return util},get objectUtil(){return objectUtil},ZodParsedType,getParsedType,ZodType,datetimeRegex,ZodString,ZodNumber,ZodBigInt,ZodBoolean,ZodDate,ZodSymbol,ZodUndefined,ZodNull,ZodAny,ZodUnknown,ZodNever,ZodVoid,ZodArray,ZodObject,ZodUnion,ZodDiscriminatedUnion,ZodIntersection,ZodTuple,ZodRecord,ZodMap,ZodSet,ZodFunction,ZodLazy,ZodLiteral,ZodEnum,ZodNativeEnum,ZodPromise,ZodEffects,ZodTransformer:ZodEffects,ZodOptional,ZodNullable,ZodDefault,ZodCatch,ZodNaN,BRAND,ZodBranded,ZodPipeline,ZodReadonly,custom,Schema:ZodType,ZodSchema:ZodType,late,get ZodFirstPartyTypeKind(){return ZodFirstPartyTypeKind},coerce,any:anyType,array:arrayType,bigint:bigIntType,boolean:booleanType,date:dateType,discriminatedUnion:discriminatedUnionType,effect:effectsType,enum:enumType,function:functionType,instanceof:instanceOfType,intersection:intersectionType,lazy:lazyType,literal:literalType,map:mapType,nan:nanType,nativeEnum:nativeEnumType,never:neverType,null:nullType,nullable:nullableType,number:numberType,object:objectType,oboolean,onumber,optional:optionalType,ostring,pipeline:pipelineType,preprocess:preprocessType,promise:promiseType,record:recordType,set:setType,strictObject:strictObjectType,string:stringType,symbol:symbolType,transformer:effectsType,tuple:tupleType,undefined:undefinedType,union:unionType,unknown:unknownType,void:voidType,NEVER,ZodIssueCode,quotelessJson,ZodError}),DEFAULT_PORT=5746,PING_ENDPOINT="/ping/stagewise",PING_RESPONSE="stagewise",contract={server:{getSessionInfo:{request:z.object({}),response:z.object({sessionId:z.string().optional(),appName:z.string().describe('The name of the application, e.g. "VS Code" or "Cursor"'),displayName:z.string().describe("Human-readable window identifier for UI display"),port:z.number().describe("Port number this VS Code instance is running on")}),update:z.object({})},triggerAgentPrompt:{request:z.object({sessionId:z.string().optional(),prompt:z.string(),model:z.string().optional().describe("The model to use for the agent prompt"),files:z.array(z.string()).optional().describe("Link project files to the agent prompt"),mode:z.enum(["agent","ask","manual"]).optional().describe("The mode to use for the agent prompt"),images:z.array(z.string()).optional().describe("Upload files like images, videos, etc.")}),response:z.object({sessionId:z.string().optional(),result:z.object({success:z.boolean(),error:z.string().optional(),errorCode:z.enum(["session_mismatch"]).optional(),output:z.string().optional()})}),update:z.object({sessionId:z.string().optional(),updateText:z.string()})}}};const MAX_CONSECUTIVE_ERRORS=2;async function discoverVSCodeWindows(maxAttempts=10,timeout=300){const windows=[];let consecutiveErrors=0;for(let attempt=0;attempt<maxAttempts;attempt++){const port=DEFAULT_PORT+attempt;try{const controller=new AbortController,timeoutId=setTimeout(()=>controller.abort(),timeout),response=await fetch(`http://localhost:${port}${PING_ENDPOINT}`,{signal:controller.signal});if(clearTimeout(timeoutId),consecutiveErrors=0,response.ok&&await response.text()===PING_RESPONSE)try{const bridge=createSRPCClientBridge(`ws://localhost:${port}`,contract);await bridge.connect();const sessionInfo=await bridge.call.getSessionInfo({},{onUpdate:()=>{}});windows.push(sessionInfo),await bridge.close()}catch(error){console.warn(`Failed to get session info from port ${port}:`,error)}else continue}catch{if(consecutiveErrors++,consecutiveErrors>=MAX_CONSECUTIVE_ERRORS){console.warn("⬆️⬆️⬆️ Those two errors are expected! (Everything is fine, they are part of stagewise's discovery mechanism!) ✅");break}continue}}return windows.length===0&&console.warn("No IDE windows found, please start an IDE with the stagewise extension installed! ❌"),windows}const getCurrentPort=()=>typeof window<"u"&&window.location&&window.location.port||"80",getStorageKey=()=>`ide-selected-session-id-on-browser-port-${getCurrentPort()}`,getStoredSessionId=()=>{try{return localStorage.getItem(getStorageKey())||void 0}catch{return}},setStoredSessionId=sessionId=>{try{sessionId?localStorage.setItem(getStorageKey(),sessionId):localStorage.removeItem(getStorageKey())}catch{}},VSCodeContext=pluginUi_jsxRuntime.K({windows:[],isDiscovering:!1,discoveryError:null,selectedSession:void 0,shouldPromptWindowSelection:!1,setShouldPromptWindowSelection:()=>{},discover:async()=>{},selectSession:()=>{},refreshSession:async()=>{},appName:void 0});function VSCodeProvider({children}){const[windows,setWindows]=d([]),[isDiscovering,setIsDiscovering]=d(!1),[discoveryError,setDiscoveryError]=d(null),[selectedSessionId,setSelectedSessionId]=d(getStoredSessionId()),[shouldPromptWindowSelection,setShouldPromptWindowSelection]=d(!1),discover=async()=>{setIsDiscovering(!0),setDiscoveryError(null);try{const discoveredWindows=await discoverVSCodeWindows();setWindows(discoveredWindows);const storedSessionId=getStoredSessionId();if(discoveredWindows.length===1){const singleWindow=discoveredWindows[0];(!storedSessionId||storedSessionId!==singleWindow.sessionId)&&(setSelectedSessionId(singleWindow.sessionId),setStoredSessionId(singleWindow.sessionId)),setShouldPromptWindowSelection(!1)}else{const noSessionIdSavedForCurrentPort=discoveredWindows.length>1&&!storedSessionId||storedSessionId&&!discoveredWindows.some(w2=>w2.sessionId===storedSessionId);setShouldPromptWindowSelection(noSessionIdSavedForCurrentPort),noSessionIdSavedForCurrentPort&&(setSelectedSessionId(void 0),setStoredSessionId(void 0))}}catch(err){setDiscoveryError(err instanceof Error?err.message:"Failed to discover windows")}finally{setIsDiscovering(!1)}},selectSession=sessionId=>{if(!sessionId||sessionId===""){setStoredSessionId(void 0),setSelectedSessionId(void 0);return}setSelectedSessionId(sessionId),setStoredSessionId(sessionId),sessionId&&setShouldPromptWindowSelection(!1)},refreshSession=async()=>{selectedSessionId&&await discover()};y(()=>{discover()},[]);const selectedSession=windows.find(w2=>w2.sessionId===selectedSessionId),value={windows,isDiscovering,discoveryError,selectedSession,shouldPromptWindowSelection,setShouldPromptWindowSelection,discover,selectSession,refreshSession,appName:selectedSession==null?void 0:selectedSession.appName};return pluginUi_jsxRuntime.u(VSCodeContext.Provider,{value,children})}function useVSCode(){return x$1(VSCodeContext)}const SRPCBridgeContext=pluginUi_jsxRuntime.K({bridge:null,isConnecting:!1,error:null});function SRPCBridgeProvider({children}){const[state,setState]=d({bridge:null,isConnecting:!0,error:null}),{selectedSession}=useVSCode(),bridgeRef=A$1(null),initializeBridge=q$1(async port=>{bridgeRef.current&&await bridgeRef.current.close();try{const bridge=createSRPCClientBridge(`ws://localhost:${port}`,contract);await bridge.connect(),bridgeRef.current=bridge,setState({bridge,isConnecting:!1,error:null})}catch(error){bridgeRef.current=null,setState({bridge:null,isConnecting:!1,error:error instanceof Error?error:new Error(String(error))})}},[]);return y(()=>{selectedSession&&initializeBridge(selectedSession.port)},[selectedSession,initializeBridge]),pluginUi_jsxRuntime.u(SRPCBridgeContext.Provider,{value:state,children})}function useSRPCBridge(){const context=x$1(SRPCBridgeContext);if(!context)throw new Error("useSRPCBridge must be used within an SRPCBridgeProvider");return context}const ConfigContext=pluginUi_jsxRuntime.K({config:void 0});function ConfigProvider({children,config}){const value=T$1(()=>({config}),[config]);return pluginUi_jsxRuntime.u(ConfigContext.Provider,{value,children})}function useConfig(){return x$1(ConfigContext)}const PluginContext=pluginUi_jsxRuntime.K({plugins:[],toolbarContext:{sendPrompt:()=>{}}});function PluginProvider({children}){const{bridge}=useSRPCBridge(),{selectedSession}=useVSCode(),{config}=useConfig(),plugins=(config==null?void 0:config.plugins)||[],toolbarContext=T$1(()=>({sendPrompt:async prompt=>{if(!bridge)throw new Error("No connection to the agent");return await bridge.call.triggerAgentPrompt(typeof prompt=="string"?{prompt,...selectedSession&&{sessionId:selectedSession.sessionId}}:{prompt:prompt.prompt,model:prompt.model,files:prompt.files,images:prompt.images,mode:prompt.mode,...selectedSession&&{sessionId:selectedSession.sessionId}},{onUpdate:_update=>{}})}}),[bridge,selectedSession]),pluginsLoadedRef=A$1(!1);y(()=>{pluginsLoadedRef.current||(pluginsLoadedRef.current=!0,plugins.forEach(plugin=>{var _a;(_a=plugin.onLoad)==null||_a.call(plugin,toolbarContext)}))},[plugins,toolbarContext]);const value=T$1(()=>({plugins,toolbarContext}),[plugins,toolbarContext]);return pluginUi_jsxRuntime.u(PluginContext.Provider,{value,children})}function usePlugins(){return x$1(PluginContext)}function r(e2){var t2,f2,n="";if(typeof e2=="string"||typeof e2=="number")n+=e2;else if(typeof e2=="object")if(Array.isArray(e2)){var o2=e2.length;for(t2=0;t2<o2;t2++)e2[t2]&&(f2=r(e2[t2]))&&(n&&(n+=" "),n+=f2)}else for(f2 in e2)e2[f2]&&(n&&(n+=" "),n+=f2);return n}function clsx(){for(var e2,t2,f2=0,n="",o2=arguments.length;f2<o2;f2++)(e2=arguments[f2])&&(t2=r(e2))&&(n&&(n+=" "),n+=t2);return n}const CLASS_PART_SEPARATOR="-",createClassGroupUtils=config=>{const classMap=createClassMap(config),{conflictingClassGroups,conflictingClassGroupModifiers}=config;return{getClassGroupId:className=>{const classParts=className.split(CLASS_PART_SEPARATOR);return classParts[0]===""&&classParts.length!==1&&classParts.shift(),getGroupRecursive(classParts,classMap)||getGroupIdForArbitraryProperty(className)},getConflictingClassGroupIds:(classGroupId,hasPostfixModifier)=>{const conflicts=conflictingClassGroups[classGroupId]||[];return hasPostfixModifier&&conflictingClassGroupModifiers[classGroupId]?[...conflicts,...conflictingClassGroupModifiers[classGroupId]]:conflicts}}},getGroupRecursive=(classParts,classPartObject)=>{var _a;if(classParts.length===0)return classPartObject.classGroupId;const currentClassPart=classParts[0],nextClassPartObject=classPartObject.nextPart.get(currentClassPart),classGroupFromNextClassPart=nextClassPartObject?getGroupRecursive(classParts.slice(1),nextClassPartObject):void 0;if(classGroupFromNextClassPart)return classGroupFromNextClassPart;if(classPartObject.validators.length===0)return;const classRest=classParts.join(CLASS_PART_SEPARATOR);return(_a=classPartObject.validators.find(({validator})=>validator(classRest)))==null?void 0:_a.classGroupId},arbitraryPropertyRegex=/^\[(.+)\]$/,getGroupIdForArbitraryProperty=className=>{if(arbitraryPropertyRegex.test(className)){const arbitraryPropertyClassName=arbitraryPropertyRegex.exec(className)[1],property=arbitraryPropertyClassName==null?void 0:arbitraryPropertyClassName.substring(0,arbitraryPropertyClassName.indexOf(":"));if(property)return"arbitrary.."+property}},createClassMap=config=>{const{theme,classGroups}=config,classMap={nextPart:new Map,validators:[]};for(const classGroupId in classGroups)processClassesRecursively(classGroups[classGroupId],classMap,classGroupId,theme);return classMap},processClassesRecursively=(classGroup,classPartObject,classGroupId,theme)=>{classGroup.forEach(classDefinition=>{if(typeof classDefinition=="string"){const classPartObjectToEdit=classDefinition===""?classPartObject:getPart(classPartObject,classDefinition);classPartObjectToEdit.classGroupId=classGroupId;return}if(typeof classDefinition=="function"){if(isThemeGetter(classDefinition)){processClassesRecursively(classDefinition(theme),classPartObject,classGroupId,theme);return}classPartObject.validators.push({validator:classDefinition,classGroupId});return}Object.entries(classDefinition).forEach(([key,classGroup2])=>{processClassesRecursively(classGroup2,getPart(classPartObject,key),classGroupId,theme)})})},getPart=(classPartObject,path)=>{let currentClassPartObject=classPartObject;return path.split(CLASS_PART_SEPARATOR).forEach(pathPart=>{currentClassPartObject.nextPart.has(pathPart)||currentClassPartObject.nextPart.set(pathPart,{nextPart:new Map,validators:[]}),currentClassPartObject=currentClassPartObject.nextPart.get(pathPart)}),currentClassPartObject},isThemeGetter=func=>func.isThemeGetter,createLruCache=maxCacheSize=>{if(maxCacheSize<1)return{get:()=>{},set:()=>{}};let cacheSize=0,cache=new Map,previousCache=new Map;const update=(key,value)=>{cache.set(key,value),cacheSize++,cacheSize>maxCacheSize&&(cacheSize=0,previousCache=cache,cache=new Map)};return{get(key){let value=cache.get(key);if(value!==void 0)return value;if((value=previousCache.get(key))!==void 0)return update(key,value),value},set(key,value){cache.has(key)?cache.set(key,value):update(key,value)}}},IMPORTANT_MODIFIER="!",MODIFIER_SEPARATOR=":",MODIFIER_SEPARATOR_LENGTH=MODIFIER_SEPARATOR.length,createParseClassName=config=>{const{prefix,experimentalParseClassName}=config;let parseClassName=className=>{const modifiers=[];let bracketDepth=0,parenDepth=0,modifierStart=0,postfixModifierPosition;for(let index=0;index<className.length;index++){let currentCharacter=className[index];if(bracketDepth===0&&parenDepth===0){if(currentCharacter===MODIFIER_SEPARATOR){modifiers.push(className.slice(modifierStart,index)),modifierStart=index+MODIFIER_SEPARATOR_LENGTH;continue}if(currentCharacter==="/"){postfixModifierPosition=index;continue}}currentCharacter==="["?bracketDepth++:currentCharacter==="]"?bracketDepth--:currentCharacter==="("?parenDepth++:currentCharacter===")"&&parenDepth--}const baseClassNameWithImportantModifier=modifiers.length===0?className:className.substring(modifierStart),baseClassName=stripImportantModifier(baseClassNameWithImportantModifier),hasImportantModifier=baseClassName!==baseClassNameWithImportantModifier,maybePostfixModifierPosition=postfixModifierPosition&&postfixModifierPosition>modifierStart?postfixModifierPosition-modifierStart:void 0;return{modifiers,hasImportantModifier,baseClassName,maybePostfixModifierPosition}};if(prefix){const fullPrefix=prefix+MODIFIER_SEPARATOR,parseClassNameOriginal=parseClassName;parseClassName=className=>className.startsWith(fullPrefix)?parseClassNameOriginal(className.substring(fullPrefix.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:className,maybePostfixModifierPosition:void 0}}if(experimentalParseClassName){const parseClassNameOriginal=parseClassName;parseClassName=className=>experimentalParseClassName({className,parseClassName:parseClassNameOriginal})}return parseClassName},stripImportantModifier=baseClassName=>baseClassName.endsWith(IMPORTANT_MODIFIER)?baseClassName.substring(0,baseClassName.length-1):baseClassName.startsWith(IMPORTANT_MODIFIER)?baseClassName.substring(1):baseClassName,createSortModifiers=config=>{const orderSensitiveModifiers=Object.fromEntries(config.orderSensitiveModifiers.map(modifier=>[modifier,!0]));return modifiers=>{if(modifiers.length<=1)return modifiers;const sortedModifiers=[];let unsortedModifiers=[];return modifiers.forEach(modifier=>{modifier[0]==="["||orderSensitiveModifiers[modifier]?(sortedModifiers.push(...unsortedModifiers.sort(),modifier),unsortedModifiers=[]):unsortedModifiers.push(modifier)}),sortedModifiers.push(...unsortedModifiers.sort()),sortedModifiers}},createConfigUtils=config=>({cache:createLruCache(config.cacheSize),parseClassName:createParseClassName(config),sortModifiers:createSortModifiers(config),...createClassGroupUtils(config)}),SPLIT_CLASSES_REGEX=/\s+/,mergeClassList=(classList,configUtils)=>{const{parseClassName,getClassGroupId,getConflictingClassGroupIds,sortModifiers}=configUtils,classGroupsInConflict=[],classNames=classList.trim().split(SPLIT_CLASSES_REGEX);let result="";for(let index=classNames.length-1;index>=0;index-=1){const originalClassName=classNames[index],{isExternal,modifiers,hasImportantModifier,baseClassName,maybePostfixModifierPosition}=parseClassName(originalClassName);if(isExternal){result=originalClassName+(result.length>0?" "+result:result);continue}let hasPostfixModifier=!!maybePostfixModifierPosition,classGroupId=getClassGroupId(hasPostfixModifier?baseClassName.substring(0,maybePostfixModifierPosition):baseClassName);if(!classGroupId){if(!hasPostfixModifier){result=originalClassName+(result.length>0?" "+result:result);continue}if(classGroupId=getClassGroupId(baseClassName),!classGroupId){result=originalClassName+(result.length>0?" "+result:result);continue}hasPostfixModifier=!1}const variantModifier=sortModifiers(modifiers).join(":"),modifierId=hasImportantModifier?variantModifier+IMPORTANT_MODIFIER:variantModifier,classId=modifierId+classGroupId;if(classGroupsInConflict.includes(classId))continue;classGroupsInConflict.push(classId);const conflictGroups=getConflictingClassGroupIds(classGroupId,hasPostfixModifier);for(let i2=0;i2<conflictGroups.length;++i2){const group=conflictGroups[i2];classGroupsInConflict.push(modifierId+group)}result=originalClassName+(result.length>0?" "+result:result)}return result};function twJoin(){let index=0,argument,resolvedValue,string="";for(;index<arguments.length;)(argument=arguments[index++])&&(resolvedValue=toValue(argument))&&(string&&(string+=" "),string+=resolvedValue);return string}const toValue=mix=>{if(typeof mix=="string")return mix;let resolvedValue,string="";for(let k2=0;k2<mix.length;k2++)mix[k2]&&(resolvedValue=toValue(mix[k2]))&&(string&&(string+=" "),string+=resolvedValue);return string};function createTailwindMerge(createConfigFirst,...createConfigRest){let configUtils,cacheGet,cacheSet,functionToCall=initTailwindMerge;function initTailwindMerge(classList){const config=createConfigRest.reduce((previousConfig,createConfigCurrent)=>createConfigCurrent(previousConfig),createConfigFirst());return configUtils=createConfigUtils(config),cacheGet=configUtils.cache.get,cacheSet=configUtils.cache.set,functionToCall=tailwindMerge,tailwindMerge(classList)}function tailwindMerge(classList){const cachedResult=cacheGet(classList);if(cachedResult)return cachedResult;const result=mergeClassList(classList,configUtils);return cacheSet(classList,result),result}return function(){return functionToCall(twJoin.apply(null,arguments))}}const fromTheme=key=>{const themeGetter=theme=>theme[key]||[];return themeGetter.isThemeGetter=!0,themeGetter},arbitraryValueRegex=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,arbitraryVariableRegex=/^\((?:(\w[\w-]*):)?(.+)\)$/i,fractionRegex=/^\d+\/\d+$/,tshirtUnitRegex=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,lengthUnitRegex=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,colorFunctionRegex=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,shadowRegex=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,imageRegex=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,isFraction=value=>fractionRegex.test(value),isNumber=value=>!!value&&!Number.isNaN(Number(value)),isInteger=value=>!!value&&Number.isInteger(Number(value)),isPercent=value=>value.endsWith("%")&&isNumber(value.slice(0,-1)),isTshirtSize=value=>tshirtUnitRegex.test(value),isAny=()=>!0,isLengthOnly=value=>lengthUnitRegex.test(value)&&!colorFunctionRegex.test(value),isNever=()=>!1,isShadow=value=>shadowRegex.test(value),isImage=value=>imageRegex.test(value),isAnyNonArbitrary=value=>!isArbitraryValue(value)&&!isArbitraryVariable(value),isArbitrarySize=value=>getIsArbitraryValue(value,isLabelSize,isNever),isArbitraryValue=value=>arbitraryValueRegex.test(value),isArbitraryLength=value=>getIsArbitraryValue(value,isLabelLength,isLengthOnly),isArbitraryNumber=value=>getIsArbitraryValue(value,isLabelNumber,isNumber),isArbitraryPosition=value=>getIsArbitraryValue(value,isLabelPosition,isNever),isArbitraryImage=value=>getIsArbitraryValue(value,isLabelImage,isImage),isArbitraryShadow=value=>getIsArbitraryValue(value,isLabelShadow,isShadow),isArbitraryVariable=value=>arbitraryVariableRegex.test(value),isArbitraryVariableLength=value=>getIsArbitraryVariable(value,isLabelLength),isArbitraryVariableFamilyName=value=>getIsArbitraryVariable(value,isLabelFamilyName),isArbitraryVariablePosition=value=>getIsArbitraryVariable(value,isLabelPosition),isArbitraryVariableSize=value=>getIsArbitraryVariable(value,isLabelSize),isArbitraryVariableImage=value=>getIsArbitraryVariable(value,isLabelImage),isArbitraryVariableShadow=value=>getIsArbitraryVariable(value,isLabelShadow,!0),getIsArbitraryValue=(value,testLabel,testValue)=>{const result=arbitraryValueRegex.exec(value);return result?result[1]?testLabel(result[1]):testValue(result[2]):!1},getIsArbitraryVariable=(value,testLabel,shouldMatchNoLabel=!1)=>{const result=arbitraryVariableRegex.exec(value);return result?result[1]?testLabel(result[1]):shouldMatchNoLabel:!1},isLabelPosition=label=>label==="position"||label==="percentage",isLabelImage=label=>label==="image"||label==="url",isLabelSize=label=>label==="length"||label==="size"||label==="bg-size",isLabelLength=label=>label==="length",isLabelNumber=label=>label==="number",isLabelFamilyName=label=>label==="family-name",isLabelShadow=label=>label==="shadow",getDefaultConfig=()=>{const themeColor=fromTheme("color"),themeFont=fromTheme("font"),themeText=fromTheme("text"),themeFontWeight=fromTheme("font-weight"),themeTracking=fromTheme("tracking"),themeLeading=fromTheme("leading"),themeBreakpoint=fromTheme("breakpoint"),themeContainer=fromTheme("container"),themeSpacing=fromTheme("spacing"),themeRadius=fromTheme("radius"),themeShadow=fromTheme("shadow"),themeInsetShadow=fromTheme("inset-shadow"),themeTextShadow=fromTheme("text-shadow"),themeDropShadow=fromTheme("drop-shadow"),themeBlur=fromTheme("blur"),themePerspective=fromTheme("perspective"),themeAspect=fromTheme("aspect"),themeEase=fromTheme("ease"),themeAnimate=fromTheme("animate"),scaleBreak=()=>["auto","avoid","all","avoid-page","page","left","right","column"],scalePosition=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],scalePositionWithArbitrary=()=>[...scalePosition(),isArbitraryVariable,isArbitraryValue],scaleOverflow=()=>["auto","hidden","clip","visible","scroll"],scaleOverscroll=()=>["auto","contain","none"],scaleUnambiguousSpacing=()=>[isArbitraryVariable,isArbitraryValue,themeSpacing],scaleInset=()=>[isFraction,"full","auto",...scaleUnambiguousSpacing()],scaleGridTemplateColsRows=()=>[isInteger,"none","subgrid",isArbitraryVariable,isArbitraryValue],scaleGridColRowStartAndEnd=()=>["auto",{span:["full",isInteger,isArbitraryVariable,isArbitraryValue]},isInteger,isArbitraryVariable,isArbitraryValue],scaleGridColRowStartOrEnd=()=>[isInteger,"auto",isArbitraryVariable,isArbitraryValue],scaleGridAutoColsRows=()=>["auto","min","max","fr",isArbitraryVariable,isArbitraryValue],scaleAlignPrimaryAxis=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],scaleAlignSecondaryAxis=()=>["start","end","center","stretch","center-safe","end-safe"],scaleMargin=()=>["auto",...scaleUnambiguousSpacing()],scaleSizing=()=>[isFraction,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...scaleUnambiguousSpacing()],scaleColor=()=>[themeColor,isArbitraryVariable,isArbitraryValue],scaleBgPosition=()=>[...scalePosition(),isArbitraryVariablePosition,isArbitraryPosition,{position:[isArbitraryVariable,isArbitraryValue]}],scaleBgRepeat=()=>["no-repeat",{repeat:["","x","y","space","round"]}],scaleBgSize=()=>["auto","cover","contain",isArbitraryVariableSize,isArbitrarySize,{size:[isArbitraryVariable,isArbitraryValue]}],scaleGradientStopPosition=()=>[isPercent,isArbitraryVariableLength,isArbitraryLength],scaleRadius=()=>["","none","full",themeRadius,isArbitraryVariable,isArbitraryValue],scaleBorderWidth=()=>["",isNumber,isArbitraryVariableLength,isArbitraryLength],scaleLineStyle=()=>["solid","dashed","dotted","double"],scaleBlendMode=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],scaleMaskImagePosition=()=>[isNumber,isPercent,isArbitraryVariablePosition,isArbitraryPosition],scaleBlur=()=>["","none",themeBlur,isArbitraryVariable,isArbitraryValue],scaleRotate=()=>["none",isNumber,isArbitraryVariable,isArbitraryValue],scaleScale=()=>["none",isNumber,isArbitraryVariable,isArbitraryValue],scaleSkew=()=>[isNumber,isArbitraryVariable,isArbitraryValue],scaleTranslate=()=>[isFraction,"full",...scaleUnambiguousSpacing()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[isTshirtSize],breakpoint:[isTshirtSize],color:[isAny],container:[isTshirtSize],"drop-shadow":[isTshirtSize],ease:["in","out","in-out"],font:[isAnyNonArbitrary],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[isTshirtSize],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[isTshirtSize],shadow:[isTshirtSize],spacing:["px",isNumber],text:[isTshirtSize],"text-shadow":[isTshirtSize],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",isFraction,isArbitraryValue,isArbitraryVariable,themeAspect]}],container:["container"],columns:[{columns:[isNumber,isArbitraryValue,isArbitraryVariable,themeContainer]}],"break-after":[{"break-after":scaleBreak()}],"break-before":[{"break-before":scaleBreak()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:scalePositionWithArbitrary()}],overflow:[{overflow:scaleOverflow()}],"overflow-x":[{"overflow-x":scaleOverflow()}],"overflow-y":[{"overflow-y":scaleOverflow()}],overscroll:[{overscroll:scaleOverscroll()}],"overscroll-x":[{"overscroll-x":scaleOverscroll()}],"overscroll-y":[{"overscroll-y":scaleOverscroll()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:scaleInset()}],"inset-x":[{"inset-x":scaleInset()}],"inset-y":[{"inset-y":scaleInset()}],start:[{start:scaleInset()}],end:[{end:scaleInset()}],top:[{top:scaleInset()}],right:[{right:scaleInset()}],bottom:[{bottom:scaleInset()}],left:[{left:scaleInset()}],visibility:["visible","invisible","collapse"],z:[{z:[isInteger,"auto",isArbitraryVariable,isArbitraryValue]}],basis:[{basis:[isFraction,"full","auto",themeContainer,...scaleUnambiguousSpacing()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[isNumber,isFraction,"auto","initial","none",isArbitraryValue]}],grow:[{grow:["",isNumber,isArbitraryVariable,isArbitraryValue]}],shrink:[{shrink:["",isNumber,isArbitraryVariable,isArbitraryValue]}],order:[{order:[isInteger,"first","last","none",isArbitraryVariable,isArbitraryValue]}],"grid-cols":[{"grid-cols":scaleGridTemplateColsRows()}],"col-start-end":[{col:scaleGridColRowStartAndEnd()}],"col-start":[{"col-start":scaleGridColRowStartOrEnd()}],"col-end":[{"col-end":scaleGridColRowStartOrEnd()}],"grid-rows":[{"grid-rows":scaleGridTemplateColsRows()}],"row-start-end":[{row:scaleGridColRowStartAndEnd()}],"row-start":[{"row-start":scaleGridColRowStartOrEnd()}],"row-end":[{"row-end":scaleGridColRowStartOrEnd()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":scaleGridAutoColsRows()}],"auto-rows":[{"auto-rows":scaleGridAutoColsRows()}],gap:[{gap:scaleUnambiguousSpacing()}],"gap-x":[{"gap-x":scaleUnambiguousSpacing()}],"gap-y":[{"gap-y":scaleUnambiguousSpacing()}],"justify-content":[{justify:[...scaleAlignPrimaryAxis(),"normal"]}],"justify-items":[{"justify-items":[...scaleAlignSecondaryAxis(),"normal"]}],"justify-self":[{"justify-self":["auto",...scaleAlignSecondaryAxis()]}],"align-content":[{content:["normal",...scaleAlignPrimaryAxis()]}],"align-items":[{items:[...scaleAlignSecondaryAxis(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...scaleAlignSecondaryAxis(),{baseline:["","last"]}]}],"place-content":[{"place-content":scaleAlignPrimaryAxis()}],"place-items":[{"place-items":[...scaleAlignSecondaryAxis(),"baseline"]}],"place-self":[{"place-self":["auto",...scaleAlignSecondaryAxis()]}],p:[{p:scaleUnambiguousSpacing()}],px:[{px:scaleUnambiguousSpacing()}],py:[{py:scaleUnambiguousSpacing()}],ps:[{ps:scaleUnambiguousSpacing()}],pe:[{pe:scaleUnambiguousSpacing()}],pt:[{pt:scaleUnambiguousSpacing()}],pr:[{pr:scaleUnambiguousSpacing()}],pb:[{pb:scaleUnambiguousSpacing()}],pl:[{pl:scaleUnambiguousSpacing()}],m:[{m:scaleMargin()}],mx:[{mx:scaleMargin()}],my:[{my:scaleMargin()}],ms:[{ms:scaleMargin()}],me:[{me:scaleMargin()}],mt:[{mt:scaleMargin()}],mr:[{mr:scaleMargin()}],mb:[{mb:scaleMargin()}],ml:[{ml:scaleMargin()}],"space-x":[{"space-x":scaleUnambiguousSpacing()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":scaleUnambiguousSpacing()}],"space-y-reverse":["space-y-reverse"],size:[{size:scaleSizing()}],w:[{w:[themeContainer,"screen",...scaleSizing()]}],"min-w":[{"min-w":[themeContainer,"screen","none",...scaleSizing()]}],"max-w":[{"max-w":[themeContainer,"screen","none","prose",{screen:[themeBreakpoint]},...scaleSizing()]}],h:[{h:["screen",...scaleSizing()]}],"min-h":[{"min-h":["screen","none",...scaleSizing()]}],"max-h":[{"max-h":["screen",...scaleSizing()]}],"font-size":[{text:["base",themeText,isArbitraryVariableLength,isArbitraryLength]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[themeFontWeight,isArbitraryVariable,isArbitraryNumber]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",isPercent,isArbitraryValue]}],"font-family":[{font:[isArbitraryVariableFamilyName,isArbitraryValue,themeFont]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[themeTracking,isArbitraryVariable,isArbitraryValue]}],"line-clamp":[{"line-clamp":[isNumber,"none",isArbitraryVariable,isArbitraryNumber]}],leading:[{leading:[themeLeading,...scaleUnambiguousSpacing()]}],"list-image":[{"list-image":["none",isArbitraryVariable,isArbitraryValue]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",isArbitraryVariable,isArbitraryValue]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:scaleColor()}],"text-color":[{text:scaleColor()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...scaleLineStyle(),"wavy"]}],"text-decoration-thickness":[{decoration:[isNumber,"from-font","auto",isArbitraryVariable,isArbitraryLength]}],"text-decoration-color":[{decoration:scaleColor()}],"underline-offset":[{"underline-offset":[isNumber,"auto",isArbitraryVariable,isArbitraryValue]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:scaleUnambiguousSpacing()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",isArbitraryVariable,isArbitraryValue]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",isArbitraryVariable,isArbitraryValue]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:scaleBgPosition()}],"bg-repeat":[{bg:scaleBgRepeat()}],"bg-size":[{bg:scaleBgSize()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},isInteger,isArbitraryVariable,isArbitraryValue],radial:["",isArbitraryVariable,isArbitraryValue],conic:[isInteger,isArbitraryVariable,isArbitraryValue]},isArbitraryVariableImage,isArbitraryImage]}],"bg-color":[{bg:scaleColor()}],"gradient-from-pos":[{from:scaleGradientStopPosition()}],"gradient-via-pos":[{via:scaleGradientStopPosition()}],"gradient-to-pos":[{to:scaleGradientStopPosition()}],"gradient-from":[{from:scaleColor()}],"gradient-via":[{via:scaleColor()}],"gradient-to":[{to:scaleColor()}],rounded:[{rounded:scaleRadius()}],"rounded-s":[{"rounded-s":scaleRadius()}],"rounded-e":[{"rounded-e":scaleRadius()}],"rounded-t":[{"rounded-t":scaleRadius()}],"rounded-r":[{"rounded-r":scaleRadius()}],"rounded-b":[{"rounded-b":scaleRadius()}],"rounded-l":[{"rounded-l":scaleRadius()}],"rounded-ss":[{"rounded-ss":scaleRadius()}],"rounded-se":[{"rounded-se":scaleRadius()}],"rounded-ee":[{"rounded-ee":scaleRadius()}],"rounded-es":[{"rounded-es":scaleRadius()}],"rounded-tl":[{"rounded-tl":scaleRadius()}],"rounded-tr":[{"rounded-tr":scaleRadius()}],"rounded-br":[{"rounded-br":scaleRadius()}],"rounded-bl":[{"rounded-bl":scaleRadius()}],"border-w":[{border:scaleBorderWidth()}],"border-w-x":[{"border-x":scaleBorderWidth()}],"border-w-y":[{"border-y":scaleBorderWidth()}],"border-w-s":[{"border-s":scaleBorderWidth()}],"border-w-e":[{"border-e":scaleBorderWidth()}],"border-w-t":[{"border-t":scaleBorderWidth()}],"border-w-r":[{"border-r":scaleBorderWidth()}],"border-w-b":[{"border-b":scaleBorderWidth()}],"border-w-l":[{"border-l":scaleBorderWidth()}],"divide-x":[{"divide-x":scaleBorderWidth()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":scaleBorderWidth()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...scaleLineStyle(),"hidden","none"]}],"divide-style":[{divide:[...scaleLineStyle(),"hidden","none"]}],"border-color":[{border:scaleColor()}],"border-color-x":[{"border-x":scaleColor()}],"border-color-y":[{"border-y":scaleColor()}],"border-color-s":[{"border-s":scaleColor()}],"border-color-e":[{"border-e":scaleColor()}],"border-color-t":[{"border-t":scaleColor()}],"border-color-r":[{"border-r":scaleColor()}],"border-color-b":[{"border-b":scaleColor()}],"border-color-l":[{"border-l":scaleColor()}],"divide-color":[{divide:scaleColor()}],"outline-style":[{outline:[...scaleLineStyle(),"none","hidden"]}],"outline-offset":[{"outline-offset":[isNumber,isArbitraryVariable,isArbitraryValue]}],"outline-w":[{outline:["",isNumber,isArbitraryVariableLength,isArbitraryLength]}],"outline-color":[{outline:scaleColor()}],shadow:[{shadow:["","none",themeShadow,isArbitraryVariableShadow,isArbitraryShadow]}],"shadow-color":[{shadow:scaleColor()}],"inset-shadow":[{"inset-shadow":["none",themeInsetShadow,isArbitraryVariableShadow,isArbitraryShadow]}],"inset-shadow-color":[{"inset-shadow":scaleColor()}],"ring-w":[{ring:scaleBorderWidth()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:scaleColor()}],"ring-offset-w":[{"ring-offset":[isNumber,isArbitraryLength]}],"ring-offset-color":[{"ring-offset":scaleColor()}],"inset-ring-w":[{"inset-ring":scaleBorderWidth()}],"inset-ring-color":[{"inset-ring":scaleColor()}],"text-shadow":[{"text-shadow":["none",themeTextShadow,isArbitraryVariableShadow,isArbitraryShadow]}],"text-shadow-color":[{"text-shadow":scaleColor()}],opacity:[{opacity:[isNumber,isArbitraryVariable,isArbitraryValue]}],"mix-blend":[{"mix-blend":[...scaleBlendMode(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":scaleBlendMode()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[isNumber]}],"mask-image-linear-from-pos":[{"mask-linear-from":scaleMaskImagePosition()}],"mask-image-linear-to-pos":[{"mask-linear-to":scaleMaskImagePosition()}],"mask-image-linear-from-color":[{"mask-linear-from":scaleColor()}],"mask-image-linear-to-color":[{"mask-linear-to":scaleColor()}],"mask-image-t-from-pos":[{"mask-t-from":scaleMaskImagePosition()}],"mask-image-t-to-pos":[{"mask-t-to":scaleMaskImagePosition()}],"mask-image-t-from-color":[{"mask-t-from":scaleColor()}],"mask-image-t-to-color":[{"mask-t-to":scaleColor()}],"mask-image-r-from-pos":[{"mask-r-from":scaleMaskImagePosition()}],"mask-image-r-to-pos":[{"mask-r-to":scaleMaskImagePosition()}],"mask-image-r-from-color":[{"mask-r-from":scaleColor()}],"mask-image-r-to-color":[{"mask-r-to":scaleColor()}],"mask-image-b-from-pos":[{"mask-b-from":scaleMaskImagePosition()}],"mask-image-b-to-pos":[{"mask-b-to":scaleMaskImagePosition()}],"mask-image-b-from-color":[{"mask-b-from":scaleColor()}],"mask-image-b-to-color":[{"mask-b-to":scaleColor()}],"mask-image-l-from-pos":[{"mask-l-from":scaleMaskImagePosition()}],"mask-image-l-to-pos":[{"mask-l-to":scaleMaskImagePosition()}],"mask-image-l-from-color":[{"mask-l-from":scaleColor()}],"mask-image-l-to-color":[{"mask-l-to":scaleColor()}],"mask-image-x-from-pos":[{"mask-x-from":scaleMaskImagePosition()}],"mask-image-x-to-pos":[{"mask-x-to":scaleMaskImagePosition()}],"mask-image-x-from-color":[{"mask-x-from":scaleColor()}],"mask-image-x-to-color":[{"mask-x-to":scaleColor()}],"mask-image-y-from-pos":[{"mask-y-from":scaleMaskImagePosition()}],"mask-image-y-to-pos":[{"mask-y-to":scaleMaskImagePosition()}],"mask-image-y-from-color":[{"mask-y-from":scaleColor()}],"mask-image-y-to-color":[{"mask-y-to":scaleColor()}],"mask-image-radial":[{"mask-radial":[isArbitraryVariable,isArbitraryValue]}],"mask-image-radial-from-pos":[{"mask-radial-from":scaleMaskImagePosition()}],"mask-image-radial-to-pos":[{"mask-radial-to":scaleMaskImagePosition()}],"mask-image-radial-from-color":[{"mask-radial-from":scaleColor()}],"mask-image-radial-to-color":[{"mask-radial-to":scaleColor()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":scalePosition()}],"mask-image-conic-pos":[{"mask-conic":[isNumber]}],"mask-image-conic-from-pos":[{"mask-conic-from":scaleMaskImagePosition()}],"mask-image-conic-to-pos":[{"mask-conic-to":scaleMaskImagePosition()}],"mask-image-conic-from-color":[{"mask-conic-from":scaleColor()}],"mask-image-conic-to-color":[{"mask-conic-to":scaleColor()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:scaleBgPosition()}],"mask-repeat":[{mask:scaleBgRepeat()}],"mask-size":[{mask:scaleBgSize()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",isArbitraryVariable,isArbitraryValue]}],filter:[{filter:["","none",isArbitraryVariable,isArbitraryValue]}],blur:[{blur:scaleBlur()}],brightness:[{brightness:[isNumber,isArbitraryVariable,isArbitraryValue]}],contrast:[{contrast:[isNumber,isArbitraryVariable,isArbitraryValue]}],"drop-shadow":[{"drop-shadow":["","none",themeDropShadow,isArbitraryVariableShadow,isArbitraryShadow]}],"drop-shadow-color":[{"drop-shadow":scaleColor()}],grayscale:[{grayscale:["",isNumber,isArbitraryVariable,isArbitraryValue]}],"hue-rotate":[{"hue-rotate":[isNumber,isArbitraryVariable,isArbitraryValue]}],invert:[{invert:["",isNumber,isArbitraryVariable,isArbitraryValue]}],saturate:[{saturate:[isNumber,isArbitraryVariable,isArbitraryValue]}],sepia:[{sepia:["",isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-filter":[{"backdrop-filter":["","none",isArbitraryVariable,isArbitraryValue]}],"backdrop-blur":[{"backdrop-blur":scaleBlur()}],"backdrop-brightness":[{"backdrop-brightness":[isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-contrast":[{"backdrop-contrast":[isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-grayscale":[{"backdrop-grayscale":["",isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-invert":[{"backdrop-invert":["",isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-opacity":[{"backdrop-opacity":[isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-saturate":[{"backdrop-saturate":[isNumber,isArbitraryVariable,isArbitraryValue]}],"backdrop-sepia":[{"backdrop-sepia":["",isNumber,isArbitraryVariable,isArbitraryValue]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":scaleUnambiguousSpacing()}],"border-spacing-x":[{"border-spacing-x":scaleUnambiguousSpacing()}],"border-spacing-y":[{"border-spacing-y":scaleUnambiguousSpacing()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",isArbitraryVariable,isArbitraryValue]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[isNumber,"initial",isArbitraryVariable,isArbitraryValue]}],ease:[{ease:["linear","initial",themeEase,isArbitraryVariable,isArbitraryValue]}],delay:[{delay:[isNumber,isArbitraryVariable,isArbitraryValue]}],animate:[{animate:["none",themeAnimate,isArbitraryVariable,isArbitraryValue]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[themePerspective,isArbitraryVariable,isArbitraryValue]}],"perspective-origin":[{"perspective-origin":scalePositionWithArbitrary()}],rotate:[{rotate:scaleRotate()}],"rotate-x":[{"rotate-x":scaleRotate()}],"rotate-y":[{"rotate-y":scaleRotate()}],"rotate-z":[{"rotate-z":scaleRotate()}],scale:[{scale:scaleScale()}],"scale-x":[{"scale-x":scaleScale()}],"scale-y":[{"scale-y":scaleScale()}],"scale-z":[{"scale-z":scaleScale()}],"scale-3d":["scale-3d"],skew:[{skew:scaleSkew()}],"skew-x":[{"skew-x":scaleSkew()}],"skew-y":[{"skew-y":scaleSkew()}],transform:[{transform:[isArbitraryVariable,isArbitraryValue,"","none","gpu","cpu"]}],"transform-origin":[{origin:scalePositionWithArbitrary()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:scaleTranslate()}],"translate-x":[{"translate-x":scaleTranslate()}],"translate-y":[{"translate-y":scaleTranslate()}],"translate-z":[{"translate-z":scaleTranslate()}],"translate-none":["translate-none"],accent:[{accent:scaleColor()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:scaleColor()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",isArbitraryVariable,isArbitraryValue]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":scaleUnambiguousSpacing()}],"scroll-mx":[{"scroll-mx":scaleUnambiguousSpacing()}],"scroll-my":[{"scroll-my":scaleUnambiguousSpacing()}],"scroll-ms":[{"scroll-ms":scaleUnambiguousSpacing()}],"scroll-me":[{"scroll-me":scaleUnambiguousSpacing()}],"scroll-mt":[{"scroll-mt":scaleUnambiguousSpacing()}],"scroll-mr":[{"scroll-mr":scaleUnambiguousSpacing()}],"scroll-mb":[{"scroll-mb":scaleUnambiguousSpacing()}],"scroll-ml":[{"scroll-ml":scaleUnambiguousSpacing()}],"scroll-p":[{"scroll-p":scaleUnambiguousSpacing()}],"scroll-px":[{"scroll-px":scaleUnambiguousSpacing()}],"scroll-py":[{"scroll-py":scaleUnambiguousSpacing()}],"scroll-ps":[{"scroll-ps":scaleUnambiguousSpacing()}],"scroll-pe":[{"scroll-pe":scaleUnambiguousSpacing()}],"scroll-pt":[{"scroll-pt":scaleUnambiguousSpacing()}],"scroll-pr":[{"scroll-pr":scaleUnambiguousSpacing()}],"scroll-pb":[{"scroll-pb":scaleUnambiguousSpacing()}],"scroll-pl":[{"scroll-pl":scaleUnambiguousSpacing()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",isArbitraryVariable,isArbitraryValue]}],fill:[{fill:["none",...scaleColor()]}],"stroke-w":[{stroke:[isNumber,isArbitraryVariableLength,isArbitraryLength,isArbitraryNumber]}],stroke:[{stroke:["none",...scaleColor()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},mergeConfigs=(baseConfig,{cacheSize,prefix,experimentalParseClassName,extend={},override={}})=>(overrideProperty(baseConfig,"cacheSize",cacheSize),overrideProperty(baseConfig,"prefix",prefix),overrideProperty(baseConfig,"experimentalParseClassName",experimentalParseClassName),overrideConfigProperties(baseConfig.theme,override.theme),overrideConfigProperties(baseConfig.classGroups,override.classGroups),overrideConfigProperties(baseConfig.conflictingClassGroups,override.conflictingClassGroups),overrideConfigProperties(baseConfig.conflictingClassGroupModifiers,override.conflictingClassGroupModifiers),overrideProperty(baseConfig,"orderSensitiveModifiers",override.orderSensitiveModifiers),mergeConfigProperties(baseConfig.theme,extend.theme),mergeConfigProperties(baseConfig.classGroups,extend.classGroups),mergeConfigProperties(baseConfig.conflictingClassGroups,extend.conflictingClassGroups),mergeConfigProperties(baseConfig.conflictingClassGroupModifiers,extend.conflictingClassGroupModifiers),mergeArrayProperties(baseConfig,extend,"orderSensitiveModifiers"),baseConfig),overrideProperty=(baseObject,overrideKey,overrideValue)=>{overrideValue!==void 0&&(baseObject[overrideKey]=overrideValue)},overrideConfigProperties=(baseObject,overrideObject)=>{if(overrideObject)for(const key in overrideObject)overrideProperty(baseObject,key,overrideObject[key])},mergeConfigProperties=(baseObject,mergeObject)=>{if(mergeObject)for(const key in mergeObject)mergeArrayProperties(baseObject,mergeObject,key)},mergeArrayProperties=(baseObject,mergeObject,key)=>{const mergeValue=mergeObject[key];mergeValue!==void 0&&(baseObject[key]=baseObject[key]?baseObject[key].concat(mergeValue):mergeValue)},extendTailwindMerge=(configExtension,...createConfig)=>typeof configExtension=="function"?createTailwindMerge(getDefaultConfig,configExtension,...createConfig):createTailwindMerge(()=>mergeConfigs(getDefaultConfig(),configExtension),...createConfig),companionAnchorTagName="stagewise-companion-anchor";function getElementAtPoint(x2,y2){return document.elementsFromPoint(x2,y2).find(element=>element.nodeName!=="STAGEWISE-COMPANION-ANCHOR"&&!element.closest(companionAnchorTagName)&&!element.closest("svg")&&isElementAtPoint(element,x2,y2))||document.body}const isElementAtPoint=(element,clientX,clientY)=>{const boundingRect=element.getBoundingClientRect(),isInHorizontalBounds=clientX>boundingRect.left&&clientX<boundingRect.left+boundingRect.width,isInVerticalBounds=clientY>boundingRect.top&&clientY<boundingRect.top+boundingRect.height;return isInHorizontalBounds&&isInVerticalBounds};var HotkeyActions=(HotkeyActions2=>(HotkeyActions2[HotkeyActions2.ESC=0]="ESC",HotkeyActions2[HotkeyActions2.CTRL_ALT_C=1]="CTRL_ALT_C",HotkeyActions2))(HotkeyActions||{});const hotkeyActionDefinitions={0:{keyComboDefault:"Esc",keyComboMac:"esc",isEventMatching:ev=>ev.code==="Escape"},1:{keyComboDefault:"Ctrl+Alt+C",keyComboMac:"⌘+⌥+C",isEventMatching:ev=>ev.code==="KeyC"&&(ev.ctrlKey||ev.metaKey)&&ev.altKey}},customTwMerge=extendTailwindMerge({extend:{classGroups:{"bg-image":["bg-gradient","bg-gradient-light-1","bg-gradient-light-2","bg-gradient-light-3"]}}});function cn(...inputs){return customTwMerge(clsx(inputs))}const generateId=(length=16)=>Math.random().toString(36).substring(2,length+2);function Panel({children,alwaysFullHeight=!1}){return pluginUi_jsxRuntime.u("section",{className:cn("flex max-h-full min-h-48 flex-col items-stretch justify-start rounded-2xl border border-border/30 bg-zinc-50/80 p-4 shadow-md backdrop-blur-md",alwaysFullHeight&&"h-full"),children})}Panel.Header=function({title,description}){return pluginUi_jsxRuntime.u("header",{className:"mb-3 flex flex-col gap-1 text-zinc-950",children:[title&&pluginUi_jsxRuntime.u("h3",{className:"font-semibold text-lg ",children:title}),description&&pluginUi_jsxRuntime.u("p",{className:"font-medium text-zinc-600",children:description})]})};Panel.Content=function({children}){return pluginUi_jsxRuntime.u("div",{className:"-mx-4 flex flex-col gap-2 overflow-y-auto border-border/30 border-t px-4 pt-4 text-zinc-950",children})};Panel.Footer=function({children}){return pluginUi_jsxRuntime.u("footer",{className:"flex flex-row items-end justify-end gap-2 text-sm text-zinc-600",children})};exports.A=A$1;exports.ConfigProvider=ConfigProvider;exports.D=D;exports.HotkeyActions=HotkeyActions;exports.Panel=Panel;exports.PluginProvider=PluginProvider;exports.Rn=Rn;exports.SRPCBridgeProvider=SRPCBridgeProvider;exports.T=T$1;exports.VSCodeProvider=VSCodeProvider;exports._=_;exports._n=_n;exports.clsx=clsx;exports.cn=cn;exports.companionAnchorTagName=companionAnchorTagName;exports.d=d;exports.g=g$1;exports.generateId=generateId;exports.getElementAtPoint=getElementAtPoint;exports.hotkeyActionDefinitions=hotkeyActionDefinitions;exports.mn=mn;exports.q=q$1;exports.usePlugins=usePlugins;exports.useSRPCBridge=useSRPCBridge;exports.useVSCode=useVSCode;exports.x=x$1;exports.y=y;
