import{s as zi,d as ko,u as le,a as Fi,c as we,p as $n,r as T,w as st,h as Co,n as on,i as ct,b as Je,_ as qt,o as nt,e as oe,f as ce,g as L,j as h,k as v,l as O,m as Li,F as Pe,q as Z,t as qi,v as be,x as te,y as Ae,z as Se,A as Yi,B as Ro,C as Er,D as Sr,E as G,G as Hi,H as $t,I as ur,J as $o,K as wn,L as xn,M as Po,N as Vo,O as Mt,P as Bi,Q as Xi,R as Gi,S as Ki,T as Wi,U as Qi}from"./index-tTBIG6iu.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const it=typeof document<"u";function Mo(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Zi(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Mo(e.default)}const ne=Object.assign;function Pn(e,t){const n={};for(const r in t){const o=t[r];n[r]=Ue(o)?o.map(e):e(o)}return n}const Pt=()=>{},Ue=Array.isArray,Ao=/#/g,Ji=/&/g,ji=/\//g,el=/=/g,tl=/\?/g,No=/\+/g,nl=/%5B/g,rl=/%5D/g,Do=/%5E/g,ol=/%60/g,Io=/%7B/g,il=/%7C/g,Uo=/%7D/g,ll=/%20/g;function cr(e){return encodeURI(""+e).replace(il,"|").replace(nl,"[").replace(rl,"]")}function al(e){return cr(e).replace(Io,"{").replace(Uo,"}").replace(Do,"^")}function On(e){return cr(e).replace(No,"%2B").replace(ll,"+").replace(Ao,"%23").replace(Ji,"%26").replace(ol,"`").replace(Io,"{").replace(Uo,"}").replace(Do,"^")}function sl(e){return On(e).replace(el,"%3D")}function ul(e){return cr(e).replace(Ao,"%23").replace(tl,"%3F")}function cl(e){return e==null?"":ul(e).replace(ji,"%2F")}function At(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const fl=/\/$/,dl=e=>e.replace(fl,"");function Vn(e,t,n="/"){let r,o={},i="",l="";const s=t.indexOf("#");let f=t.indexOf("?");return s<f&&s>=0&&(f=-1),f>-1&&(r=t.slice(0,f),i=t.slice(f+1,s>-1?s:t.length),o=e(i)),s>-1&&(r=r||t.slice(0,s),l=t.slice(s,t.length)),r=_l(r??t,n),{fullPath:r+(i&&"?")+i+l,path:r,query:o,hash:At(l)}}function pl(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function kr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function hl(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&ft(t.matched[r],n.matched[o])&&To(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function ft(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function To(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ml(e[n],t[n]))return!1;return!0}function ml(e,t){return Ue(e)?Cr(e,t):Ue(t)?Cr(t,e):e===t}function Cr(e,t){return Ue(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function _l(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let i=n.length-1,l,s;for(l=0;l<r.length;l++)if(s=r[l],s!==".")if(s==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+r.slice(l).join("/")}const Xe={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Nt;(function(e){e.pop="pop",e.push="push"})(Nt||(Nt={}));var Vt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Vt||(Vt={}));function gl(e){if(!e)if(it){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),dl(e)}const vl=/^[^#]+#/;function yl(e,t){return e.replace(vl,"#")+t}function wl(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const bn=()=>({left:window.scrollX,top:window.scrollY});function xl(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=wl(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Rr(e,t){return(history.state?history.state.position-t:-1)+e}const zn=new Map;function bl(e,t){zn.set(e,t)}function El(e){const t=zn.get(e);return zn.delete(e),t}let Sl=()=>location.protocol+"//"+location.host;function Oo(e,t){const{pathname:n,search:r,hash:o}=t,i=e.indexOf("#");if(i>-1){let s=o.includes(e.slice(i))?e.slice(i).length:1,f=o.slice(s);return f[0]!=="/"&&(f="/"+f),kr(f,"")}return kr(n,e)+r+o}function kl(e,t,n,r){let o=[],i=[],l=null;const s=({state:c})=>{const m=Oo(e,location),y=n.value,C=t.value;let w=0;if(c){if(n.value=m,t.value=c,l&&l===y){l=null;return}w=C?c.position-C.position:0}else r(m);o.forEach(S=>{S(n.value,y,{delta:w,type:Nt.pop,direction:w?w>0?Vt.forward:Vt.back:Vt.unknown})})};function f(){l=n.value}function d(c){o.push(c);const m=()=>{const y=o.indexOf(c);y>-1&&o.splice(y,1)};return i.push(m),m}function a(){const{history:c}=window;c.state&&c.replaceState(ne({},c.state,{scroll:bn()}),"")}function u(){for(const c of i)c();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:f,listen:d,destroy:u}}function $r(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?bn():null}}function Cl(e){const{history:t,location:n}=window,r={value:Oo(e,n)},o={value:t.state};o.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(f,d,a){const u=e.indexOf("#"),c=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+f:Sl()+e+f;try{t[a?"replaceState":"pushState"](d,"",c),o.value=d}catch(m){console.error(m),n[a?"replace":"assign"](c)}}function l(f,d){const a=ne({},t.state,$r(o.value.back,f,o.value.forward,!0),d,{position:o.value.position});i(f,a,!0),r.value=f}function s(f,d){const a=ne({},o.value,t.state,{forward:f,scroll:bn()});i(a.current,a,!0);const u=ne({},$r(r.value,f,null),{position:a.position+1},d);i(f,u,!1),r.value=f}return{location:r,state:o,push:s,replace:l}}function Rl(e){e=gl(e);const t=Cl(e),n=kl(e,t.state,t.location,t.replace);function r(i,l=!0){l||n.pauseListeners(),history.go(i)}const o=ne({location:"",base:e,go:r,createHref:yl.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function $l(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Rl(e)}function Pl(e){return typeof e=="string"||e&&typeof e=="object"}function zo(e){return typeof e=="string"||typeof e=="symbol"}const Fo=Symbol("");var Pr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Pr||(Pr={}));function dt(e,t){return ne(new Error,{type:e,[Fo]:!0},t)}function qe(e,t){return e instanceof Error&&Fo in e&&(t==null||!!(e.type&t))}const Vr="[^/]+?",Vl={sensitive:!1,strict:!1,start:!0,end:!0},Ml=/[.+*?^${}()[\]/\\]/g;function Al(e,t){const n=ne({},Vl,t),r=[];let o=n.start?"^":"";const i=[];for(const d of e){const a=d.length?[]:[90];n.strict&&!d.length&&(o+="/");for(let u=0;u<d.length;u++){const c=d[u];let m=40+(n.sensitive?.25:0);if(c.type===0)u||(o+="/"),o+=c.value.replace(Ml,"\\$&"),m+=40;else if(c.type===1){const{value:y,repeatable:C,optional:w,regexp:S}=c;i.push({name:y,repeatable:C,optional:w});const b=S||Vr;if(b!==Vr){m+=10;try{new RegExp(`(${b})`)}catch(M){throw new Error(`Invalid custom RegExp for param "${y}" (${b}): `+M.message)}}let R=C?`((?:${b})(?:/(?:${b}))*)`:`(${b})`;u||(R=w&&d.length<2?`(?:/${R})`:"/"+R),w&&(R+="?"),o+=R,m+=20,w&&(m+=-8),C&&(m+=-20),b===".*"&&(m+=-50)}a.push(m)}r.push(a)}if(n.strict&&n.end){const d=r.length-1;r[d][r[d].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,n.sensitive?"":"i");function s(d){const a=d.match(l),u={};if(!a)return null;for(let c=1;c<a.length;c++){const m=a[c]||"",y=i[c-1];u[y.name]=m&&y.repeatable?m.split("/"):m}return u}function f(d){let a="",u=!1;for(const c of e){(!u||!a.endsWith("/"))&&(a+="/"),u=!1;for(const m of c)if(m.type===0)a+=m.value;else if(m.type===1){const{value:y,repeatable:C,optional:w}=m,S=y in d?d[y]:"";if(Ue(S)&&!C)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const b=Ue(S)?S.join("/"):S;if(!b)if(w)c.length<2&&(a.endsWith("/")?a=a.slice(0,-1):u=!0);else throw new Error(`Missing required param "${y}"`);a+=b}}return a||"/"}return{re:l,score:r,keys:i,parse:s,stringify:f}}function Nl(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Lo(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const i=Nl(r[n],o[n]);if(i)return i;n++}if(Math.abs(o.length-r.length)===1){if(Mr(r))return 1;if(Mr(o))return-1}return o.length-r.length}function Mr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Dl={type:0,value:""},Il=/[a-zA-Z0-9_]/;function Ul(e){if(!e)return[[]];if(e==="/")return[[Dl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,r=n;const o=[];let i;function l(){i&&o.push(i),i=[]}let s=0,f,d="",a="";function u(){d&&(n===0?i.push({type:0,value:d}):n===1||n===2||n===3?(i.length>1&&(f==="*"||f==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:a,repeatable:f==="*"||f==="+",optional:f==="*"||f==="?"})):t("Invalid state to consume buffer"),d="")}function c(){d+=f}for(;s<e.length;){if(f=e[s++],f==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:f==="/"?(d&&u(),l()):f===":"?(u(),n=1):c();break;case 4:c(),n=r;break;case 1:f==="("?n=2:Il.test(f)?c():(u(),n=0,f!=="*"&&f!=="?"&&f!=="+"&&s--);break;case 2:f===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+f:n=3:a+=f;break;case 3:u(),n=0,f!=="*"&&f!=="?"&&f!=="+"&&s--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),u(),l(),o}function Tl(e,t,n){const r=Al(Ul(e.path),n),o=ne(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Ol(e,t){const n=[],r=new Map;t=Ir({strict:!1,end:!0,sensitive:!1},t);function o(u){return r.get(u)}function i(u,c,m){const y=!m,C=Nr(u);C.aliasOf=m&&m.record;const w=Ir(t,u),S=[C];if("alias"in u){const M=typeof u.alias=="string"?[u.alias]:u.alias;for(const I of M)S.push(Nr(ne({},C,{components:m?m.record.components:C.components,path:I,aliasOf:m?m.record:C})))}let b,R;for(const M of S){const{path:I}=M;if(c&&I[0]!=="/"){const D=c.record.path,V=D[D.length-1]==="/"?"":"/";M.path=c.record.path+(I&&V+I)}if(b=Tl(M,c,w),m?m.alias.push(b):(R=R||b,R!==b&&R.alias.push(b),y&&u.name&&!Dr(b)&&l(u.name)),qo(b)&&f(b),C.children){const D=C.children;for(let V=0;V<D.length;V++)i(D[V],b,m&&m.children[V])}m=m||b}return R?()=>{l(R)}:Pt}function l(u){if(zo(u)){const c=r.get(u);c&&(r.delete(u),n.splice(n.indexOf(c),1),c.children.forEach(l),c.alias.forEach(l))}else{const c=n.indexOf(u);c>-1&&(n.splice(c,1),u.record.name&&r.delete(u.record.name),u.children.forEach(l),u.alias.forEach(l))}}function s(){return n}function f(u){const c=Ll(u,n);n.splice(c,0,u),u.record.name&&!Dr(u)&&r.set(u.record.name,u)}function d(u,c){let m,y={},C,w;if("name"in u&&u.name){if(m=r.get(u.name),!m)throw dt(1,{location:u});w=m.record.name,y=ne(Ar(c.params,m.keys.filter(R=>!R.optional).concat(m.parent?m.parent.keys.filter(R=>R.optional):[]).map(R=>R.name)),u.params&&Ar(u.params,m.keys.map(R=>R.name))),C=m.stringify(y)}else if(u.path!=null)C=u.path,m=n.find(R=>R.re.test(C)),m&&(y=m.parse(C),w=m.record.name);else{if(m=c.name?r.get(c.name):n.find(R=>R.re.test(c.path)),!m)throw dt(1,{location:u,currentLocation:c});w=m.record.name,y=ne({},c.params,u.params),C=m.stringify(y)}const S=[];let b=m;for(;b;)S.unshift(b.record),b=b.parent;return{name:w,path:C,params:y,matched:S,meta:Fl(S)}}e.forEach(u=>i(u));function a(){n.length=0,r.clear()}return{addRoute:i,resolve:d,removeRoute:l,clearRoutes:a,getRoutes:s,getRecordMatcher:o}}function Ar(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Nr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:zl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function zl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Dr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Fl(e){return e.reduce((t,n)=>ne(t,n.meta),{})}function Ir(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Ll(e,t){let n=0,r=t.length;for(;n!==r;){const i=n+r>>1;Lo(e,t[i])<0?r=i:n=i+1}const o=ql(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function ql(e){let t=e;for(;t=t.parent;)if(qo(t)&&Lo(e,t)===0)return t}function qo({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Yl(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const i=r[o].replace(No," "),l=i.indexOf("="),s=At(l<0?i:i.slice(0,l)),f=l<0?null:At(i.slice(l+1));if(s in t){let d=t[s];Ue(d)||(d=t[s]=[d]),d.push(f)}else t[s]=f}return t}function Ur(e){let t="";for(let n in e){const r=e[n];if(n=sl(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ue(r)?r.map(i=>i&&On(i)):[r&&On(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function Hl(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Ue(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const Bl=Symbol(""),Tr=Symbol(""),En=Symbol(""),Yo=Symbol(""),Fn=Symbol("");function yt(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ge(e,t,n,r,o,i=l=>l()){const l=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((s,f)=>{const d=c=>{c===!1?f(dt(4,{from:n,to:t})):c instanceof Error?f(c):Pl(c)?f(dt(2,{from:t,to:c})):(l&&r.enterCallbacks[o]===l&&typeof c=="function"&&l.push(c),s())},a=i(()=>e.call(r&&r.instances[o],t,n,d));let u=Promise.resolve(a);e.length<3&&(u=u.then(d)),u.catch(c=>f(c))})}function Mn(e,t,n,r,o=i=>i()){const i=[];for(const l of e)for(const s in l.components){let f=l.components[s];if(!(t!=="beforeRouteEnter"&&!l.instances[s]))if(Mo(f)){const a=(f.__vccOpts||f)[t];a&&i.push(Ge(a,n,r,l,s,o))}else{let d=f();i.push(()=>d.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${s}" at "${l.path}"`);const u=Zi(a)?a.default:a;l.mods[s]=a,l.components[s]=u;const m=(u.__vccOpts||u)[t];return m&&Ge(m,n,r,l,s,o)()}))}}return i}function Or(e){const t=ct(En),n=ct(Yo),r=we(()=>{const f=le(e.to);return t.resolve(f)}),o=we(()=>{const{matched:f}=r.value,{length:d}=f,a=f[d-1],u=n.matched;if(!a||!u.length)return-1;const c=u.findIndex(ft.bind(null,a));if(c>-1)return c;const m=zr(f[d-2]);return d>1&&zr(a)===m&&u[u.length-1].path!==m?u.findIndex(ft.bind(null,f[d-2])):c}),i=we(()=>o.value>-1&&Ql(n.params,r.value.params)),l=we(()=>o.value>-1&&o.value===n.matched.length-1&&To(n.params,r.value.params));function s(f={}){if(Wl(f)){const d=t[le(e.replace)?"replace":"push"](le(e.to)).catch(Pt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:r,href:we(()=>r.value.href),isActive:i,isExactActive:l,navigate:s}}function Xl(e){return e.length===1?e[0]:e}const Gl=ko({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Or,setup(e,{slots:t}){const n=Je(Or(e)),{options:r}=ct(En),o=we(()=>({[Fr(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Fr(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&Xl(t.default(n));return e.custom?i:Co("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},i)}}}),Kl=Gl;function Wl(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ql(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!Ue(o)||o.length!==r.length||r.some((i,l)=>i!==o[l]))return!1}return!0}function zr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Fr=(e,t,n)=>e??t??n,Zl=ko({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=ct(Fn),o=we(()=>e.route||r.value),i=ct(Tr,0),l=we(()=>{let d=le(i);const{matched:a}=o.value;let u;for(;(u=a[d])&&!u.components;)d++;return d}),s=we(()=>o.value.matched[l.value]);$n(Tr,we(()=>l.value+1)),$n(Bl,s),$n(Fn,o);const f=T();return st(()=>[f.value,s.value,e.name],([d,a,u],[c,m,y])=>{a&&(a.instances[u]=d,m&&m!==a&&d&&d===c&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),d&&a&&(!m||!ft(a,m)||!c)&&(a.enterCallbacks[u]||[]).forEach(C=>C(d))},{flush:"post"}),()=>{const d=o.value,a=e.name,u=s.value,c=u&&u.components[a];if(!c)return Lr(n.default,{Component:c,route:d});const m=u.props[a],y=m?m===!0?d.params:typeof m=="function"?m(d):m:null,w=Co(c,ne({},y,t,{onVnodeUnmounted:S=>{S.component.isUnmounted&&(u.instances[a]=null)},ref:f}));return Lr(n.default,{Component:w,route:d})||w}}});function Lr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Jl=Zl;function jl(e){const t=Ol(e.routes,e),n=e.parseQuery||Yl,r=e.stringifyQuery||Ur,o=e.history,i=yt(),l=yt(),s=yt(),f=zi(Xe);let d=Xe;it&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=Pn.bind(null,k=>""+k),u=Pn.bind(null,cl),c=Pn.bind(null,At);function m(k,F){let U,B;return zo(k)?(U=t.getRecordMatcher(k),B=F):B=k,t.addRoute(B,U)}function y(k){const F=t.getRecordMatcher(k);F&&t.removeRoute(F)}function C(){return t.getRoutes().map(k=>k.record)}function w(k){return!!t.getRecordMatcher(k)}function S(k,F){if(F=ne({},F||f.value),typeof k=="string"){const W=Vn(n,k,F.path),_=t.resolve({path:W.path},F),x=o.createHref(W.fullPath);return ne(W,_,{params:c(_.params),hash:At(W.hash),redirectedFrom:void 0,href:x})}let U;if(k.path!=null)U=ne({},k,{path:Vn(n,k.path,F.path).path});else{const W=ne({},k.params);for(const _ in W)W[_]==null&&delete W[_];U=ne({},k,{params:u(W)}),F.params=u(F.params)}const B=t.resolve(U,F),re=k.hash||"";B.params=a(c(B.params));const de=pl(r,ne({},k,{hash:al(re),path:B.path})),Q=o.createHref(de);return ne({fullPath:de,hash:re,query:r===Ur?Hl(k.query):k.query||{}},B,{redirectedFrom:void 0,href:Q})}function b(k){return typeof k=="string"?Vn(n,k,f.value.path):ne({},k)}function R(k,F){if(d!==k)return dt(8,{from:F,to:k})}function M(k){return V(k)}function I(k){return M(ne(b(k),{replace:!0}))}function D(k){const F=k.matched[k.matched.length-1];if(F&&F.redirect){const{redirect:U}=F;let B=typeof U=="function"?U(k):U;return typeof B=="string"&&(B=B.includes("?")||B.includes("#")?B=b(B):{path:B},B.params={}),ne({query:k.query,hash:k.hash,params:B.path!=null?{}:k.params},B)}}function V(k,F){const U=d=S(k),B=f.value,re=k.state,de=k.force,Q=k.replace===!0,W=D(U);if(W)return V(ne(b(W),{state:typeof W=="object"?ne({},re,W.state):re,force:de,replace:Q}),F||U);const _=U;_.redirectedFrom=F;let x;return!de&&hl(r,B,U)&&(x=dt(16,{to:_,from:B}),ee(B,B,!0,!1)),(x?Promise.resolve(x):p(_,B)).catch(X=>qe(X)?qe(X,2)?X:pe(X):j(X,_,B)).then(X=>{if(X){if(qe(X,2))return V(ne({replace:Q},b(X.to),{state:typeof X.to=="object"?ne({},re,X.to.state):re,force:de}),F||_)}else X=P(_,B,!0,Q,re);return g(_,B,X),X})}function N(k,F){const U=R(k,F);return U?Promise.reject(U):Promise.resolve()}function A(k){const F=Re.values().next().value;return F&&typeof F.runWithContext=="function"?F.runWithContext(k):k()}function p(k,F){let U;const[B,re,de]=ea(k,F);U=Mn(B.reverse(),"beforeRouteLeave",k,F);for(const W of B)W.leaveGuards.forEach(_=>{U.push(Ge(_,k,F))});const Q=N.bind(null,k,F);return U.push(Q),Ee(U).then(()=>{U=[];for(const W of i.list())U.push(Ge(W,k,F));return U.push(Q),Ee(U)}).then(()=>{U=Mn(re,"beforeRouteUpdate",k,F);for(const W of re)W.updateGuards.forEach(_=>{U.push(Ge(_,k,F))});return U.push(Q),Ee(U)}).then(()=>{U=[];for(const W of de)if(W.beforeEnter)if(Ue(W.beforeEnter))for(const _ of W.beforeEnter)U.push(Ge(_,k,F));else U.push(Ge(W.beforeEnter,k,F));return U.push(Q),Ee(U)}).then(()=>(k.matched.forEach(W=>W.enterCallbacks={}),U=Mn(de,"beforeRouteEnter",k,F,A),U.push(Q),Ee(U))).then(()=>{U=[];for(const W of l.list())U.push(Ge(W,k,F));return U.push(Q),Ee(U)}).catch(W=>qe(W,8)?W:Promise.reject(W))}function g(k,F,U){s.list().forEach(B=>A(()=>B(k,F,U)))}function P(k,F,U,B,re){const de=R(k,F);if(de)return de;const Q=F===Xe,W=it?history.state:{};U&&(B||Q?o.replace(k.fullPath,ne({scroll:Q&&W&&W.scroll},re)):o.push(k.fullPath,re)),f.value=k,ee(k,F,U,Q),pe()}let q;function Y(){q||(q=o.listen((k,F,U)=>{if(!K.listening)return;const B=S(k),re=D(B);if(re){V(ne(re,{replace:!0,force:!0}),B).catch(Pt);return}d=B;const de=f.value;it&&bl(Rr(de.fullPath,U.delta),bn()),p(B,de).catch(Q=>qe(Q,12)?Q:qe(Q,2)?(V(ne(b(Q.to),{force:!0}),B).then(W=>{qe(W,20)&&!U.delta&&U.type===Nt.pop&&o.go(-1,!1)}).catch(Pt),Promise.reject()):(U.delta&&o.go(-U.delta,!1),j(Q,B,de))).then(Q=>{Q=Q||P(B,de,!1),Q&&(U.delta&&!qe(Q,8)?o.go(-U.delta,!1):U.type===Nt.pop&&qe(Q,20)&&o.go(-1,!1)),g(B,de,Q)}).catch(Pt)}))}let E=yt(),z=yt(),H;function j(k,F,U){pe(k);const B=z.list();return B.length?B.forEach(re=>re(k,F,U)):console.error(k),Promise.reject(k)}function se(){return H&&f.value!==Xe?Promise.resolve():new Promise((k,F)=>{E.add([k,F])})}function pe(k){return H||(H=!k,Y(),E.list().forEach(([F,U])=>k?U(k):F()),E.reset()),k}function ee(k,F,U,B){const{scrollBehavior:re}=e;if(!it||!re)return Promise.resolve();const de=!U&&El(Rr(k.fullPath,0))||(B||!U)&&history.state&&history.state.scroll||null;return on().then(()=>re(k,F,de)).then(Q=>Q&&xl(Q)).catch(Q=>j(Q,k,F))}const ve=k=>o.go(k);let ye;const Re=new Set,K={currentRoute:f,listening:!0,addRoute:m,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:C,resolve:S,options:e,push:M,replace:I,go:ve,back:()=>ve(-1),forward:()=>ve(1),beforeEach:i.add,beforeResolve:l.add,afterEach:s.add,onError:z.add,isReady:se,install(k){const F=this;k.component("RouterLink",Kl),k.component("RouterView",Jl),k.config.globalProperties.$router=F,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>le(f)}),it&&!ye&&f.value===Xe&&(ye=!0,M(o.location).catch(re=>{}));const U={};for(const re in Xe)Object.defineProperty(U,re,{get:()=>f.value[re],enumerable:!0});k.provide(En,F),k.provide(Yo,Fi(U)),k.provide(Fn,f);const B=k.unmount;Re.add(k),k.unmount=function(){Re.delete(k),Re.size<1&&(d=Xe,q&&q(),q=null,f.value=Xe,ye=!1,H=!1),B()}}};function Ee(k){return k.reduce((F,U)=>F.then(()=>A(U)),Promise.resolve())}return K}function ea(e,t){const n=[],r=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let l=0;l<i;l++){const s=t.matched[l];s&&(e.matched.find(d=>ft(d,s))?r.push(s):n.push(s));const f=e.matched[l];f&&(t.matched.find(d=>ft(d,f))||o.push(f))}return[n,r,o]}function ta(){return ct(En)}const na={class:"app-container"},ra={class:"grid-container"},oa={class:"header"},ia={class:"header-left"},la={class:"user-info"},aa={class:"el-dropdown-link"},sa={class:"user-name"},ua={class:"sidebar"},ca={class:"content",ref:"contentRef"},fa={__name:"App",setup(e){const t=ta(),n=T(""),r=T([]),o=T(!1),i=T(""),l=T(null);nt(async()=>{await s(),await f(),t.push("/unit-management")});const s=async()=>{const D=await oe.post("/api/get_user_info.php");n.value=D.user.name},f=async()=>{const D=await oe.post("/api/get_user_app.php");r.value=D.data},d=D=>{console.log("点击的菜单对应的路由是:",D),t.push(D)},a=()=>{o.value=!o.value},u=T(!1),c=T({oldPassword:"",newPassword:"",confirmPassword:""}),m=T(null),y=T(!1),C=T(!1),w=()=>{y.value=!y.value},S=()=>{C.value=!C.value},b=async D=>{D==="logout"?(await oe.get("/api/logout.php"),window.location.href="login.html"):D==="changePassword"&&(u.value=!0)},R=async()=>{m.value&&await m.value.validate(D=>{if(D){if(c.value.newPassword!==c.value.confirmPassword){G.error("两次输入的密码不一致");return}const V=new FormData;V.append("old_password",c.value.oldPassword),V.append("new_password",c.value.newPassword),oe.post("/api/change_password.php",V).then(()=>{G.success("密码修改成功，请重新登录"),u.value=!1,c.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{G.error("密码修改失败")})}})},M=Je({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(D,V,N)=>{V!==c.value.newPassword?N(new Error("两次输入的密码不一致")):N()},trigger:"blur"}]}),I=()=>{const D=l.value;D&&(document.fullscreenElement?document.exitFullscreen():D.requestFullscreen().catch(V=>{console.error("全屏失败:",V),G.error("全屏功能不支持")}))};return(D,V)=>{const N=O("el-button"),A=O("el-icon"),p=O("el-avatar"),g=O("el-dropdown-item"),P=O("el-dropdown-menu"),q=O("el-dropdown"),Y=O("el-menu-item"),E=O("el-menu"),z=O("router-view"),H=O("el-input"),j=O("el-form-item"),se=O("el-form"),pe=O("el-dialog");return Z(),ce(Pe,null,[L("div",na,[L("div",ra,[L("div",oa,[L("div",ia,[h(N,{onClick:a,type:"text",class:"menu-btn"},{default:v(()=>V[5]||(V[5]=[L("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),V[6]||(V[6]=L("img",{src:Li,alt:"Logo",class:"header-logo"},null,-1)),V[7]||(V[7]=L("span",{class:"logo"},"CloudPivot",-1))])]),L("div",la,[h(q,{onCommand:b},{dropdown:v(()=>[h(P,null,{default:v(()=>[h(g,{command:"profile"},{default:v(()=>V[8]||(V[8]=[te("个人信息")])),_:1,__:[8]}),h(g,{command:"changePassword"},{default:v(()=>V[9]||(V[9]=[te("修改密码")])),_:1,__:[9]}),h(g,{command:"logout"},{default:v(()=>V[10]||(V[10]=[te("退出登录")])),_:1,__:[10]})]),_:1})]),default:v(()=>[L("span",aa,[h(p,{size:"small"},{default:v(()=>[h(A,null,{default:v(()=>[h(le(qi),{style:{color:"#409EFF"}})]),_:1})]),_:1}),L("span",sa,be(n.value),1)])]),_:1})]),L("div",ua,[h(E,{"default-active":i.value,class:"el-menu-vertical",mode:"vertical",collapse:o.value,onOpen:D.handleOpen,onClose:D.handleClose},{default:v(()=>[(Z(!0),ce(Pe,null,Ae(r.value,ee=>(Z(),Se(Y,{key:ee.id,index:ee.id.toString(),router:ee.url,onClick:ve=>d(ee.url)},{title:v(()=>[L("span",null,be(ee.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),L("div",ca,[h(N,{onClick:I,type:"text",class:"fullscreen-btn"},{default:v(()=>[h(A,null,{default:v(()=>[h(le(Yi))]),_:1})]),_:1}),L("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:l},[h(z,{ref:"routerViewRef"},null,512)],512)],512)])]),h(pe,{modelValue:u.value,"onUpdate:modelValue":V[4]||(V[4]=ee=>u.value=ee),width:"400px"},{default:v(()=>[h(se,{model:c.value,ref_key:"passwordFormRef",ref:m,rules:M,"label-width":"120px",onSubmit:Ro(R,["prevent"])},{default:v(()=>[h(j,{label:"旧密码",prop:"oldPassword",required:""},{default:v(()=>[h(H,{modelValue:c.value.oldPassword,"onUpdate:modelValue":V[0]||(V[0]=ee=>c.value.oldPassword=ee),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),h(j,{label:"新密码",prop:"newPassword",required:""},{default:v(()=>[h(H,{modelValue:c.value.newPassword,"onUpdate:modelValue":V[1]||(V[1]=ee=>c.value.newPassword=ee),type:y.value?"text":"password",placeholder:"请输入新密码"},{suffix:v(()=>[h(N,{icon:y.value?le(Er):le(Sr),onClick:w,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),h(j,{label:"确认新密码",prop:"confirmPassword",required:""},{default:v(()=>[h(H,{modelValue:c.value.confirmPassword,"onUpdate:modelValue":V[2]||(V[2]=ee=>c.value.confirmPassword=ee),type:C.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:v(()=>[h(N,{icon:C.value?le(Er):le(Sr),onClick:S,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),h(j,null,{default:v(()=>[h(N,{type:"primary","native-type":"submit"},{default:v(()=>V[11]||(V[11]=[te("确定")])),_:1,__:[11]}),h(N,{onClick:V[3]||(V[3]=ee=>u.value=!1)},{default:v(()=>V[12]||(V[12]=[te("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},da=qt(fa,[["__scopeId","data-v-1e64b65d"]]),pa={class:"unit-management"},ha={style:{"text-align":"right",margin:"10px"}},ma={__name:"UnitManagement",setup(e){const t=T(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=p=>{if(p&&p.length>0){const g=p[p.length-1],P=i(g);P&&(o.parentId=g,u.value=P.children||[])}else o.parentId=null,u.value=[]},o=Je({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:0});st(()=>o.parentId,(p,g)=>{if(console.log("parentId 发生变化，旧值: ",g,"新值: ",p),p){const P=i(p);P&&(u.value=P.children||[])}else u.value=[]},{immediate:!1});const i=(p,g=l.value)=>{for(let P=0;P<g.length;P++){if(g[P].id===p)return g[P];if(g[P].children){const q=i(p,g[P].children);if(q)return q}}return null},l=T([]),s=T(new Set),f=we(()=>{const p=[],g=(P,q=0,Y=null)=>{P.forEach(E=>{p.push({...E,level:q,parentId:Y,expanded:s.value.has(E.id)}),E.children&&g(E.children,q+1,E.id)})};return g(l.value),p}),d=we(()=>{const p=[],g=P=>{if(P.level===0)return!0;let q=f.value.find(Y=>Y.id===P.parentId);for(;q;){if(!q.expanded)return!1;q=f.value.find(Y=>Y.id===q.parentId)}return!0};return f.value.forEach(P=>{g(P)&&p.push(P)}),p}),a=T(!1),u=T([]),c=T(null);nt(async()=>{await b()});const m=T(!1),y=p=>{const g=[];function P(q,Y){for(const E of q){const z=[...Y,E.id];if(E.id===p)return g.push(...z),!0;if(E.children&&E.children.length>0&&P(E.children,z))return!0}return!1}return P(l.value,[]),g},C=p=>{if(c.value&&c.value.resetFields(),t.value=!1,o.id=null,o.name="",o.code="",o.parentId=null,o.parentIdPath=[],o.sort_order=0,m.value=!!p,p){const g=i(p);g&&(o.parentId=p,console.log("parentId位置一:",p),o.parentIdPath=y(p),u.value=g.children||[])}else o.parentId=null,o.parentIdPath=[],u.value=[];a.value=!0,console.log("dialogVisible 已设为 true")},w=p=>{c.value&&c.value.resetFields(),t.value=!0,o.id=p.id;let g={currentUnit:p.unit_name,selectedSortOrder:null,selectedUnit:null};o.name=p.unit_name,o.code=p.code,o.parentId=p.parent_id,o.parentIdPath=y(p.parent_id);const P=i(p.parent_id);if(P){u.value=(P.children||[]).filter(E=>E.id!==p.id);const q=P.children||[],Y=q.findIndex(E=>E.id===p.id);if(Y>0){const E=q[Y-1];o.sort_order=E.sort_order,g.selectedUnit=E.unit_name}else o.sort_order=0,g.selectedUnit="最前";g.selectedSortOrder=o.sort_order}else{u.value=l.value.filter(Y=>Y.id!==p.id);const q=l.value.findIndex(Y=>Y.id===p.id);if(q>0){const Y=l.value[q-1];o.sort_order=Y.sort_order,g.selectedUnit=Y.unit_name}else o.sort_order=0,g.selectedUnit="最前";g.selectedSortOrder=o.sort_order}console.log("排序选项:",u.value),console.log("自动选择结果:",g),a.value=!0},S=()=>{c.value.validate(async p=>{if(p)try{const g=new FormData;o.id?(g.append("action","edit"),g.append("id",o.id)):g.append("action","add"),g.append("sort_order",o.sort_order+1),g.append("unit_name",o.name),g.append("code",o.code),g.append("parent_id",o.parentId||null);const P=await oe.post("api/unit_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});P.status===1?(await b(),a.value=!1,G.success(o.id?"编辑成功":"新增成功")):G.error(P.message)}catch(g){console.error("保存单位失败:",g),G.error("保存单位失败，请稍后重试")}})},b=async()=>{const p=await oe.post("api/get_unit_info.php");l.value=[p.data],console.log("获取单位数据成功:",l.value);const g=P=>{P.forEach(q=>{q.children&&q.children.length>0&&(s.value.add(q.id),g(q.children))})};g(l.value)},R=p=>{Mt.confirm(`确定要删除 ${p.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const g=new FormData;g.append("action","del"),g.append("id",p.id),await oe.post("api/unit_manage.php",g),G.success("删除成功"),await b()})},M=p=>{const g=new Set(s.value);g.has(p.id)?g.delete(p.id):g.add(p.id),s.value=g},I=p=>((p.parentId?i(p.parentId):{children:l.value}).children||[]).findIndex(Y=>Y.id===p.id)===0,D=p=>{const P=(p.parentId?i(p.parentId):{children:l.value}).children||[];return P.findIndex(Y=>Y.id===p.id)===P.length-1},V=async p=>{const g=new FormData;g.append("action","edit"),g.append("id",p.id),g.append("sort_order",p.sort_order-1),g.append("unit_name",p.unit_name),g.append("code",p.code),g.append("parent_id",p.parentId),await oe.post("api/unit_manage.php",g),await b()},N=async p=>{const g=new FormData;g.append("action","edit"),g.append("id",p.id),g.append("sort_order",p.sort_order+1),g.append("unit_name",p.unit_name),g.append("code",p.code),g.append("parent_id",p.parentId),await oe.post("api/unit_manage.php",g),await b()},A=we(()=>u.value.filter(p=>p.id!==o.id));return(p,g)=>{const P=O("el-button"),q=O("el-col"),Y=O("el-row"),E=O("el-table-column"),z=O("el-icon"),H=O("el-button-group"),j=O("el-table"),se=O("el-input"),pe=O("el-form-item"),ee=O("el-cascader"),ve=O("el-option"),ye=O("el-select"),Re=O("el-form");return Z(),ce("div",pa,[h(Y,null,{default:v(()=>[h(q,{span:24},{default:v(()=>[L("div",ha,[h(P,{type:"primary",round:"",onClick:g[0]||(g[0]=K=>C(null))},{default:v(()=>g[7]||(g[7]=[te("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),h(j,{data:d.value,style:{width:"100%",height:"calc(100vh - 200px)","overflow-y":"auto"},border:""},{default:v(()=>[h(E,{label:"操作",width:"60",align:"center"},{default:v(K=>[K.row.children&&K.row.children.length?(Z(),Se(P,{key:0,size:"mini",type:"text",onClick:Ee=>M(K.row)},{default:v(()=>[te(be(K.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):$t("",!0)]),_:1}),h(E,{prop:"unit_name",label:"单位名称"},{default:v(K=>[L("span",{style:ur({paddingLeft:`${K.row.level*20}px`})},be(K.row.unit_name),5)]),_:1}),h(E,{prop:"code",label:"组织机构代码",width:"250"}),h(E,{label:"操作",width:"350"},{default:v(K=>[h(H,null,{default:v(()=>[h(P,{size:"mini",type:"primary",onClick:Ee=>C(K.row.id)},{default:v(()=>[h(z,null,{default:v(()=>[h(le($o))]),_:1})]),_:2},1032,["onClick"]),h(P,{size:"mini",type:"warning",onClick:Ee=>w(K.row)},{default:v(()=>[h(z,null,{default:v(()=>[h(le(wn))]),_:1})]),_:2},1032,["onClick"]),h(P,{size:"mini",type:"danger",onClick:Ee=>R(K.row)},{default:v(()=>[h(z,null,{default:v(()=>[h(le(xn))]),_:1})]),_:2},1032,["onClick"]),h(P,{size:"mini",type:"info",onClick:Ee=>V(K.row),disabled:I(K.row)},{default:v(()=>[h(z,null,{default:v(()=>[h(le(Po))]),_:1})]),_:2},1032,["onClick","disabled"]),h(P,{size:"mini",type:"info",onClick:Ee=>N(K.row),disabled:D(K.row)},{default:v(()=>[h(z,null,{default:v(()=>[h(le(Vo))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),h(le(Hi),{modelValue:a.value,"onUpdate:modelValue":g[6]||(g[6]=K=>a.value=K),title:"",width:"450px","close-on-click-modal":!1},{footer:v(()=>[h(P,{onClick:g[5]||(g[5]=K=>a.value=!1)},{default:v(()=>g[8]||(g[8]=[te("取消")])),_:1,__:[8]}),h(P,{type:"primary",onClick:S},{default:v(()=>g[9]||(g[9]=[te("确定")])),_:1,__:[9]})]),default:v(()=>[h(Re,{model:o,ref_key:"formRef",ref:c,"label-width":"130px"},{default:v(()=>[h(pe,{label:"单位名称",prop:"name",required:""},{default:v(()=>[h(se,{modelValue:o.name,"onUpdate:modelValue":g[1]||(g[1]=K=>o.name=K),required:""},null,8,["modelValue"])]),_:1}),h(pe,{label:"组织机构代码",prop:"code"},{default:v(()=>[h(se,{modelValue:o.code,"onUpdate:modelValue":g[2]||(g[2]=K=>o.code=K)},null,8,["modelValue"])]),_:1}),h(pe,{label:"上级单位",prop:"parentId"},{default:v(()=>[h(ee,{modelValue:o.parentIdPath,"onUpdate:modelValue":g[3]||(g[3]=K=>o.parentIdPath=K),options:l.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),h(pe,{label:"排序",prop:"sort",required:""},{default:v(()=>[h(ye,{modelValue:o.sort_order,"onUpdate:modelValue":g[4]||(g[4]=K=>o.sort_order=K),placeholder:"请选择排序位置"},{default:v(()=>[h(ve,{label:"置于 最前",value:0}),(Z(!0),ce(Pe,null,Ae(A.value,K=>(Z(),Se(ve,{key:K.id,label:`置于 ${K.unit_name} 之后`,value:K.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},_a={class:"user-management-container"},ga={class:"left-panel"},va=["onClick"],ya={class:"right-panel"},wa={class:"action-buttons",style:{display:"flex","align-items":"center"}},xa={class:"left-buttons",style:{display:"flex","align-items":"center",gap:"8px"}},ba={class:"right-switches",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},Ea={class:"form-row"},Sa={class:"form-row"},ka={class:"form-row"},Ca={class:"form-row"},Ra={class:"form-row"},$a={class:"form-row"},Pa={class:"form-row"},Va={class:"form-row"},Ma={class:"dialog-footer"},Aa={__name:"UserManagement",setup(e){const t=T([]),n=T([]),r=T([]),o=T(null);T("");const i=T([]),l=T([]),s=we(()=>{if(!o.value)return"";const _=c.value.find(x=>x.id===o.value);return console.log("当前选中的部门ID:",o.value),console.log("当前选中的部门:",_.unit_name),_?_.unit_name:""});T(null);const f=T(1),d=T(10),a=T(0),u=T([]),c=T([]),m=T(""),y=T([]),C=T([]),w=T([]),S=T([]),b=T(!1),R=T(""),M=T(!1),I=T({});T(!1);const D=T(!1),V=T(null),N=()=>{M.value?b.value=!1:F()},A=we(()=>m.value?c.value.filter(_=>_.show&&_.unit_name.toLowerCase().includes(m.value.toLowerCase())):c.value.filter(_=>_.show)),p=Je({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sort_order:null,desc:""}),g={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},P=T(null),q=async()=>{try{const _=new FormData;_.append("type","identity");const x=await oe({url:"/api/person_info_api.php",method:"post",data:_});x.status===1?t.value=x.data.map(X=>({label:X,value:X})):G.error("获取人员身份数据失败："+x.message)}catch{G.error("网络请求失败")}},Y=async()=>{try{const _=new FormData;_.append("type","status");const x=await oe({url:"/api/person_info_api.php",method:"post",data:_});x.status===1?n.value=x.data.map(X=>({label:X,value:X})):G.error("获取人员状态数据失败："+x.message)}catch{G.error("网络请求失败")}},E=async()=>{try{const _=new FormData;_.append("type","rank");const x=await oe({url:"/api/person_info_api.php",method:"post",data:_});x.status===1?r.value=x.data.map(X=>({label:X,value:X})):G.error("获取职级数据失败："+x.message)}catch{G.error("网络请求失败")}};nt(async()=>{try{const _=await oe.post("api/get_unit_info.php");u.value=[_.data],H(_.data),y.value=u.value,C.value=u.value,console.log("获取部门数据成功:",u.value),await z(),q(),Y(),E()}catch(_){console.error("获取数据失败:",_),G.error("获取数据失败，请稍后重试")}});const z=async()=>{console.log("开始获取用户数据...");try{const _=new FormData;_.append("controlCode","query"),_.append("page",f.value),_.append("pagesize",d.value),o.value&&_.append("organization_unit",o.value),pe.value?_.append("isShowInPersonnel","1"):_.append("isShowInPersonnel","0"),se.value?_.append("isShowOutPersonnel","1"):_.append("isShowOutPersonnel","0");const x=await oe.post("api/user_manage.php",_,{headers:{"Content-Type":"multipart/form-data"}});w.value=x.data,console.log("获取用户数据成功:",w.value),a.value=x.total}catch(_){console.error("获取用户数据失败:",_),G.error("获取用户数据失败，请稍后重试")}},H=(_,x=0,X=null)=>{const ie={..._,level:x,expanded:_.children&&_.children.length>0,parent:X,indent:x*20,show:!0};c.value.push(ie),_.children&&_.children.length>0&&_.children.forEach(he=>{H(he,x+1,ie)})};console.log("扁平化后的部门列表:",c.value);const j=_=>{_.expanded=!_.expanded;const x=c.value.indexOf(_)+1;let X=_.level+1;for(let ie=x;ie<c.value.length;ie++){const he=c.value[ie];if(he.level<=_.level)break;he.level===X?he.show=_.expanded:he.level>X&&(he.show=_.expanded&&c.value[ie-1].show)}},se=T(!0),pe=T(!0),ee=()=>{b.value=!0,P.value.resetFields()},ve=async()=>{if(S.value.length===0){G.warning("请先选择要删除的用户");return}try{await Mt.confirm(`确定要删除选中的 ${S.value.length} 个用户吗？删除后数据不可恢复`,"批量删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const _=new FormData;_.append("controlCode","del");const x=S.value.map(X=>X.id);_.append("id",x.join(",")),await oe.post("api/user_manage.php",_,{headers:{"Content-Type":"multipart/form-data"}}),G.success(`成功删除 ${S.value.length} 个用户`),await z(),S.value=[]}catch(_){_!=="cancel"&&G.error(`删除失败：${_.message||"未知错误"}`)}},ye=async _=>{P.value&&P.value.resetFields(),D.value=!0,V.value=_.id,b.value=!0,R.value="编辑用户信息",M.value=!1,p.name=_.name,p.id_number=_.id_number,p.phone=_.phone,p.archive_birthdate=_.archive_birthdate,p.gender=_.gender,console.log("性别值:",_.gender),p.short_code=_.short_code,p.alt_phone_1=_.alt_phone_1,p.alt_phone_2=_.alt_phone_2,p.landline=_.landline,p.organization_unit=_.organization_unit,p.work_unit=_.work_unit,p.employment_date=_.employment_date,p.political_status=_.political_status,p.party_join_date=_.party_join_date,p.personnel_type=_.personnel_type,p.police_number=_.police_number,p.is_assisting_officer=_.is_assisting_officer,p.employment_status=_.employment_status,p.job_rank=_.job_rank,p.current_rank_date=_.current_rank_date,p.position=_.position,p.current_position_date=_.current_position_date,await Q(_.organization_unit),p.sort_order=_.sort_order,console.log("排序值:",_.sort_order),p.desc=_.desc},Re=_=>{R.value="查看用户详情",I.value={..._},M.value=!0,b.value=!0},K=async _=>{try{await Mt.confirm(`确定要删除用户 ${_.name} 吗？删除后数据不可恢复`,"删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const x=new FormData;x.append("controlCode","del"),x.append("id",_.id),await oe.post("api/user_manage.php",x,{headers:{"Content-Type":"multipart/form-data"}}),G.success("用户删除成功"),await z()}catch(x){x!=="cancel"&&G.error(`删除失败：${x.message||"未知错误"}`)}},Ee=_=>{S.value=_},k=async _=>{console.log("点击的部门名称:",_),o.value=_.id,await z()},F=async()=>{try{await P.value.validate();const _=new FormData;D.value?(_.append("controlCode","modify"),_.append("id",V.value)):_.append("controlCode","add"),_.append("name",p.name),_.append("id_number",p.id_number||""),_.append("phone",p.phone),_.append("archive_birthdate",p.archive_birthdate||""),_.append("gender",p.gender||""),_.append("short_code",p.short_code||""),_.append("alt_phone_1",p.alt_phone_1||""),_.append("alt_phone_2",p.alt_phone_2||""),_.append("landline",p.landline||"");const x=Array.isArray(p.organization_unit)?p.organization_unit[p.organization_unit.length-1]:p.organization_unit;_.append("organization_unit",x||"");const X=Array.isArray(p.work_unit)?p.work_unit[p.work_unit.length-1]:p.work_unit;_.append("work_unit",X||x),_.append("employment_date",p.employment_date||""),_.append("political_status",p.political_status||""),_.append("party_join_date",p.party_join_date||""),_.append("personnel_type",p.personnel_type||""),_.append("police_number",p.police_number||""),_.append("assisting_officer",p.is_assisting_officer||""),_.append("employment_status",p.employment_status||""),_.append("job_rank",p.job_rank||""),_.append("current_rank_date",p.current_rank_date||""),_.append("position",p.position||""),_.append("current_position_date",p.current_position_date||""),_.append("sort_order",p.sort_order+1),_.append("desc",p.desc||""),await oe.post("api/user_manage.php",_,{headers:{"Content-Type":"multipart/form-data"}});const ie=D.value?"编辑":"添加";G.success(`用户${ie}成功`),b.value=!1,D.value=!1,V.value=null,await z(),P.value.resetFields()}catch(_){G.error("提交失败："+(_.message||"未知错误"))}},U=we(()=>{const _=new Map;return c.value.forEach(x=>{_.set(x.id,x.unit_name)}),_}),B=_=>(console.log("单位ID:",_),console.log("组织映射:",U.value),console.log("单位名称:",U.value.get(_)),U.value.get(_)||"未知单位"),re=async _=>{try{const x=new FormData;x.append("controlCode","modify");for(const ie in _)ie!=="sort_order"&&x.append(ie,_[ie]);const X=_.sort_order-1;x.append("sort_order",X),await oe.post("api/user_manage.php",x),await z(),G.success("上移成功")}catch{G.error("上移失败，请稍后重试")}},de=async _=>{try{const x=new FormData;x.append("action","adjust_sort"),x.append("user_id",_.id),x.append("direction","down"),await oe.post("api/user_manage.php",x),await z(),G.success("下移成功")}catch{G.error("下移失败，请稍后重试")}};st(pe,async _=>{f.value=1,await z()}),st(se,async _=>{f.value=1,await z()});const Q=async _=>{if(!_){l.value=[];return}try{const x=new FormData;x.append("controlCode","getSortOptions"),x.append("organization_unit",_);const X=await oe.post("/api/user_manage.php",x,{headers:{"Content-Type":"multipart/form-data"}});l.value=X.data,console.log("获取排序选项成功:",l.value)}catch(x){console.error("获取排序选项失败:",x),G.error("获取排序选项失败，请稍后重试"),l.value=[]}},W=async _=>{if(!_){i.value=[];return}try{const x=new FormData;x.append("controlCode","getPspInfo"),x.append("organization_unit",_);const X=await oe({url:"/api/user_manage.php",method:"post",data:x});X.status===1?i.value=X.data.map(ie=>({label:ie.name,value:ie.id})):G.error("获取带辅民警数据失败")}catch{G.error("网络请求失败")}};return st(()=>p.organization_unit,async _=>{const x=Array.isArray(_)?_[_.length-1]:_;await Q(x),await W(x)}),(_,x)=>{const X=O("el-input"),ie=O("el-button"),he=O("el-table-column"),wr=O("el-table"),Di=O("el-tag"),xr=O("el-switch"),gt=O("el-icon"),Ii=O("el-button-group"),Ui=O("el-pagination"),ue=O("el-form-item"),vt=O("el-date-picker"),xe=O("el-option"),We=O("el-select"),br=O("el-cascader"),Ti=O("el-form"),Oi=O("el-dialog");return Z(),ce("div",_a,[L("div",ga,[h(X,{modelValue:m.value,"onUpdate:modelValue":x[0]||(x[0]=$=>m.value=$),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),h(wr,{data:A.value,border:"",class:"department-table"},{default:v(()=>[h(he,{label:"操作",width:"55"},{default:v(({row:$})=>[$.children&&$.children.length>0?(Z(),Se(ie,{key:0,type:"text",size:"small",onClick:Ro(ot=>j($),["stop"])},{default:v(()=>[te(be($.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):$t("",!0)]),_:1}),h(he,{prop:"unit_name",label:"单位名称"},{default:v(({row:$})=>[L("span",{class:"indent",style:ur({width:`${$.indent}px`})},null,4),L("span",{onClick:ot=>k($),style:{cursor:"pointer"}},be($.unit_name),9,va)]),_:1})]),_:1},8,["data"])]),L("div",ya,[L("div",wa,[L("div",xa,[h(ie,{type:"primary",onClick:ee},{default:v(()=>x[31]||(x[31]=[te("新增")])),_:1,__:[31]}),h(ie,{type:"danger",onClick:ve},{default:v(()=>x[32]||(x[32]=[te("删除")])),_:1,__:[32]})]),L("div",ba,[s.value?(Z(),Se(Di,{key:0,type:"info",style:{"margin-left":"10px"}},{default:v(()=>[te(" 当前单位："+be(s.value),1)]),_:1})):$t("",!0),h(xr,{modelValue:se.value,"onUpdate:modelValue":x[1]||(x[1]=$=>se.value=$),"inline-prompt":"","active-text":"显示抽出人员","inactive-text":"不显示抽出人员",style:{"margin-left":"10px"}},null,8,["modelValue"]),h(xr,{modelValue:pe.value,"onUpdate:modelValue":x[2]||(x[2]=$=>pe.value=$),"active-text":"显示抽入人员","inactive-text":"显示抽入人员","inline-prompt":"",style:{"margin-left":"10px"}},null,8,["modelValue"])])]),h(wr,{data:w.value,border:"",class:"user-table",onSelectionChange:Ee},{default:v(()=>[h(he,{type:"selection",width:"55"}),h(he,{prop:"name",label:"姓名",width:"70"}),h(he,{prop:"id_number",label:"身份证号",width:"100"}),h(he,{prop:"phone",label:"手机号码",width:"115"}),h(he,{label:"性别",width:"55"},{default:v($=>[te(be($.row.gender===1?"男":$.row.gender===2?"女":"未知"),1)]),_:1}),h(he,{label:"编制单位"},{default:v($=>[te(be(B($.row.organization_unit)),1)]),_:1}),h(he,{label:"工作单位"},{default:v($=>[te(be(B($.row.work_unit)),1)]),_:1}),h(he,{prop:"personnel_type",label:"人员身份"}),h(he,{prop:"employment_status",label:"人员状态"}),h(he,{prop:"desc",label:"备注"}),h(he,{label:"操作",width:"260",align:"center"},{default:v(({row:$})=>[h(Ii,null,{default:v(()=>[h(ie,{type:"warning",size:"mini",onClick:ot=>ye($)},{default:v(()=>[h(gt,null,{default:v(()=>[h(le(wn))]),_:1})]),_:2},1032,["onClick"]),h(ie,{type:"success",size:"mini",onClick:ot=>Re($)},{default:v(()=>[h(gt,null,{default:v(()=>[h(le(Bi))]),_:1})]),_:2},1032,["onClick"]),h(ie,{type:"danger",size:"mini",onClick:ot=>K($)},{default:v(()=>[h(gt,null,{default:v(()=>[h(le(xn))]),_:1})]),_:2},1032,["onClick"]),h(ie,{size:"mini",type:"info",onClick:ot=>re($),disabled:$.sort_order<=1},{default:v(()=>[h(gt,null,{default:v(()=>[h(le(Po))]),_:1})]),_:2},1032,["onClick","disabled"]),h(ie,{size:"mini",type:"info",onClick:ot=>de($),disabled:$.sort_order>=$.sortMax},{default:v(()=>[h(gt,null,{default:v(()=>[h(le(Vo))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),h(Ui,{"current-page":f.value,"page-size":d.value,total:a.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:x[3]||(x[3]=$=>{f.value=$,z()}),onSizeChange:x[4]||(x[4]=$=>{d.value=$,f.value=1,z()})},null,8,["current-page","page-size","total"])]),h(Oi,{modelValue:b.value,"onUpdate:modelValue":x[30]||(x[30]=$=>b.value=$),title:R.value,width:"1000px"},{footer:v(()=>[L("span",Ma,[M.value?$t("",!0):(Z(),Se(ie,{key:0,onClick:x[29]||(x[29]=$=>b.value=!1)},{default:v(()=>x[34]||(x[34]=[te("取消")])),_:1,__:[34]})),h(ie,{type:"primary",onClick:N},{default:v(()=>x[35]||(x[35]=[te("确定")])),_:1,__:[35]})])]),default:v(()=>[h(Ti,{model:p,rules:g,ref_key:"newUserFormRef",ref:P,"label-width":"120px",inline:!1,class:"user-form",disabled:M.value},{default:v(()=>[L("div",Ea,[h(ue,{label:"姓名",prop:"name",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.name,"onUpdate:modelValue":x[5]||(x[5]=$=>p.name=$)},null,8,["modelValue"])]),_:1}),h(ue,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.id_number,"onUpdate:modelValue":x[6]||(x[6]=$=>p.id_number=$),maxlength:"18"},null,8,["modelValue"])]),_:1}),h(ue,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.phone,"onUpdate:modelValue":x[7]||(x[7]=$=>p.phone=$),maxlength:"11"},null,8,["modelValue"])]),_:1})]),L("div",Sa,[h(ue,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:v(()=>[h(vt,{modelValue:p.archive_birthdate,"onUpdate:modelValue":x[8]||(x[8]=$=>p.archive_birthdate=$),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),h(ue,{label:"性别",prop:"gender",style:{flex:"1"}},{default:v(()=>[h(We,{modelValue:p.gender,"onUpdate:modelValue":x[9]||(x[9]=$=>p.gender=$)},{default:v(()=>[h(xe,{label:"未知",value:0}),h(xe,{label:"男",value:1}),h(xe,{label:"女",value:2})]),_:1},8,["modelValue"])]),_:1}),h(ue,{label:"备注",prop:"desc",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.desc,"onUpdate:modelValue":x[10]||(x[10]=$=>p.desc=$)},null,8,["modelValue"])]),_:1})]),L("div",ka,[h(ue,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.short_code,"onUpdate:modelValue":x[11]||(x[11]=$=>p.short_code=$)},null,8,["modelValue"]),x[33]||(x[33]=L("div",{class:""},null,-1))]),_:1,__:[33]}),h(ue,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.alt_phone_1,"onUpdate:modelValue":x[12]||(x[12]=$=>p.alt_phone_1=$)},null,8,["modelValue"])]),_:1}),h(ue,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.alt_phone_2,"onUpdate:modelValue":x[13]||(x[13]=$=>p.alt_phone_2=$)},null,8,["modelValue"])]),_:1})]),L("div",Ca,[h(ue,{label:"座机",prop:"landline",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.landline,"onUpdate:modelValue":x[14]||(x[14]=$=>p.landline=$)},null,8,["modelValue"])]),_:1}),h(ue,{label:"编制单位",prop:"organization_unit",required:""},{default:v(()=>[h(br,{modelValue:p.organization_unit,"onUpdate:modelValue":x[15]||(x[15]=$=>p.organization_unit=$),options:y.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),h(ue,{label:"工作单位",prop:"work_unit"},{default:v(()=>[h(br,{modelValue:p.work_unit,"onUpdate:modelValue":x[16]||(x[16]=$=>p.work_unit=$),options:C.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),L("div",Ra,[h(ue,{label:"参工日期",prop:"employment_date",style:{flex:"1"}},{default:v(()=>[h(vt,{modelValue:p.employment_date,"onUpdate:modelValue":x[17]||(x[17]=$=>p.employment_date=$),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),h(ue,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:v(()=>[h(We,{modelValue:p.political_status,"onUpdate:modelValue":x[18]||(x[18]=$=>p.political_status=$),placeholder:"请选择政治面貌"},{default:v(()=>[h(xe,{label:"中共党员",value:"中共党员"}),h(xe,{label:"中共预备党员",value:"中共预备党员"}),h(xe,{label:"共青团员",value:"共青团员"}),h(xe,{label:"民主党派",value:"民主党派"}),h(xe,{label:"无党派人士",value:"无党派人士"}),h(xe,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),h(ue,{label:"加入组织日期",prop:"party_join_date",style:{flex:"1"}},{default:v(()=>[h(vt,{modelValue:p.party_join_date,"onUpdate:modelValue":x[19]||(x[19]=$=>p.party_join_date=$),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),L("div",$a,[h(ue,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:v(()=>[h(We,{modelValue:p.personnel_type,"onUpdate:modelValue":x[20]||(x[20]=$=>p.personnel_type=$),placeholder:"请选择人员身份"},{default:v(()=>[(Z(!0),ce(Pe,null,Ae(t.value,$=>(Z(),Se(xe,{key:$.value,label:$.label,value:$.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(ue,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.police_number,"onUpdate:modelValue":x[21]||(x[21]=$=>p.police_number=$)},null,8,["modelValue"])]),_:1}),h(ue,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:v(()=>[h(We,{modelValue:p.is_assisting_officer,"onUpdate:modelValue":x[22]||(x[22]=$=>p.is_assisting_officer=$),placeholder:"请选择带辅民警",clearable:""},{default:v(()=>[(Z(!0),ce(Pe,null,Ae(i.value,$=>(Z(),Se(xe,{key:$.value,label:$.label,value:$.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),L("div",Pa,[h(ue,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:v(()=>[h(We,{modelValue:p.employment_status,"onUpdate:modelValue":x[23]||(x[23]=$=>p.employment_status=$)},{default:v(()=>[(Z(!0),ce(Pe,null,Ae(n.value,$=>(Z(),Se(xe,{key:$.value,label:$.label,value:$.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(ue,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:v(()=>[h(We,{modelValue:p.job_rank,"onUpdate:modelValue":x[24]||(x[24]=$=>p.job_rank=$)},{default:v(()=>[(Z(!0),ce(Pe,null,Ae(r.value,$=>(Z(),Se(xe,{key:$.value,label:$.label,value:$.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),h(ue,{label:"任现职级日期",prop:"current_rank_date",style:{flex:"1"}},{default:v(()=>[h(vt,{modelValue:p.current_rank_date,"onUpdate:modelValue":x[25]||(x[25]=$=>p.current_rank_date=$),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),L("div",Va,[h(ue,{label:"职务",prop:"position",style:{flex:"1"}},{default:v(()=>[h(X,{modelValue:p.position,"onUpdate:modelValue":x[26]||(x[26]=$=>p.position=$)},null,8,["modelValue"])]),_:1}),h(ue,{label:"任现职务日期",prop:"current_position_date",style:{flex:"1"}},{default:v(()=>[h(vt,{modelValue:p.current_position_date,"onUpdate:modelValue":x[27]||(x[27]=$=>p.current_position_date=$),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),h(ue,{label:"人员排序",prop:"sort_order",style:{flex:"1"}},{default:v(()=>[h(We,{modelValue:p.sort_order,"onUpdate:modelValue":x[28]||(x[28]=$=>p.sort_order=$),placeholder:"请选择人员排序"},{default:v(()=>[h(xe,{label:"置于 最前",value:"0"}),(Z(!0),ce(Pe,null,Ae(l.value,$=>(Z(),Se(xe,{key:$.id,label:`置于 ${$.name} 之后`,value:$.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","disabled"])]),_:1},8,["modelValue","title"])])}}},Na=qt(Aa,[["__scopeId","data-v-8ba57e9f"]]);class je{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let o=0;o<this._n&&o<32;o++){const i=n[o],l=t+i,s=Math.abs(t)<Math.abs(i)?t-(l-i):i-(l-t);s&&(n[r++]=s),t=l}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,o,i,l=0;if(n>0){for(l=t[--n];n>0&&(r=l,o=t[--n],l=r+o,i=o-(l-r),!i););n>0&&(i<0&&t[n-1]<0||i>0&&t[n-1]>0)&&(o=i*2,r=l+o,o==r-l&&(l=r))}return l}}function*Da(e){for(const t of e)yield*t}function Ho(e){return Array.from(Da(e))}var Ia={value:()=>{}};function Bo(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new en(n)}function en(e){this._=e}function Ua(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}en.prototype=Bo.prototype={constructor:en,on:function(e,t){var n=this._,r=Ua(e+"",n),o,i=-1,l=r.length;if(arguments.length<2){for(;++i<l;)if((o=(e=r[i]).type)&&(o=Ta(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++i<l;)if(o=(e=r[i]).type)n[o]=qr(n[o],e.name,t);else if(t==null)for(o in n)n[o]=qr(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new en(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,i;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=this._[e],r=0,o=i.length;r<o;++r)i[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};function Ta(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function qr(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=Ia,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var Ln="http://www.w3.org/1999/xhtml";const Yr={svg:"http://www.w3.org/2000/svg",xhtml:Ln,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Sn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Yr.hasOwnProperty(t)?{space:Yr[t],local:e}:e}function Oa(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Ln&&t.documentElement.namespaceURI===Ln?t.createElement(e):t.createElementNS(n,e)}}function za(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Xo(e){var t=Sn(e);return(t.local?za:Oa)(t)}function Fa(){}function fr(e){return e==null?Fa:function(){return this.querySelector(e)}}function La(e){typeof e!="function"&&(e=fr(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],l=i.length,s=r[o]=new Array(l),f,d,a=0;a<l;++a)(f=i[a])&&(d=e.call(f,f.__data__,a,i))&&("__data__"in f&&(d.__data__=f.__data__),s[a]=d);return new Ve(r,this._parents)}function qa(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Ya(){return[]}function Go(e){return e==null?Ya:function(){return this.querySelectorAll(e)}}function Ha(e){return function(){return qa(e.apply(this,arguments))}}function Ba(e){typeof e=="function"?e=Ha(e):e=Go(e);for(var t=this._groups,n=t.length,r=[],o=[],i=0;i<n;++i)for(var l=t[i],s=l.length,f,d=0;d<s;++d)(f=l[d])&&(r.push(e.call(f,f.__data__,d,l)),o.push(f));return new Ve(r,o)}function Ko(e){return function(){return this.matches(e)}}function Wo(e){return function(t){return t.matches(e)}}var Xa=Array.prototype.find;function Ga(e){return function(){return Xa.call(this.children,e)}}function Ka(){return this.firstElementChild}function Wa(e){return this.select(e==null?Ka:Ga(typeof e=="function"?e:Wo(e)))}var Qa=Array.prototype.filter;function Za(){return Array.from(this.children)}function Ja(e){return function(){return Qa.call(this.children,e)}}function ja(e){return this.selectAll(e==null?Za:Ja(typeof e=="function"?e:Wo(e)))}function es(e){typeof e!="function"&&(e=Ko(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],l=i.length,s=r[o]=[],f,d=0;d<l;++d)(f=i[d])&&e.call(f,f.__data__,d,i)&&s.push(f);return new Ve(r,this._parents)}function Qo(e){return new Array(e.length)}function ts(){return new Ve(this._enter||this._groups.map(Qo),this._parents)}function ln(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}ln.prototype={constructor:ln,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function ns(e){return function(){return e}}function rs(e,t,n,r,o,i){for(var l=0,s,f=t.length,d=i.length;l<d;++l)(s=t[l])?(s.__data__=i[l],r[l]=s):n[l]=new ln(e,i[l]);for(;l<f;++l)(s=t[l])&&(o[l]=s)}function os(e,t,n,r,o,i,l){var s,f,d=new Map,a=t.length,u=i.length,c=new Array(a),m;for(s=0;s<a;++s)(f=t[s])&&(c[s]=m=l.call(f,f.__data__,s,t)+"",d.has(m)?o[s]=f:d.set(m,f));for(s=0;s<u;++s)m=l.call(e,i[s],s,i)+"",(f=d.get(m))?(r[s]=f,f.__data__=i[s],d.delete(m)):n[s]=new ln(e,i[s]);for(s=0;s<a;++s)(f=t[s])&&d.get(c[s])===f&&(o[s]=f)}function is(e){return e.__data__}function ls(e,t){if(!arguments.length)return Array.from(this,is);var n=t?os:rs,r=this._parents,o=this._groups;typeof e!="function"&&(e=ns(e));for(var i=o.length,l=new Array(i),s=new Array(i),f=new Array(i),d=0;d<i;++d){var a=r[d],u=o[d],c=u.length,m=as(e.call(a,a&&a.__data__,d,r)),y=m.length,C=s[d]=new Array(y),w=l[d]=new Array(y),S=f[d]=new Array(c);n(a,u,C,w,S,m,t);for(var b=0,R=0,M,I;b<y;++b)if(M=C[b]){for(b>=R&&(R=b+1);!(I=w[R])&&++R<y;);M._next=I||null}}return l=new Ve(l,r),l._enter=s,l._exit=f,l}function as(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function ss(){return new Ve(this._exit||this._groups.map(Qo),this._parents)}function us(e,t,n){var r=this.enter(),o=this,i=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?i.remove():n(i),r&&o?r.merge(o).order():o}function cs(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,l=Math.min(o,i),s=new Array(o),f=0;f<l;++f)for(var d=n[f],a=r[f],u=d.length,c=s[f]=new Array(u),m,y=0;y<u;++y)(m=d[y]||a[y])&&(c[y]=m);for(;f<o;++f)s[f]=n[f];return new Ve(s,this._parents)}function fs(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,i=r[o],l;--o>=0;)(l=r[o])&&(i&&l.compareDocumentPosition(i)^4&&i.parentNode.insertBefore(l,i),i=l);return this}function ds(e){e||(e=ps);function t(u,c){return u&&c?e(u.__data__,c.__data__):!u-!c}for(var n=this._groups,r=n.length,o=new Array(r),i=0;i<r;++i){for(var l=n[i],s=l.length,f=o[i]=new Array(s),d,a=0;a<s;++a)(d=l[a])&&(f[a]=d);f.sort(t)}return new Ve(o,this._parents).order()}function ps(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function hs(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function ms(){return Array.from(this)}function _s(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var l=r[o];if(l)return l}return null}function gs(){let e=0;for(const t of this)++e;return e}function vs(){return!this.node()}function ys(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],i=0,l=o.length,s;i<l;++i)(s=o[i])&&e.call(s,s.__data__,i,o);return this}function ws(e){return function(){this.removeAttribute(e)}}function xs(e){return function(){this.removeAttributeNS(e.space,e.local)}}function bs(e,t){return function(){this.setAttribute(e,t)}}function Es(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function Ss(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function ks(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function Cs(e,t){var n=Sn(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?xs:ws:typeof t=="function"?n.local?ks:Ss:n.local?Es:bs)(n,t))}function Zo(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function Rs(e){return function(){this.style.removeProperty(e)}}function $s(e,t,n){return function(){this.style.setProperty(e,t,n)}}function Ps(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function Vs(e,t,n){return arguments.length>1?this.each((t==null?Rs:typeof t=="function"?Ps:$s)(e,t,n??"")):pt(this.node(),e)}function pt(e,t){return e.style.getPropertyValue(t)||Zo(e).getComputedStyle(e,null).getPropertyValue(t)}function Ms(e){return function(){delete this[e]}}function As(e,t){return function(){this[e]=t}}function Ns(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function Ds(e,t){return arguments.length>1?this.each((t==null?Ms:typeof t=="function"?Ns:As)(e,t)):this.node()[e]}function Jo(e){return e.trim().split(/^|\s+/)}function dr(e){return e.classList||new jo(e)}function jo(e){this._node=e,this._names=Jo(e.getAttribute("class")||"")}jo.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function ei(e,t){for(var n=dr(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function ti(e,t){for(var n=dr(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function Is(e){return function(){ei(this,e)}}function Us(e){return function(){ti(this,e)}}function Ts(e,t){return function(){(t.apply(this,arguments)?ei:ti)(this,e)}}function Os(e,t){var n=Jo(e+"");if(arguments.length<2){for(var r=dr(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?Ts:t?Is:Us)(n,t))}function zs(){this.textContent=""}function Fs(e){return function(){this.textContent=e}}function Ls(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function qs(e){return arguments.length?this.each(e==null?zs:(typeof e=="function"?Ls:Fs)(e)):this.node().textContent}function Ys(){this.innerHTML=""}function Hs(e){return function(){this.innerHTML=e}}function Bs(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Xs(e){return arguments.length?this.each(e==null?Ys:(typeof e=="function"?Bs:Hs)(e)):this.node().innerHTML}function Gs(){this.nextSibling&&this.parentNode.appendChild(this)}function Ks(){return this.each(Gs)}function Ws(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Qs(){return this.each(Ws)}function Zs(e){var t=typeof e=="function"?e:Xo(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Js(){return null}function js(e,t){var n=typeof e=="function"?e:Xo(e),r=t==null?Js:typeof t=="function"?t:fr(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function eu(){var e=this.parentNode;e&&e.removeChild(this)}function tu(){return this.each(eu)}function nu(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ru(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ou(e){return this.select(e?ru:nu)}function iu(e){return arguments.length?this.property("__data__",e):this.node().__data__}function lu(e){return function(t){e.call(this,t,this.__data__)}}function au(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function su(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,i;n<o;++n)i=t[n],(!e.type||i.type===e.type)&&i.name===e.name?this.removeEventListener(i.type,i.listener,i.options):t[++r]=i;++r?t.length=r:delete this.__on}}}function uu(e,t,n){return function(){var r=this.__on,o,i=lu(t);if(r){for(var l=0,s=r.length;l<s;++l)if((o=r[l]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),o.value=t;return}}this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function cu(e,t,n){var r=au(e+""),o,i=r.length,l;if(arguments.length<2){var s=this.node().__on;if(s){for(var f=0,d=s.length,a;f<d;++f)for(o=0,a=s[f];o<i;++o)if((l=r[o]).type===a.type&&l.name===a.name)return a.value}return}for(s=t?uu:su,o=0;o<i;++o)this.each(s(r[o],t,n));return this}function ni(e,t,n){var r=Zo(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function fu(e,t){return function(){return ni(this,e,t)}}function du(e,t){return function(){return ni(this,e,t.apply(this,arguments))}}function pu(e,t){return this.each((typeof t=="function"?du:fu)(e,t))}function*hu(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length,l;o<i;++o)(l=r[o])&&(yield l)}var ri=[null];function Ve(e,t){this._groups=e,this._parents=t}function Yt(){return new Ve([[document.documentElement]],ri)}function mu(){return this}Ve.prototype=Yt.prototype={constructor:Ve,select:La,selectAll:Ba,selectChild:Wa,selectChildren:ja,filter:es,data:ls,enter:ts,exit:ss,join:us,merge:cs,selection:mu,order:fs,sort:ds,call:hs,nodes:ms,node:_s,size:gs,empty:vs,each:ys,attr:Cs,style:Vs,property:Ds,classed:Os,text:qs,html:Xs,raise:Ks,lower:Qs,append:Zs,insert:js,remove:tu,clone:ou,datum:iu,on:cu,dispatch:pu,[Symbol.iterator]:hu};function Bt(e){return typeof e=="string"?new Ve([[document.querySelector(e)]],[document.documentElement]):new Ve([[e]],ri)}function pr(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function oi(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Ht(){}var Dt=.7,an=1/Dt,ut="\\s*([+-]?\\d+)\\s*",It="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Fe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",_u=/^#([0-9a-f]{3,8})$/,gu=new RegExp(`^rgb\\(${ut},${ut},${ut}\\)$`),vu=new RegExp(`^rgb\\(${Fe},${Fe},${Fe}\\)$`),yu=new RegExp(`^rgba\\(${ut},${ut},${ut},${It}\\)$`),wu=new RegExp(`^rgba\\(${Fe},${Fe},${Fe},${It}\\)$`),xu=new RegExp(`^hsl\\(${It},${Fe},${Fe}\\)$`),bu=new RegExp(`^hsla\\(${It},${Fe},${Fe},${It}\\)$`),Hr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};pr(Ht,Ut,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Br,formatHex:Br,formatHex8:Eu,formatHsl:Su,formatRgb:Xr,toString:Xr});function Br(){return this.rgb().formatHex()}function Eu(){return this.rgb().formatHex8()}function Su(){return ii(this).formatHsl()}function Xr(){return this.rgb().formatRgb()}function Ut(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=_u.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Gr(t):n===3?new ke(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Xt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Xt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=gu.exec(e))?new ke(t[1],t[2],t[3],1):(t=vu.exec(e))?new ke(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=yu.exec(e))?Xt(t[1],t[2],t[3],t[4]):(t=wu.exec(e))?Xt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=xu.exec(e))?Qr(t[1],t[2]/100,t[3]/100,1):(t=bu.exec(e))?Qr(t[1],t[2]/100,t[3]/100,t[4]):Hr.hasOwnProperty(e)?Gr(Hr[e]):e==="transparent"?new ke(NaN,NaN,NaN,0):null}function Gr(e){return new ke(e>>16&255,e>>8&255,e&255,1)}function Xt(e,t,n,r){return r<=0&&(e=t=n=NaN),new ke(e,t,n,r)}function ku(e){return e instanceof Ht||(e=Ut(e)),e?(e=e.rgb(),new ke(e.r,e.g,e.b,e.opacity)):new ke}function qn(e,t,n,r){return arguments.length===1?ku(e):new ke(e,t,n,r??1)}function ke(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}pr(ke,qn,oi(Ht,{brighter(e){return e=e==null?an:Math.pow(an,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Dt:Math.pow(Dt,e),new ke(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ke(Ze(this.r),Ze(this.g),Ze(this.b),sn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Kr,formatHex:Kr,formatHex8:Cu,formatRgb:Wr,toString:Wr}));function Kr(){return`#${Qe(this.r)}${Qe(this.g)}${Qe(this.b)}`}function Cu(){return`#${Qe(this.r)}${Qe(this.g)}${Qe(this.b)}${Qe((isNaN(this.opacity)?1:this.opacity)*255)}`}function Wr(){const e=sn(this.opacity);return`${e===1?"rgb(":"rgba("}${Ze(this.r)}, ${Ze(this.g)}, ${Ze(this.b)}${e===1?")":`, ${e})`}`}function sn(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Ze(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Qe(e){return e=Ze(e),(e<16?"0":"")+e.toString(16)}function Qr(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Ie(e,t,n,r)}function ii(e){if(e instanceof Ie)return new Ie(e.h,e.s,e.l,e.opacity);if(e instanceof Ht||(e=Ut(e)),!e)return new Ie;if(e instanceof Ie)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),l=NaN,s=i-o,f=(i+o)/2;return s?(t===i?l=(n-r)/s+(n<r)*6:n===i?l=(r-t)/s+2:l=(t-n)/s+4,s/=f<.5?i+o:2-i-o,l*=60):s=f>0&&f<1?0:l,new Ie(l,s,f,e.opacity)}function Ru(e,t,n,r){return arguments.length===1?ii(e):new Ie(e,t,n,r??1)}function Ie(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}pr(Ie,Ru,oi(Ht,{brighter(e){return e=e==null?an:Math.pow(an,e),new Ie(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Dt:Math.pow(Dt,e),new Ie(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new ke(An(e>=240?e-240:e+120,o,r),An(e,o,r),An(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new Ie(Zr(this.h),Gt(this.s),Gt(this.l),sn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=sn(this.opacity);return`${e===1?"hsl(":"hsla("}${Zr(this.h)}, ${Gt(this.s)*100}%, ${Gt(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Zr(e){return e=(e||0)%360,e<0?e+360:e}function Gt(e){return Math.max(0,Math.min(1,e||0))}function An(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const li=e=>()=>e;function $u(e,t){return function(n){return e+n*t}}function Pu(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function Vu(e){return(e=+e)==1?ai:function(t,n){return n-t?Pu(t,n,e):li(isNaN(t)?n:t)}}function ai(e,t){var n=t-e;return n?$u(e,n):li(isNaN(e)?t:e)}const Jr=function e(t){var n=Vu(t);function r(o,i){var l=n((o=qn(o)).r,(i=qn(i)).r),s=n(o.g,i.g),f=n(o.b,i.b),d=ai(o.opacity,i.opacity);return function(a){return o.r=l(a),o.g=s(a),o.b=f(a),o.opacity=d(a),o+""}}return r.gamma=e,r}(1);function Ke(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Yn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Nn=new RegExp(Yn.source,"g");function Mu(e){return function(){return e}}function Au(e){return function(t){return e(t)+""}}function Nu(e,t){var n=Yn.lastIndex=Nn.lastIndex=0,r,o,i,l=-1,s=[],f=[];for(e=e+"",t=t+"";(r=Yn.exec(e))&&(o=Nn.exec(t));)(i=o.index)>n&&(i=t.slice(n,i),s[l]?s[l]+=i:s[++l]=i),(r=r[0])===(o=o[0])?s[l]?s[l]+=o:s[++l]=o:(s[++l]=null,f.push({i:l,x:Ke(r,o)})),n=Nn.lastIndex;return n<t.length&&(i=t.slice(n),s[l]?s[l]+=i:s[++l]=i),s.length<2?f[0]?Au(f[0].x):Mu(t):(t=f.length,function(d){for(var a=0,u;a<t;++a)s[(u=f[a]).i]=u.x(d);return s.join("")})}var jr=180/Math.PI,Hn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function si(e,t,n,r,o,i){var l,s,f;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(f=e*n+t*r)&&(n-=e*f,r-=t*f),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,f/=s),e*r<t*n&&(e=-e,t=-t,f=-f,l=-l),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*jr,skewX:Math.atan(f)*jr,scaleX:l,scaleY:s}}var Kt;function Du(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Hn:si(t.a,t.b,t.c,t.d,t.e,t.f)}function Iu(e){return e==null||(Kt||(Kt=document.createElementNS("http://www.w3.org/2000/svg","g")),Kt.setAttribute("transform",e),!(e=Kt.transform.baseVal.consolidate()))?Hn:(e=e.matrix,si(e.a,e.b,e.c,e.d,e.e,e.f))}function ui(e,t,n,r){function o(d){return d.length?d.pop()+" ":""}function i(d,a,u,c,m,y){if(d!==u||a!==c){var C=m.push("translate(",null,t,null,n);y.push({i:C-4,x:Ke(d,u)},{i:C-2,x:Ke(a,c)})}else(u||c)&&m.push("translate("+u+t+c+n)}function l(d,a,u,c){d!==a?(d-a>180?a+=360:a-d>180&&(d+=360),c.push({i:u.push(o(u)+"rotate(",null,r)-2,x:Ke(d,a)})):a&&u.push(o(u)+"rotate("+a+r)}function s(d,a,u,c){d!==a?c.push({i:u.push(o(u)+"skewX(",null,r)-2,x:Ke(d,a)}):a&&u.push(o(u)+"skewX("+a+r)}function f(d,a,u,c,m,y){if(d!==u||a!==c){var C=m.push(o(m)+"scale(",null,",",null,")");y.push({i:C-4,x:Ke(d,u)},{i:C-2,x:Ke(a,c)})}else(u!==1||c!==1)&&m.push(o(m)+"scale("+u+","+c+")")}return function(d,a){var u=[],c=[];return d=e(d),a=e(a),i(d.translateX,d.translateY,a.translateX,a.translateY,u,c),l(d.rotate,a.rotate,u,c),s(d.skewX,a.skewX,u,c),f(d.scaleX,d.scaleY,a.scaleX,a.scaleY,u,c),d=a=null,function(m){for(var y=-1,C=c.length,w;++y<C;)u[(w=c[y]).i]=w.x(m);return u.join("")}}}var Uu=ui(Du,"px, ","px)","deg)"),Tu=ui(Iu,", ",")",")"),ht=0,xt=0,wt=0,ci=1e3,un,bt,cn=0,et=0,kn=0,Tt=typeof performance=="object"&&performance.now?performance:Date,fi=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function hr(){return et||(fi(Ou),et=Tt.now()+kn)}function Ou(){et=0}function fn(){this._call=this._time=this._next=null}fn.prototype=di.prototype={constructor:fn,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?hr():+n)+(t==null?0:+t),!this._next&&bt!==this&&(bt?bt._next=this:un=this,bt=this),this._call=e,this._time=n,Bn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Bn())}};function di(e,t,n){var r=new fn;return r.restart(e,t,n),r}function zu(){hr(),++ht;for(var e=un,t;e;)(t=et-e._time)>=0&&e._call.call(void 0,t),e=e._next;--ht}function eo(){et=(cn=Tt.now())+kn,ht=xt=0;try{zu()}finally{ht=0,Lu(),et=0}}function Fu(){var e=Tt.now(),t=e-cn;t>ci&&(kn-=t,cn=e)}function Lu(){for(var e,t=un,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:un=n);bt=e,Bn(r)}function Bn(e){if(!ht){xt&&(xt=clearTimeout(xt));var t=e-et;t>24?(e<1/0&&(xt=setTimeout(eo,e-Tt.now()-kn)),wt&&(wt=clearInterval(wt))):(wt||(cn=Tt.now(),wt=setInterval(Fu,ci)),ht=1,fi(eo))}}function to(e,t,n){var r=new fn;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var qu=Bo("start","end","cancel","interrupt"),Yu=[],pi=0,no=1,Xn=2,tn=3,ro=4,Gn=5,nn=6;function Cn(e,t,n,r,o,i){var l=e.__transition;if(!l)e.__transition={};else if(n in l)return;Hu(e,n,{name:t,index:r,group:o,on:qu,tween:Yu,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:pi})}function mr(e,t){var n=Te(e,t);if(n.state>pi)throw new Error("too late; already scheduled");return n}function Le(e,t){var n=Te(e,t);if(n.state>tn)throw new Error("too late; already running");return n}function Te(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Hu(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=di(i,0,n.time);function i(d){n.state=no,n.timer.restart(l,n.delay,n.time),n.delay<=d&&l(d-n.delay)}function l(d){var a,u,c,m;if(n.state!==no)return f();for(a in r)if(m=r[a],m.name===n.name){if(m.state===tn)return to(l);m.state===ro?(m.state=nn,m.timer.stop(),m.on.call("interrupt",e,e.__data__,m.index,m.group),delete r[a]):+a<t&&(m.state=nn,m.timer.stop(),m.on.call("cancel",e,e.__data__,m.index,m.group),delete r[a])}if(to(function(){n.state===tn&&(n.state=ro,n.timer.restart(s,n.delay,n.time),s(d))}),n.state=Xn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Xn){for(n.state=tn,o=new Array(c=n.tween.length),a=0,u=-1;a<c;++a)(m=n.tween[a].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=m);o.length=u+1}}function s(d){for(var a=d<n.duration?n.ease.call(null,d/n.duration):(n.timer.restart(f),n.state=Gn,1),u=-1,c=o.length;++u<c;)o[u].call(e,a);n.state===Gn&&(n.on.call("end",e,e.__data__,n.index,n.group),f())}function f(){n.state=nn,n.timer.stop(),delete r[t];for(var d in r)return;delete e.__transition}}function Bu(e,t){var n=e.__transition,r,o,i=!0,l;if(n){t=t==null?null:t+"";for(l in n){if((r=n[l]).name!==t){i=!1;continue}o=r.state>Xn&&r.state<Gn,r.state=nn,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[l]}i&&delete e.__transition}}function Xu(e){return this.each(function(){Bu(this,e)})}function Gu(e,t){var n,r;return function(){var o=Le(this,e),i=o.tween;if(i!==n){r=n=i;for(var l=0,s=r.length;l<s;++l)if(r[l].name===t){r=r.slice(),r.splice(l,1);break}}o.tween=r}}function Ku(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var i=Le(this,e),l=i.tween;if(l!==r){o=(r=l).slice();for(var s={name:t,value:n},f=0,d=o.length;f<d;++f)if(o[f].name===t){o[f]=s;break}f===d&&o.push(s)}i.tween=o}}function Wu(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Te(this.node(),n).tween,o=0,i=r.length,l;o<i;++o)if((l=r[o]).name===e)return l.value;return null}return this.each((t==null?Gu:Ku)(n,e,t))}function _r(e,t,n){var r=e._id;return e.each(function(){var o=Le(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return Te(o,r).value[t]}}function hi(e,t){var n;return(typeof t=="number"?Ke:t instanceof Ut?Jr:(n=Ut(t))?(t=n,Jr):Nu)(e,t)}function Qu(e){return function(){this.removeAttribute(e)}}function Zu(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Ju(e,t,n){var r,o=n+"",i;return function(){var l=this.getAttribute(e);return l===o?null:l===r?i:i=t(r=l,n)}}function ju(e,t,n){var r,o=n+"",i;return function(){var l=this.getAttributeNS(e.space,e.local);return l===o?null:l===r?i:i=t(r=l,n)}}function ec(e,t,n){var r,o,i;return function(){var l,s=n(this),f;return s==null?void this.removeAttribute(e):(l=this.getAttribute(e),f=s+"",l===f?null:l===r&&f===o?i:(o=f,i=t(r=l,s)))}}function tc(e,t,n){var r,o,i;return function(){var l,s=n(this),f;return s==null?void this.removeAttributeNS(e.space,e.local):(l=this.getAttributeNS(e.space,e.local),f=s+"",l===f?null:l===r&&f===o?i:(o=f,i=t(r=l,s)))}}function nc(e,t){var n=Sn(e),r=n==="transform"?Tu:hi;return this.attrTween(e,typeof t=="function"?(n.local?tc:ec)(n,r,_r(this,"attr."+e,t)):t==null?(n.local?Zu:Qu)(n):(n.local?ju:Ju)(n,r,t))}function rc(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function oc(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function ic(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&oc(e,i)),n}return o._value=t,o}function lc(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&rc(e,i)),n}return o._value=t,o}function ac(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=Sn(e);return this.tween(n,(r.local?ic:lc)(r,t))}function sc(e,t){return function(){mr(this,e).delay=+t.apply(this,arguments)}}function uc(e,t){return t=+t,function(){mr(this,e).delay=t}}function cc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?sc:uc)(t,e)):Te(this.node(),t).delay}function fc(e,t){return function(){Le(this,e).duration=+t.apply(this,arguments)}}function dc(e,t){return t=+t,function(){Le(this,e).duration=t}}function pc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?fc:dc)(t,e)):Te(this.node(),t).duration}function hc(e,t){if(typeof t!="function")throw new Error;return function(){Le(this,e).ease=t}}function mc(e){var t=this._id;return arguments.length?this.each(hc(t,e)):Te(this.node(),t).ease}function _c(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;Le(this,e).ease=n}}function gc(e){if(typeof e!="function")throw new Error;return this.each(_c(this._id,e))}function vc(e){typeof e!="function"&&(e=Ko(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],l=i.length,s=r[o]=[],f,d=0;d<l;++d)(f=i[d])&&e.call(f,f.__data__,d,i)&&s.push(f);return new Be(r,this._parents,this._name,this._id)}function yc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),l=new Array(r),s=0;s<i;++s)for(var f=t[s],d=n[s],a=f.length,u=l[s]=new Array(a),c,m=0;m<a;++m)(c=f[m]||d[m])&&(u[m]=c);for(;s<r;++s)l[s]=t[s];return new Be(l,this._parents,this._name,this._id)}function wc(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function xc(e,t,n){var r,o,i=wc(t)?mr:Le;return function(){var l=i(this,e),s=l.on;s!==r&&(o=(r=s).copy()).on(t,n),l.on=o}}function bc(e,t){var n=this._id;return arguments.length<2?Te(this.node(),n).on.on(e):this.each(xc(n,e,t))}function Ec(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function Sc(){return this.on("end.remove",Ec(this._id))}function kc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=fr(e));for(var r=this._groups,o=r.length,i=new Array(o),l=0;l<o;++l)for(var s=r[l],f=s.length,d=i[l]=new Array(f),a,u,c=0;c<f;++c)(a=s[c])&&(u=e.call(a,a.__data__,c,s))&&("__data__"in a&&(u.__data__=a.__data__),d[c]=u,Cn(d[c],t,n,c,d,Te(a,n)));return new Be(i,this._parents,t,n)}function Cc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Go(e));for(var r=this._groups,o=r.length,i=[],l=[],s=0;s<o;++s)for(var f=r[s],d=f.length,a,u=0;u<d;++u)if(a=f[u]){for(var c=e.call(a,a.__data__,u,f),m,y=Te(a,n),C=0,w=c.length;C<w;++C)(m=c[C])&&Cn(m,t,n,C,c,y);i.push(c),l.push(a)}return new Be(i,l,t,n)}var Rc=Yt.prototype.constructor;function $c(){return new Rc(this._groups,this._parents)}function Pc(e,t){var n,r,o;return function(){var i=pt(this,e),l=(this.style.removeProperty(e),pt(this,e));return i===l?null:i===n&&l===r?o:o=t(n=i,r=l)}}function mi(e){return function(){this.style.removeProperty(e)}}function Vc(e,t,n){var r,o=n+"",i;return function(){var l=pt(this,e);return l===o?null:l===r?i:i=t(r=l,n)}}function Mc(e,t,n){var r,o,i;return function(){var l=pt(this,e),s=n(this),f=s+"";return s==null&&(f=s=(this.style.removeProperty(e),pt(this,e))),l===f?null:l===r&&f===o?i:(o=f,i=t(r=l,s))}}function Ac(e,t){var n,r,o,i="style."+t,l="end."+i,s;return function(){var f=Le(this,e),d=f.on,a=f.value[i]==null?s||(s=mi(t)):void 0;(d!==n||o!==a)&&(r=(n=d).copy()).on(l,o=a),f.on=r}}function Nc(e,t,n){var r=(e+="")=="transform"?Uu:hi;return t==null?this.styleTween(e,Pc(e,r)).on("end.style."+e,mi(e)):typeof t=="function"?this.styleTween(e,Mc(e,r,_r(this,"style."+e,t))).each(Ac(this._id,e)):this.styleTween(e,Vc(e,r,t),n).on("end.style."+e,null)}function Dc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function Ic(e,t,n){var r,o;function i(){var l=t.apply(this,arguments);return l!==o&&(r=(o=l)&&Dc(e,l,n)),r}return i._value=t,i}function Uc(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,Ic(e,t,n??""))}function Tc(e){return function(){this.textContent=e}}function Oc(e){return function(){var t=e(this);this.textContent=t??""}}function zc(e){return this.tween("text",typeof e=="function"?Oc(_r(this,"text",e)):Tc(e==null?"":e+""))}function Fc(e){return function(t){this.textContent=e.call(this,t)}}function Lc(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&Fc(o)),t}return r._value=e,r}function qc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Lc(e))}function Yc(){for(var e=this._name,t=this._id,n=_i(),r=this._groups,o=r.length,i=0;i<o;++i)for(var l=r[i],s=l.length,f,d=0;d<s;++d)if(f=l[d]){var a=Te(f,t);Cn(f,e,n,d,l,{time:a.time+a.delay+a.duration,delay:0,duration:a.duration,ease:a.ease})}return new Be(r,this._parents,e,n)}function Hc(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,l){var s={value:l},f={value:function(){--o===0&&i()}};n.each(function(){var d=Le(this,r),a=d.on;a!==e&&(t=(e=a).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(f)),d.on=t}),o===0&&i()})}var Bc=0;function Be(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function _i(){return++Bc}var Ye=Yt.prototype;Be.prototype={constructor:Be,select:kc,selectAll:Cc,selectChild:Ye.selectChild,selectChildren:Ye.selectChildren,filter:vc,merge:yc,selection:$c,transition:Yc,call:Ye.call,nodes:Ye.nodes,node:Ye.node,size:Ye.size,empty:Ye.empty,each:Ye.each,on:bc,attr:nc,attrTween:ac,style:Nc,styleTween:Uc,text:zc,textTween:qc,remove:Sc,tween:Wu,delay:cc,duration:pc,ease:mc,easeVarying:gc,end:Hc,[Symbol.iterator]:Ye[Symbol.iterator]};function Xc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Gc={time:null,delay:0,duration:250,ease:Xc};function Kc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Wc(e){var t,n;e instanceof Be?(t=e._id,e=e._name):(t=_i(),(n=Gc).time=hr(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var l=r[i],s=l.length,f,d=0;d<s;++d)(f=l[d])&&Cn(f,e,t,d,l,n||Kc(f,t));return new Be(r,this._parents,e,t)}Yt.prototype.interrupt=Xu;Yt.prototype.transition=Wc;function Qc(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function Zc(e,t){return fetch(e,t).then(Qc)}var ae=1e-6,J=Math.PI,Ce=J/2,oo=J/4,Me=J*2,$e=180/J,ge=J/180,fe=Math.abs,gi=Math.atan,Ot=Math.atan2,me=Math.cos,Jc=Math.exp,jc=Math.log,_e=Math.sin,ef=Math.sign||function(e){return e>0?1:e<0?-1:0},rt=Math.sqrt,tf=Math.tan;function nf(e){return e>1?0:e<-1?J:Math.acos(e)}function zt(e){return e>1?Ce:e<-1?-Ce:Math.asin(e)}function De(){}function dn(e,t){e&&lo.hasOwnProperty(e.type)&&lo[e.type](e,t)}var io={Feature:function(e,t){dn(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,o=n.length;++r<o;)dn(n[r].geometry,t)}},lo={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){Kn(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)Kn(n[r],t,0)},Polygon:function(e,t){ao(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)ao(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,o=n.length;++r<o;)dn(n[r],t)}};function Kn(e,t,n){var r=-1,o=e.length-n,i;for(t.lineStart();++r<o;)i=e[r],t.point(i[0],i[1],i[2]);t.lineEnd()}function ao(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)Kn(e[n],t,1);t.polygonEnd()}function lt(e,t){e&&io.hasOwnProperty(e.type)?io[e.type](e,t):dn(e,t)}function Wn(e){return[Ot(e[1],e[0]),zt(e[2])]}function mt(e){var t=e[0],n=e[1],r=me(n);return[r*me(t),r*_e(t),_e(n)]}function Wt(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function pn(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function Dn(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function Qt(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function Qn(e){var t=rt(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function Zn(e,t){function n(r,o){return r=e(r,o),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,o){return r=t.invert(r,o),r&&e.invert(r[0],r[1])}),n}function Jn(e,t){return fe(e)>J&&(e-=Math.round(e/Me)*Me),[e,t]}Jn.invert=Jn;function vi(e,t,n){return(e%=Me)?t||n?Zn(uo(e),co(t,n)):uo(e):t||n?co(t,n):Jn}function so(e){return function(t,n){return t+=e,fe(t)>J&&(t-=Math.round(t/Me)*Me),[t,n]}}function uo(e){var t=so(e);return t.invert=so(-e),t}function co(e,t){var n=me(e),r=_e(e),o=me(t),i=_e(t);function l(s,f){var d=me(f),a=me(s)*d,u=_e(s)*d,c=_e(f),m=c*n+a*r;return[Ot(u*o-m*i,a*n-c*r),zt(m*o+u*i)]}return l.invert=function(s,f){var d=me(f),a=me(s)*d,u=_e(s)*d,c=_e(f),m=c*o-u*i;return[Ot(u*o+c*i,a*n+m*r),zt(m*n-a*r)]},l}function rf(e){e=vi(e[0]*ge,e[1]*ge,e.length>2?e[2]*ge:0);function t(n){return n=e(n[0]*ge,n[1]*ge),n[0]*=$e,n[1]*=$e,n}return t.invert=function(n){return n=e.invert(n[0]*ge,n[1]*ge),n[0]*=$e,n[1]*=$e,n},t}function of(e,t,n,r,o,i){if(n){var l=me(t),s=_e(t),f=r*n;o==null?(o=t+r*Me,i=t-f/2):(o=fo(l,o),i=fo(l,i),(r>0?o<i:o>i)&&(o+=r*Me));for(var d,a=o;r>0?a>i:a<i;a-=f)d=Wn([l,-s*me(a),-s*_e(a)]),e.point(d[0],d[1])}}function fo(e,t){t=mt(t),t[0]-=e,Qn(t);var n=nf(-t[1]);return((-t[2]<0?-n:n)+Me-ae)%Me}function yi(){var e=[],t;return{point:function(n,r,o){t.push([n,r,o])},lineStart:function(){e.push(t=[])},lineEnd:De,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function rn(e,t){return fe(e[0]-t[0])<ae&&fe(e[1]-t[1])<ae}function Zt(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function wi(e,t,n,r,o){var i=[],l=[],s,f;if(e.forEach(function(y){if(!((C=y.length-1)<=0)){var C,w=y[0],S=y[C],b;if(rn(w,S)){if(!w[2]&&!S[2]){for(o.lineStart(),s=0;s<C;++s)o.point((w=y[s])[0],w[1]);o.lineEnd();return}S[0]+=2*ae}i.push(b=new Zt(w,y,null,!0)),l.push(b.o=new Zt(w,null,b,!1)),i.push(b=new Zt(S,y,null,!1)),l.push(b.o=new Zt(S,null,b,!0))}}),!!i.length){for(l.sort(t),po(i),po(l),s=0,f=l.length;s<f;++s)l[s].e=n=!n;for(var d=i[0],a,u;;){for(var c=d,m=!0;c.v;)if((c=c.n)===d)return;a=c.z,o.lineStart();do{if(c.v=c.o.v=!0,c.e){if(m)for(s=0,f=a.length;s<f;++s)o.point((u=a[s])[0],u[1]);else r(c.x,c.n.x,1,o);c=c.n}else{if(m)for(a=c.p.z,s=a.length-1;s>=0;--s)o.point((u=a[s])[0],u[1]);else r(c.x,c.p.x,-1,o);c=c.p}c=c.o,a=c.z,m=!m}while(!c.v);o.lineEnd()}}}function po(e){if(t=e.length){for(var t,n=0,r=e[0],o;++n<t;)r.n=o=e[n],o.p=r,r=o;r.n=o=e[0],o.p=r}}function In(e){return fe(e[0])<=J?e[0]:ef(e[0])*((fe(e[0])+J)%Me-J)}function lf(e,t){var n=In(t),r=t[1],o=_e(r),i=[_e(n),-me(n),0],l=0,s=0,f=new je;o===1?r=Ce+ae:o===-1&&(r=-Ce-ae);for(var d=0,a=e.length;d<a;++d)if(c=(u=e[d]).length)for(var u,c,m=u[c-1],y=In(m),C=m[1]/2+oo,w=_e(C),S=me(C),b=0;b<c;++b,y=M,w=D,S=V,m=R){var R=u[b],M=In(R),I=R[1]/2+oo,D=_e(I),V=me(I),N=M-y,A=N>=0?1:-1,p=A*N,g=p>J,P=w*D;if(f.add(Ot(P*A*_e(p),S*V+P*me(p))),l+=g?N+A*Me:N,g^y>=n^M>=n){var q=pn(mt(m),mt(R));Qn(q);var Y=pn(i,q);Qn(Y);var E=(g^N>=0?-1:1)*zt(Y[2]);(r>E||r===E&&(q[0]||q[1]))&&(s+=g^N>=0?1:-1)}}return(l<-1e-6||l<ae&&f<-1e-12)^s&1}function xi(e,t,n,r){return function(o){var i=t(o),l=yi(),s=t(l),f=!1,d,a,u,c={point:m,lineStart:C,lineEnd:w,polygonStart:function(){c.point=S,c.lineStart=b,c.lineEnd=R,a=[],d=[]},polygonEnd:function(){c.point=m,c.lineStart=C,c.lineEnd=w,a=Ho(a);var M=lf(d,r);a.length?(f||(o.polygonStart(),f=!0),wi(a,sf,M,n,o)):M&&(f||(o.polygonStart(),f=!0),o.lineStart(),n(null,null,1,o),o.lineEnd()),f&&(o.polygonEnd(),f=!1),a=d=null},sphere:function(){o.polygonStart(),o.lineStart(),n(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function m(M,I){e(M,I)&&o.point(M,I)}function y(M,I){i.point(M,I)}function C(){c.point=y,i.lineStart()}function w(){c.point=m,i.lineEnd()}function S(M,I){u.push([M,I]),s.point(M,I)}function b(){s.lineStart(),u=[]}function R(){S(u[0][0],u[0][1]),s.lineEnd();var M=s.clean(),I=l.result(),D,V=I.length,N,A,p;if(u.pop(),d.push(u),u=null,!!V){if(M&1){if(A=I[0],(N=A.length-1)>0){for(f||(o.polygonStart(),f=!0),o.lineStart(),D=0;D<N;++D)o.point((p=A[D])[0],p[1]);o.lineEnd()}return}V>1&&M&2&&I.push(I.pop().concat(I.shift())),a.push(I.filter(af))}}return c}}function af(e){return e.length>1}function sf(e,t){return((e=e.x)[0]<0?e[1]-Ce-ae:Ce-e[1])-((t=t.x)[0]<0?t[1]-Ce-ae:Ce-t[1])}const ho=xi(function(){return!0},uf,ff,[-J,-Ce]);function uf(e){var t=NaN,n=NaN,r=NaN,o;return{lineStart:function(){e.lineStart(),o=1},point:function(i,l){var s=i>0?J:-J,f=fe(i-t);fe(f-J)<ae?(e.point(t,n=(n+l)/2>0?Ce:-Ce),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),e.point(i,n),o=0):r!==s&&f>=J&&(fe(t-r)<ae&&(t-=r*ae),fe(i-s)<ae&&(i-=s*ae),n=cf(t,n,i,l),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),o=0),e.point(t=i,n=l),r=s},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-o}}}function cf(e,t,n,r){var o,i,l=_e(e-n);return fe(l)>ae?gi((_e(t)*(i=me(r))*_e(n)-_e(r)*(o=me(t))*_e(e))/(o*i*l)):(t+r)/2}function ff(e,t,n,r){var o;if(e==null)o=n*Ce,r.point(-J,o),r.point(0,o),r.point(J,o),r.point(J,0),r.point(J,-o),r.point(0,-o),r.point(-J,-o),r.point(-J,0),r.point(-J,o);else if(fe(e[0]-t[0])>ae){var i=e[0]<t[0]?J:-J;o=n*i/2,r.point(-i,o),r.point(0,o),r.point(i,o)}else r.point(t[0],t[1])}function df(e){var t=me(e),n=2*ge,r=t>0,o=fe(t)>ae;function i(a,u,c,m){of(m,e,n,c,a,u)}function l(a,u){return me(a)*me(u)>t}function s(a){var u,c,m,y,C;return{lineStart:function(){y=m=!1,C=1},point:function(w,S){var b=[w,S],R,M=l(w,S),I=r?M?0:d(w,S):M?d(w+(w<0?J:-J),S):0;if(!u&&(y=m=M)&&a.lineStart(),M!==m&&(R=f(u,b),(!R||rn(u,R)||rn(b,R))&&(b[2]=1)),M!==m)C=0,M?(a.lineStart(),R=f(b,u),a.point(R[0],R[1])):(R=f(u,b),a.point(R[0],R[1],2),a.lineEnd()),u=R;else if(o&&u&&r^M){var D;!(I&c)&&(D=f(b,u,!0))&&(C=0,r?(a.lineStart(),a.point(D[0][0],D[0][1]),a.point(D[1][0],D[1][1]),a.lineEnd()):(a.point(D[1][0],D[1][1]),a.lineEnd(),a.lineStart(),a.point(D[0][0],D[0][1],3)))}M&&(!u||!rn(u,b))&&a.point(b[0],b[1]),u=b,m=M,c=I},lineEnd:function(){m&&a.lineEnd(),u=null},clean:function(){return C|(y&&m)<<1}}}function f(a,u,c){var m=mt(a),y=mt(u),C=[1,0,0],w=pn(m,y),S=Wt(w,w),b=w[0],R=S-b*b;if(!R)return!c&&a;var M=t*S/R,I=-t*b/R,D=pn(C,w),V=Qt(C,M),N=Qt(w,I);Dn(V,N);var A=D,p=Wt(V,A),g=Wt(A,A),P=p*p-g*(Wt(V,V)-1);if(!(P<0)){var q=rt(P),Y=Qt(A,(-p-q)/g);if(Dn(Y,V),Y=Wn(Y),!c)return Y;var E=a[0],z=u[0],H=a[1],j=u[1],se;z<E&&(se=E,E=z,z=se);var pe=z-E,ee=fe(pe-J)<ae,ve=ee||pe<ae;if(!ee&&j<H&&(se=H,H=j,j=se),ve?ee?H+j>0^Y[1]<(fe(Y[0]-E)<ae?H:j):H<=Y[1]&&Y[1]<=j:pe>J^(E<=Y[0]&&Y[0]<=z)){var ye=Qt(A,(-p+q)/g);return Dn(ye,V),[Y,Wn(ye)]}}}function d(a,u){var c=r?e:J-e,m=0;return a<-c?m|=1:a>c&&(m|=2),u<-c?m|=4:u>c&&(m|=8),m}return xi(l,s,i,r?[0,-e]:[-J,e-J])}function pf(e,t,n,r,o,i){var l=e[0],s=e[1],f=t[0],d=t[1],a=0,u=1,c=f-l,m=d-s,y;if(y=n-l,!(!c&&y>0)){if(y/=c,c<0){if(y<a)return;y<u&&(u=y)}else if(c>0){if(y>u)return;y>a&&(a=y)}if(y=o-l,!(!c&&y<0)){if(y/=c,c<0){if(y>u)return;y>a&&(a=y)}else if(c>0){if(y<a)return;y<u&&(u=y)}if(y=r-s,!(!m&&y>0)){if(y/=m,m<0){if(y<a)return;y<u&&(u=y)}else if(m>0){if(y>u)return;y>a&&(a=y)}if(y=i-s,!(!m&&y<0)){if(y/=m,m<0){if(y>u)return;y>a&&(a=y)}else if(m>0){if(y<a)return;y<u&&(u=y)}return a>0&&(e[0]=l+a*c,e[1]=s+a*m),u<1&&(t[0]=l+u*c,t[1]=s+u*m),!0}}}}}var Jt=1e9,jt=-1e9;function hf(e,t,n,r){function o(d,a){return e<=d&&d<=n&&t<=a&&a<=r}function i(d,a,u,c){var m=0,y=0;if(d==null||(m=l(d,u))!==(y=l(a,u))||f(d,a)<0^u>0)do c.point(m===0||m===3?e:n,m>1?r:t);while((m=(m+u+4)%4)!==y);else c.point(a[0],a[1])}function l(d,a){return fe(d[0]-e)<ae?a>0?0:3:fe(d[0]-n)<ae?a>0?2:1:fe(d[1]-t)<ae?a>0?1:0:a>0?3:2}function s(d,a){return f(d.x,a.x)}function f(d,a){var u=l(d,1),c=l(a,1);return u!==c?u-c:u===0?a[1]-d[1]:u===1?d[0]-a[0]:u===2?d[1]-a[1]:a[0]-d[0]}return function(d){var a=d,u=yi(),c,m,y,C,w,S,b,R,M,I,D,V={point:N,lineStart:P,lineEnd:q,polygonStart:p,polygonEnd:g};function N(E,z){o(E,z)&&a.point(E,z)}function A(){for(var E=0,z=0,H=m.length;z<H;++z)for(var j=m[z],se=1,pe=j.length,ee=j[0],ve,ye,Re=ee[0],K=ee[1];se<pe;++se)ve=Re,ye=K,ee=j[se],Re=ee[0],K=ee[1],ye<=r?K>r&&(Re-ve)*(r-ye)>(K-ye)*(e-ve)&&++E:K<=r&&(Re-ve)*(r-ye)<(K-ye)*(e-ve)&&--E;return E}function p(){a=u,c=[],m=[],D=!0}function g(){var E=A(),z=D&&E,H=(c=Ho(c)).length;(z||H)&&(d.polygonStart(),z&&(d.lineStart(),i(null,null,1,d),d.lineEnd()),H&&wi(c,s,E,i,d),d.polygonEnd()),a=d,c=m=y=null}function P(){V.point=Y,m&&m.push(y=[]),I=!0,M=!1,b=R=NaN}function q(){c&&(Y(C,w),S&&M&&u.rejoin(),c.push(u.result())),V.point=N,M&&a.lineEnd()}function Y(E,z){var H=o(E,z);if(m&&y.push([E,z]),I)C=E,w=z,S=H,I=!1,H&&(a.lineStart(),a.point(E,z));else if(H&&M)a.point(E,z);else{var j=[b=Math.max(jt,Math.min(Jt,b)),R=Math.max(jt,Math.min(Jt,R))],se=[E=Math.max(jt,Math.min(Jt,E)),z=Math.max(jt,Math.min(Jt,z))];pf(j,se,e,t,n,r)?(M||(a.lineStart(),a.point(j[0],j[1])),a.point(se[0],se[1]),H||a.lineEnd(),D=!1):H&&(a.lineStart(),a.point(E,z),D=!1)}b=E,R=z,M=H}return V}}const jn=e=>e;var Un=new je,er=new je,bi,Ei,tr,nr,He={point:De,lineStart:De,lineEnd:De,polygonStart:function(){He.lineStart=mf,He.lineEnd=gf},polygonEnd:function(){He.lineStart=He.lineEnd=He.point=De,Un.add(fe(er)),er=new je},result:function(){var e=Un/2;return Un=new je,e}};function mf(){He.point=_f}function _f(e,t){He.point=Si,bi=tr=e,Ei=nr=t}function Si(e,t){er.add(nr*e-tr*t),tr=e,nr=t}function gf(){Si(bi,Ei)}var _t=1/0,hn=_t,Ft=-_t,mn=Ft,_n={point:vf,lineStart:De,lineEnd:De,polygonStart:De,polygonEnd:De,result:function(){var e=[[_t,hn],[Ft,mn]];return Ft=mn=-(hn=_t=1/0),e}};function vf(e,t){e<_t&&(_t=e),e>Ft&&(Ft=e),t<hn&&(hn=t),t>mn&&(mn=t)}var rr=0,or=0,Et=0,gn=0,vn=0,at=0,ir=0,lr=0,St=0,ki,Ci,Oe,ze,Ne={point:tt,lineStart:mo,lineEnd:_o,polygonStart:function(){Ne.lineStart=xf,Ne.lineEnd=bf},polygonEnd:function(){Ne.point=tt,Ne.lineStart=mo,Ne.lineEnd=_o},result:function(){var e=St?[ir/St,lr/St]:at?[gn/at,vn/at]:Et?[rr/Et,or/Et]:[NaN,NaN];return rr=or=Et=gn=vn=at=ir=lr=St=0,e}};function tt(e,t){rr+=e,or+=t,++Et}function mo(){Ne.point=yf}function yf(e,t){Ne.point=wf,tt(Oe=e,ze=t)}function wf(e,t){var n=e-Oe,r=t-ze,o=rt(n*n+r*r);gn+=o*(Oe+e)/2,vn+=o*(ze+t)/2,at+=o,tt(Oe=e,ze=t)}function _o(){Ne.point=tt}function xf(){Ne.point=Ef}function bf(){Ri(ki,Ci)}function Ef(e,t){Ne.point=Ri,tt(ki=Oe=e,Ci=ze=t)}function Ri(e,t){var n=e-Oe,r=t-ze,o=rt(n*n+r*r);gn+=o*(Oe+e)/2,vn+=o*(ze+t)/2,at+=o,o=ze*e-Oe*t,ir+=o*(Oe+e),lr+=o*(ze+t),St+=o*3,tt(Oe=e,ze=t)}function $i(e){this._context=e}$i.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,Me);break}}},result:De};var ar=new je,Tn,Pi,Vi,kt,Ct,Lt={point:De,lineStart:function(){Lt.point=Sf},lineEnd:function(){Tn&&Mi(Pi,Vi),Lt.point=De},polygonStart:function(){Tn=!0},polygonEnd:function(){Tn=null},result:function(){var e=+ar;return ar=new je,e}};function Sf(e,t){Lt.point=Mi,Pi=kt=e,Vi=Ct=t}function Mi(e,t){kt-=e,Ct-=t,ar.add(rt(kt*kt+Ct*Ct)),kt=e,Ct=t}let go,yn,vo,yo;class wo{constructor(t){this._append=t==null?Ai:kf(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==vo||this._append!==yn){const r=this._radius,o=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,vo=r,yn=this._append,yo=this._,this._=o}this._+=yo;break}}}result(){const t=this._;return this._="",t.length?t:null}}function Ai(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function kf(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return Ai;if(t!==go){const n=10**t;go=t,yn=function(o){let i=1;this._+=o[0];for(const l=o.length;i<l;++i)this._+=Math.round(arguments[i]*n)/n+o[i]}}return yn}function xo(e,t){let n=3,r=4.5,o,i;function l(s){return s&&(typeof r=="function"&&i.pointRadius(+r.apply(this,arguments)),lt(s,o(i))),i.result()}return l.area=function(s){return lt(s,o(He)),He.result()},l.measure=function(s){return lt(s,o(Lt)),Lt.result()},l.bounds=function(s){return lt(s,o(_n)),_n.result()},l.centroid=function(s){return lt(s,o(Ne)),Ne.result()},l.projection=function(s){return arguments.length?(o=s==null?(e=null,jn):(e=s).stream,l):e},l.context=function(s){return arguments.length?(i=s==null?(t=null,new wo(n)):new $i(t=s),typeof r!="function"&&i.pointRadius(r),l):t},l.pointRadius=function(s){return arguments.length?(r=typeof s=="function"?s:(i.pointRadius(+s),+s),l):r},l.digits=function(s){if(!arguments.length)return n;if(s==null)n=null;else{const f=Math.floor(s);if(!(f>=0))throw new RangeError(`invalid digits: ${s}`);n=f}return t===null&&(i=new wo(n)),l},l.projection(e).digits(n).context(t)}function gr(e){return function(t){var n=new sr;for(var r in e)n[r]=e[r];return n.stream=t,n}}function sr(){}sr.prototype={constructor:sr,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function vr(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),lt(n,e.stream(_n)),t(_n.result()),r!=null&&e.clipExtent(r),e}function Ni(e,t,n){return vr(e,function(r){var o=t[1][0]-t[0][0],i=t[1][1]-t[0][1],l=Math.min(o/(r[1][0]-r[0][0]),i/(r[1][1]-r[0][1])),s=+t[0][0]+(o-l*(r[1][0]+r[0][0]))/2,f=+t[0][1]+(i-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([s,f])},n)}function Cf(e,t,n){return Ni(e,[[0,0],t],n)}function Rf(e,t,n){return vr(e,function(r){var o=+t,i=o/(r[1][0]-r[0][0]),l=(o-i*(r[1][0]+r[0][0]))/2,s=-i*r[0][1];e.scale(150*i).translate([l,s])},n)}function $f(e,t,n){return vr(e,function(r){var o=+t,i=o/(r[1][1]-r[0][1]),l=-i*r[0][0],s=(o-i*(r[1][1]+r[0][1]))/2;e.scale(150*i).translate([l,s])},n)}var bo=16,Pf=me(30*ge);function Eo(e,t){return+t?Mf(e,t):Vf(e)}function Vf(e){return gr({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function Mf(e,t){function n(r,o,i,l,s,f,d,a,u,c,m,y,C,w){var S=d-r,b=a-o,R=S*S+b*b;if(R>4*t&&C--){var M=l+c,I=s+m,D=f+y,V=rt(M*M+I*I+D*D),N=zt(D/=V),A=fe(fe(D)-1)<ae||fe(i-u)<ae?(i+u)/2:Ot(I,M),p=e(A,N),g=p[0],P=p[1],q=g-r,Y=P-o,E=b*q-S*Y;(E*E/R>t||fe((S*q+b*Y)/R-.5)>.3||l*c+s*m+f*y<Pf)&&(n(r,o,i,l,s,f,g,P,A,M/=V,I/=V,D,C,w),w.point(g,P),n(g,P,A,M,I,D,d,a,u,c,m,y,C,w))}}return function(r){var o,i,l,s,f,d,a,u,c,m,y,C,w={point:S,lineStart:b,lineEnd:M,polygonStart:function(){r.polygonStart(),w.lineStart=I},polygonEnd:function(){r.polygonEnd(),w.lineStart=b}};function S(N,A){N=e(N,A),r.point(N[0],N[1])}function b(){u=NaN,w.point=R,r.lineStart()}function R(N,A){var p=mt([N,A]),g=e(N,A);n(u,c,a,m,y,C,u=g[0],c=g[1],a=N,m=p[0],y=p[1],C=p[2],bo,r),r.point(u,c)}function M(){w.point=S,r.lineEnd()}function I(){b(),w.point=D,w.lineEnd=V}function D(N,A){R(o=N,A),i=u,l=c,s=m,f=y,d=C,w.point=R}function V(){n(u,c,a,m,y,C,i,l,o,s,f,d,bo,r),w.lineEnd=M,M()}return w}}var Af=gr({point:function(e,t){this.stream.point(e*ge,t*ge)}});function Nf(e){return gr({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function Df(e,t,n,r,o){function i(l,s){return l*=r,s*=o,[t+e*l,n-e*s]}return i.invert=function(l,s){return[(l-t)/e*r,(n-s)/e*o]},i}function So(e,t,n,r,o,i){if(!i)return Df(e,t,n,r,o);var l=me(i),s=_e(i),f=l*e,d=s*e,a=l/e,u=s/e,c=(s*n-l*t)/e,m=(s*t+l*n)/e;function y(C,w){return C*=r,w*=o,[f*C-d*w+t,n-d*C-f*w]}return y.invert=function(C,w){return[r*(a*C-u*w+c),o*(m-u*C-a*w)]},y}function If(e){return Uf(function(){return e})()}function Uf(e){var t,n=150,r=480,o=250,i=0,l=0,s=0,f=0,d=0,a,u=0,c=1,m=1,y=null,C=ho,w=null,S,b,R,M=jn,I=.5,D,V,N,A,p;function g(E){return N(E[0]*ge,E[1]*ge)}function P(E){return E=N.invert(E[0],E[1]),E&&[E[0]*$e,E[1]*$e]}g.stream=function(E){return A&&p===E?A:A=Af(Nf(a)(C(D(M(p=E)))))},g.preclip=function(E){return arguments.length?(C=E,y=void 0,Y()):C},g.postclip=function(E){return arguments.length?(M=E,w=S=b=R=null,Y()):M},g.clipAngle=function(E){return arguments.length?(C=+E?df(y=E*ge):(y=null,ho),Y()):y*$e},g.clipExtent=function(E){return arguments.length?(M=E==null?(w=S=b=R=null,jn):hf(w=+E[0][0],S=+E[0][1],b=+E[1][0],R=+E[1][1]),Y()):w==null?null:[[w,S],[b,R]]},g.scale=function(E){return arguments.length?(n=+E,q()):n},g.translate=function(E){return arguments.length?(r=+E[0],o=+E[1],q()):[r,o]},g.center=function(E){return arguments.length?(i=E[0]%360*ge,l=E[1]%360*ge,q()):[i*$e,l*$e]},g.rotate=function(E){return arguments.length?(s=E[0]%360*ge,f=E[1]%360*ge,d=E.length>2?E[2]%360*ge:0,q()):[s*$e,f*$e,d*$e]},g.angle=function(E){return arguments.length?(u=E%360*ge,q()):u*$e},g.reflectX=function(E){return arguments.length?(c=E?-1:1,q()):c<0},g.reflectY=function(E){return arguments.length?(m=E?-1:1,q()):m<0},g.precision=function(E){return arguments.length?(D=Eo(V,I=E*E),Y()):rt(I)},g.fitExtent=function(E,z){return Ni(g,E,z)},g.fitSize=function(E,z){return Cf(g,E,z)},g.fitWidth=function(E,z){return Rf(g,E,z)},g.fitHeight=function(E,z){return $f(g,E,z)};function q(){var E=So(n,0,0,c,m,u).apply(null,t(i,l)),z=So(n,r-E[0],o-E[1],c,m,u);return a=vi(s,f,d),V=Zn(t,z),N=Zn(a,V),D=Eo(V,I),Y()}function Y(){return A=p=null,g}return function(){return t=e.apply(this,arguments),g.invert=t.invert&&P,q()}}function yr(e,t){return[e,jc(tf((Ce+t)/2))]}yr.invert=function(e,t){return[e,2*gi(Jc(t))-Ce]};function Tf(){return Of(yr).scale(961/Me)}function Of(e){var t=If(e),n=t.center,r=t.scale,o=t.translate,i=t.clipExtent,l=null,s,f,d;t.scale=function(u){return arguments.length?(r(u),a()):r()},t.translate=function(u){return arguments.length?(o(u),a()):o()},t.center=function(u){return arguments.length?(n(u),a()):n()},t.clipExtent=function(u){return arguments.length?(u==null?l=s=f=d=null:(l=+u[0][0],s=+u[0][1],f=+u[1][0],d=+u[1][1]),a()):l==null?null:[[l,s],[f,d]]};function a(){var u=J*r(),c=t(rf(t.rotate()).invert([0,0]));return i(l==null?[[c[0]-u,c[1]-u],[c[0]+u,c[1]+u]]:e===yr?[[Math.max(c[0]-u,l),s],[Math.min(c[0]+u,f),d]]:[[l,Math.max(c[1]-u,s)],[f,Math.min(c[1]+u,d)]])}return a()}function Rt(e,t,n){this.k=e,this.x=t,this.y=n}Rt.prototype={constructor:Rt,scale:function(e){return e===1?this:new Rt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new Rt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};Rt.prototype;const zf={class:"clock-container"},Ff={class:"holographic-clock",viewBox:"0 0 100 100"},Lf={class:"clock-markers"},qf=["x1","y1","x2","y2"],Yf={class:"clock-hands"},Hf=["x2","y2"],Bf=["x2","y2"],Xf=["x2","y2"],Gf=["id"],Kf=["id"],Wf={__name:"VisualScreen",setup(e){const t=T(null),n=T(null),r=T(0),o=T(!1),i=new Map,l=T({x:0,y:0}),s=T(0),f=T(0),d=T(0),a=T(0);let u;const c=T([{id:"china",url:"/public/geo/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"/public/geo/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"/public/geo/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"/public/geo/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),m={longitude:106.43,latitude:30.55},y=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},C=()=>{const N=new Date;s.value=N.getHours()%12,f.value=N.getMinutes(),d.value=N.getSeconds(),a.value=N.getMilliseconds()},w=N=>{if(i.has(N.id))return;const A=y(),p=Bt(`#${N.id}-map`).attr("viewBox",`0 0 ${A.width} ${A.height}`).attr("preserveAspectRatio","xMidYMid meet");Zc(N.url).then(g=>{const P=Tf().center(N.center).scale(Math.min(A.width,A.height)*N.scaleFactor).translate([A.width/2,A.height/2]);i.set(N.id,{projection:P,data:g}),p.selectAll(".boundary").data(g.features).enter().append("path").attr("class","boundary").attr("d",xo().projection(P)),S(N.id)}).catch(g=>console.error("地图加载失败:",g))},S=N=>{const{projection:A}=i.get(N)||{};if(!A)return;const[p,g]=A([m.longitude,m.latitude]);l.value={x:p,y:g}},b=()=>{const N=y();c.value.forEach(A=>{const{projection:p,data:g}=i.get(A.id)||{};!p||!g||(p.scale(Math.min(N.width,N.height)*A.scaleFactor).translate([N.width/2,N.height/2]),Bt(`#${A.id}-map`).attr("viewBox",`0 0 ${N.width} ${N.height}`).selectAll(".boundary").attr("d",xo().projection(p)),A.id===c.value[r.value].id&&S(A.id))})},R=N=>{if(o.value)return;o.value=!0;const A=c.value.length,p=(r.value+N+A)%A,g=c.value[r.value],P=c.value[p];Bt(`#${g.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{Bt(`#${P.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=p,o.value=!1,S(P.id)})})},M=()=>{const{width:A,height:p}=y();for(let g=0;g<100;g++){const P=document.createElement("div");P.className="particle";const q=Math.random()*A,Y=Math.random()*p,E=Math.random()*10,z=Math.random()*2+1;P.style.left=`${q}px`,P.style.top=`${Y}px`,P.style.animationDelay=`${E}s`,P.style.width=`${z}px`,P.style.height=`${z}px`,n.value.appendChild(P)}};let I;const D=()=>{I=setInterval(()=>{R(1)},c.value[0].duration+1500)};nt(()=>{C(),u=setInterval(C,50),c.value.forEach(w),M(),D(),document.addEventListener("fullscreenchange",V)}),Xi(()=>{clearInterval(u),clearInterval(I),i.clear(),document.removeEventListener("fullscreenchange",V)});const V=()=>{on(()=>{b(),n.value&&(n.value.innerHTML=""),M()})};return st([()=>window.innerWidth,()=>window.innerHeight,()=>{var N;return(N=t.value)==null?void 0:N.clientWidth},()=>{var N;return(N=t.value)==null?void 0:N.clientHeight}],()=>{on(b)}),(N,A)=>(Z(),ce("div",{class:"holographic-container",ref_key:"container",ref:t},[A[3]||(A[3]=L("div",{class:"slogan-container"},[L("div",null,"对党忠诚  服务人民"),L("div",null,"执法公正  纪律严明")],-1)),A[4]||(A[4]=L("div",{class:"title-container"},[L("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),L("div",zf,[(Z(),ce("svg",Ff,[A[0]||(A[0]=L("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),A[1]||(A[1]=L("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),L("g",Lf,[(Z(),ce(Pe,null,Ae(12,p=>L("line",{key:p,x1:50+38*Math.cos((p*30-90)*Math.PI/180),y1:50+38*Math.sin((p*30-90)*Math.PI/180),x2:50+42*Math.cos((p*30-90)*Math.PI/180),y2:50+42*Math.sin((p*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,qf)),64))]),L("g",Yf,[L("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((s.value*30+f.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((s.value*30+f.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,Hf),L("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((f.value*6+d.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((f.value*6+d.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Bf),L("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((d.value*6+a.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((d.value*6+a.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,Xf)]),A[2]||(A[2]=L("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),L("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),A[5]||(A[5]=L("div",{class:"hologram-grid"},null,-1)),A[6]||(A[6]=L("div",{class:"scan-line-vertical"},null,-1)),A[7]||(A[7]=L("div",{class:"scan-line-horizontal"},null,-1)),A[8]||(A[8]=L("div",{class:"hologram-frame"},[L("div")],-1)),(Z(!0),ce(Pe,null,Ae(c.value,(p,g)=>(Z(),ce("div",{key:p.id,id:`${p.id}-container`,class:Gi(["map-container",{"map-visible":r.value===g}])},[(Z(),ce("svg",{id:`${p.id}-map`,class:"map-svg"},null,8,Kf)),r.value===g?(Z(),ce("div",{key:0,class:"location-marker",style:ur({left:l.value.x+"px",top:l.value.y+"px"})},null,4)):$t("",!0)],10,Gf))),128))],512))}},Qf={class:"app-management-container"},Zf={class:"clearfix"},Jf={__name:"AppManagement",setup(e){const t=T([]),n=T([]),r=T(0),o=T(!1);T(!1),T("");const i=T(null),l=Je({id:null,name:"",url:"",isPublic:"",roles:[]}),s=Je({name:[{required:!0,message:"请输入应用名称",trigger:"blur"}],url:[{required:!0,message:"请输入应用URL",trigger:"blur"}],isPublic:[{required:!0,message:"请选择可见范围",trigger:"change"}],roles:[{type:"array",required:!0,validator:(w,S,b)=>{S&&S.length>0?b():b(new Error("至少选择一个角色组"))},trigger:"change"}]}),f=w=>{try{return JSON.parse(w).map(b=>{const R=n.value.find(M=>String(M.value)===String(b));return R?R.label:b})}catch(S){return console.error("解析 roleList 失败:",S),[]}},d=async()=>{try{const w=new FormData;w.append("controlCode","query");const S=await oe.post("/api/application_manage.php",w);S.status===1&&(t.value=S.data.application,n.value=S.data.rolelist.map(b=>({value:b.id,label:b.roleName})),r.value=S.data.application.length)}catch(w){console.error("获取应用列表失败:",w),G.error("获取应用列表失败")}};nt(()=>{d()});const a=w=>{switch(w){case"0":return"公开";case"1":return"非第三方人员";case"2":return"民警";case"3":return"授权用户";default:return"未知"}},u=()=>{m(),o.value=!0},c=w=>{if(m(),n.value.length===0){console.error("角色选项未加载完成"),G.error("角色数据加载中，请稍后再试");return}l.id=w.id,l.name=w.application_name,l.url=w.url,l.isPublic=w.public;try{const S=JSON.parse(w.roleList);l.roles=S.map(b=>String(b)),on(()=>{l.roles=[...l.roles]})}catch(S){console.error("解析角色列表失败:",S),l.roles=[]}o.value=!0},m=()=>{i.value&&i.value.resetFields(),l.id=null,l.name="",l.url="",l.isPublic="",l.roles=[]},y=w=>{Mt.confirm(`确定要删除应用 "${w.application_name}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const S=new FormData;S.append("controlCode","del"),S.append("id",w.id);const b=await oe.post("/api/application_manage.php",S);b.status===1?(G.success("删除成功"),d()):G.error("删除失败: "+(b.message||"未知错误"))}catch(S){console.error("删除应用失败:",S),G.error("删除应用失败: "+S.message)}}).catch(()=>{G.info("已取消删除")})},C=async()=>{try{await i.value.validate()}catch(w){console.error("表单验证失败:",w);return}try{const w=new FormData;w.append("controlCode",l.id?"modify":"add"),w.append("id",l.id),w.append("application_name",l.name),w.append("url",l.url),w.append("public",l.isPublic),w.append("roleList",JSON.stringify(l.roles));const S=await oe.post("/api/application_manage.php",w);S.status===1?(G.success("更新成功"),o.value=!1,d()):G.error(S.message||"更新失败")}catch(w){console.error("更新应用失败:",w),G.error("更新应用失败: "+w.message)}};return(w,S)=>{const b=O("el-button"),R=O("el-table-column"),M=O("el-tag"),I=O("el-icon"),D=O("el-button-group"),V=O("el-table"),N=O("el-card"),A=O("el-input"),p=O("el-form-item"),g=O("el-option"),P=O("el-select"),q=O("el-checkbox"),Y=O("el-checkbox-group"),E=O("el-form"),z=O("el-dialog");return Z(),ce("div",Qf,[h(N,{class:"box-card"},{header:v(()=>[L("div",Zf,[h(b,{style:{float:"right"},round:"",type:"primary",onClick:u},{default:v(()=>S[6]||(S[6]=[te(" 添加应用 ")])),_:1,__:[6]})])]),default:v(()=>[h(V,{data:t.value,stripe:"",border:"",fit:"","highlight-current-row":"",onRowDblclick:c,style:{width:"100%"}},{default:v(()=>[h(R,{label:"序号",width:"80",align:"center"},{default:v(H=>[te(be(H.$index+1),1)]),_:1}),h(R,{prop:"application_name",label:"应用名称","min-width":"120"}),h(R,{prop:"url",label:"URL","min-width":"150"}),h(R,{prop:"public",label:"是否公开",width:"150",align:"center"},{default:v(H=>[te(be(a(H.row.public)),1)]),_:1}),h(R,{prop:"roles",label:"授权角色组","min-width":"180"},{default:v(H=>[(Z(!0),ce(Pe,null,Ae(f(H.row.roleList),j=>(Z(),Se(M,{key:j,size:"small",type:"info"},{default:v(()=>[te(be(j),1)]),_:2},1024))),128))]),_:1}),h(R,{label:"操作",width:"160",align:"center"},{default:v(H=>[h(D,null,{default:v(()=>[h(b,{size:"mini",type:"warning",onClick:j=>c(H.row)},{default:v(()=>[h(I,null,{default:v(()=>[h(le(wn))]),_:1})]),_:2},1032,["onClick"]),h(b,{size:"mini",type:"danger",onClick:j=>y(H.row)},{default:v(()=>[h(I,null,{default:v(()=>[h(le(xn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1}),h(z,{modelValue:o.value,"onUpdate:modelValue":S[5]||(S[5]=H=>o.value=H),title:l.id?"编辑应用":"添加应用",width:"50%"},{footer:v(()=>[h(b,{onClick:S[4]||(S[4]=H=>o.value=!1)},{default:v(()=>S[7]||(S[7]=[te("取消")])),_:1,__:[7]}),h(b,{type:"primary",onClick:C},{default:v(()=>S[8]||(S[8]=[te("保存")])),_:1,__:[8]})]),default:v(()=>[h(E,{model:l,rules:s,ref_key:"formRef",ref:i,"label-width":"100px"},{default:v(()=>[h(p,{label:"应用名称",prop:"name"},{default:v(()=>[h(A,{modelValue:l.name,"onUpdate:modelValue":S[0]||(S[0]=H=>l.name=H),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1}),h(p,{label:"URL",prop:"url"},{default:v(()=>[h(A,{modelValue:l.url,"onUpdate:modelValue":S[1]||(S[1]=H=>l.url=H),placeholder:"请输入应用URL"},null,8,["modelValue"])]),_:1}),h(p,{label:"是否公开",prop:"isPublic"},{default:v(()=>[h(P,{modelValue:l.isPublic,"onUpdate:modelValue":S[2]||(S[2]=H=>l.isPublic=H),placeholder:"请选择是否公开"},{default:v(()=>[h(g,{value:"0",label:"公开"}),h(g,{value:"1",label:"非第三方人员"}),h(g,{value:"2",label:"民警"}),h(g,{value:"3",label:"授权用户"})]),_:1},8,["modelValue"])]),_:1}),h(p,{label:"授权角色组",prop:"roles"},{default:v(()=>[h(Y,{modelValue:l.roles,"onUpdate:modelValue":S[3]||(S[3]=H=>l.roles=H)},{default:v(()=>[(Z(!0),ce(Pe,null,Ae(n.value,H=>(Z(),Se(q,{key:H.value,label:H.value},{default:v(()=>[te(be(H.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}},jf=qt(Jf,[["__scopeId","data-v-2a67e87b"]]),ed={class:"container"},td={class:"left-section"},nd={class:"query-bar"},rd={class:"user-table"},od={class:"right-section"},id={class:"query-bar"},ld={class:"authorization-table"},ad={__name:"PowerManagement",setup(e){const t=T(""),n=T(""),r=T([]),o=T(""),i=T([]),l=T([]),s=T(!1),f=async()=>{s.value=!0;try{const u=new FormData;u.append("controlCode","query");const c=await oe("/api/application_manage.php",{method:"post",data:u});console.log("请求jieguo:",c),c.status===1?(l.value=c.data.application,console.log("获取应用列表成功:",l.value)):console.error("获取应用列表失败:",data.message||"未知错误")}catch(u){console.error("网络请求错误:",u)}finally{s.value=!1}},d=()=>{r.value=[]},a=()=>{i.value=[]};return nt(()=>{f()}),(u,c)=>{const m=O("el-select"),y=O("el-input"),C=O("el-button"),w=O("el-table-column"),S=O("el-table"),b=O("el-option");return Z(),ce("div",ed,[L("div",td,[L("div",nd,[h(m,{modelValue:t.value,"onUpdate:modelValue":c[0]||(c[0]=R=>t.value=R),placeholder:"选择单位",class:"select-unit"},null,8,["modelValue"]),h(y,{modelValue:n.value,"onUpdate:modelValue":c[1]||(c[1]=R=>n.value=R),placeholder:"输入姓名、警号或身份证号",class:"input-user"},null,8,["modelValue"]),h(C,{type:"primary",onClick:d},{default:v(()=>c[3]||(c[3]=[te("查询用户")])),_:1,__:[3]})]),L("div",rd,[c[5]||(c[5]=L("h4",null,"用户列表",-1)),h(S,{data:r.value,"empty-text":"No Data",class:"table"},{default:v(()=>[h(w,{prop:"unit",label:"单位"}),h(w,{prop:"name",label:"姓名"}),h(w,{prop:"policeNumber",label:"警号"}),h(w,{prop:"idCard",label:"身份证号"}),h(w,{prop:"identity",label:"身份"}),h(w,{label:"操作"},{default:v(()=>c[4]||(c[4]=[])),_:1})]),_:1},8,["data"])])]),L("div",od,[L("div",id,[h(m,{modelValue:o.value,"onUpdate:modelValue":c[2]||(c[2]=R=>o.value=R),placeholder:"选择应用",class:"select-app"},{default:v(()=>[h(b,{label:"选择应用",value:""}),(Z(!0),ce(Pe,null,Ae(l.value,R=>(Z(),Se(b,{key:R.id,label:R.application_name,value:R.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),h(C,{type:"primary",onClick:a},{default:v(()=>c[6]||(c[6]=[te("查询授权人员")])),_:1,__:[6]})]),L("div",ld,[c[8]||(c[8]=L("h4",null,"授权信息",-1)),h(S,{data:i.value,"empty-text":"No Data",class:"table"},{default:v(()=>[h(w,{prop:"authorizedApp",label:"授权应用"}),h(w,{prop:"authorizedUnit",label:"授权单位"}),h(w,{prop:"authorizedRole",label:"授权角色"}),h(w,{label:"操作"},{default:v(()=>c[7]||(c[7]=[])),_:1})]),_:1},8,["data"])])])])}}},sd=qt(ad,[["__scopeId","data-v-22663c97"]]),ud={class:"role-management-container"},cd={class:"toolbar flex justify-end items-center mb-4"},fd={__name:"RoleManagement",setup(e){const t=T([]),n=T(null),r=async()=>{try{const a=new FormData;a.append("controlCode","query");const u=await oe.post("/api/role_List_manage.php",a);u.status===1?t.value=u.data.map(c=>({id:c.id,name:c.roleName,desc:c.roleDesc})):G.error(u.message||"获取角色列表失败")}catch(a){console.error("获取角色列表失败:",a),G.error("获取角色列表失败")}};nt(()=>{r()});const o=T(!1),i=Je({id:null,name:"",desc:""}),l=()=>{n.value&&n.value.resetFields(),i.id=null,i.name="",i.desc="",o.value=!0},s=a=>{n.value&&n.value.resetFields(),i.id=a.id,i.name=a.name,i.desc=a.desc,o.value=!0},f=a=>{Mt.confirm(`确定要删除角色 "${a.name}" 吗？`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const u=new FormData;u.append("controlCode","del"),u.append("id",a.id);const c=await oe.post("/api/role_List_manage.php",u);c.status===1?(G.success("删除成功"),r()):G.error(c.message||"删除失败")}catch(u){console.error("删除角色失败:",u),G.error("删除角色失败: "+u.message)}}).catch(()=>{G.info("已取消删除")})},d=async()=>{if(!i.name){G.error("角色名称不能为空");return}try{const a=new FormData;a.append("controlCode",i.id?"modify":"add"),a.append("roleName",i.name),a.append("roleDesc",i.desc),i.id&&a.append("id",i.id);const u=await oe.post("/api/role_List_manage.php",a);u.status===1?(G.success(i.id?"更新成功":"添加成功"),o.value=!1,r()):G.error(u.message||(i.id?"更新失败":"添加失败"))}catch(a){console.error("操作失败:",a),G.error("操作失败: "+a.message)}};return(a,u)=>{const c=O("el-icon"),m=O("el-button"),y=O("el-table-column"),C=O("el-button-group"),w=O("el-table"),S=O("el-input"),b=O("el-form-item"),R=O("el-form"),M=O("el-dialog");return Z(),ce("div",ud,[L("div",cd,[h(m,{type:"primary",onClick:l},{default:v(()=>[h(c,null,{default:v(()=>[h(le($o))]),_:1}),u[4]||(u[4]=te(" 添加角色 "))]),_:1,__:[4]})]),h(w,{data:t.value,style:{width:"100%"},border:""},{default:v(()=>[h(y,{label:"序号",width:"80",align:"center"},{default:v(I=>[te(be(I.$index+1),1)]),_:1}),h(y,{prop:"name",label:"角色名称"}),h(y,{prop:"desc",label:"角色描述"}),h(y,{label:"操作"},{default:v(I=>[h(C,null,{default:v(()=>[h(m,{size:"mini",type:"warning",onClick:D=>s(I.row)},{default:v(()=>[h(c,null,{default:v(()=>[h(le(wn))]),_:1})]),_:2},1032,["onClick"]),h(m,{size:"mini",type:"danger",onClick:D=>f(I.row)},{default:v(()=>[h(c,null,{default:v(()=>[h(le(xn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),h(M,{modelValue:o.value,"onUpdate:modelValue":u[3]||(u[3]=I=>o.value=I),width:"20%"},{default:v(()=>[h(R,{model:i,"label-width":"100px",ref_key:"formRef",ref:n},{default:v(()=>[h(b,{label:"角色名称",prop:"name"},{default:v(()=>[h(S,{modelValue:i.name,"onUpdate:modelValue":u[0]||(u[0]=I=>i.name=I)},null,8,["modelValue"])]),_:1}),h(b,{label:"角色描述",prop:"desc"},{default:v(()=>[h(S,{modelValue:i.desc,"onUpdate:modelValue":u[1]||(u[1]=I=>i.desc=I),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),footer:v(()=>[h(m,{onClick:u[2]||(u[2]=I=>o.value=!1)},{default:v(()=>u[5]||(u[5]=[te("取消")])),_:1,__:[5]}),h(m,{type:"primary",onClick:d},{default:v(()=>u[6]||(u[6]=[te("确定")])),_:1,__:[6]})]),_:1},8,["modelValue"])])}}},dd=qt(fd,[["__scopeId","data-v-2a4abc11"]]),pd=[{path:"/unit-management",name:"UnitManagement",component:ma},{path:"/user-management",name:"UserManagement",component:Na},{path:"/visual-screen",name:"VisualScreen",component:Wf},{path:"/app-management",name:"AppManagement",component:jf},{path:"/role-management",name:"RoleManagement",component:dd},{path:"/power-management",name:"PowerManagement",component:sd}],hd=jl({history:$l(),routes:pd}),Rn=Ki(da);Rn.use(Wi);for(const[e,t]of Object.entries(Qi))Rn.component(e,t);Rn.use(hd);Rn.mount("#app");
