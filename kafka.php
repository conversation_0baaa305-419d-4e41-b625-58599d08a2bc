<?php
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

$conf = new RdKafka\Conf();
$conf->set('group.id', 'alarm-consumer-group');
$conf->set('metadata.broker.list', '80.164.12.151:9192');

$consumer = new RdKafka\KafkaConsumer($conf);
$consumer->subscribe(['face_alarm_000005']);

while (true) {
    $message = $consumer->consume(120*1000);
    
    switch ($message->err) {
        case RD_KAFKA_RESP_ERR_NO_ERROR:
            $alarmData = json_decode($message->payload, true);
            
            // 格式化报警数据
            $eventData = [
                'id' => $alarmData['id'],
                'alarmNo' => $alarmData['alarmNo'],
                'time' => date('Y-m-d H:i:s', $alarmData['capturedTime']/1000),
                'score' => $alarmData['score'],
                'name' => $alarmData['photo']['identity']['sfz'] ?? '未知',
                'location' => $alarmData['camera']['name'],
                'lat' => $alarmData['camera']['lat'],
                'lon' => $alarmData['camera']['lon'],
                'image' => $alarmData['fullUrl'],
                'album' => $alarmData['album']['name']
            ];
            
            // 发送SSE事件
            echo "event: alarm\n";
            echo "data: " . json_encode($eventData) . "\n\n";
            ob_flush();
            flush();
            break;
            
        case RD_KAFKA_RESP_ERR__PARTITION_EOF:
            echo ": heartbeat\n\n";
            ob_flush();
            flush();
            break;
            
        case RD_KAFKA_RESP_ERR__TIMED_OUT:
            break;
            
        default:
            throw new \Exception($message->errstr(), $message->err);
    }
}
?>