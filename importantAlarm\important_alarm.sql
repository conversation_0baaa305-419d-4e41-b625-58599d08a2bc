-- 重点警情基本情况表
CREATE TABLE `important_alarm_basic` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alarm_name` varchar(255) NOT NULL COMMENT '警情名称',
  `alarm_content` text NOT NULL COMMENT '警情内容',
  `occur_time` datetime NOT NULL COMMENT '事发时间',
  `area_id` int(11) NOT NULL COMMENT '事发辖区（关联2_unit表id）',
  `alarm_level` int(11) NOT NULL COMMENT '警情等级',
  `alarm_status` varchar(20) NOT NULL COMMENT '警情状态',
  `is_display` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否展示大屏',
  `is_top` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶',
  PRIMARY KEY (`id`),
  KEY `idx_area` (`area_id`),
  CONSTRAINT `fk_area_unit` FOREIGN KEY (`area_id`) REFERENCES `2_unit` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='重点警情基本情况表';

-- 重点警情处置情况表
CREATE TABLE `important_alarm_handling` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `alarm_id` int(11) NOT NULL COMMENT '警情ID',
  `handling_time` datetime NOT NULL COMMENT '最新处置时间',
  `handling_content` text NOT NULL COMMENT '最新处置情况',
  PRIMARY KEY (`id`),
  KEY `idx_alarm` (`alarm_id`),
  CONSTRAINT `fk_alarm_basic` FOREIGN KEY (`alarm_id`) REFERENCES `important_alarm_basic` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='重点警情处置情况表';