<?php
require_once '../config.php';


// 检查登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 0, 'message' => '请先登录']);
    exit;
}

// 获取输入数据
$oldPassword = $_POST['old_password'] ?? '';
$newPassword = $_POST['new_password'] ?? '';


// 验证输入
if (empty($oldPassword) || empty($newPassword)) {
    echo json_encode(['status' => 0, 'message' => '所有字段都必须填写']);
    exit;
}



// 查询当前用户信息
$sql = "SELECT password FROM user WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

// 验证旧密码
if (!password_verify($oldPassword, $user['password'])) {
    echo json_encode(['status' => 0, 'message' => '旧密码错误']);
    exit;
}

// 更新密码
$hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
$updateSql = "UPDATE user SET password = ? WHERE id = ?";
$updateStmt = $conn->prepare($updateSql);
$updateStmt->bind_param('si', $hashedPassword, $_SESSION['user_id']);

if ($updateStmt->execute()) {
    // 清除session并重定向到登录页面
    session_unset();
    session_destroy();
    echo json_encode(['status' => 1, 'message' => '密码修改成功，请重新登录']);
} else {
    echo json_encode(['status' => 0, 'message' => '密码修改失败']);
}
?>