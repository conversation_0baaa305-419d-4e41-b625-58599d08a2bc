-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.4
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-06-03 17:00:08
-- 服务器版本： 5.7.44-log
-- PHP 版本： 8.0.26

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `application`
--

-- --------------------------------------------------------

--
-- 表的结构 `2_unit`
--

CREATE TABLE `2_unit` (
  `id` int(11) NOT NULL COMMENT '自增ID，主键',
  `unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '单位名称，唯一且非空',
  `parent_id` int(11) DEFAULT NULL COMMENT '上级单位ID，可以为空',
  `sort_order` int(11) NOT NULL COMMENT '排序字段，用于控制显示顺序，非空',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织机构代码'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- 表的结构 `3_user`
--

CREATE TABLE `3_user` (
  `id` int(11) NOT NULL COMMENT '自增ID，主键',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `id_number` char(18) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '身份证号',
  `phone` varchar(11) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号码',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `archive_birthdate` date NOT NULL COMMENT '档案出生时间',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别 (0:未知 1:男 2:女)',
  `short_code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短号',
  `alt_phone_1` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话号码2',
  `alt_phone_2` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电话号码3',
  `landline` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '座机',
  `organization_unit` int(11) DEFAULT NULL COMMENT '编制单位',
  `work_unit` int(11) DEFAULT NULL COMMENT '工作单位',
  `employment_date` date DEFAULT NULL COMMENT '参工时间',
  `political_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '政治面貌',
  `party_join_date` date DEFAULT NULL COMMENT '加入组织时间',
  `personnel_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '人员身份',
  `police_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '警号/辅警号',
  `is_assisting_officer` tinyint(1) DEFAULT NULL COMMENT '带辅民警 (0:否 1:是)',
  `employment_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '人员状态',
  `job_rank` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职级',
  `current_rank_date` date DEFAULT NULL COMMENT '任现职级时间',
  `position` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职务',
  `current_position_date` date DEFAULT NULL COMMENT '任现职务时间',
  `desc` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `3_user_Role`
--

CREATE TABLE `3_user_Role` (
  `id` int(11) NOT NULL COMMENT '自增id唯一',
  `userId` int(11) NOT NULL COMMENT '用户id',
  `roleId` int(11) NOT NULL COMMENT '用户角色ID',
  `unitId` int(11) NOT NULL COMMENT '角色单位ID',
  `appId` int(11) NOT NULL COMMENT '关联应用ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `4_Role_List`
--

CREATE TABLE `4_Role_List` (
  `id` int(11) NOT NULL COMMENT '自增ID',
  `roleName` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `roleDesc` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `5_application`
--

CREATE TABLE `5_application` (
  `id` int(11) NOT NULL COMMENT '自增ID，主键',
  `application_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称，唯一且非空',
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用首页的URL',
  `public` int(2) NOT NULL COMMENT '是否公用，0为所有人可用，1为非第三方合作人员可用，2为民警可用，3为必须指定角色才可用。',
  'rold_list' json NOT NULL COMMENT '该应用可以指定关联哪些角色，json数组格式，存储角色id，如[1,2,3]'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `6_duty_sched`
--

CREATE TABLE `6_duty_sched` (
  `id` int(11) NOT NULL COMMENT '自增id唯一',
  `sched_date` date NOT NULL COMMENT '值班时间',
  `a_dir` int(11) NOT NULL COMMENT 'A岗局领导用户ID',
  `b_dir` int(11) NOT NULL COMMENT 'B岗局领导用户ID',
  `a_com` int(11) NOT NULL COMMENT 'A岗指挥长用户ID',
  `b_com` int(11) NOT NULL COMMENT 'B岗指挥长用户ID',
  `sub` int(11) NOT NULL COMMENT '值班长用户ID',
  `op` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '值班员用户ID，（存储多个用户id）',
  `tech` int(11) NOT NULL COMMENT '技术支持人员用户ID',
  `sec` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '安全员用户ID（存储多个用户id）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `log`
--

CREATE TABLE `log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `application_id` int(11) NOT NULL COMMENT '应用ID',
  `operation` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作内容',
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `ip` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作者的IP地址'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- 表的结构 `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL COMMENT '主键，自增ID',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `attempts` int(11) DEFAULT '0' COMMENT '当前尝试登录次数',
  `last_attempt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上一次尝试登录时间',
  `locked_until` timestamp NULL DEFAULT NULL COMMENT '解锁时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `application_id` (`application_id`);

--
-- 表的索引 `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `2_unit`
--
ALTER TABLE `2_unit`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID，主键';

--
-- 使用表AUTO_INCREMENT `3_user`
--
ALTER TABLE `3_user`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID，主键';

--
-- 使用表AUTO_INCREMENT `3_user_Role`
--
ALTER TABLE `3_user_Role`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id唯一';

--
-- 使用表AUTO_INCREMENT `4_Role_List`
--
ALTER TABLE `4_Role_List`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID';

--
-- 使用表AUTO_INCREMENT `5_application`
--
ALTER TABLE `5_application`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID，主键';

--
-- 使用表AUTO_INCREMENT `6_duty_sched`
--
ALTER TABLE `6_duty_sched`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id唯一';

--
-- 使用表AUTO_INCREMENT `log`
--
ALTER TABLE `log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- 使用表AUTO_INCREMENT `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键，自增ID';

--
-- 限制导出的表
--

--
-- 限制表 `log`
--
ALTER TABLE `log`
  ADD CONSTRAINT `log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `3_user` (`id`),
  ADD CONSTRAINT `log_ibfk_2` FOREIGN KEY (`application_id`) REFERENCES `5_application` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
