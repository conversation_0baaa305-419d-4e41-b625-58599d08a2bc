/**
 * 文件网盘系统 - 主样式表
 * 创建时间: 2025-06-23
 */

/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
    background: rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #157347);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
    border: none;
    color: #000;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
    border: none;
}

/* 表格样式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table-hover tbody tr:hover {
    background-color: rgba(var(--primary-color), 0.05);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
}

/* 徽章样式 */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* 警告框样式 */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: var(--box-shadow);
}

/* 分享码样式 */
.share-code {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
}

.share-code:hover {
    background: #e9ecef;
}

/* 文件图标样式 */
.bi-file-earmark {
    color: var(--primary-color);
}

.bi-file-pdf {
    color: #dc3545;
}

.bi-file-word {
    color: #2b579a;
}

.bi-file-excel {
    color: #217346;
}

.bi-file-ppt {
    color: #d24726;
}

.bi-file-image {
    color: #6f42c1;
}

.bi-file-zip {
    color: #fd7e14;
}

.bi-file-text {
    color: #6c757d;
}

/* 统计卡片样式 */
.card.bg-primary,
.card.bg-success,
.card.bg-info,
.card.bg-warning {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.card.bg-success {
    background: linear-gradient(135deg, var(--success-color), #157347) !important;
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
}

.card.bg-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0) !important;
    box-shadow: 0 4px 15px rgba(13, 202, 240, 0.3);
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800) !important;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    color: #000 !important;
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.05);
}

/* 信息项样式 */
.info-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item:last-child {
    border-bottom: none;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        border-radius: var(--border-radius);
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    /* 文件管理页面移动端优化 */
    .card-header .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    .card-header .d-flex > div {
        width: 100%;
    }

    .table th:nth-child(3),
    .table td:nth-child(3),
    .table th:nth-child(7),
    .table td:nth-child(7) {
        display: none; /* 隐藏类型和下载量列 */
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .pagination .page-item {
        margin: 0.1rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 自定义动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 6px;
    font-size: 0.875rem;
}

/* 面包屑样式 */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--secondary-color);
}

/* 进度条样式 */
.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 空状态样式 */
.empty-state {
    padding: 3rem 2rem;
    text-align: center;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: #0056b3;
}

/* 代码样式 */
code {
    color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

/* 文本截断 */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 阴影工具类 */
.shadow-soft {
    box-shadow: 0 2px 15px rgba(0,0,0,0.08) !important;
}

.shadow-strong {
    box-shadow: 0 5px 25px rgba(0,0,0,0.15) !important;
}

/* 边框工具类 */
.border-soft {
    border: 1px solid rgba(0,0,0,0.05) !important;
}

/* 背景工具类 */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color), #157347) !important;
}

.bg-soft-primary {
    background-color: rgba(13, 110, 253, 0.1) !important;
}

/* 文件管理特定样式 */
.file-checkbox {
    cursor: pointer;
}

.file-checkbox:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.table tbody tr:hover .file-checkbox {
    opacity: 1;
}

.batch-actions {
    transition: var(--transition);
}

.batch-actions.disabled {
    opacity: 0.6;
}

/* 选中行高亮 */
.table tbody tr:has(.file-checkbox:checked) {
    background-color: rgba(13, 110, 253, 0.1);
}

/* 文件类型徽章样式 */
.badge.bg-light {
    color: #6c757d !important;
    border: 1px solid #dee2e6;
}

/* 分页样式优化 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Toast通知样式 */
.toast {
    border-radius: 10px;
    box-shadow: var(--box-shadow);
}

.toast-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

/* 模态框优化 */
.modal-content {
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .modal,
    .sidebar {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }

    body {
        background: white !important;
    }
}
