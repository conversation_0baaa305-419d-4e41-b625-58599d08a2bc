<?php
require_once '../conn_waf.php';
$APP_ID=31;
$user_id = $_SESSION['user_id'];

function isHasPerm($APP_ID){
    global $conn;

    // 检查用户登录状态
    if (!isset($_SESSION['user_id'])) {
        // 重定向到美观的错误页面
        $errorMessage = urlencode('您尚未登录系统，请先完成身份验证。');
        header("Location: ../permission_error.html?type=login&message=" . $errorMessage);
        exit;
    }

    // 系统管理员拥有所有权限
    if (isAdmin()) {
        return true;
    }

    // 检查应用管理员权限
    if (isAppAdmin($APP_ID)) {
        return true;
    }

    // 获取基于角色授权的应用权限
    $userId = $_SESSION['user_id'];
    $sql = "SELECT DISTINCT a.id FROM 5_application a
            JOIN 3_user_Role ur ON a.id = ur.appId
            WHERE ur.userId = ? AND JSON_CONTAINS(a.roleList, CAST(ur.roleId AS JSON)) AND ur.appId = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'ii', $userId , $APP_ID);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($result && $result->num_rows > 0) {
        return true;
    }

    // 权限不足，重定向到美观的错误页面
    $errorMessage = urlencode('您当前的账户权限不足以人员调动模拟系统，请联系系统管理员申请相应权限。');
    header("Location: ../permission_error.html?type=permission&message=" . $errorMessage);
    exit;
}

isHasPerm($APP_ID);


// 简化的用户信息获取
$sql = "SELECT id, name FROM 3_user WHERE id = ?";

$stmt = $conn->prepare($sql);
if (!$stmt) {
    die('SQL语句准备失败: ' . $conn->error);
}

$stmt->bind_param('i', $user_id);
if (!$stmt->execute()) {
    die('SQL执行失败: ' . $stmt->error);
}

$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // 用户信息不存在，跳转到登录页面
    header('Location: ../login.html');
    exit();
}

$userInfo = $result->fetch_assoc();
// 添加默认的role_id
$userInfo['role_id'] = 1;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人员调动模拟系统</title>
    <link rel="stylesheet" href="assets/css/simulation.css?v=1.0.9">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
</head>
<body>
    <div class="container">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <h1>人员调动模拟系统</h1>
            </div>
            <div class="toolbar-center">
                <select id="scenarioSelect" class="scenario-select">
                    <option value="">选择模拟方案</option>
                </select>
                <button id="refreshBtn" class="btn btn-secondary">刷新</button>
            </div>
            <div class="toolbar-right">
                <button id="createScenarioBtn" class="btn btn-primary">新建方案</button>
                <button id="deleteScenarioBtn" class="btn btn-danger" disabled>删除方案</button>
                <button id="unitManageBtn" class="btn btn-warning">单位管理</button>
                <button id="operationLogsBtn" class="btn btn-info">操作日志</button>
                <button id="statisticsBtn" class="btn btn-info">统计报表</button>
                <button id="exportBtn" class="btn btn-success">导出数据</button>
            </div>
            <div class="toolbar-user">
                <div class="user-info">
                    <span class="user-avatar">👤</span>
                    <span class="user-name"><?php echo htmlspecialchars($userInfo['name'] ?: '用户' . $userInfo['id']); ?></span>
                    <button id="logoutBtn" class="btn btn-sm btn-secondary">退出登录</button>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <div class="panel-header">
                    <h3>组织架构</h3>
                    <div class="unit-stats">
                        <span id="unitCount">单位: 0</span>
                        <span id="personnelCount">人员: 0</span>
                    </div>
                </div>
                <div class="unit-tree" id="unitTree">
                    <div class="empty-state">请先选择模拟方案</div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <div class="panel-header">
                    <h3 id="currentUnitName">请选择单位</h3>
                    <div class="personnel-actions">
                        <input type="text" id="searchInput" class="search-input" placeholder="搜索人员...">
                        <button id="batchTransferBtn" class="btn btn-primary" disabled>批量调动</button>
                    </div>
                </div>

                <!-- 人员筛选 -->
                <div class="personnel-filters">
                    <div class="filter-group">
                        <span class="filter-label">人员身份:</span>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-type="">全部</button>
                            <button class="filter-btn" data-type="民警">民警</button>
                            <button class="filter-btn" data-type="辅警">辅警</button>
                            <button class="filter-btn" data-type="其他">其他</button>
                        </div>
                    </div>
                    <button id="clearFiltersBtn" class="clear-filter-btn">
                        <span class="clear-icon">✕</span>
                        清除筛选
                    </button>
                </div>

                <!-- 人员容器 -->
                <div class="personnel-container">
                    <div class="personnel-header">
                        <div class="selected-count">
                            <label class="checkbox-container">
                                <input type="checkbox" id="selectAllCheckbox">
                                <span class="checkmark"></span>
                            </label>
                            <span id="selectedCount">已选择 0 人</span>
                        </div>
                    </div>
                    <div class="personnel-list" id="personnelList">
                        <div class="empty-state">请选择单位查看人员</div>
                    </div>
                    <div class="pagination" id="pagination" style="display: none;">
                        <button id="prevPage" class="btn btn-sm">上一页</button>
                        <span id="pageInfo">第 1 页，共 1 页</span>
                        <button id="nextPage" class="btn btn-sm">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text">正在处理...</div>
        </div>
    </div>

    <!-- 消息容器 -->
    <div id="messageContainer" class="message-container"></div>

    <!-- 新建方案模态框 -->
    <div id="createScenarioModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新建模拟方案</h3>
                <span class="close" id="closeCreateModal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="createScenarioForm">
                    <div class="form-group">
                        <label for="scenarioName">方案名称 *</label>
                        <input type="text" id="scenarioName" name="scenario_name" required maxlength="100">
                    </div>
                    <div class="form-group">
                        <label for="scenarioDescription">方案描述</label>
                        <textarea id="scenarioDescription" name="description" rows="4" maxlength="500"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelCreateBtn">取消</button>
                <button type="submit" form="createScenarioForm" class="btn btn-primary">创建</button>
            </div>
        </div>
    </div>

    <!-- 删除方案模态框 -->
    <div id="deleteScenarioModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>删除模拟方案</h3>
                <span class="close" id="closeDeleteModal">&times;</span>
            </div>
            <div class="modal-body">
                <p>确定要删除模拟方案 "<span id="deleteScenarioName"></span>" 吗？</p>
                <p class="text-warning">⚠️ 删除后将无法恢复，请谨慎操作！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 调动模态框 -->
    <div id="transferModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量调动人员</h3>
                <span class="close" id="closeTransferModal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="transferForm">
                    <div class="form-group">
                        <label>选中人员</label>
                        <div id="selectedPersonnelList" class="selected-personnel-list">
                            <!-- 动态填充选中的人员 -->
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="targetUnit">目标单位 *</label>
                        <select id="targetUnit" name="target_unit_id" required>
                            <option value="">请选择目标单位</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="transferReason">调动原因</label>
                        <input type="text" id="transferReason" name="transfer_reason" maxlength="200">
                    </div>
                    <div class="form-group">
                        <label for="transferRemarks">备注</label>
                        <textarea id="transferRemarks" name="remarks" rows="3" maxlength="500"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelTransferBtn">取消</button>
                <button type="submit" form="transferForm" class="btn btn-primary">确认调动</button>
            </div>
        </div>
    </div>

    <!-- 操作日志模态框 -->
    <div id="operationLogsModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>我的操作日志</h3>
                <span class="close" id="closeLogsModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="logs-filters">
                    <div class="filter-row">
                        <div class="filter-item">
                            <label for="logScenarioFilter">方案筛选:</label>
                            <select id="logScenarioFilter" class="filter-select">
                                <option value="">全部方案</option>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label for="logDateFilter">日期筛选:</label>
                            <input type="date" id="logDateFilter" class="filter-input">
                        </div>
                        <div class="filter-item">
                            <button id="searchLogsBtn" class="btn btn-primary">查询</button>
                            <button id="clearLogFiltersBtn" class="btn btn-secondary">清除</button>
                        </div>
                    </div>
                </div>
                <div class="logs-container" id="logsContainer">
                    <div class="loading">正在加载操作日志...</div>
                </div>
                <div class="logs-pagination" id="logsPagination" style="display: none;">
                    <button id="prevLogsPage" class="btn btn-sm">上一页</button>
                    <span id="logsPageInfo">第 1 页，共 1 页</span>
                    <button id="nextLogsPage" class="btn btn-sm">下一页</button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="closeLogsBtn">关闭</button>
                <button type="button" class="btn btn-success" id="exportLogsBtn">导出日志</button>
            </div>
        </div>
    </div>

    <!-- 单位管理模态框 -->
    <div id="unitManageModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>单位管理</h3>
                <span class="close" id="closeUnitManageModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="unit-manage-toolbar">
                    <button id="addUnitBtn" class="btn btn-primary">新增单位</button>
                    <button id="editUnitBtn" class="btn btn-warning" disabled>编辑单位</button>
                    <button id="deleteUnitBtn" class="btn btn-danger" disabled>删除单位</button>
                </div>
                <div class="unit-manage-container" id="unitManageContainer">
                    <div class="loading">正在加载单位信息...</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="closeUnitManageBtn">关闭</button>
            </div>
        </div>
    </div>

    <!-- 单位编辑模态框 -->
    <div id="unitEditModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="unitEditTitle">新增单位</h3>
                <span class="close" id="closeUnitEditModal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="unitEditForm">
                    <input type="hidden" id="editUnitId" name="unit_id">
                    <div class="form-group">
                        <label for="unitName">单位名称 *</label>
                        <input type="text" id="unitName" name="unit_name" required maxlength="100">
                    </div>
                    <div class="form-group">
                        <label for="parentUnit">上级单位</label>
                        <select id="parentUnit" name="parent_id">
                            <option value="">无上级单位</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="unitCode">单位代码</label>
                        <input type="text" id="unitCode" name="code" maxlength="50">
                    </div>
                    <div class="form-group">
                        <label for="sortOrder">排序号</label>
                        <input type="number" id="sortOrder" name="sort_order" min="0" max="9999">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelUnitEditBtn">取消</button>
                <button type="submit" form="unitEditForm" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 统计报表模态框 -->
    <div id="statisticsModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>统计报表</h3>
                <span class="close" id="closeStatisticsModal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="statisticsContainer" class="statistics-container">
                    <div class="loading">正在生成统计报表...</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="closeStatisticsBtn">关闭</button>
                <button type="button" class="btn btn-success" id="exportStatisticsBtn">导出报表</button>
            </div>
        </div>
    </div>

    <!-- 导出数据模态框 -->
    <div id="exportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>导出数据</h3>
                <span class="close" id="closeExportModal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>导出内容</label>
                    <div class="radio-group">
                        <label class="radio-container">
                            <input type="radio" name="export_type" value="personnel" checked>
                            <span class="radio-mark"></span>
                            人员信息
                        </label>
                        <label class="radio-container">
                            <input type="radio" name="export_type" value="units">
                            <span class="radio-mark"></span>
                            单位信息
                        </label>
                        <label class="radio-container">
                            <input type="radio" name="export_type" value="transfers">
                            <span class="radio-mark"></span>
                            调动记录
                        </label>
                        <label class="radio-container">
                            <input type="radio" name="export_type" value="all">
                            <span class="radio-mark"></span>
                            全部数据
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label>导出格式</label>
                    <div class="radio-group">
                        <label class="radio-container">
                            <input type="radio" name="export_format" value="excel" checked>
                            <span class="radio-mark"></span>
                            Excel (.xlsx)
                        </label>
                        <label class="radio-container">
                            <input type="radio" name="export_format" value="csv">
                            <span class="radio-mark"></span>
                            CSV (.csv)
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelExportBtn">取消</button>
                <button type="button" class="btn btn-primary" id="confirmExportBtn">开始导出</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 将用户信息传递给JavaScript
        window.currentUser = <?php echo json_encode($userInfo); ?>;
    </script>
    <script src="assets/js/version.js?v=1.0.9"></script>
    <script src="assets/js/simulation.js?v=1.0.19"></script>
</body>
</html>
