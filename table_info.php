<?php
// 引入配置文件
require_once 'config.php';

// 连接数据库
$conn = new mysqli($servername, $username, $password, $dbname);

// 检查连接
if ($conn->connect_error) {
    die('数据库连接失败: ' . $conn->connect_error);
}

// 获取所有表名
$tablesResult = $conn->query('SHOW TABLES');
$tables = [];
while ($row = $tablesResult->fetch_row()) {
    $tables[] = $row[0];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库表信息</title>
    <style>
        body {
            display: flex;
            margin: 0;
            padding: 0;
        }
        #left-sidebar {
            width: 25%;
            height: 100vh;
            overflow-y: auto;
            border-right: 1px solid #ccc;
            padding: 10px;
        }
        #right-content {
            width: 75%;
            height: 100vh;
            overflow-y: auto;
            padding: 10px;
        }
        .table-item {
            cursor: pointer;
            padding: 5px;
            margin-bottom: 5px;
        }
        .table-item:hover {
            background-color: #f0f0f0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
        }
    </style>
</head>
<body>
    <div id="left-sidebar">
        <h2>所有表</h2>
        <?php foreach ($tables as $table): ?>
            <div class="table-item" onclick="showTableInfo('<?php echo $table; ?>')">
                <?php echo $table; ?>
            </div>
        <?php endforeach; ?>
    </div>
    <div id="right-content">
        <h2>表字段信息</h2>
        <div id="table-fields"></div>
    </div>

    <script>
        function showTableInfo(tableName) {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_table_fields.php?table=' + tableName, true);
            xhr.onreadystatechange = function() {
                if (this.readyState === 4 && this.status === 200) {
                    document.getElementById('table-fields').innerHTML = this.responseText;
                }
            };
            xhr.send();
        }
    </script>
</body>
</html>