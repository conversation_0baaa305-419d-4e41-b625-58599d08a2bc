import{_ as b,c as v,r as n,o as l,w as o,a as s,b as e,d as k,e as y,F as w,f as x,n as B,t as i,g as C,i as F,E}from"./index-C1M1678s.js";/* empty css              */const V={class:"card-container"},I=["onClick"],N={class:"app-info"},P={__name:"Front",setup(r){const c=[{id:1,name:"通讯录",path:"/contacts",desc:"组织架构与人员信息管理",color:"linear-gradient(135deg, #409eff 0%, #66b1ff 100%)"},{id:2,name:"单位管理",path:"/unit",desc:"组织机构与部门管理",color:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)"},{id:3,name:"用户管理",path:"/users",desc:"系统用户账户管理",color:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)"},{id:4,name:"角色管理",path:"/roles",desc:"角色权限配置",color:"linear-gradient(135deg, #fa8c16 0%, #ff7a45 100%)"},{id:5,name:"应用管理",path:"/applications",desc:"子系统接入管理",color:"linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)"},{id:6,name:"授权管理",path:"/auth",desc:"权限分配与审计",color:"linear-gradient(135deg, #faad14 0%, #ffc53d 100%)"},{id:7,name:"值班管理",path:"/duty",desc:"值班计划与排班管理",color:"linear-gradient(135deg, #eb2f96 0%, #ff79c6 100%)"},{id:8,name:"数据大屏",path:"/dashboard",desc:"数据可视化分析",color:"linear-gradient(135deg, #2f54eb 0%, #5c70ff 100%)"}],p=_=>{window.location.href="back.html"};return(_,t)=>{const f=n("el-header"),m=n("el-card"),u=n("el-main"),g=n("el-footer"),h=n("el-container");return l(),v(h,{class:"portal-container"},{default:o(()=>[s(f,{class:"portal-header"},{default:o(()=>t[0]||(t[0]=[e("div",{class:"header-content"},[e("img",{src:k,class:"logo"}),e("h1",null,"云枢应用平台")],-1)])),_:1,__:[0]}),s(u,null,{default:o(()=>[e("div",V,[(l(),y(w,null,x(c,a=>s(m,{key:a.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:o(()=>[e("div",{class:"card-content",onClick:j=>p(a.path)},[e("div",{class:"app-icon",style:B({background:a.color})},null,4),e("div",N,[e("h3",null,i(a.name),1),e("p",null,i(a.desc),1)])],8,I)]),_:2},1024)),64))])]),_:1}),s(g,{class:"portal-footer"},{default:o(()=>t[1]||(t[1]=[e("p",null,"Powered by ：科技通信中队",-1)])),_:1,__:[1]})]),_:1})}}},S=b(P,[["__scopeId","data-v-0d037a49"]]),d=C(S);d.use(F);for(const[r,c]of Object.entries(E))d.component(r,c);d.mount("#app");
