<?php
/**
 * 数据库更新脚本 - 添加uploadIp字段
 * 创建时间: 2025-07-03
 */

require_once 'functions.php';

echo "<!DOCTYPE html>\n";
echo "<html lang='zh-CN'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>数据库更新 - 添加uploadIp字段</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }\n";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
echo "h1 { color: #333; text-align: center; }\n";
echo ".success { color: green; background: #d4edda; padding: 10px; border-radius: 4px; margin: 10px 0; }\n";
echo ".error { color: red; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 10px 0; }\n";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 10px 0; }\n";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<div class='container'>\n";
echo "<h1>🗄️ 数据库更新 - 添加uploadIp字段</h1>\n";

try {
    // 检查字段是否已存在
    echo "<h3>1. 检查uploadIp字段是否存在</h3>\n";
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'application' 
        AND TABLE_NAME = 'filecloud_info' 
        AND COLUMN_NAME = 'uploadIp'
    ");
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "<div class='info'>ℹ️ uploadIp字段已存在，跳过添加步骤</div>\n";
    } else {
        echo "<div class='info'>📝 uploadIp字段不存在，开始添加...</div>\n";
        
        // 添加uploadIp字段
        echo "<h3>2. 添加uploadIp字段</h3>\n";
        $sql = "ALTER TABLE filecloud_info ADD COLUMN uploadIp VARCHAR(45) NULL COMMENT '上传者IP地址' AFTER file_path";
        $pdo->exec($sql);
        echo "<div class='success'>✅ 成功添加uploadIp字段</div>\n";
        echo "<pre>执行的SQL: $sql</pre>\n";
    }
    
    // 检查索引是否存在
    echo "<h3>3. 检查uploadIp索引</h3>\n";
    $stmt = $pdo->query("
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = 'application' 
        AND TABLE_NAME = 'filecloud_info' 
        AND INDEX_NAME = 'idx_upload_ip'
    ");
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "<div class='info'>ℹ️ uploadIp索引已存在</div>\n";
    } else {
        echo "<div class='info'>📝 添加uploadIp索引...</div>\n";
        
        // 添加索引
        $sql = "ALTER TABLE filecloud_info ADD INDEX idx_upload_ip (uploadIp)";
        $pdo->exec($sql);
        echo "<div class='success'>✅ 成功添加uploadIp索引</div>\n";
        echo "<pre>执行的SQL: $sql</pre>\n";
    }
    
    // 显示更新后的表结构
    echo "<h3>4. 验证表结构</h3>\n";
    $stmt = $pdo->query("
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'application' 
        AND TABLE_NAME = 'filecloud_info' 
        AND COLUMN_NAME = 'uploadIp'
    ");
    $fieldInfo = $stmt->fetch();
    
    if ($fieldInfo) {
        echo "<div class='success'>✅ uploadIp字段验证成功</div>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f8f9fa;'><th>属性</th><th>值</th></tr>\n";
        echo "<tr><td>字段名</td><td>{$fieldInfo['COLUMN_NAME']}</td></tr>\n";
        echo "<tr><td>数据类型</td><td>{$fieldInfo['DATA_TYPE']}</td></tr>\n";
        echo "<tr><td>允许NULL</td><td>{$fieldInfo['IS_NULLABLE']}</td></tr>\n";
        echo "<tr><td>默认值</td><td>" . ($fieldInfo['COLUMN_DEFAULT'] ?? 'NULL') . "</td></tr>\n";
        echo "<tr><td>注释</td><td>{$fieldInfo['COLUMN_COMMENT']}</td></tr>\n";
        echo "</table>\n";
    } else {
        echo "<div class='error'>❌ uploadIp字段验证失败</div>\n";
    }
    
    // 显示索引信息
    echo "<h3>5. 验证索引</h3>\n";
    $stmt = $pdo->query("SHOW INDEX FROM filecloud_info WHERE Key_name = 'idx_upload_ip'");
    $indexInfo = $stmt->fetchAll();
    
    if (!empty($indexInfo)) {
        echo "<div class='success'>✅ uploadIp索引验证成功</div>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>\n";
        echo "<tr style='background: #f8f9fa;'><th>索引名</th><th>列名</th><th>排序</th><th>基数</th></tr>\n";
        foreach ($indexInfo as $index) {
            echo "<tr>";
            echo "<td>{$index['Key_name']}</td>";
            echo "<td>{$index['Column_name']}</td>";
            echo "<td>{$index['Collation']}</td>";
            echo "<td>{$index['Cardinality']}</td>";
            echo "</tr>";
        }
        echo "</table>\n";
    } else {
        echo "<div class='info'>ℹ️ uploadIp索引不存在（可选）</div>\n";
    }
    
    // 测试IP获取函数
    echo "<h3>6. 测试IP获取功能</h3>\n";
    $currentIP = getClientRealIP();
    echo "<div class='success'>✅ IP获取功能正常，当前IP: <code>$currentIP</code></div>\n";
    
    echo "<h3>7. 更新完成</h3>\n";
    echo "<div class='success'>\n";
    echo "🎉 数据库更新完成！现在可以进行以下测试：<br>\n";
    echo "• <a href='test_ip_recording.php' target='_blank'>查看IP记录测试页面</a><br>\n";
    echo "• <a href='upload.php' target='_blank'>测试登录用户上传</a><br>\n";
    echo "• <a href='anonymous_upload.php' target='_blank'>测试匿名用户上传</a><br>\n";
    echo "</div>\n";
    
} catch (PDOException $e) {
    echo "<div class='error'>❌ 数据库操作失败: " . htmlspecialchars($e->getMessage()) . "</div>\n";
    echo "<h3>手动执行SQL</h3>\n";
    echo "<p>如果自动更新失败，请手动执行以下SQL语句：</p>\n";
    echo "<pre>\n";
    echo "-- 添加uploadIp字段\n";
    echo "ALTER TABLE filecloud_info ADD COLUMN uploadIp VARCHAR(45) NULL COMMENT '上传者IP地址' AFTER file_path;\n\n";
    echo "-- 添加索引（可选）\n";
    echo "ALTER TABLE filecloud_info ADD INDEX idx_upload_ip (uploadIp);\n";
    echo "</pre>\n";
} catch (Exception $e) {
    echo "<div class='error'>❌ 系统错误: " . htmlspecialchars($e->getMessage()) . "</div>\n";
}

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
