<?php
/**
 * 文件网盘系统 - 主界面
 * 创建时间: 2025-06-23
 */

require_once 'functions.php';

// 检查用户是否已登录
if (!isLoggedIn()) {
    redirect('download.php');
}

$currentUserId = $_SESSION['user_id'];
$currentUsername = $_SESSION['username'] ?? '用户';

// 处理文件删除
if (($_POST['action'] ?? '') === 'del' && isset($_POST['file_id'])) {
    if (validateCsrfToken($_POST['csrf_token'] ?? '')) {
        $fileId = (int)$_POST['file_id'];
        try {
            // 绕过WAF检查
            $stmt = $pdo->prepare("UPDATE filecloud_info SET is_deleted = 1 WHERE file_id = ? AND user_id = ?");
            $stmt->execute([$fileId, $currentUserId]);
            $message = ['type' => 'success', 'text' => '文件删除成功'];
        } catch (PDOException $e) {
            $message = ['type' => 'error', 'text' => '删除失败：' . $e->getMessage()];
        }
    } else {
        $message = ['type' => 'error', 'text' => '无效的请求'];
    }
}

// 获取用户文件列表
try {
    $stmt = $pdo->prepare("
        SELECT file_id, original_name, file_size, file_type, upload_time, 
               share_code, expiration_time, download_count, is_public
        FROM filecloud_info 
        WHERE user_id = ? AND is_deleted = 0 
        ORDER BY upload_time DESC
    ");
    $stmt->execute([$currentUserId]);
    $files = $stmt->fetchAll();
} catch (PDOException $e) {
    $files = [];
    $message = ['type' => 'error', 'text' => '获取文件列表失败'];
}

// 获取存储统计
try {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as file_count, COALESCE(SUM(file_size), 0) as total_size
        FROM filecloud_info 
        WHERE user_id = ? AND is_deleted = 0
    ");
    $stmt->execute([$currentUserId]);
    $stats = $stmt->fetch();
} catch (PDOException $e) {
    $stats = ['file_count' => 0, 'total_size' => 0];
}

$csrfToken = generateCsrfToken();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 我的文件</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .selected-users-area {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 0.25rem;
            min-height: 60px;
            max-height: 200px;
            overflow-y: auto;
            width: 100%;
            box-sizing: border-box;
        }

        .selected-users-container {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        .user-card {
            background: white;
            border-radius: 4px;
            padding: 8px 10px; /* 简化内边距，移除右侧额外空间 */
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
            transition: all 0.2s ease;
            min-height: 32px; /* 减小最小高度，使卡片更紧凑 */
            display: flex;
            align-items: center;
            box-sizing: border-box;
            width: 100%;
            margin-bottom: 0; /* 移除底部边距，因为grid已经有gap了 */
        }
        
        .user-card:hover {
            box-shadow: 0 3px 6px rgba(0,0,0,0.15);
            background-color: #f8f9fa; /* 添加hover时的背景色变化 */
        }
        
        .user-card .user-name {
            font-weight: 500; /* 稍微减轻字体粗细 */
            font-size: 0.85rem; /* 进一步减小字体大小 */
            color: #333;
            white-space: nowrap; /* 不允许文本换行 */
            overflow: hidden;
            text-overflow: ellipsis; /* 文本溢出时显示省略号 */
            line-height: 1.2;
            padding-right: 20px; /* 增加右侧内边距，避免与右上角的关闭按钮重叠 */
        }
        
        /* 移除未使用的btn-close样式，统一使用remove-user样式 */
        
        .user-card .remove-user {
            position: absolute;
            top: 4px;
            right: 4px;
            color: #dc3545; /* 默认使用红色 */
            cursor: pointer;
            padding: 0;
            font-size: 0.7rem;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            opacity: 0.85; /* 默认透明度调高，使红色更醒目 */
            border-radius: 50%;
            border: 1px solid #dc3545; /* 添加红色边框 */
            box-sizing: border-box; /* 确保边框不影响整体尺寸 */
        }
        
        .user-card .remove-user:hover {
            color: #fff;
            background-color: #dc3545;
            border-color: #fff; /* 悬停时边框变为白色 */
            opacity: 1;
            transform: scale(1.1); /* 添加轻微的放大效果 */
            box-shadow: 0 1px 3px rgba(220,53,69,0.3); /* 添加红色阴影效果 */
        }
        
        /* 添加用户卡片的进入和退出动画 */
        .user-card {
            animation: cardEnter 0.2s ease-out;
        }
        
        @keyframes cardEnter {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @media (max-width: 992px) {
            .selected-users-area {
                grid-template-columns: repeat(3, 1fr); /* 平板端3列 */
            }
        }
        
        @media (max-width: 576px) {
            .selected-users-area {
                grid-template-columns: repeat(2, 1fr); /* 移动端2列 */
            }
        }

        /* 确保模态框有足够的宽度 */
        .modal-lg, .modal-xl {
            max-width: 800px; /* 增加模态框的最大宽度 */
            width: 95%; /* 设置宽度为95%，确保在小屏幕上也有足够的宽度 */
            margin: 1.75rem auto;
        }
        
        /* 确保用户卡片中的文本正确显示 */
        .user-card {
            overflow: hidden;
            position: relative;
        }
        
        .user-card .user-name {
            display: block;
            width: calc(100% - 20px); /* 减去关闭按钮的宽度 */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap; /* 不允许文本换行 */
            line-height: 1.2;
            font-size: 0.85rem; /* 减小字体大小，确保短名称不会换行 */
        }
        
        .user-selection-controls select[multiple] {
            height: 150px;
        }
        
        .user-selection-controls select[multiple] option:checked {
            background-color: #0d6efd;
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="bi bi-files me-1"></i>我的文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>上传文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="shared.php">
                            <i class="bi bi-share me-1"></i>与我共享
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <?php if (isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="admin.php">
                            <i class="bi bi-gear me-1"></i>系统管理
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?= h($currentUsername) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 消息提示 -->
        <?php if (isset($message)): ?>
        <div class="alert alert-<?= $message['type'] === 'success' ? 'success' : 'danger' ?> alert-dismissible fade show">
            <?= h($message['text']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-0">文件总数</h5>
                                <h3 class="mb-0"><?= number_format($stats['file_count']) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-files"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-0">存储空间</h5>
                                <h3 class="mb-0"><?= formatFileSize($stats['total_size']) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-hdd"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-0">今日上传</h5>
                                <h3 class="mb-0">
                                    <?php
                                    $todayCount = 0;
                                    foreach ($files as $file) {
                                        if (date('Y-m-d', strtotime($file['upload_time'])) === date('Y-m-d')) {
                                            $todayCount++;
                                        }
                                    }
                                    echo $todayCount;
                                    ?>
                                </h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-calendar-today"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-0">总下载量</h5>
                                <h3 class="mb-0">
                                    <?php
                                    $totalDownloads = array_sum(array_column($files, 'download_count'));
                                    echo number_format($totalDownloads);
                                    ?>
                                </h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-download"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-files me-2"></i>我的文件
                    </h5>
                    <a href="upload.php" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>上传文件
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($files)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-cloud-upload display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">还没有上传任何文件</h5>
                    <p class="text-muted">点击上传文件开始使用文件网盘</p>
                    <a href="upload.php" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>上传第一个文件
                    </a>
                </div>
                <?php else: ?>
                <!-- 批量操作工具栏 -->
                <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
                    <div class="d-flex align-items-center">
                        <div class="form-check me-3">
                            <input class="form-check-input" type="checkbox" id="selectAllFiles">
                            <label class="form-check-label" for="selectAllFiles">
                                全选
                            </label>
                        </div>
                        <span class="text-muted small" id="selectedCountFiles">已选择 0 个文件</span>
                    </div>
                    <div>
                        <button class="btn btn-danger btn-sm" id="batchDeleteFilesBtn" disabled onclick="batchDeleteFiles()">
                            <i class="bi bi-trash me-1"></i>批量删除
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50"></th>
                                <th>文件名</th>
                                <th>大小</th>
                                <th>类型</th>
                                <th>上传时间</th>
                                <th>分享码</th>
                                <th>分享到期时间</th>
                                <th>下载量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($files as $file): ?>
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input file-checkbox-files" type="checkbox"
                                               value="<?= $file['file_id'] ?>"
                                               data-file-name="<?= h($file['original_name']) ?>">
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-file-earmark me-2 text-primary"></i>
                                        <span class="text-truncate" style="max-width: 200px;" title="<?= h($file['original_name']) ?>">
                                            <?= h($file['original_name']) ?>
                                        </span>
                                    </div>
                                </td>
                                <td><?= formatFileSize($file['file_size']) ?></td>
                                <td>
                                    <span class="badge bg-secondary"><?= h(strtoupper($file['file_type'] ?? 'Unknown')) ?></span>
                                </td>
                                <td><?= date('Y-m-d H:i', strtotime($file['upload_time'])) ?></td>
                                <td>
                                    <?php if (!empty($file['share_code'])): ?>
                                        <code class="share-code" data-code="<?= h($file['share_code']) ?>">
                                            <?= h($file['share_code']) ?>
                                        </code>
                                        <button class="btn btn-sm btn-outline-warning ms-1" onclick="regenerateShareCode(<?= $file['file_id'] ?>)" title="重新生成分享码">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="cancelShareCode(<?= $file['file_id'] ?>)" title="取消分享码">
                                            <i class="bi bi-x-circle"></i>
                                        </button>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-outline-primary" onclick="generateShareCode(<?= $file['file_id'] ?>)" title="生成分享码">
                                            <i class="bi bi-plus-circle"></i> 生成
                                        </button>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($file['expiration_time'])): ?>
                                        <span class="text-<?= strtotime($file['expiration_time']) < time() ? 'danger' : 'success' ?>">
                                            <?= date('Y-m-d H:i', strtotime($file['expiration_time'])) ?>
                                        </span>
                                        <?php if (strtotime($file['expiration_time']) < time()): ?>
                                            <small class="text-danger d-block">已过期</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">永不过期</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= number_format($file['download_count']) ?></span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="shareFile(<?= $file['file_id'] ?>)" title="分享给其他用户">
                                            <i class="bi bi-share"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="downloadFile(<?= $file['file_id'] ?>)" title="下载">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteFile(<?= $file['file_id'] ?>)" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 分享文件模态框 -->
    <div class="modal fade" id="shareModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">分享文件给其他用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="shareForm">
                        <input type="hidden" id="shareFileId" name="file_id">
                        <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                        
                        <!-- 已选用户展示区 -->
                        <div class="selected-users-container mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">已选用户 (<span id="selectedCount">0</span>)</h6>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="clearSelected">
                                    清空已选
                                </button>
                            </div>
                            <div id="selectedUsersArea" class="selected-users-area">
                                <!-- 动态生成的用户卡片 -->
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="shareUserId" class="form-label">选择用户</label>
                            <input type="text" class="form-control" id="shareUserSearch" placeholder="输入用户名或单位名称搜索..." autocomplete="off">
                            
                            <!-- 用户选择控件 -->
                            <div class="user-selection-controls mt-2" style="display: none;">
                                <select class="form-select" id="shareUserId" name="user_ids[]" multiple>
                                    <option value="">请从搜索结果中选择用户...</option>
                                </select>
                            </div>
                            
                            <div id="searchResults" class="list-group mt-2" style="display: none;">
                                <!-- 搜索结果将通过JavaScript动态添加 -->
                            </div>
                            <div class="d-flex align-items-center mt-2">
                                <div class="form-check me-2">
                                    <input class="form-check-input" type="checkbox" id="selectAllResults">
                                    <label class="form-check-label" for="selectAllResults">全选/取消全选</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitShare()">确认分享</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除这个文件吗？此操作不可撤销。</p>
                    <form id="deleteForm" method="post" action="">
                        <input type="hidden" name="action" value="del">
                        <input type="hidden" id="deleteFileId" name="file_id">
                        <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量删除确认模态框 -->
    <div class="modal fade" id="batchDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认批量删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        确定要删除选中的文件吗？此操作不可撤销。
                    </div>
                    <div id="selectedFilesList" class="mt-3">
                        <!-- 选中的文件列表将在这里显示 -->
                    </div>
                    <form id="batchDeleteForm" method="post" action="api/batch_delete_files.php">
                        <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                        <input type="hidden" id="batchDeleteFileIds" name="file_ids">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmBatchDeleteBtn" onclick="confirmBatchDeleteFiles()">
                        <i class="bi bi-trash me-1"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>



    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // 全局变量存储用户数据
        let userSearchResults = [];
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化批量文件操作
            initBatchFileOperations();

            // 清空已选按钮事件
            document.getElementById('clearSelected').addEventListener('click', function() {
                clearSelectedUsers();
            });
            
            // 全选功能事件监听
            const selectAllCheckbox = document.getElementById('selectAllResults');
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    console.log('全选状态变化:', this.checked);
                    
                    // 获取所有用户复选框
                    const checkboxes = document.querySelectorAll('#searchResults .form-check-input');
                    if (!checkboxes.length) return;

                    // 更新复选框状态
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });

                    // 更新选择状态
                    updateUserSelections();
                });
            }

            // 统一更新用户选择状态
            function updateUserSelections() {
                try {
                    const checkboxes = document.querySelectorAll('#searchResults .form-check-input');
                    let selectedUsers = [];
                    
                    checkboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            const userId = checkbox.closest('.list-group-item').dataset.userId;
                            const user = userSearchResults.find(u => u.id == userId);
                            if (user) selectedUsers.push(user);
                        }
                    });

                    // 保存到本地存储
                    localStorage.setItem('selectedUsers', JSON.stringify(selectedUsers));
                    console.log('更新后的已选用户:', selectedUsers);
                    
                    // 更新UI
                    updateSelectedUsers();
                    updateSelectAllCheckbox();
                } catch (error) {
                    console.error('更新用户选择出错:', error);
                }
            }

            // 监听选择变化
            document.getElementById('shareUserId').addEventListener('change', function() {
                updateSelectedUsers();
            });
        });
        
        // 用户搜索功能
        document.getElementById('shareUserSearch').addEventListener('input', function(e) {
            const keyword = e.target.value.trim();
            if (keyword.length < 1) {
                document.getElementById('searchResults').style.display = 'none';
                document.querySelector('.user-selection-controls').style.display = 'none';
                return;
            }
            
            const formData = new FormData();
            formData.append('controlCode', 'query');
            formData.append('search_keyword', keyword);
            
            fetch('api/user_manage.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const resultsContainer = document.getElementById('searchResults');
                const selectElement = document.getElementById('shareUserId');
                const selectionControls = document.querySelector('.user-selection-controls');
                
                resultsContainer.innerHTML = '';
                // 保留第一个默认选项
                selectElement.innerHTML = '<option value="">请从搜索结果中选择用户...</option>';
                
                                    if (data.status === 1 && data.data.length > 0) {
                                        // 存储用户数据以供后续使用
                                        userSearchResults = data.data;
                        
                                        data.data.forEach(user => {
                                            // 创建带有复选框的搜索结果项
                                            const resultItem = document.createElement('div');
                                            resultItem.className = 'list-group-item';
                                            resultItem.dataset.userId = user.id;
                            
                                            resultItem.innerHTML = `
                                                <div class="form-check d-flex align-items-center">
                                                    <input type="checkbox" class="form-check-input me-2" 
                                                           id="user-${user.id}" 
                                                           onchange="toggleUserSelection(${user.id}, this.checked)">
                                                    <label class="form-check-label flex-grow-1" for="user-${user.id}">
                                                        ${user.name}（${user.organization_unitName}）
                                                    </label>
                                                </div>
                                            `;
                                            resultsContainer.appendChild(resultItem);
                                        });
                    
                    resultsContainer.style.display = 'block';
                } else {
                    const noResult = document.createElement('div');
                    noResult.className = 'list-group-item text-muted';
                    noResult.textContent = '未找到匹配的用户';
                    resultsContainer.appendChild(noResult);
                    resultsContainer.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('搜索用户失败:', error);
            });
        });
        
        // 更新已选用户区域
        function updateSelectedUsers() {
                    const selectedUsersArea = document.getElementById('selectedUsersArea');
                    const selectedCount = document.getElementById('selectedCount');
            
                    // 从localStorage获取已选用户
                    const selectedUsers = JSON.parse(localStorage.getItem('selectedUsers') || '[]');
            
                    // 更新计数
                    selectedCount.textContent = selectedUsers.length;
            
                    // 清空已选区域
                    selectedUsersArea.innerHTML = '';
            
                    // 添加用户卡片
                    selectedUsers.forEach(user => {
                        const userId = user.id;
                        const userName = user.name.split('（')[0]; // 只显示用户名，不显示单位
                
                const userCard = document.createElement('div');
                userCard.className = 'user-card';
                userCard.dataset.userId = userId;
                
                userCard.innerHTML = `
                    <div class="user-name">${userName}</div>
                    <button type="button" class="remove-user" onclick="removeUser(${userId})" title="移除">
                        <i class="bi bi-x"></i>
                    </button>
                `;
                
                // 添加进入动画
                userCard.style.opacity = '0';
                userCard.style.transform = 'translateY(10px)';
                selectedUsersArea.appendChild(userCard);
                
                // 触发重排以应用动画
                setTimeout(() => {
                    userCard.style.opacity = '1';
                    userCard.style.transform = 'translateY(0)';
                }, 10);
            });
            
            // 如果没有选中的用户，显示提示
            if (selectedUsers.length === 0) {
                selectedUsersArea.innerHTML = '<div class="text-muted text-center py-3" style="white-space: nowrap;">未选择任何用户</div>';
            }
        }
        
        // 移除已选用户
        function removeSelectedUser(userId) {
            const selectElement = document.getElementById('shareUserId');
            const option = selectElement.querySelector(`option[value="${userId}"]`);
            const userCard = document.querySelector(`.user-card[data-user-id="${userId}"]`);
            
            if (userCard) {
                // 添加移除动画
                userCard.style.opacity = '0';
                userCard.style.transform = 'translateY(10px)';
                
                // 等待动画完成后更新选择状态
                setTimeout(() => {
                    if (option) {
                        option.selected = false;
                    }
                    updateSelectedUsers();
                }, 200);
            } else {
                // 如果找不到卡片元素，直接更新
                if (option) {
                    option.selected = false;
                }
                updateSelectedUsers();
            }
        }
        
        // 清空所有已选用户
        function clearSelectedUsers() {
            // 清除本地存储的已选用户
            localStorage.setItem('selectedUsers', '[]');
            
            // 取消所有选择框的选中状态
            const checkboxes = document.querySelectorAll('#searchResults .form-check-input');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // 取消全选选择框
            document.getElementById('selectAllResults').checked = false;
            
            // 更新UI
            updateSelectedUsers();
            updateSelectAllCheckbox();
        }
        // 切换用户选择状态
        function toggleUserSelection(userId, isChecked) {
            const selectedUsers = JSON.parse(localStorage.getItem('selectedUsers') || '[]');
            
            if (isChecked) {
                // 添加用户到已选列表
                const user = userSearchResults.find(u => u.id == userId);
                if (user && !selectedUsers.some(u => u.id == userId)) {
                    selectedUsers.push(user);
                }
            } else {
                // 从已选列表中移除用户
                const index = selectedUsers.findIndex(u => u.id == userId);
                if (index !== -1) {
                    selectedUsers.splice(index, 1);
                }
            }
            
            localStorage.setItem('selectedUsers', JSON.stringify(selectedUsers));
            updateSelectedUsers();
            
            // 更新全选按钮状态
            updateSelectAllCheckbox();
        }
        
        // 全选/取消全选搜索结果
        function toggleAllSearchResults() {
            const selectAllCheckbox = document.getElementById('selectAllResults');
            const isChecked = selectAllCheckbox.checked;
            const checkboxes = document.querySelectorAll('#searchResults .form-check-input');
            
            // 更新所有用户选择框状态
            checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const userId = checkbox.closest('.list-group-item').dataset.userId;
                
                // 更新选择状态
                const selectedUsers = JSON.parse(localStorage.getItem('selectedUsers') || '[]');
                const userIndex = selectedUsers.findIndex(u => u.id == userId);
                
                if (isChecked && userIndex === -1) {
                    const user = userSearchResults.find(u => u.id == userId);
                    if (user) selectedUsers.push(user);
                } else if (!isChecked && userIndex !== -1) {
                    selectedUsers.splice(userIndex, 1);
                }
                
                localStorage.setItem('selectedUsers', JSON.stringify(selectedUsers));
            });
            
            // 更新UI
            updateSelectedUsers();
        }
        
        // 更新全选按钮状态
        function updateSelectAllCheckbox() {
            const checkboxes = document.querySelectorAll('#searchResults .form-check-input');
            const selectAllCheckbox = document.getElementById('selectAllResults');
            
            if (checkboxes.length === 0) {
                selectAllCheckbox.checked = false;
                return;
            }
            
            const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
            selectAllCheckbox.checked = allChecked;
        }
        
        // 移除用户
        function removeUser(userId) {
            toggleUserSelection(userId, false);
        }
        
        function copyShareCode(code) {
            if (!navigator.clipboard) {
                showToast('浏览器不支持剪贴板API，请手动复制', 'error');
                return;
            }
            navigator.clipboard.writeText(code).then(() => {
                showToast('分享码已复制到剪贴板', 'success');
            }).catch(() => {
                showToast('复制失败，请手动复制', 'error');
            });
        }

        // 生成分享码
        function generateShareCode(fileId) {
            if (!confirm('确定要为此文件生成分享码吗？')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'generate_share_code');
            formData.append('file_id', fileId);
            formData.append('csrf_token', '<?= generateCsrfToken() ?>');

            fetch('api/share_code_manage.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('分享码生成成功', 'success');
                    location.reload(); // 刷新页面显示新的分享码
                } else {
                    showToast(data.message || '生成分享码失败', 'error');
                }
            })
            .catch(error => {
                console.error('生成分享码失败:', error);
                showToast('生成分享码失败，请稍后重试', 'error');
            });
        }

        // 重新生成分享码
        function regenerateShareCode(fileId) {
            if (!confirm('确定要重新生成分享码吗？这将覆盖原有的分享码和到期时间。')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'regenerate_share_code');
            formData.append('file_id', fileId);
            formData.append('csrf_token', '<?= generateCsrfToken() ?>');

            fetch('api/share_code_manage.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('分享码重新生成成功', 'success');
                    location.reload(); // 刷新页面显示新的分享码
                } else {
                    showToast(data.message || '重新生成分享码失败', 'error');
                }
            })
            .catch(error => {
                console.error('重新生成分享码失败:', error);
                showToast('重新生成分享码失败，请稍后重试', 'error');
            });
        }

        // 取消分享码
        function cancelShareCode(fileId) {
            if (!confirm('确定要取消分享码吗？这将删除分享码并设置文件为私有。')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'cancel_share_code');
            formData.append('file_id', fileId);
            formData.append('csrf_token', '<?= generateCsrfToken() ?>');

            fetch('api/share_code_manage.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('分享码已取消', 'success');
                    location.reload(); // 刷新页面显示更新后的状态
                } else {
                    showToast(data.message || '取消分享码失败', 'error');
                }
            })
            .catch(error => {
                console.error('取消分享码失败:', error);
                showToast('取消分享码失败，请稍后重试', 'error');
            });
        }

        function shareFile(fileId) {
            document.getElementById('shareFileId').value = fileId;
            new bootstrap.Modal(document.getElementById('shareModal')).show();
        }

        function downloadFile(fileId) {
            // 使用现有的下载机制，通过API获取文件信息然后下载
            const formData = new FormData();
            formData.append('action', 'get_file_info');
            formData.append('file_id', fileId);
            formData.append('csrf_token', '<?= generateCsrfToken() ?>');

            // 创建下载表单
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'api/download_file.php';
            form.style.display = 'none';

            const fileIdInput = document.createElement('input');
            fileIdInput.type = 'hidden';
            fileIdInput.name = 'file_id';
            fileIdInput.value = fileId;
            form.appendChild(fileIdInput);

            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = '<?= generateCsrfToken() ?>';
            form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);


        }

        function deleteFile(fileId) {
            document.getElementById('deleteFileId').value = fileId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function confirmDelete() {
            document.getElementById('deleteForm').submit();
        }

        function submitShare() {
            const form = document.getElementById('shareForm');
            const formData = new FormData(form);
            
            // 从localStorage获取已选用户
            const selectedUsers = JSON.parse(localStorage.getItem('selectedUsers') || '[]');
            
            // 清除之前的用户ID
            formData.delete('user_ids[]');
            
            // 添加所有选中的用户ID
            selectedUsers.forEach(user => {
                formData.append('user_ids[]', user.id);
            });
            
            // 如果没有选择用户，显示错误
            if (selectedUsers.length === 0) {
                showToast('请至少选择一个用户进行分享', 'error');
                return;
            }
            
            fetch('api/share.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('shareModal')).hide();
                    
                    // 重置表单
                    clearSelectedUsers();
                    document.getElementById('shareUserSearch').value = '';
                    document.querySelector('.user-selection-controls').style.display = 'none';
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('分享失败:', error);
                showToast('操作失败，请稍后重试', 'error');
            });
        }

        function showToast(message, type) {
            // 简单的提示实现
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', alertHtml);

            // 3秒后自动消失
            setTimeout(() => {
                const alert = document.querySelector('.alert:last-child');
                if (alert) {
                    bootstrap.Alert.getOrCreateInstance(alert).close();
                }
            }, 3000);
        }

        // 批量操作功能
        function initBatchFileOperations() {
            const selectAllFiles = document.getElementById('selectAllFiles');
            const fileCheckboxes = document.querySelectorAll('.file-checkbox-files');
            const batchDeleteBtn = document.getElementById('batchDeleteFilesBtn');
            const selectedCount = document.getElementById('selectedCountFiles');

            if (!selectAllFiles || fileCheckboxes.length === 0) return;

            // 同步主复选框 - 仅工具栏版本
            function syncSelectAll() {
                const checkedCount = Array.from(fileCheckboxes).filter(cb => cb.checked).length;
                const allChecked = checkedCount === fileCheckboxes.length && fileCheckboxes.length > 0;
                const someChecked = checkedCount > 0;

                selectAllFiles.checked = allChecked;
                selectAllFiles.indeterminate = someChecked && !allChecked;

                selectedCount.textContent = `已选择 ${checkedCount} 个文件`;
                batchDeleteBtn.disabled = !someChecked;
            }

            const toggleAll = (checked) => {
                fileCheckboxes.forEach(cb => cb.checked = checked);
                syncSelectAll();
            };

            selectAllFiles.addEventListener('change', () => toggleAll(selectAllFiles.checked));
            fileCheckboxes.forEach(cb => cb.addEventListener('change', syncSelectAll));

            syncSelectAll();
        }

        // 批量删除文件
        function batchDeleteFiles() {
            const checkedBoxes = document.querySelectorAll('.file-checkbox-files:checked');
            if (checkedBoxes.length === 0) {
                showToast('请选择要删除的文件', 'error');
                return;
            }

            const fileIds = Array.from(checkedBoxes).map(cb => cb.value);
            const fileNames = Array.from(checkedBoxes).map(cb => cb.dataset.fileName);

            showBatchDeleteModal(fileIds, fileNames);
        }

        // 显示批量删除确认模态框
        function showBatchDeleteModal(fileIds, fileNames) {
            document.getElementById('batchDeleteFileIds').value = fileIds.join(',');

            // 显示选中的文件列表
            const filesList = document.getElementById('selectedFilesList');
            filesList.innerHTML = `
                <h6>选中的文件 (${fileNames.length} 个):</h6>
                <ul class="list-group list-group-flush">
                    ${fileNames.map(name => `<li class="list-group-item py-1 px-0"><i class="bi bi-file-earmark me-2"></i>${name}</li>`).join('')}
                </ul>
            `;

            new bootstrap.Modal(document.getElementById('batchDeleteModal')).show();
        }

        // 确认批量删除
        function confirmBatchDeleteFiles() {
            const form = document.getElementById('batchDeleteForm');
            const formData = new FormData(form);

            // 禁用确认按钮防止重复提交
            const confirmBtn = document.getElementById('confirmBatchDeleteBtn');
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>处理中...';

            fetch('api/batch_delete_files.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    // 关闭模态框并刷新页面
                    bootstrap.Modal.getInstance(document.getElementById('batchDeleteModal')).hide();
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showToast(data.message, 'error');
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '<i class="bi bi-trash me-1"></i>确认删除';
                }
            })
            .catch(error => {
                showToast('操作失败，请重试', 'error');
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="bi bi-trash me-1"></i>确认删除';
            });
        }
    </script>
</body>
</html>