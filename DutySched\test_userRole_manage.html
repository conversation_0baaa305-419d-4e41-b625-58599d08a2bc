<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户角色管理测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .operation-select {
            width: 100%;
            padding: 8px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }

        button:hover {
            background-color: #45a049;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .data-list {
            margin-top: 15px;
            border-collapse: collapse;
            width: 100%;
            display: none;
        }

        .data-list th, .data-list td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .data-list tr:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>用户角色管理测试页面</h2>
        
        <select class="operation-select" id="operationSelect">
            <option value="query">查询</option>
            <option value="add">新增</option>
            <option value="modify">修改</option>
            <option value="del">删除</option>
        </select>

        <div id="formContainer" style="display: none;">
            <!-- 新增表单 -->
            <div id="addModifyForm">
                <div class="form-group">
                    <label for="userId">用户ID:</label>
                    <input type="text" id="userId" placeholder="请输入用户ID">
                    <label for="roleId">角色ID:</label>
                    <input type="text" id="roleId" placeholder="请输入角色ID">
                    <label for="unitId">单位ID:</label>
                    <input type="text" id="unitId" placeholder="请输入单位ID">
                    <label for="appId">应用ID:</label>
                    <input type="text" id="appId" placeholder="请输入应用ID">
                </div>
            </div>

            <!-- 修改表单 -->
            <div id="modifyForm">
                <div class="form-group">
                    <label for="id">ID:</label>
                    <input type="text" id="id" placeholder="请输入ID">
                </div>
                <div class="form-group">
                    <label for="newUserId">新用户ID:</label>
                    <input type="text" id="newUserId" placeholder="请输入新用户ID">
                    <label for="newRoleId">新角色ID:</label>
                    <input type="text" id="newRoleId" placeholder="请输入新角色ID">
                    <label for="newUnitId">新单位ID:</label>
                    <input type="text" id="newUnitId" placeholder="请输入新单位ID">
                    <label for="newAppId">新应用ID:</label>
                    <input type="text" id="newAppId" placeholder="请输入新应用ID">
                </div>
            </div>

            <!-- 删除表单 -->
            <div id="deleteForm">
                <div class="form-group">
                    <label for="deleteId">ID:</label>
                    <input type="text" id="deleteId" placeholder="请输入ID">
                </div>
            </div>
        </div>

        <button>执行操作</button>

        <div class="result" id="resultArea">
            <!-- 查询结果将显示在这里 -->
        </div>

        <div id="dataList" class="data-list" style="display: none;">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户ID</th>
                    <th>角色ID</th>
                    <th>单位ID</th>
                    <th>应用ID</th>
                </tr>
            </thead>
            <tbody id="dataBody"></tbody>
        </div>
    </div>

    <script>
        // 绑定下拉框的change事件，显示相应的表单
        document.getElementById('operationSelect').addEventListener('change', showFormBasedOnOperation);

        // 绑定按钮的click事件，负责执行操作
        const button = document.querySelector('button');
        if (!button.dataset.isBound) {
            button.addEventListener('click', executeOperation);
            button.dataset.isBound = 'true';
        }

        let isRequestInProgress = false;

        function showFormBasedOnOperation() {
            const operation = document.getElementById('operationSelect').value;
            const formContainer = document.getElementById('formContainer');
            const addModifyForm = document.getElementById('addModifyForm');
            const modifyForm = document.getElementById('modifyForm');
            const deleteForm = document.getElementById('deleteForm');

            // 初始化时隐藏所有表单
            formContainer.style.display = 'none';
            addModifyForm.style.display = 'none';
            modifyForm.style.display = 'none';
            deleteForm.style.display = 'none';

            // 根据操作类型显示相应的表单
            switch(operation) {
                case 'add':
                    formContainer.style.display = 'block';
                    addModifyForm.style.display = 'block';
                    break;
                case 'modify':
                    formContainer.style.display = 'block';
                    modifyForm.style.display = 'block';
                    break;
                case 'del':
                    formContainer.style.display = 'block';
                    deleteForm.style.display = 'block';
                    break;
                case 'query':
                    // 查询时不需要表单
                    formContainer.style.display = 'none';
                    break;
            }
        }

        function executeOperation() {
            const operationSelect = document.getElementById('operationSelect');
            const controlCode = operationSelect.value;
            //console.log('controlCode:', controlCode);
            if (!controlCode || ![ 'add', 'modify', 'del', 'query' ].includes(controlCode)) {
                alert('请选择有效的操作类型');
                return;
            }
            const formData = {
                controlCode: controlCode
            };

            // 根据操作类型获取参数
            switch(controlCode) {
                case 'add':
                    const userId = document.getElementById('userId').value;
                    const roleId = document.getElementById('roleId').value;
                    const unitId = document.getElementById('unitId').value;
                    const appId = document.getElementById('appId').value;
                    
                    if(!userId || !roleId || !unitId || !appId) {
                        alert('所有字段都必须填写');
                        return;
                    }
                    
                    formData.userId = userId;
                    formData.roleId = roleId;
                    formData.unitId = unitId;
                    formData.appId = appId;
                    break;
                case 'modify':
                    const id = document.getElementById('id').value;
                    const newUserId = document.getElementById('newUserId').value;
                    const newRoleId = document.getElementById('newRoleId').value;
                    const newUnitId = document.getElementById('newUnitId').value;
                    const newAppId = document.getElementById('newAppId').value;
                    
                    if(!id || !newUserId || !newRoleId || !newUnitId || !newAppId) {
                        alert('所有字段都必须填写');
                        return;
                    }
                    
                    formData.id = id;
                    formData.userId = newUserId;
                    formData.roleId = newRoleId;
                    formData.unitId = newUnitId;
                    formData.appId = newAppId;
                    break;
                case 'del':
                    const deleteId = document.getElementById('deleteId').value;
                    
                    if(!deleteId) {
                        alert('请输入要删除的ID');
                        return;
                    }
                    
                    formData.id = deleteId;
                    break;
                case 'query':
                    // 查询时不需要额外参数
                    break;
            }

            if (isRequestInProgress) return;
            isRequestInProgress = true;

            // 发送请求
            fetch('../api/user_Role_manage.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => {
                //console.log('Server response:', data);
                const resultArea = document.getElementById('resultArea');
                if (!resultArea) {
                    console.error('找不到resultArea元素');
                    return;
                }
                if (!data || !data.status || !data.message) {
                    console.log('服务器返回数据格式错误:', data);
                    return;
                }
                console.log(data.message);
                console.log(data.status === 1 ? '操作成功' : '操作失败');

                // 显示完整的服务器响应
                //resultArea.innerHTML = '<h3>服务器响应:</h3>';
                //resultArea.innerHTML += `<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">${JSON.stringify(data, null, 2)}</pre>`;
                resultArea.innerHTML = `
                    <p>${data.message}</p>
                    ${data.status === 1 ? '<p style="color: green;">成功</p>' : '<p style="color: red;">失败</p>'}
                `;
                if (data.status === 1) {
                    if (data.data && data.data.length > 0) {
                        // 显示纯文本数据
                        let textOutput = '<h3>数据列表:</h3>';
                        data.data.forEach(item => {
                            textOutput += `ID: ${item.id}, 用户ID: ${item.userId}, 角色ID: ${item.roleId}, 单位ID: ${item.unitId}, 应用ID: ${item.appId}<br>`;
                        });
                        resultArea.innerHTML += textOutput;
                    } else {
                        // 所有操作的成功提示
                        resultArea.innerHTML += `<p style="color: green; font-weight: bold;">${data.message}</p>`;
                    }
                } else {
                    // 操作失败的提示
                    resultArea.innerHTML += `<p style="color: red; font-weight: bold;">${data.message}</p>`;
                }
            })
            .catch(error => {
                //console.error('Error:', error);
                const resultArea = document.getElementById('resultArea');
                resultArea.innerHTML = '<p style="color: red;">操作失败</p>';
                resultArea.innerHTML += `<p>详细错误信息: ${error.message}</p>`;
            })
            .finally(() => {
                isRequestInProgress = false;
            });
        }
    </script>
</body>
</html>