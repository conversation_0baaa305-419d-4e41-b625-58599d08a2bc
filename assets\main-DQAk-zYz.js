import{s as Fa,d as Eo,u as de,a as za,c as Ee,p as Vn,r as N,w as ut,h as Ro,n as an,i as dt,b as Ue,_ as rt,o as Qe,e as ne,f as ae,g as W,j as f,k as v,l as F,m as qa,F as fe,q as Q,t as Ya,v as Ce,x as re,y as ge,z as me,A as Ba,B as Vo,C as Sr,D as kr,E as G,G as Ha,H as Pt,I as ur,J as $o,K as bn,L as Yt,M as Po,N as Do,O as ft,P as Xa,Q as Ga,R as Ka,S as Wa,T as Qa,U as ja,V as Ja}from"./index-D8OmjUoy.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const lt=typeof document<"u";function Io(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Za(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Io(e.default)}const ue=Object.assign;function $n(e,t){const n={};for(const r in t){const o=t[r];n[r]=Te(o)?o.map(e):e(o)}return n}const Dt=()=>{},Te=Array.isArray,Mo=/#/g,el=/&/g,tl=/\//g,nl=/=/g,rl=/\?/g,Ao=/\+/g,ol=/%5B/g,al=/%5D/g,No=/%5E/g,ll=/%60/g,Uo=/%7B/g,il=/%7C/g,To=/%7D/g,sl=/%20/g;function cr(e){return encodeURI(""+e).replace(il,"|").replace(ol,"[").replace(al,"]")}function ul(e){return cr(e).replace(Uo,"{").replace(To,"}").replace(No,"^")}function On(e){return cr(e).replace(Ao,"%2B").replace(sl,"+").replace(Mo,"%23").replace(el,"%26").replace(ll,"`").replace(Uo,"{").replace(To,"}").replace(No,"^")}function cl(e){return On(e).replace(nl,"%3D")}function dl(e){return cr(e).replace(Mo,"%23").replace(rl,"%3F")}function fl(e){return e==null?"":dl(e).replace(tl,"%2F")}function Mt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const pl=/\/$/,hl=e=>e.replace(pl,"");function Pn(e,t,n="/"){let r,o={},a="",l="";const s=t.indexOf("#");let c=t.indexOf("?");return s<c&&s>=0&&(c=-1),c>-1&&(r=t.slice(0,c),a=t.slice(c+1,s>-1?s:t.length),o=e(a)),s>-1&&(r=r||t.slice(0,s),l=t.slice(s,t.length)),r=gl(r??t,n),{fullPath:r+(a&&"?")+a+l,path:r,query:o,hash:Mt(l)}}function ml(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Cr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function _l(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&pt(t.matched[r],n.matched[o])&&Oo(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function pt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Oo(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!vl(e[n],t[n]))return!1;return!0}function vl(e,t){return Te(e)?Er(e,t):Te(t)?Er(t,e):e===t}function Er(e,t){return Te(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function gl(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let a=n.length-1,l,s;for(l=0;l<r.length;l++)if(s=r[l],s!==".")if(s==="..")a>1&&a--;else break;return n.slice(0,a).join("/")+"/"+r.slice(l).join("/")}const Ge={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var At;(function(e){e.pop="pop",e.push="push"})(At||(At={}));var It;(function(e){e.back="back",e.forward="forward",e.unknown=""})(It||(It={}));function yl(e){if(!e)if(lt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),hl(e)}const wl=/^[^#]+#/;function bl(e,t){return e.replace(wl,"#")+t}function xl(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const xn=()=>({left:window.scrollX,top:window.scrollY});function Sl(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=xl(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Rr(e,t){return(history.state?history.state.position-t:-1)+e}const Ln=new Map;function kl(e,t){Ln.set(e,t)}function Cl(e){const t=Ln.get(e);return Ln.delete(e),t}let El=()=>location.protocol+"//"+location.host;function Lo(e,t){const{pathname:n,search:r,hash:o}=t,a=e.indexOf("#");if(a>-1){let s=o.includes(e.slice(a))?e.slice(a).length:1,c=o.slice(s);return c[0]!=="/"&&(c="/"+c),Cr(c,"")}return Cr(n,e)+r+o}function Rl(e,t,n,r){let o=[],a=[],l=null;const s=({state:d})=>{const h=Lo(e,location),w=n.value,D=t.value;let _=0;if(d){if(n.value=h,t.value=d,l&&l===w){l=null;return}_=D?d.position-D.position:0}else r(h);o.forEach(C=>{C(n.value,w,{delta:_,type:At.pop,direction:_?_>0?It.forward:It.back:It.unknown})})};function c(){l=n.value}function p(d){o.push(d);const h=()=>{const w=o.indexOf(d);w>-1&&o.splice(w,1)};return a.push(h),h}function i(){const{history:d}=window;d.state&&d.replaceState(ue({},d.state,{scroll:xn()}),"")}function u(){for(const d of a)d();a=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",i)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",i,{passive:!0}),{pauseListeners:c,listen:p,destroy:u}}function Vr(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?xn():null}}function Vl(e){const{history:t,location:n}=window,r={value:Lo(e,n)},o={value:t.state};o.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(c,p,i){const u=e.indexOf("#"),d=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+c:El()+e+c;try{t[i?"replaceState":"pushState"](p,"",d),o.value=p}catch(h){console.error(h),n[i?"replace":"assign"](d)}}function l(c,p){const i=ue({},t.state,Vr(o.value.back,c,o.value.forward,!0),p,{position:o.value.position});a(c,i,!0),r.value=c}function s(c,p){const i=ue({},o.value,t.state,{forward:c,scroll:xn()});a(i.current,i,!0);const u=ue({},Vr(r.value,c,null),{position:i.position+1},p);a(c,u,!1),r.value=c}return{location:r,state:o,push:s,replace:l}}function $l(e){e=yl(e);const t=Vl(e),n=Rl(e,t.state,t.location,t.replace);function r(a,l=!0){l||n.pauseListeners(),history.go(a)}const o=ue({location:"",base:e,go:r,createHref:bl.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Pl(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),$l(e)}function Dl(e){return typeof e=="string"||e&&typeof e=="object"}function Fo(e){return typeof e=="string"||typeof e=="symbol"}const zo=Symbol("");var $r;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})($r||($r={}));function ht(e,t){return ue(new Error,{type:e,[zo]:!0},t)}function Ye(e,t){return e instanceof Error&&zo in e&&(t==null||!!(e.type&t))}const Pr="[^/]+?",Il={sensitive:!1,strict:!1,start:!0,end:!0},Ml=/[.+*?^${}()[\]/\\]/g;function Al(e,t){const n=ue({},Il,t),r=[];let o=n.start?"^":"";const a=[];for(const p of e){const i=p.length?[]:[90];n.strict&&!p.length&&(o+="/");for(let u=0;u<p.length;u++){const d=p[u];let h=40+(n.sensitive?.25:0);if(d.type===0)u||(o+="/"),o+=d.value.replace(Ml,"\\$&"),h+=40;else if(d.type===1){const{value:w,repeatable:D,optional:_,regexp:C}=d;a.push({name:w,repeatable:D,optional:_});const k=C||Pr;if(k!==Pr){h+=10;try{new RegExp(`(${k})`)}catch(U){throw new Error(`Invalid custom RegExp for param "${w}" (${k}): `+U.message)}}let A=D?`((?:${k})(?:/(?:${k}))*)`:`(${k})`;u||(A=_&&p.length<2?`(?:/${A})`:"/"+A),_&&(A+="?"),o+=A,h+=20,_&&(h+=-8),D&&(h+=-20),k===".*"&&(h+=-50)}i.push(h)}r.push(i)}if(n.strict&&n.end){const p=r.length-1;r[p][r[p].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,n.sensitive?"":"i");function s(p){const i=p.match(l),u={};if(!i)return null;for(let d=1;d<i.length;d++){const h=i[d]||"",w=a[d-1];u[w.name]=h&&w.repeatable?h.split("/"):h}return u}function c(p){let i="",u=!1;for(const d of e){(!u||!i.endsWith("/"))&&(i+="/"),u=!1;for(const h of d)if(h.type===0)i+=h.value;else if(h.type===1){const{value:w,repeatable:D,optional:_}=h,C=w in p?p[w]:"";if(Te(C)&&!D)throw new Error(`Provided param "${w}" is an array but it is not repeatable (* or + modifiers)`);const k=Te(C)?C.join("/"):C;if(!k)if(_)d.length<2&&(i.endsWith("/")?i=i.slice(0,-1):u=!0);else throw new Error(`Missing required param "${w}"`);i+=k}}return i||"/"}return{re:l,score:r,keys:a,parse:s,stringify:c}}function Nl(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function qo(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const a=Nl(r[n],o[n]);if(a)return a;n++}if(Math.abs(o.length-r.length)===1){if(Dr(r))return 1;if(Dr(o))return-1}return o.length-r.length}function Dr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ul={type:0,value:""},Tl=/[a-zA-Z0-9_]/;function Ol(e){if(!e)return[[]];if(e==="/")return[[Ul]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${n})/"${p}": ${h}`)}let n=0,r=n;const o=[];let a;function l(){a&&o.push(a),a=[]}let s=0,c,p="",i="";function u(){p&&(n===0?a.push({type:0,value:p}):n===1||n===2||n===3?(a.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${p}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:p,regexp:i,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),p="")}function d(){p+=c}for(;s<e.length;){if(c=e[s++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(p&&u(),l()):c===":"?(u(),n=1):d();break;case 4:d(),n=r;break;case 1:c==="("?n=2:Tl.test(c)?d():(u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--);break;case 2:c===")"?i[i.length-1]=="\\"?i=i.slice(0,-1)+c:n=3:i+=c;break;case 3:u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--,i="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${p}"`),u(),l(),o}function Ll(e,t,n){const r=Al(Ol(e.path),n),o=ue(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Fl(e,t){const n=[],r=new Map;t=Nr({strict:!1,end:!0,sensitive:!1},t);function o(u){return r.get(u)}function a(u,d,h){const w=!h,D=Mr(u);D.aliasOf=h&&h.record;const _=Nr(t,u),C=[D];if("alias"in u){const U=typeof u.alias=="string"?[u.alias]:u.alias;for(const H of U)C.push(Mr(ue({},D,{components:h?h.record.components:D.components,path:H,aliasOf:h?h.record:D})))}let k,A;for(const U of C){const{path:H}=U;if(d&&H[0]!=="/"){const q=d.record.path,O=q[q.length-1]==="/"?"":"/";U.path=d.record.path+(H&&O+H)}if(k=Ll(U,d,_),h?h.alias.push(k):(A=A||k,A!==k&&A.alias.push(k),w&&u.name&&!Ar(k)&&l(u.name)),Yo(k)&&c(k),D.children){const q=D.children;for(let O=0;O<q.length;O++)a(q[O],k,h&&h.children[O])}h=h||k}return A?()=>{l(A)}:Dt}function l(u){if(Fo(u)){const d=r.get(u);d&&(r.delete(u),n.splice(n.indexOf(d),1),d.children.forEach(l),d.alias.forEach(l))}else{const d=n.indexOf(u);d>-1&&(n.splice(d,1),u.record.name&&r.delete(u.record.name),u.children.forEach(l),u.alias.forEach(l))}}function s(){return n}function c(u){const d=Yl(u,n);n.splice(d,0,u),u.record.name&&!Ar(u)&&r.set(u.record.name,u)}function p(u,d){let h,w={},D,_;if("name"in u&&u.name){if(h=r.get(u.name),!h)throw ht(1,{location:u});_=h.record.name,w=ue(Ir(d.params,h.keys.filter(A=>!A.optional).concat(h.parent?h.parent.keys.filter(A=>A.optional):[]).map(A=>A.name)),u.params&&Ir(u.params,h.keys.map(A=>A.name))),D=h.stringify(w)}else if(u.path!=null)D=u.path,h=n.find(A=>A.re.test(D)),h&&(w=h.parse(D),_=h.record.name);else{if(h=d.name?r.get(d.name):n.find(A=>A.re.test(d.path)),!h)throw ht(1,{location:u,currentLocation:d});_=h.record.name,w=ue({},d.params,u.params),D=h.stringify(w)}const C=[];let k=h;for(;k;)C.unshift(k.record),k=k.parent;return{name:_,path:D,params:w,matched:C,meta:ql(C)}}e.forEach(u=>a(u));function i(){n.length=0,r.clear()}return{addRoute:a,resolve:p,removeRoute:l,clearRoutes:i,getRoutes:s,getRecordMatcher:o}}function Ir(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Mr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:zl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function zl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Ar(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ql(e){return e.reduce((t,n)=>ue(t,n.meta),{})}function Nr(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Yl(e,t){let n=0,r=t.length;for(;n!==r;){const a=n+r>>1;qo(e,t[a])<0?r=a:n=a+1}const o=Bl(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function Bl(e){let t=e;for(;t=t.parent;)if(Yo(t)&&qo(e,t)===0)return t}function Yo({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Hl(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const a=r[o].replace(Ao," "),l=a.indexOf("="),s=Mt(l<0?a:a.slice(0,l)),c=l<0?null:Mt(a.slice(l+1));if(s in t){let p=t[s];Te(p)||(p=t[s]=[p]),p.push(c)}else t[s]=c}return t}function Ur(e){let t="";for(let n in e){const r=e[n];if(n=cl(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Te(r)?r.map(a=>a&&On(a)):[r&&On(r)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+n,a!=null&&(t+="="+a))})}return t}function Xl(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Te(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const Gl=Symbol(""),Tr=Symbol(""),Sn=Symbol(""),Bo=Symbol(""),Fn=Symbol("");function bt(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ke(e,t,n,r,o,a=l=>l()){const l=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((s,c)=>{const p=d=>{d===!1?c(ht(4,{from:n,to:t})):d instanceof Error?c(d):Dl(d)?c(ht(2,{from:t,to:d})):(l&&r.enterCallbacks[o]===l&&typeof d=="function"&&l.push(d),s())},i=a(()=>e.call(r&&r.instances[o],t,n,p));let u=Promise.resolve(i);e.length<3&&(u=u.then(p)),u.catch(d=>c(d))})}function Dn(e,t,n,r,o=a=>a()){const a=[];for(const l of e)for(const s in l.components){let c=l.components[s];if(!(t!=="beforeRouteEnter"&&!l.instances[s]))if(Io(c)){const i=(c.__vccOpts||c)[t];i&&a.push(Ke(i,n,r,l,s,o))}else{let p=c();a.push(()=>p.then(i=>{if(!i)throw new Error(`Couldn't resolve component "${s}" at "${l.path}"`);const u=Za(i)?i.default:i;l.mods[s]=i,l.components[s]=u;const h=(u.__vccOpts||u)[t];return h&&Ke(h,n,r,l,s,o)()}))}}return a}function Or(e){const t=dt(Sn),n=dt(Bo),r=Ee(()=>{const c=de(e.to);return t.resolve(c)}),o=Ee(()=>{const{matched:c}=r.value,{length:p}=c,i=c[p-1],u=n.matched;if(!i||!u.length)return-1;const d=u.findIndex(pt.bind(null,i));if(d>-1)return d;const h=Lr(c[p-2]);return p>1&&Lr(i)===h&&u[u.length-1].path!==h?u.findIndex(pt.bind(null,c[p-2])):d}),a=Ee(()=>o.value>-1&&Jl(n.params,r.value.params)),l=Ee(()=>o.value>-1&&o.value===n.matched.length-1&&Oo(n.params,r.value.params));function s(c={}){if(jl(c)){const p=t[de(e.replace)?"replace":"push"](de(e.to)).catch(Dt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>p),p}return Promise.resolve()}return{route:r,href:Ee(()=>r.value.href),isActive:a,isExactActive:l,navigate:s}}function Kl(e){return e.length===1?e[0]:e}const Wl=Eo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Or,setup(e,{slots:t}){const n=Ue(Or(e)),{options:r}=dt(Sn),o=Ee(()=>({[Fr(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Fr(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const a=t.default&&Kl(t.default(n));return e.custom?a:Ro("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},a)}}}),Ql=Wl;function jl(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Jl(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!Te(o)||o.length!==r.length||r.some((a,l)=>a!==o[l]))return!1}return!0}function Lr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Fr=(e,t,n)=>e??t??n,Zl=Eo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=dt(Fn),o=Ee(()=>e.route||r.value),a=dt(Tr,0),l=Ee(()=>{let p=de(a);const{matched:i}=o.value;let u;for(;(u=i[p])&&!u.components;)p++;return p}),s=Ee(()=>o.value.matched[l.value]);Vn(Tr,Ee(()=>l.value+1)),Vn(Gl,s),Vn(Fn,o);const c=N();return ut(()=>[c.value,s.value,e.name],([p,i,u],[d,h,w])=>{i&&(i.instances[u]=p,h&&h!==i&&p&&p===d&&(i.leaveGuards.size||(i.leaveGuards=h.leaveGuards),i.updateGuards.size||(i.updateGuards=h.updateGuards))),p&&i&&(!h||!pt(i,h)||!d)&&(i.enterCallbacks[u]||[]).forEach(D=>D(p))},{flush:"post"}),()=>{const p=o.value,i=e.name,u=s.value,d=u&&u.components[i];if(!d)return zr(n.default,{Component:d,route:p});const h=u.props[i],w=h?h===!0?p.params:typeof h=="function"?h(p):h:null,_=Ro(d,ue({},w,t,{onVnodeUnmounted:C=>{C.component.isUnmounted&&(u.instances[i]=null)},ref:c}));return zr(n.default,{Component:_,route:p})||_}}});function zr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ei=Zl;function ti(e){const t=Fl(e.routes,e),n=e.parseQuery||Hl,r=e.stringifyQuery||Ur,o=e.history,a=bt(),l=bt(),s=bt(),c=Fa(Ge);let p=Ge;lt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const i=$n.bind(null,$=>""+$),u=$n.bind(null,fl),d=$n.bind(null,Mt);function h($,K){let X,T;return Fo($)?(X=t.getRecordMatcher($),T=K):T=$,t.addRoute(T,X)}function w($){const K=t.getRecordMatcher($);K&&t.removeRoute(K)}function D(){return t.getRoutes().map($=>$.record)}function _($){return!!t.getRecordMatcher($)}function C($,K){if(K=ue({},K||c.value),typeof $=="string"){const oe=Pn(n,$,K.path),g=t.resolve({path:oe.path},K),S=o.createHref(oe.fullPath);return ue(oe,g,{params:d(g.params),hash:Mt(oe.hash),redirectedFrom:void 0,href:S})}let X;if($.path!=null)X=ue({},$,{path:Pn(n,$.path,K.path).path});else{const oe=ue({},$.params);for(const g in oe)oe[g]==null&&delete oe[g];X=ue({},$,{params:u(oe)}),K.params=u(K.params)}const T=t.resolve(X,K),L=$.hash||"";T.params=i(d(T.params));const _e=ml(r,ue({},$,{hash:ul(L),path:T.path})),le=o.createHref(_e);return ue({fullPath:_e,hash:L,query:r===Ur?Xl($.query):$.query||{}},T,{redirectedFrom:void 0,href:le})}function k($){return typeof $=="string"?Pn(n,$,c.value.path):ue({},$)}function A($,K){if(p!==$)return ht(8,{from:K,to:$})}function U($){return O($)}function H($){return U(ue(k($),{replace:!0}))}function q($){const K=$.matched[$.matched.length-1];if(K&&K.redirect){const{redirect:X}=K;let T=typeof X=="function"?X($):X;return typeof T=="string"&&(T=T.includes("?")||T.includes("#")?T=k(T):{path:T},T.params={}),ue({query:$.query,hash:$.hash,params:T.path!=null?{}:$.params},T)}}function O($,K){const X=p=C($),T=c.value,L=$.state,_e=$.force,le=$.replace===!0,oe=q(X);if(oe)return O(ue(k(oe),{state:typeof oe=="object"?ue({},L,oe.state):L,force:_e,replace:le}),K||X);const g=X;g.redirectedFrom=K;let S;return!_e&&_l(r,T,X)&&(S=ht(16,{to:g,from:T}),z(T,T,!0,!1)),(S?Promise.resolve(S):m(g,T)).catch(Z=>Ye(Z)?Ye(Z,2)?Z:Y(Z):y(Z,g,T)).then(Z=>{if(Z){if(Ye(Z,2))return O(ue({replace:le},k(Z.to),{state:typeof Z.to=="object"?ue({},L,Z.to.state):L,force:_e}),K||g)}else Z=P(g,T,!0,le,L);return b(g,T,Z),Z})}function j($,K){const X=A($,K);return X?Promise.reject(X):Promise.resolve()}function ee($){const K=we.values().next().value;return K&&typeof K.runWithContext=="function"?K.runWithContext($):$()}function m($,K){let X;const[T,L,_e]=ni($,K);X=Dn(T.reverse(),"beforeRouteLeave",$,K);for(const oe of T)oe.leaveGuards.forEach(g=>{X.push(Ke(g,$,K))});const le=j.bind(null,$,K);return X.push(le),ce(X).then(()=>{X=[];for(const oe of a.list())X.push(Ke(oe,$,K));return X.push(le),ce(X)}).then(()=>{X=Dn(L,"beforeRouteUpdate",$,K);for(const oe of L)oe.updateGuards.forEach(g=>{X.push(Ke(g,$,K))});return X.push(le),ce(X)}).then(()=>{X=[];for(const oe of _e)if(oe.beforeEnter)if(Te(oe.beforeEnter))for(const g of oe.beforeEnter)X.push(Ke(g,$,K));else X.push(Ke(oe.beforeEnter,$,K));return X.push(le),ce(X)}).then(()=>($.matched.forEach(oe=>oe.enterCallbacks={}),X=Dn(_e,"beforeRouteEnter",$,K,ee),X.push(le),ce(X))).then(()=>{X=[];for(const oe of l.list())X.push(Ke(oe,$,K));return X.push(le),ce(X)}).catch(oe=>Ye(oe,8)?oe:Promise.reject(oe))}function b($,K,X){s.list().forEach(T=>ee(()=>T($,K,X)))}function P($,K,X,T,L){const _e=A($,K);if(_e)return _e;const le=K===Ge,oe=lt?history.state:{};X&&(T||le?o.replace($.fullPath,ue({scroll:le&&oe&&oe.scroll},L)):o.push($.fullPath,L)),c.value=$,z($,K,X,le),Y()}let V;function I(){V||(V=o.listen(($,K,X)=>{if(!te.listening)return;const T=C($),L=q(T);if(L){O(ue(L,{replace:!0,force:!0}),T).catch(Dt);return}p=T;const _e=c.value;lt&&kl(Rr(_e.fullPath,X.delta),xn()),m(T,_e).catch(le=>Ye(le,12)?le:Ye(le,2)?(O(ue(k(le.to),{force:!0}),T).then(oe=>{Ye(oe,20)&&!X.delta&&X.type===At.pop&&o.go(-1,!1)}).catch(Dt),Promise.reject()):(X.delta&&o.go(-X.delta,!1),y(le,T,_e))).then(le=>{le=le||P(T,_e,!1),le&&(X.delta&&!Ye(le,8)?o.go(-X.delta,!1):X.type===At.pop&&Ye(le,20)&&o.go(-1,!1)),b(T,_e,le)}).catch(Dt)}))}let x=bt(),B=bt(),E;function y($,K,X){Y($);const T=B.list();return T.length?T.forEach(L=>L($,K,X)):console.error($),Promise.reject($)}function R(){return E&&c.value!==Ge?Promise.resolve():new Promise(($,K)=>{x.add([$,K])})}function Y($){return E||(E=!$,I(),x.list().forEach(([K,X])=>$?X($):K()),x.reset()),$}function z($,K,X,T){const{scrollBehavior:L}=e;if(!lt||!L)return Promise.resolve();const _e=!X&&Cl(Rr($.fullPath,0))||(T||!X)&&history.state&&history.state.scroll||null;return an().then(()=>L($,K,_e)).then(le=>le&&Sl(le)).catch(le=>y(le,$,K))}const J=$=>o.go($);let se;const we=new Set,te={currentRoute:c,listening:!0,addRoute:h,removeRoute:w,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:D,resolve:C,options:e,push:U,replace:H,go:J,back:()=>J(-1),forward:()=>J(1),beforeEach:a.add,beforeResolve:l.add,afterEach:s.add,onError:B.add,isReady:R,install($){const K=this;$.component("RouterLink",Ql),$.component("RouterView",ei),$.config.globalProperties.$router=K,Object.defineProperty($.config.globalProperties,"$route",{enumerable:!0,get:()=>de(c)}),lt&&!se&&c.value===Ge&&(se=!0,U(o.location).catch(L=>{}));const X={};for(const L in Ge)Object.defineProperty(X,L,{get:()=>c.value[L],enumerable:!0});$.provide(Sn,K),$.provide(Bo,za(X)),$.provide(Fn,c);const T=$.unmount;we.add($),$.unmount=function(){we.delete($),we.size<1&&(p=Ge,V&&V(),V=null,c.value=Ge,se=!1,E=!1),T()}}};function ce($){return $.reduce((K,X)=>K.then(()=>ee(X)),Promise.resolve())}return te}function ni(e,t){const n=[],r=[],o=[],a=Math.max(t.matched.length,e.matched.length);for(let l=0;l<a;l++){const s=t.matched[l];s&&(e.matched.find(p=>pt(p,s))?r.push(s):n.push(s));const c=e.matched[l];c&&(t.matched.find(p=>pt(p,c))||o.push(c))}return[n,r,o]}function ri(){return dt(Sn)}const oi={class:"app-container"},ai={class:"grid-container"},li={class:"header"},ii={class:"header-left"},si={class:"user-info"},ui={class:"el-dropdown-link"},ci={class:"user-name"},di={class:"sidebar"},fi={class:"content",ref:"contentRef"},pi={__name:"App",setup(e){const t=ri(),n=N(""),r=N([]),o=N(!1),a=N(""),l=N(null);Qe(async()=>{await s(),await c(),t.push("/unit-management")});const s=async()=>{const q=await ne.post("/api/get_user_info.php");n.value=q.user.name},c=async()=>{const q=await ne.post("/api/get_user_app.php");r.value=q.data},p=q=>{console.log("点击的菜单对应的路由是:",q),t.push(q)},i=()=>{o.value=!o.value},u=N(!1),d=N({oldPassword:"",newPassword:"",confirmPassword:""}),h=N(null),w=N(!1),D=N(!1),_=()=>{w.value=!w.value},C=()=>{D.value=!D.value},k=async q=>{q==="logout"?(await ne.get("/api/logout.php"),window.location.href="login.html"):q==="changePassword"&&(u.value=!0)},A=async()=>{h.value&&await h.value.validate(q=>{if(q){if(d.value.newPassword!==d.value.confirmPassword){G.error("两次输入的密码不一致");return}const O=new FormData;O.append("old_password",d.value.oldPassword),O.append("new_password",d.value.newPassword),ne.post("/api/change_password.php",O).then(()=>{G.success("密码修改成功，请重新登录"),u.value=!1,d.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{G.error("密码修改失败")})}})},U=Ue({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(q,O,j)=>{O!==d.value.newPassword?j(new Error("两次输入的密码不一致")):j()},trigger:"blur"}]}),H=()=>{const q=l.value;q&&(document.fullscreenElement?document.exitFullscreen():q.requestFullscreen().catch(O=>{console.error("全屏失败:",O),G.error("全屏功能不支持")}))};return(q,O)=>{const j=F("el-button"),ee=F("el-icon"),m=F("el-avatar"),b=F("el-dropdown-item"),P=F("el-dropdown-menu"),V=F("el-dropdown"),I=F("el-menu-item"),x=F("el-menu"),B=F("router-view"),E=F("el-input"),y=F("el-form-item"),R=F("el-form"),Y=F("el-dialog");return Q(),ae(fe,null,[W("div",oi,[W("div",ai,[W("div",li,[W("div",ii,[f(j,{onClick:i,type:"text",class:"menu-btn"},{default:v(()=>O[5]||(O[5]=[W("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),O[6]||(O[6]=W("img",{src:qa,alt:"Logo",class:"header-logo"},null,-1)),O[7]||(O[7]=W("span",{class:"logo"},"CloudPivot",-1))])]),W("div",si,[f(V,{onCommand:k},{dropdown:v(()=>[f(P,null,{default:v(()=>[f(b,{command:"profile"},{default:v(()=>O[8]||(O[8]=[re("个人信息")])),_:1,__:[8]}),f(b,{command:"changePassword"},{default:v(()=>O[9]||(O[9]=[re("修改密码")])),_:1,__:[9]}),f(b,{command:"logout"},{default:v(()=>O[10]||(O[10]=[re("退出登录")])),_:1,__:[10]})]),_:1})]),default:v(()=>[W("span",ui,[f(m,{size:"small"},{default:v(()=>[f(ee,null,{default:v(()=>[f(de(Ya),{style:{color:"#409EFF"}})]),_:1})]),_:1}),W("span",ci,Ce(n.value),1)])]),_:1})]),W("div",di,[f(x,{"default-active":a.value,class:"el-menu-vertical",mode:"vertical",collapse:o.value,onOpen:q.handleOpen,onClose:q.handleClose},{default:v(()=>[(Q(!0),ae(fe,null,ge(r.value,z=>(Q(),me(I,{key:z.id,index:z.id.toString(),router:z.url,onClick:J=>p(z.url)},{title:v(()=>[W("span",null,Ce(z.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),W("div",fi,[f(j,{onClick:H,type:"text",class:"fullscreen-btn"},{default:v(()=>[f(ee,null,{default:v(()=>[f(de(Ba))]),_:1})]),_:1}),W("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:l},[f(B,{ref:"routerViewRef"},null,512)],512)],512)])]),f(Y,{modelValue:u.value,"onUpdate:modelValue":O[4]||(O[4]=z=>u.value=z),width:"400px"},{default:v(()=>[f(R,{model:d.value,ref_key:"passwordFormRef",ref:h,rules:U,"label-width":"120px",onSubmit:Vo(A,["prevent"])},{default:v(()=>[f(y,{label:"旧密码",prop:"oldPassword",required:""},{default:v(()=>[f(E,{modelValue:d.value.oldPassword,"onUpdate:modelValue":O[0]||(O[0]=z=>d.value.oldPassword=z),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),f(y,{label:"新密码",prop:"newPassword",required:""},{default:v(()=>[f(E,{modelValue:d.value.newPassword,"onUpdate:modelValue":O[1]||(O[1]=z=>d.value.newPassword=z),type:w.value?"text":"password",placeholder:"请输入新密码"},{suffix:v(()=>[f(j,{icon:w.value?de(Sr):de(kr),onClick:_,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),f(y,{label:"确认新密码",prop:"confirmPassword",required:""},{default:v(()=>[f(E,{modelValue:d.value.confirmPassword,"onUpdate:modelValue":O[2]||(O[2]=z=>d.value.confirmPassword=z),type:D.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:v(()=>[f(j,{icon:D.value?de(Sr):de(kr),onClick:C,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),f(y,null,{default:v(()=>[f(j,{type:"primary","native-type":"submit"},{default:v(()=>O[11]||(O[11]=[re("确定")])),_:1,__:[11]}),f(j,{onClick:O[3]||(O[3]=z=>u.value=!1)},{default:v(()=>O[12]||(O[12]=[re("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},hi=rt(pi,[["__scopeId","data-v-873a0a85"]]),mi={class:"unit-management"},_i={style:{"text-align":"right",margin:"10px"}},vi={__name:"UnitManagement",setup(e){const t=N(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=m=>{if(m&&m.length>0){const b=m[m.length-1],P=a(b);P&&(o.parentId=b,u.value=P.children||[])}else o.parentId=null,u.value=[]},o=Ue({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:0});ut(()=>o.parentId,(m,b)=>{if(console.log("parentId 发生变化，旧值: ",b,"新值: ",m),m){const P=a(m);P&&(u.value=P.children||[])}else u.value=[]},{immediate:!1});const a=(m,b=l.value)=>{for(let P=0;P<b.length;P++){if(b[P].id===m)return b[P];if(b[P].children){const V=a(m,b[P].children);if(V)return V}}return null},l=N([]),s=N(new Set),c=Ee(()=>{const m=[],b=(P,V=0,I=null)=>{P.forEach(x=>{m.push({...x,level:V,parentId:I,expanded:s.value.has(x.id)}),x.children&&b(x.children,V+1,x.id)})};return b(l.value),m}),p=Ee(()=>{const m=[],b=P=>{if(P.level===0)return!0;let V=c.value.find(I=>I.id===P.parentId);for(;V;){if(!V.expanded)return!1;V=c.value.find(I=>I.id===V.parentId)}return!0};return c.value.forEach(P=>{b(P)&&m.push(P)}),m}),i=N(!1),u=N([]),d=N(null);Qe(async()=>{await k()});const h=N(!1),w=m=>{const b=[];function P(V,I){for(const x of V){const B=[...I,x.id];if(x.id===m)return b.push(...B),!0;if(x.children&&x.children.length>0&&P(x.children,B))return!0}return!1}return P(l.value,[]),b},D=m=>{if(d.value&&d.value.resetFields(),t.value=!1,o.id=null,o.name="",o.code="",o.parentId=null,o.parentIdPath=[],o.sort_order=0,h.value=!!m,m){const b=a(m);b&&(o.parentId=m,console.log("parentId位置一:",m),o.parentIdPath=w(m),u.value=b.children||[])}else o.parentId=null,o.parentIdPath=[],u.value=[];i.value=!0,console.log("dialogVisible 已设为 true")},_=m=>{d.value&&d.value.resetFields(),t.value=!0,o.id=m.id;let b={currentUnit:m.unit_name,selectedSortOrder:null,selectedUnit:null};o.name=m.unit_name,o.code=m.code,o.parentId=m.parent_id,o.parentIdPath=w(m.parent_id);const P=a(m.parent_id);if(P){u.value=(P.children||[]).filter(x=>x.id!==m.id);const V=P.children||[],I=V.findIndex(x=>x.id===m.id);if(I>0){const x=V[I-1];o.sort_order=x.sort_order,b.selectedUnit=x.unit_name}else o.sort_order=0,b.selectedUnit="最前";b.selectedSortOrder=o.sort_order}else{u.value=l.value.filter(I=>I.id!==m.id);const V=l.value.findIndex(I=>I.id===m.id);if(V>0){const I=l.value[V-1];o.sort_order=I.sort_order,b.selectedUnit=I.unit_name}else o.sort_order=0,b.selectedUnit="最前";b.selectedSortOrder=o.sort_order}console.log("排序选项:",u.value),console.log("自动选择结果:",b),i.value=!0},C=()=>{d.value.validate(async m=>{if(m)try{const b=new FormData;o.id?(b.append("action","edit"),b.append("id",o.id)):b.append("action","add"),b.append("sort_order",o.sort_order+1),b.append("unit_name",o.name),b.append("code",o.code),b.append("parent_id",o.parentId||null);const P=await ne.post("api/unit_manage.php",b,{headers:{"Content-Type":"multipart/form-data"}});P.status===1?(await k(),i.value=!1,G.success(o.id?"编辑成功":"新增成功")):G.error(P.message)}catch(b){console.error("保存单位失败:",b),G.error("保存单位失败，请稍后重试")}})},k=async()=>{const m=await ne.post("api/get_unit_info.php");l.value=[m.data],console.log("获取单位数据成功:",l.value);const b=P=>{P.forEach(V=>{V.children&&V.children.length>0&&(s.value.add(V.id),b(V.children))})};b(l.value)},A=m=>{ft.confirm(`确定要删除 ${m.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const b=new FormData;b.append("action","del"),b.append("id",m.id),await ne.post("api/unit_manage.php",b),G.success("删除成功"),await k()})},U=m=>{const b=new Set(s.value);b.has(m.id)?b.delete(m.id):b.add(m.id),s.value=b},H=m=>((m.parentId?a(m.parentId):{children:l.value}).children||[]).findIndex(I=>I.id===m.id)===0,q=m=>{const P=(m.parentId?a(m.parentId):{children:l.value}).children||[];return P.findIndex(I=>I.id===m.id)===P.length-1},O=async m=>{const b=new FormData;b.append("action","edit"),b.append("id",m.id),b.append("sort_order",m.sort_order-1),b.append("unit_name",m.unit_name),b.append("code",m.code),b.append("parent_id",m.parentId),await ne.post("api/unit_manage.php",b),await k()},j=async m=>{const b=new FormData;b.append("action","edit"),b.append("id",m.id),b.append("sort_order",m.sort_order+1),b.append("unit_name",m.unit_name),b.append("code",m.code),b.append("parent_id",m.parentId),await ne.post("api/unit_manage.php",b),await k()},ee=Ee(()=>u.value.filter(m=>m.id!==o.id));return(m,b)=>{const P=F("el-button"),V=F("el-col"),I=F("el-row"),x=F("el-table-column"),B=F("el-icon"),E=F("el-button-group"),y=F("el-table"),R=F("el-input"),Y=F("el-form-item"),z=F("el-cascader"),J=F("el-option"),se=F("el-select"),we=F("el-form");return Q(),ae("div",mi,[f(I,null,{default:v(()=>[f(V,{span:24},{default:v(()=>[W("div",_i,[f(P,{type:"primary",round:"",onClick:b[0]||(b[0]=te=>D(null))},{default:v(()=>b[7]||(b[7]=[re("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),f(y,{data:p.value,style:{width:"100%",height:"calc(100vh - 200px)","overflow-y":"auto"},border:""},{default:v(()=>[f(x,{label:"操作",width:"60",align:"center"},{default:v(te=>[te.row.children&&te.row.children.length?(Q(),me(P,{key:0,size:"mini",type:"text",onClick:ce=>U(te.row)},{default:v(()=>[re(Ce(te.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Pt("",!0)]),_:1}),f(x,{prop:"unit_name",label:"单位名称"},{default:v(te=>[W("span",{style:ur({paddingLeft:`${te.row.level*20}px`})},Ce(te.row.unit_name),5)]),_:1}),f(x,{prop:"code",label:"组织机构代码",width:"250"}),f(x,{label:"操作",width:"350"},{default:v(te=>[f(E,null,{default:v(()=>[f(P,{size:"mini",type:"primary",onClick:ce=>D(te.row.id)},{default:v(()=>[f(B,null,{default:v(()=>[f(de($o))]),_:1})]),_:2},1032,["onClick"]),f(P,{size:"mini",type:"warning",onClick:ce=>_(te.row)},{default:v(()=>[f(B,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"]),f(P,{size:"mini",type:"danger",onClick:ce=>A(te.row)},{default:v(()=>[f(B,null,{default:v(()=>[f(de(Yt))]),_:1})]),_:2},1032,["onClick"]),f(P,{size:"mini",type:"info",onClick:ce=>O(te.row),disabled:H(te.row)},{default:v(()=>[f(B,null,{default:v(()=>[f(de(Po))]),_:1})]),_:2},1032,["onClick","disabled"]),f(P,{size:"mini",type:"info",onClick:ce=>j(te.row),disabled:q(te.row)},{default:v(()=>[f(B,null,{default:v(()=>[f(de(Do))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),f(de(Ha),{modelValue:i.value,"onUpdate:modelValue":b[6]||(b[6]=te=>i.value=te),title:"",width:"450px","close-on-click-modal":!1},{footer:v(()=>[f(P,{onClick:b[5]||(b[5]=te=>i.value=!1)},{default:v(()=>b[8]||(b[8]=[re("取消")])),_:1,__:[8]}),f(P,{type:"primary",onClick:C},{default:v(()=>b[9]||(b[9]=[re("确定")])),_:1,__:[9]})]),default:v(()=>[f(we,{model:o,ref_key:"formRef",ref:d,"label-width":"130px"},{default:v(()=>[f(Y,{label:"单位名称",prop:"name",required:""},{default:v(()=>[f(R,{modelValue:o.name,"onUpdate:modelValue":b[1]||(b[1]=te=>o.name=te),required:""},null,8,["modelValue"])]),_:1}),f(Y,{label:"组织机构代码",prop:"code"},{default:v(()=>[f(R,{modelValue:o.code,"onUpdate:modelValue":b[2]||(b[2]=te=>o.code=te)},null,8,["modelValue"])]),_:1}),f(Y,{label:"上级单位",prop:"parentId"},{default:v(()=>[f(z,{modelValue:o.parentIdPath,"onUpdate:modelValue":b[3]||(b[3]=te=>o.parentIdPath=te),options:l.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),f(Y,{label:"排序",prop:"sort",required:""},{default:v(()=>[f(se,{modelValue:o.sort_order,"onUpdate:modelValue":b[4]||(b[4]=te=>o.sort_order=te),placeholder:"请选择排序位置"},{default:v(()=>[f(J,{label:"置于 最前",value:0}),(Q(!0),ae(fe,null,ge(ee.value,te=>(Q(),me(J,{key:te.id,label:`置于 ${te.unit_name} 之后`,value:te.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},gi=rt(vi,[["__scopeId","data-v-bcca994a"]]),yi={class:"user-management-container"},wi={class:"left-panel"},bi=["onClick"],xi={class:"right-panel"},Si={class:"action-buttons",style:{display:"flex","align-items":"center"}},ki={class:"left-buttons",style:{display:"flex","align-items":"center",gap:"8px"}},Ci={class:"right-switches",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},Ei={class:"form-row"},Ri={class:"form-row"},Vi={class:"form-row"},$i={class:"form-row"},Pi={class:"form-row"},Di={class:"form-row"},Ii={class:"form-row"},Mi={class:"form-row"},Ai={class:"dialog-footer"},Ni={__name:"UserManagement",setup(e){const t=N([]),n=N([]),r=N([]),o=N(null);N("");const a=N([]),l=N([]),s=Ee(()=>{if(!o.value)return"";const g=d.value.find(S=>S.id===o.value);return console.log("当前选中的部门ID:",o.value),console.log("当前选中的部门:",g.unit_name),g?g.unit_name:""});N(null);const c=N(1),p=N(10),i=N(0),u=N([]),d=N([]),h=N(""),w=N([]),D=N([]),_=N([]),C=N([]),k=N(!1),A=N(""),U=N(!1),H=N({});N(!1);const q=N(!1),O=N(null),j=()=>{U.value?k.value=!1:K()},ee=Ee(()=>h.value?d.value.filter(g=>g.show&&g.unit_name.toLowerCase().includes(h.value.toLowerCase())):d.value.filter(g=>g.show)),m=Ue({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sort_order:null,desc:""}),b={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},P=N(null),V=async()=>{try{const g=new FormData;g.append("type","identity");const S=await ne({url:"/api/person_info_api.php",method:"post",data:g});S.status===1?t.value=S.data.map(Z=>({label:Z,value:Z})):G.error("获取人员身份数据失败："+S.message)}catch{G.error("网络请求失败")}},I=async()=>{try{const g=new FormData;g.append("type","status");const S=await ne({url:"/api/person_info_api.php",method:"post",data:g});S.status===1?n.value=S.data.map(Z=>({label:Z,value:Z})):G.error("获取人员状态数据失败："+S.message)}catch{G.error("网络请求失败")}},x=async()=>{try{const g=new FormData;g.append("type","rank");const S=await ne({url:"/api/person_info_api.php",method:"post",data:g});S.status===1?r.value=S.data.map(Z=>({label:Z,value:Z})):G.error("获取职级数据失败："+S.message)}catch{G.error("网络请求失败")}};Qe(async()=>{try{const g=await ne.post("api/get_unit_info.php");u.value=[g.data],E(g.data),w.value=u.value,D.value=u.value,console.log("获取部门数据成功:",u.value),await B(),V(),I(),x()}catch(g){console.error("获取数据失败:",g),G.error("获取数据失败，请稍后重试")}});const B=async()=>{console.log("开始获取用户数据...");try{const g=new FormData;g.append("controlCode","query"),g.append("page",c.value),g.append("pagesize",p.value),o.value&&g.append("organization_unit",o.value),Y.value?g.append("isShowInPersonnel","1"):g.append("isShowInPersonnel","0"),R.value?g.append("isShowOutPersonnel","1"):g.append("isShowOutPersonnel","0");const S=await ne.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});_.value=S.data,console.log("获取用户数据成功:",_.value),i.value=S.total}catch(g){console.error("获取用户数据失败:",g),G.error("获取用户数据失败，请稍后重试")}},E=(g,S=0,Z=null)=>{const ve={...g,level:S,expanded:g.children&&g.children.length>0,parent:Z,indent:S*20,show:!0};d.value.push(ve),g.children&&g.children.length>0&&g.children.forEach(be=>{E(be,S+1,ve)})};console.log("扁平化后的部门列表:",d.value);const y=g=>{g.expanded=!g.expanded;const S=d.value.indexOf(g)+1;let Z=g.level+1;for(let ve=S;ve<d.value.length;ve++){const be=d.value[ve];if(be.level<=g.level)break;be.level===Z?be.show=g.expanded:be.level>Z&&(be.show=g.expanded&&d.value[ve-1].show)}},R=N(!0),Y=N(!0),z=()=>{k.value=!0,P.value.resetFields()},J=async()=>{if(C.value.length===0){G.warning("请先选择要删除的用户");return}try{await ft.confirm(`确定要删除选中的 ${C.value.length} 个用户吗？删除后数据不可恢复`,"批量删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const g=new FormData;g.append("controlCode","del");const S=C.value.map(Z=>Z.id);g.append("id",S.join(",")),await ne.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}}),G.success(`成功删除 ${C.value.length} 个用户`),await B(),C.value=[]}catch(g){g!=="cancel"&&G.error(`删除失败：${g.message||"未知错误"}`)}},se=async g=>{P.value&&P.value.resetFields(),q.value=!0,O.value=g.id,k.value=!0,A.value="编辑用户信息",U.value=!1,m.name=g.name,m.id_number=g.id_number,m.phone=g.phone,m.archive_birthdate=g.archive_birthdate,m.gender=g.gender,console.log("性别值:",g.gender),m.short_code=g.short_code,m.alt_phone_1=g.alt_phone_1,m.alt_phone_2=g.alt_phone_2,m.landline=g.landline,m.organization_unit=g.organization_unit,m.work_unit=g.work_unit,m.employment_date=g.employment_date,m.political_status=g.political_status,m.party_join_date=g.party_join_date,m.personnel_type=g.personnel_type,m.police_number=g.police_number,m.is_assisting_officer=g.is_assisting_officer,m.employment_status=g.employment_status,m.job_rank=g.job_rank,m.current_rank_date=g.current_rank_date,m.position=g.position,m.current_position_date=g.current_position_date,await le(g.organization_unit),m.sort_order=g.sort_order,console.log("排序值:",g.sort_order),m.desc=g.desc},we=g=>{A.value="查看用户详情",H.value={...g},U.value=!0,k.value=!0},te=async g=>{try{await ft.confirm(`确定要删除用户 ${g.name} 吗？删除后数据不可恢复`,"删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const S=new FormData;S.append("controlCode","del"),S.append("id",g.id),await ne.post("api/user_manage.php",S,{headers:{"Content-Type":"multipart/form-data"}}),G.success("用户删除成功"),await B()}catch(S){S!=="cancel"&&G.error(`删除失败：${S.message||"未知错误"}`)}},ce=g=>{C.value=g},$=async g=>{console.log("点击的部门名称:",g),o.value=g.id,await B()},K=async()=>{try{await P.value.validate();const g=new FormData;q.value?(g.append("controlCode","modify"),g.append("id",O.value)):g.append("controlCode","add"),g.append("name",m.name),g.append("id_number",m.id_number||""),g.append("phone",m.phone),g.append("archive_birthdate",m.archive_birthdate||""),g.append("gender",m.gender||""),g.append("short_code",m.short_code||""),g.append("alt_phone_1",m.alt_phone_1||""),g.append("alt_phone_2",m.alt_phone_2||""),g.append("landline",m.landline||"");const S=Array.isArray(m.organization_unit)?m.organization_unit[m.organization_unit.length-1]:m.organization_unit;g.append("organization_unit",S||"");const Z=Array.isArray(m.work_unit)?m.work_unit[m.work_unit.length-1]:m.work_unit;g.append("work_unit",Z||S),g.append("employment_date",m.employment_date||""),g.append("political_status",m.political_status||""),g.append("party_join_date",m.party_join_date||""),g.append("personnel_type",m.personnel_type||""),g.append("police_number",m.police_number||""),g.append("assisting_officer",m.is_assisting_officer||""),g.append("employment_status",m.employment_status||""),g.append("job_rank",m.job_rank||""),g.append("current_rank_date",m.current_rank_date||""),g.append("position",m.position||""),g.append("current_position_date",m.current_position_date||""),g.append("sort_order",m.sort_order+1),g.append("desc",m.desc||""),await ne.post("api/user_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});const ve=q.value?"编辑":"添加";G.success(`用户${ve}成功`),k.value=!1,q.value=!1,O.value=null,await B(),P.value.resetFields()}catch(g){G.error("提交失败："+(g.message||"未知错误"))}},X=Ee(()=>{const g=new Map;return d.value.forEach(S=>{g.set(S.id,S.unit_name)}),g}),T=g=>(console.log("单位ID:",g),console.log("组织映射:",X.value),console.log("单位名称:",X.value.get(g)),X.value.get(g)||"未知单位"),L=async g=>{try{const S=new FormData;S.append("controlCode","modify"),S.append("id",g.id);const Z=g.sort_order-1;S.append("sort_order",Z),await ne.post("api/user_manage.php",S),await B(),G.success("上移成功")}catch{G.error("上移失败，请稍后重试")}},_e=async g=>{try{const S=new FormData;S.append("controlCode","modify"),S.append("id",g.id);const Z=g.sort_order+1;S.append("sort_order",Z),await ne.post("api/user_manage.php",S),await B(),G.success("下移成功")}catch{G.error("下移失败，请稍后重试")}};ut(Y,async g=>{c.value=1,await B()}),ut(R,async g=>{c.value=1,await B()});const le=async g=>{if(!g){l.value=[];return}try{const S=new FormData;S.append("controlCode","getSortOptions"),S.append("organization_unit",g);const Z=await ne.post("/api/user_manage.php",S,{headers:{"Content-Type":"multipart/form-data"}});l.value=Z.data,console.log("获取排序选项成功:",l.value)}catch(S){console.error("获取排序选项失败:",S),G.error("获取排序选项失败，请稍后重试"),l.value=[]}},oe=async g=>{if(!g){a.value=[];return}try{const S=new FormData;S.append("controlCode","getPspInfo"),S.append("organization_unit",g);const Z=await ne({url:"/api/user_manage.php",method:"post",data:S});Z.status===1?a.value=Z.data.map(ve=>({label:ve.name,value:ve.id})):G.error("获取带辅民警数据失败")}catch{G.error("网络请求失败")}};return ut(()=>m.organization_unit,async g=>{const S=Array.isArray(g)?g[g.length-1]:g;await le(S),await oe(S)}),(g,S)=>{const Z=F("el-input"),ve=F("el-button"),be=F("el-table-column"),wr=F("el-table"),Na=F("el-tag"),br=F("el-switch"),yt=F("el-icon"),Ua=F("el-button-group"),Ta=F("el-pagination"),he=F("el-form-item"),wt=F("el-date-picker"),Re=F("el-option"),je=F("el-select"),xr=F("el-cascader"),Oa=F("el-form"),La=F("el-dialog");return Q(),ae("div",yi,[W("div",wi,[f(Z,{modelValue:h.value,"onUpdate:modelValue":S[0]||(S[0]=M=>h.value=M),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),f(wr,{data:ee.value,border:"",class:"department-table"},{default:v(()=>[f(be,{label:"操作",width:"55"},{default:v(({row:M})=>[M.children&&M.children.length>0?(Q(),me(ve,{key:0,type:"text",size:"small",onClick:Vo(at=>y(M),["stop"])},{default:v(()=>[re(Ce(M.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):Pt("",!0)]),_:1}),f(be,{prop:"unit_name",label:"单位名称"},{default:v(({row:M})=>[W("span",{class:"indent",style:ur({width:`${M.indent}px`})},null,4),W("span",{onClick:at=>$(M),style:{cursor:"pointer"}},Ce(M.unit_name),9,bi)]),_:1})]),_:1},8,["data"])]),W("div",xi,[W("div",Si,[W("div",ki,[f(ve,{type:"primary",onClick:z},{default:v(()=>S[31]||(S[31]=[re("新增")])),_:1,__:[31]}),f(ve,{type:"danger",onClick:J},{default:v(()=>S[32]||(S[32]=[re("删除")])),_:1,__:[32]})]),W("div",Ci,[s.value?(Q(),me(Na,{key:0,type:"info",style:{"margin-left":"10px"}},{default:v(()=>[re(" 当前单位："+Ce(s.value),1)]),_:1})):Pt("",!0),f(br,{modelValue:R.value,"onUpdate:modelValue":S[1]||(S[1]=M=>R.value=M),"inline-prompt":"","active-text":"显示抽出人员","inactive-text":"不显示抽出人员",style:{"margin-left":"10px"}},null,8,["modelValue"]),f(br,{modelValue:Y.value,"onUpdate:modelValue":S[2]||(S[2]=M=>Y.value=M),"active-text":"显示抽入人员","inactive-text":"显示抽入人员","inline-prompt":"",style:{"margin-left":"10px"}},null,8,["modelValue"])])]),f(wr,{data:_.value,border:"",class:"user-table",onSelectionChange:ce},{default:v(()=>[f(be,{type:"selection",width:"55"}),f(be,{prop:"name",label:"姓名",width:"70"}),f(be,{prop:"id_number",label:"身份证号",width:"100"}),f(be,{prop:"phone",label:"手机号码",width:"115"}),f(be,{label:"性别",width:"55"},{default:v(M=>[re(Ce(M.row.gender===1?"男":M.row.gender===2?"女":"未知"),1)]),_:1}),f(be,{label:"编制单位"},{default:v(M=>[re(Ce(T(M.row.organization_unit)),1)]),_:1}),f(be,{label:"工作单位"},{default:v(M=>[re(Ce(T(M.row.work_unit)),1)]),_:1}),f(be,{prop:"personnel_type",label:"人员身份"}),f(be,{prop:"employment_status",label:"人员状态"}),f(be,{prop:"desc",label:"备注"}),f(be,{label:"操作",width:"260",align:"center"},{default:v(({row:M})=>[f(Ua,null,{default:v(()=>[f(ve,{type:"warning",size:"mini",onClick:at=>se(M)},{default:v(()=>[f(yt,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"]),f(ve,{type:"success",size:"mini",onClick:at=>we(M)},{default:v(()=>[f(yt,null,{default:v(()=>[f(de(Xa))]),_:1})]),_:2},1032,["onClick"]),f(ve,{type:"danger",size:"mini",onClick:at=>te(M)},{default:v(()=>[f(yt,null,{default:v(()=>[f(de(Yt))]),_:1})]),_:2},1032,["onClick"]),f(ve,{size:"mini",type:"info",onClick:at=>L(M),disabled:M.sort_order<=1},{default:v(()=>[f(yt,null,{default:v(()=>[f(de(Po))]),_:1})]),_:2},1032,["onClick","disabled"]),f(ve,{size:"mini",type:"info",onClick:at=>_e(M),disabled:M.sort_order>=M.sortMax},{default:v(()=>[f(yt,null,{default:v(()=>[f(de(Do))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),f(Ta,{"current-page":c.value,"page-size":p.value,total:i.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:S[3]||(S[3]=M=>{c.value=M,B()}),onSizeChange:S[4]||(S[4]=M=>{p.value=M,c.value=1,B()})},null,8,["current-page","page-size","total"])]),f(La,{modelValue:k.value,"onUpdate:modelValue":S[30]||(S[30]=M=>k.value=M),title:A.value,width:"1000px"},{footer:v(()=>[W("span",Ai,[U.value?Pt("",!0):(Q(),me(ve,{key:0,onClick:S[29]||(S[29]=M=>k.value=!1)},{default:v(()=>S[34]||(S[34]=[re("取消")])),_:1,__:[34]})),f(ve,{type:"primary",onClick:j},{default:v(()=>S[35]||(S[35]=[re("确定")])),_:1,__:[35]})])]),default:v(()=>[f(Oa,{model:m,rules:b,ref_key:"newUserFormRef",ref:P,"label-width":"120px",inline:!1,class:"user-form",disabled:U.value},{default:v(()=>[W("div",Ei,[f(he,{label:"姓名",prop:"name",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.name,"onUpdate:modelValue":S[5]||(S[5]=M=>m.name=M)},null,8,["modelValue"])]),_:1}),f(he,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.id_number,"onUpdate:modelValue":S[6]||(S[6]=M=>m.id_number=M),maxlength:"18"},null,8,["modelValue"])]),_:1}),f(he,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.phone,"onUpdate:modelValue":S[7]||(S[7]=M=>m.phone=M),maxlength:"11"},null,8,["modelValue"])]),_:1})]),W("div",Ri,[f(he,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:v(()=>[f(wt,{modelValue:m.archive_birthdate,"onUpdate:modelValue":S[8]||(S[8]=M=>m.archive_birthdate=M),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(he,{label:"性别",prop:"gender",style:{flex:"1"}},{default:v(()=>[f(je,{modelValue:m.gender,"onUpdate:modelValue":S[9]||(S[9]=M=>m.gender=M)},{default:v(()=>[f(Re,{label:"未知",value:0}),f(Re,{label:"男",value:1}),f(Re,{label:"女",value:2})]),_:1},8,["modelValue"])]),_:1}),f(he,{label:"备注",prop:"desc",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.desc,"onUpdate:modelValue":S[10]||(S[10]=M=>m.desc=M)},null,8,["modelValue"])]),_:1})]),W("div",Vi,[f(he,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.short_code,"onUpdate:modelValue":S[11]||(S[11]=M=>m.short_code=M)},null,8,["modelValue"]),S[33]||(S[33]=W("div",{class:""},null,-1))]),_:1,__:[33]}),f(he,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.alt_phone_1,"onUpdate:modelValue":S[12]||(S[12]=M=>m.alt_phone_1=M)},null,8,["modelValue"])]),_:1}),f(he,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.alt_phone_2,"onUpdate:modelValue":S[13]||(S[13]=M=>m.alt_phone_2=M)},null,8,["modelValue"])]),_:1})]),W("div",$i,[f(he,{label:"座机",prop:"landline",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.landline,"onUpdate:modelValue":S[14]||(S[14]=M=>m.landline=M)},null,8,["modelValue"])]),_:1}),f(he,{label:"编制单位",prop:"organization_unit",required:""},{default:v(()=>[f(xr,{modelValue:m.organization_unit,"onUpdate:modelValue":S[15]||(S[15]=M=>m.organization_unit=M),options:w.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),f(he,{label:"工作单位",prop:"work_unit"},{default:v(()=>[f(xr,{modelValue:m.work_unit,"onUpdate:modelValue":S[16]||(S[16]=M=>m.work_unit=M),options:D.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),W("div",Pi,[f(he,{label:"参工日期",prop:"employment_date",style:{flex:"1"}},{default:v(()=>[f(wt,{modelValue:m.employment_date,"onUpdate:modelValue":S[17]||(S[17]=M=>m.employment_date=M),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(he,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:v(()=>[f(je,{modelValue:m.political_status,"onUpdate:modelValue":S[18]||(S[18]=M=>m.political_status=M),placeholder:"请选择政治面貌"},{default:v(()=>[f(Re,{label:"中共党员",value:"中共党员"}),f(Re,{label:"中共预备党员",value:"中共预备党员"}),f(Re,{label:"共青团员",value:"共青团员"}),f(Re,{label:"民主党派",value:"民主党派"}),f(Re,{label:"无党派人士",value:"无党派人士"}),f(Re,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),f(he,{label:"加入组织日期",prop:"party_join_date",style:{flex:"1"}},{default:v(()=>[f(wt,{modelValue:m.party_join_date,"onUpdate:modelValue":S[19]||(S[19]=M=>m.party_join_date=M),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),W("div",Di,[f(he,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:v(()=>[f(je,{modelValue:m.personnel_type,"onUpdate:modelValue":S[20]||(S[20]=M=>m.personnel_type=M),placeholder:"请选择人员身份"},{default:v(()=>[(Q(!0),ae(fe,null,ge(t.value,M=>(Q(),me(Re,{key:M.value,label:M.label,value:M.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(he,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.police_number,"onUpdate:modelValue":S[21]||(S[21]=M=>m.police_number=M)},null,8,["modelValue"])]),_:1}),f(he,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:v(()=>[f(je,{modelValue:m.is_assisting_officer,"onUpdate:modelValue":S[22]||(S[22]=M=>m.is_assisting_officer=M),placeholder:"请选择带辅民警",clearable:""},{default:v(()=>[(Q(!0),ae(fe,null,ge(a.value,M=>(Q(),me(Re,{key:M.value,label:M.label,value:M.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),W("div",Ii,[f(he,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:v(()=>[f(je,{modelValue:m.employment_status,"onUpdate:modelValue":S[23]||(S[23]=M=>m.employment_status=M)},{default:v(()=>[(Q(!0),ae(fe,null,ge(n.value,M=>(Q(),me(Re,{key:M.value,label:M.label,value:M.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(he,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:v(()=>[f(je,{modelValue:m.job_rank,"onUpdate:modelValue":S[24]||(S[24]=M=>m.job_rank=M)},{default:v(()=>[(Q(!0),ae(fe,null,ge(r.value,M=>(Q(),me(Re,{key:M.value,label:M.label,value:M.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f(he,{label:"任现职级日期",prop:"current_rank_date",style:{flex:"1"}},{default:v(()=>[f(wt,{modelValue:m.current_rank_date,"onUpdate:modelValue":S[25]||(S[25]=M=>m.current_rank_date=M),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),W("div",Mi,[f(he,{label:"职务",prop:"position",style:{flex:"1"}},{default:v(()=>[f(Z,{modelValue:m.position,"onUpdate:modelValue":S[26]||(S[26]=M=>m.position=M)},null,8,["modelValue"])]),_:1}),f(he,{label:"任现职务日期",prop:"current_position_date",style:{flex:"1"}},{default:v(()=>[f(wt,{modelValue:m.current_position_date,"onUpdate:modelValue":S[27]||(S[27]=M=>m.current_position_date=M),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(he,{label:"人员排序",prop:"sort_order",style:{flex:"1"}},{default:v(()=>[f(je,{modelValue:m.sort_order,"onUpdate:modelValue":S[28]||(S[28]=M=>m.sort_order=M),placeholder:"请选择人员排序"},{default:v(()=>[f(Re,{label:"置于 最前",value:"0"}),(Q(!0),ae(fe,null,ge(l.value,M=>(Q(),me(Re,{key:M.id,label:`置于 ${M.name} 之后`,value:M.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","disabled"])]),_:1},8,["modelValue","title"])])}}},Ui=rt(Ni,[["__scopeId","data-v-5df0d9eb"]]);class et{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let o=0;o<this._n&&o<32;o++){const a=n[o],l=t+a,s=Math.abs(t)<Math.abs(a)?t-(l-a):a-(l-t);s&&(n[r++]=s),t=l}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,o,a,l=0;if(n>0){for(l=t[--n];n>0&&(r=l,o=t[--n],l=r+o,a=o-(l-r),!a););n>0&&(a<0&&t[n-1]<0||a>0&&t[n-1]>0)&&(o=a*2,r=l+o,o==r-l&&(l=r))}return l}}function*Ti(e){for(const t of e)yield*t}function Ho(e){return Array.from(Ti(e))}var Oi={value:()=>{}};function Xo(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new tn(n)}function tn(e){this._=e}function Li(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}tn.prototype=Xo.prototype={constructor:tn,on:function(e,t){var n=this._,r=Li(e+"",n),o,a=-1,l=r.length;if(arguments.length<2){for(;++a<l;)if((o=(e=r[a]).type)&&(o=Fi(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++a<l;)if(o=(e=r[a]).type)n[o]=qr(n[o],e.name,t);else if(t==null)for(o in n)n[o]=qr(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new tn(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,a;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(a=this._[e],r=0,o=a.length;r<o;++r)a[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,a=r.length;o<a;++o)r[o].value.apply(t,n)}};function Fi(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function qr(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=Oi,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var zn="http://www.w3.org/1999/xhtml";const Yr={svg:"http://www.w3.org/2000/svg",xhtml:zn,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function kn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Yr.hasOwnProperty(t)?{space:Yr[t],local:e}:e}function zi(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===zn&&t.documentElement.namespaceURI===zn?t.createElement(e):t.createElementNS(n,e)}}function qi(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Go(e){var t=kn(e);return(t.local?qi:zi)(t)}function Yi(){}function dr(e){return e==null?Yi:function(){return this.querySelector(e)}}function Bi(e){typeof e!="function"&&(e=dr(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var a=t[o],l=a.length,s=r[o]=new Array(l),c,p,i=0;i<l;++i)(c=a[i])&&(p=e.call(c,c.__data__,i,a))&&("__data__"in c&&(p.__data__=c.__data__),s[i]=p);return new De(r,this._parents)}function Hi(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Xi(){return[]}function Ko(e){return e==null?Xi:function(){return this.querySelectorAll(e)}}function Gi(e){return function(){return Hi(e.apply(this,arguments))}}function Ki(e){typeof e=="function"?e=Gi(e):e=Ko(e);for(var t=this._groups,n=t.length,r=[],o=[],a=0;a<n;++a)for(var l=t[a],s=l.length,c,p=0;p<s;++p)(c=l[p])&&(r.push(e.call(c,c.__data__,p,l)),o.push(c));return new De(r,o)}function Wo(e){return function(){return this.matches(e)}}function Qo(e){return function(t){return t.matches(e)}}var Wi=Array.prototype.find;function Qi(e){return function(){return Wi.call(this.children,e)}}function ji(){return this.firstElementChild}function Ji(e){return this.select(e==null?ji:Qi(typeof e=="function"?e:Qo(e)))}var Zi=Array.prototype.filter;function es(){return Array.from(this.children)}function ts(e){return function(){return Zi.call(this.children,e)}}function ns(e){return this.selectAll(e==null?es:ts(typeof e=="function"?e:Qo(e)))}function rs(e){typeof e!="function"&&(e=Wo(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var a=t[o],l=a.length,s=r[o]=[],c,p=0;p<l;++p)(c=a[p])&&e.call(c,c.__data__,p,a)&&s.push(c);return new De(r,this._parents)}function jo(e){return new Array(e.length)}function os(){return new De(this._enter||this._groups.map(jo),this._parents)}function ln(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}ln.prototype={constructor:ln,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function as(e){return function(){return e}}function ls(e,t,n,r,o,a){for(var l=0,s,c=t.length,p=a.length;l<p;++l)(s=t[l])?(s.__data__=a[l],r[l]=s):n[l]=new ln(e,a[l]);for(;l<c;++l)(s=t[l])&&(o[l]=s)}function is(e,t,n,r,o,a,l){var s,c,p=new Map,i=t.length,u=a.length,d=new Array(i),h;for(s=0;s<i;++s)(c=t[s])&&(d[s]=h=l.call(c,c.__data__,s,t)+"",p.has(h)?o[s]=c:p.set(h,c));for(s=0;s<u;++s)h=l.call(e,a[s],s,a)+"",(c=p.get(h))?(r[s]=c,c.__data__=a[s],p.delete(h)):n[s]=new ln(e,a[s]);for(s=0;s<i;++s)(c=t[s])&&p.get(d[s])===c&&(o[s]=c)}function ss(e){return e.__data__}function us(e,t){if(!arguments.length)return Array.from(this,ss);var n=t?is:ls,r=this._parents,o=this._groups;typeof e!="function"&&(e=as(e));for(var a=o.length,l=new Array(a),s=new Array(a),c=new Array(a),p=0;p<a;++p){var i=r[p],u=o[p],d=u.length,h=cs(e.call(i,i&&i.__data__,p,r)),w=h.length,D=s[p]=new Array(w),_=l[p]=new Array(w),C=c[p]=new Array(d);n(i,u,D,_,C,h,t);for(var k=0,A=0,U,H;k<w;++k)if(U=D[k]){for(k>=A&&(A=k+1);!(H=_[A])&&++A<w;);U._next=H||null}}return l=new De(l,r),l._enter=s,l._exit=c,l}function cs(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function ds(){return new De(this._exit||this._groups.map(jo),this._parents)}function fs(e,t,n){var r=this.enter(),o=this,a=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?a.remove():n(a),r&&o?r.merge(o).order():o}function ps(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,a=r.length,l=Math.min(o,a),s=new Array(o),c=0;c<l;++c)for(var p=n[c],i=r[c],u=p.length,d=s[c]=new Array(u),h,w=0;w<u;++w)(h=p[w]||i[w])&&(d[w]=h);for(;c<o;++c)s[c]=n[c];return new De(s,this._parents)}function hs(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,a=r[o],l;--o>=0;)(l=r[o])&&(a&&l.compareDocumentPosition(a)^4&&a.parentNode.insertBefore(l,a),a=l);return this}function ms(e){e||(e=_s);function t(u,d){return u&&d?e(u.__data__,d.__data__):!u-!d}for(var n=this._groups,r=n.length,o=new Array(r),a=0;a<r;++a){for(var l=n[a],s=l.length,c=o[a]=new Array(s),p,i=0;i<s;++i)(p=l[i])&&(c[i]=p);c.sort(t)}return new De(o,this._parents).order()}function _s(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function vs(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function gs(){return Array.from(this)}function ys(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,a=r.length;o<a;++o){var l=r[o];if(l)return l}return null}function ws(){let e=0;for(const t of this)++e;return e}function bs(){return!this.node()}function xs(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],a=0,l=o.length,s;a<l;++a)(s=o[a])&&e.call(s,s.__data__,a,o);return this}function Ss(e){return function(){this.removeAttribute(e)}}function ks(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Cs(e,t){return function(){this.setAttribute(e,t)}}function Es(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function Rs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function Vs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function $s(e,t){var n=kn(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?ks:Ss:typeof t=="function"?n.local?Vs:Rs:n.local?Es:Cs)(n,t))}function Jo(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function Ps(e){return function(){this.style.removeProperty(e)}}function Ds(e,t,n){return function(){this.style.setProperty(e,t,n)}}function Is(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function Ms(e,t,n){return arguments.length>1?this.each((t==null?Ps:typeof t=="function"?Is:Ds)(e,t,n??"")):mt(this.node(),e)}function mt(e,t){return e.style.getPropertyValue(t)||Jo(e).getComputedStyle(e,null).getPropertyValue(t)}function As(e){return function(){delete this[e]}}function Ns(e,t){return function(){this[e]=t}}function Us(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function Ts(e,t){return arguments.length>1?this.each((t==null?As:typeof t=="function"?Us:Ns)(e,t)):this.node()[e]}function Zo(e){return e.trim().split(/^|\s+/)}function fr(e){return e.classList||new ea(e)}function ea(e){this._node=e,this._names=Zo(e.getAttribute("class")||"")}ea.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function ta(e,t){for(var n=fr(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function na(e,t){for(var n=fr(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function Os(e){return function(){ta(this,e)}}function Ls(e){return function(){na(this,e)}}function Fs(e,t){return function(){(t.apply(this,arguments)?ta:na)(this,e)}}function zs(e,t){var n=Zo(e+"");if(arguments.length<2){for(var r=fr(this.node()),o=-1,a=n.length;++o<a;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?Fs:t?Os:Ls)(n,t))}function qs(){this.textContent=""}function Ys(e){return function(){this.textContent=e}}function Bs(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Hs(e){return arguments.length?this.each(e==null?qs:(typeof e=="function"?Bs:Ys)(e)):this.node().textContent}function Xs(){this.innerHTML=""}function Gs(e){return function(){this.innerHTML=e}}function Ks(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Ws(e){return arguments.length?this.each(e==null?Xs:(typeof e=="function"?Ks:Gs)(e)):this.node().innerHTML}function Qs(){this.nextSibling&&this.parentNode.appendChild(this)}function js(){return this.each(Qs)}function Js(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Zs(){return this.each(Js)}function eu(e){var t=typeof e=="function"?e:Go(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function tu(){return null}function nu(e,t){var n=typeof e=="function"?e:Go(e),r=t==null?tu:typeof t=="function"?t:dr(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function ru(){var e=this.parentNode;e&&e.removeChild(this)}function ou(){return this.each(ru)}function au(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function lu(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function iu(e){return this.select(e?lu:au)}function su(e){return arguments.length?this.property("__data__",e):this.node().__data__}function uu(e){return function(t){e.call(this,t,this.__data__)}}function cu(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function du(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,a;n<o;++n)a=t[n],(!e.type||a.type===e.type)&&a.name===e.name?this.removeEventListener(a.type,a.listener,a.options):t[++r]=a;++r?t.length=r:delete this.__on}}}function fu(e,t,n){return function(){var r=this.__on,o,a=uu(t);if(r){for(var l=0,s=r.length;l<s;++l)if((o=r[l]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=a,o.options=n),o.value=t;return}}this.addEventListener(e.type,a,n),o={type:e.type,name:e.name,value:t,listener:a,options:n},r?r.push(o):this.__on=[o]}}function pu(e,t,n){var r=cu(e+""),o,a=r.length,l;if(arguments.length<2){var s=this.node().__on;if(s){for(var c=0,p=s.length,i;c<p;++c)for(o=0,i=s[c];o<a;++o)if((l=r[o]).type===i.type&&l.name===i.name)return i.value}return}for(s=t?fu:du,o=0;o<a;++o)this.each(s(r[o],t,n));return this}function ra(e,t,n){var r=Jo(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function hu(e,t){return function(){return ra(this,e,t)}}function mu(e,t){return function(){return ra(this,e,t.apply(this,arguments))}}function _u(e,t){return this.each((typeof t=="function"?mu:hu)(e,t))}function*vu(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,a=r.length,l;o<a;++o)(l=r[o])&&(yield l)}var oa=[null];function De(e,t){this._groups=e,this._parents=t}function Bt(){return new De([[document.documentElement]],oa)}function gu(){return this}De.prototype=Bt.prototype={constructor:De,select:Bi,selectAll:Ki,selectChild:Ji,selectChildren:ns,filter:rs,data:us,enter:os,exit:ds,join:fs,merge:ps,selection:gu,order:hs,sort:ms,call:vs,nodes:gs,node:ys,size:ws,empty:bs,each:xs,attr:$s,style:Ms,property:Ts,classed:zs,text:Hs,html:Ws,raise:js,lower:Zs,append:eu,insert:nu,remove:ou,clone:iu,datum:su,on:pu,dispatch:_u,[Symbol.iterator]:vu};function Xt(e){return typeof e=="string"?new De([[document.querySelector(e)]],[document.documentElement]):new De([[e]],oa)}function pr(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function aa(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Ht(){}var Nt=.7,sn=1/Nt,ct="\\s*([+-]?\\d+)\\s*",Ut="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ze="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",yu=/^#([0-9a-f]{3,8})$/,wu=new RegExp(`^rgb\\(${ct},${ct},${ct}\\)$`),bu=new RegExp(`^rgb\\(${ze},${ze},${ze}\\)$`),xu=new RegExp(`^rgba\\(${ct},${ct},${ct},${Ut}\\)$`),Su=new RegExp(`^rgba\\(${ze},${ze},${ze},${Ut}\\)$`),ku=new RegExp(`^hsl\\(${Ut},${ze},${ze}\\)$`),Cu=new RegExp(`^hsla\\(${Ut},${ze},${ze},${Ut}\\)$`),Br={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};pr(Ht,Tt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Hr,formatHex:Hr,formatHex8:Eu,formatHsl:Ru,formatRgb:Xr,toString:Xr});function Hr(){return this.rgb().formatHex()}function Eu(){return this.rgb().formatHex8()}function Ru(){return la(this).formatHsl()}function Xr(){return this.rgb().formatRgb()}function Tt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=yu.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Gr(t):n===3?new Ve(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Gt(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Gt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=wu.exec(e))?new Ve(t[1],t[2],t[3],1):(t=bu.exec(e))?new Ve(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=xu.exec(e))?Gt(t[1],t[2],t[3],t[4]):(t=Su.exec(e))?Gt(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=ku.exec(e))?Qr(t[1],t[2]/100,t[3]/100,1):(t=Cu.exec(e))?Qr(t[1],t[2]/100,t[3]/100,t[4]):Br.hasOwnProperty(e)?Gr(Br[e]):e==="transparent"?new Ve(NaN,NaN,NaN,0):null}function Gr(e){return new Ve(e>>16&255,e>>8&255,e&255,1)}function Gt(e,t,n,r){return r<=0&&(e=t=n=NaN),new Ve(e,t,n,r)}function Vu(e){return e instanceof Ht||(e=Tt(e)),e?(e=e.rgb(),new Ve(e.r,e.g,e.b,e.opacity)):new Ve}function qn(e,t,n,r){return arguments.length===1?Vu(e):new Ve(e,t,n,r??1)}function Ve(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}pr(Ve,qn,aa(Ht,{brighter(e){return e=e==null?sn:Math.pow(sn,e),new Ve(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Nt:Math.pow(Nt,e),new Ve(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ve(Ze(this.r),Ze(this.g),Ze(this.b),un(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Kr,formatHex:Kr,formatHex8:$u,formatRgb:Wr,toString:Wr}));function Kr(){return`#${Je(this.r)}${Je(this.g)}${Je(this.b)}`}function $u(){return`#${Je(this.r)}${Je(this.g)}${Je(this.b)}${Je((isNaN(this.opacity)?1:this.opacity)*255)}`}function Wr(){const e=un(this.opacity);return`${e===1?"rgb(":"rgba("}${Ze(this.r)}, ${Ze(this.g)}, ${Ze(this.b)}${e===1?")":`, ${e})`}`}function un(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Ze(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Je(e){return e=Ze(e),(e<16?"0":"")+e.toString(16)}function Qr(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Ne(e,t,n,r)}function la(e){if(e instanceof Ne)return new Ne(e.h,e.s,e.l,e.opacity);if(e instanceof Ht||(e=Tt(e)),!e)return new Ne;if(e instanceof Ne)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),a=Math.max(t,n,r),l=NaN,s=a-o,c=(a+o)/2;return s?(t===a?l=(n-r)/s+(n<r)*6:n===a?l=(r-t)/s+2:l=(t-n)/s+4,s/=c<.5?a+o:2-a-o,l*=60):s=c>0&&c<1?0:l,new Ne(l,s,c,e.opacity)}function Pu(e,t,n,r){return arguments.length===1?la(e):new Ne(e,t,n,r??1)}function Ne(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}pr(Ne,Pu,aa(Ht,{brighter(e){return e=e==null?sn:Math.pow(sn,e),new Ne(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Nt:Math.pow(Nt,e),new Ne(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new Ve(In(e>=240?e-240:e+120,o,r),In(e,o,r),In(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new Ne(jr(this.h),Kt(this.s),Kt(this.l),un(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=un(this.opacity);return`${e===1?"hsl(":"hsla("}${jr(this.h)}, ${Kt(this.s)*100}%, ${Kt(this.l)*100}%${e===1?")":`, ${e})`}`}}));function jr(e){return e=(e||0)%360,e<0?e+360:e}function Kt(e){return Math.max(0,Math.min(1,e||0))}function In(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const ia=e=>()=>e;function Du(e,t){return function(n){return e+n*t}}function Iu(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function Mu(e){return(e=+e)==1?sa:function(t,n){return n-t?Iu(t,n,e):ia(isNaN(t)?n:t)}}function sa(e,t){var n=t-e;return n?Du(e,n):ia(isNaN(e)?t:e)}const Jr=function e(t){var n=Mu(t);function r(o,a){var l=n((o=qn(o)).r,(a=qn(a)).r),s=n(o.g,a.g),c=n(o.b,a.b),p=sa(o.opacity,a.opacity);return function(i){return o.r=l(i),o.g=s(i),o.b=c(i),o.opacity=p(i),o+""}}return r.gamma=e,r}(1);function We(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Yn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Mn=new RegExp(Yn.source,"g");function Au(e){return function(){return e}}function Nu(e){return function(t){return e(t)+""}}function Uu(e,t){var n=Yn.lastIndex=Mn.lastIndex=0,r,o,a,l=-1,s=[],c=[];for(e=e+"",t=t+"";(r=Yn.exec(e))&&(o=Mn.exec(t));)(a=o.index)>n&&(a=t.slice(n,a),s[l]?s[l]+=a:s[++l]=a),(r=r[0])===(o=o[0])?s[l]?s[l]+=o:s[++l]=o:(s[++l]=null,c.push({i:l,x:We(r,o)})),n=Mn.lastIndex;return n<t.length&&(a=t.slice(n),s[l]?s[l]+=a:s[++l]=a),s.length<2?c[0]?Nu(c[0].x):Au(t):(t=c.length,function(p){for(var i=0,u;i<t;++i)s[(u=c[i]).i]=u.x(p);return s.join("")})}var Zr=180/Math.PI,Bn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function ua(e,t,n,r,o,a){var l,s,c;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,c/=s),e*r<t*n&&(e=-e,t=-t,c=-c,l=-l),{translateX:o,translateY:a,rotate:Math.atan2(t,e)*Zr,skewX:Math.atan(c)*Zr,scaleX:l,scaleY:s}}var Wt;function Tu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Bn:ua(t.a,t.b,t.c,t.d,t.e,t.f)}function Ou(e){return e==null||(Wt||(Wt=document.createElementNS("http://www.w3.org/2000/svg","g")),Wt.setAttribute("transform",e),!(e=Wt.transform.baseVal.consolidate()))?Bn:(e=e.matrix,ua(e.a,e.b,e.c,e.d,e.e,e.f))}function ca(e,t,n,r){function o(p){return p.length?p.pop()+" ":""}function a(p,i,u,d,h,w){if(p!==u||i!==d){var D=h.push("translate(",null,t,null,n);w.push({i:D-4,x:We(p,u)},{i:D-2,x:We(i,d)})}else(u||d)&&h.push("translate("+u+t+d+n)}function l(p,i,u,d){p!==i?(p-i>180?i+=360:i-p>180&&(p+=360),d.push({i:u.push(o(u)+"rotate(",null,r)-2,x:We(p,i)})):i&&u.push(o(u)+"rotate("+i+r)}function s(p,i,u,d){p!==i?d.push({i:u.push(o(u)+"skewX(",null,r)-2,x:We(p,i)}):i&&u.push(o(u)+"skewX("+i+r)}function c(p,i,u,d,h,w){if(p!==u||i!==d){var D=h.push(o(h)+"scale(",null,",",null,")");w.push({i:D-4,x:We(p,u)},{i:D-2,x:We(i,d)})}else(u!==1||d!==1)&&h.push(o(h)+"scale("+u+","+d+")")}return function(p,i){var u=[],d=[];return p=e(p),i=e(i),a(p.translateX,p.translateY,i.translateX,i.translateY,u,d),l(p.rotate,i.rotate,u,d),s(p.skewX,i.skewX,u,d),c(p.scaleX,p.scaleY,i.scaleX,i.scaleY,u,d),p=i=null,function(h){for(var w=-1,D=d.length,_;++w<D;)u[(_=d[w]).i]=_.x(h);return u.join("")}}}var Lu=ca(Tu,"px, ","px)","deg)"),Fu=ca(Ou,", ",")",")"),_t=0,St=0,xt=0,da=1e3,cn,kt,dn=0,tt=0,Cn=0,Ot=typeof performance=="object"&&performance.now?performance:Date,fa=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function hr(){return tt||(fa(zu),tt=Ot.now()+Cn)}function zu(){tt=0}function fn(){this._call=this._time=this._next=null}fn.prototype=pa.prototype={constructor:fn,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?hr():+n)+(t==null?0:+t),!this._next&&kt!==this&&(kt?kt._next=this:cn=this,kt=this),this._call=e,this._time=n,Hn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Hn())}};function pa(e,t,n){var r=new fn;return r.restart(e,t,n),r}function qu(){hr(),++_t;for(var e=cn,t;e;)(t=tt-e._time)>=0&&e._call.call(void 0,t),e=e._next;--_t}function eo(){tt=(dn=Ot.now())+Cn,_t=St=0;try{qu()}finally{_t=0,Bu(),tt=0}}function Yu(){var e=Ot.now(),t=e-dn;t>da&&(Cn-=t,dn=e)}function Bu(){for(var e,t=cn,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:cn=n);kt=e,Hn(r)}function Hn(e){if(!_t){St&&(St=clearTimeout(St));var t=e-tt;t>24?(e<1/0&&(St=setTimeout(eo,e-Ot.now()-Cn)),xt&&(xt=clearInterval(xt))):(xt||(dn=Ot.now(),xt=setInterval(Yu,da)),_t=1,fa(eo))}}function to(e,t,n){var r=new fn;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var Hu=Xo("start","end","cancel","interrupt"),Xu=[],ha=0,no=1,Xn=2,nn=3,ro=4,Gn=5,rn=6;function En(e,t,n,r,o,a){var l=e.__transition;if(!l)e.__transition={};else if(n in l)return;Gu(e,n,{name:t,index:r,group:o,on:Hu,tween:Xu,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:ha})}function mr(e,t){var n=Oe(e,t);if(n.state>ha)throw new Error("too late; already scheduled");return n}function qe(e,t){var n=Oe(e,t);if(n.state>nn)throw new Error("too late; already running");return n}function Oe(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Gu(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=pa(a,0,n.time);function a(p){n.state=no,n.timer.restart(l,n.delay,n.time),n.delay<=p&&l(p-n.delay)}function l(p){var i,u,d,h;if(n.state!==no)return c();for(i in r)if(h=r[i],h.name===n.name){if(h.state===nn)return to(l);h.state===ro?(h.state=rn,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[i]):+i<t&&(h.state=rn,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[i])}if(to(function(){n.state===nn&&(n.state=ro,n.timer.restart(s,n.delay,n.time),s(p))}),n.state=Xn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Xn){for(n.state=nn,o=new Array(d=n.tween.length),i=0,u=-1;i<d;++i)(h=n.tween[i].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(p){for(var i=p<n.duration?n.ease.call(null,p/n.duration):(n.timer.restart(c),n.state=Gn,1),u=-1,d=o.length;++u<d;)o[u].call(e,i);n.state===Gn&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){n.state=rn,n.timer.stop(),delete r[t];for(var p in r)return;delete e.__transition}}function Ku(e,t){var n=e.__transition,r,o,a=!0,l;if(n){t=t==null?null:t+"";for(l in n){if((r=n[l]).name!==t){a=!1;continue}o=r.state>Xn&&r.state<Gn,r.state=rn,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[l]}a&&delete e.__transition}}function Wu(e){return this.each(function(){Ku(this,e)})}function Qu(e,t){var n,r;return function(){var o=qe(this,e),a=o.tween;if(a!==n){r=n=a;for(var l=0,s=r.length;l<s;++l)if(r[l].name===t){r=r.slice(),r.splice(l,1);break}}o.tween=r}}function ju(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var a=qe(this,e),l=a.tween;if(l!==r){o=(r=l).slice();for(var s={name:t,value:n},c=0,p=o.length;c<p;++c)if(o[c].name===t){o[c]=s;break}c===p&&o.push(s)}a.tween=o}}function Ju(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Oe(this.node(),n).tween,o=0,a=r.length,l;o<a;++o)if((l=r[o]).name===e)return l.value;return null}return this.each((t==null?Qu:ju)(n,e,t))}function _r(e,t,n){var r=e._id;return e.each(function(){var o=qe(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return Oe(o,r).value[t]}}function ma(e,t){var n;return(typeof t=="number"?We:t instanceof Tt?Jr:(n=Tt(t))?(t=n,Jr):Uu)(e,t)}function Zu(e){return function(){this.removeAttribute(e)}}function ec(e){return function(){this.removeAttributeNS(e.space,e.local)}}function tc(e,t,n){var r,o=n+"",a;return function(){var l=this.getAttribute(e);return l===o?null:l===r?a:a=t(r=l,n)}}function nc(e,t,n){var r,o=n+"",a;return function(){var l=this.getAttributeNS(e.space,e.local);return l===o?null:l===r?a:a=t(r=l,n)}}function rc(e,t,n){var r,o,a;return function(){var l,s=n(this),c;return s==null?void this.removeAttribute(e):(l=this.getAttribute(e),c=s+"",l===c?null:l===r&&c===o?a:(o=c,a=t(r=l,s)))}}function oc(e,t,n){var r,o,a;return function(){var l,s=n(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(l=this.getAttributeNS(e.space,e.local),c=s+"",l===c?null:l===r&&c===o?a:(o=c,a=t(r=l,s)))}}function ac(e,t){var n=kn(e),r=n==="transform"?Fu:ma;return this.attrTween(e,typeof t=="function"?(n.local?oc:rc)(n,r,_r(this,"attr."+e,t)):t==null?(n.local?ec:Zu)(n):(n.local?nc:tc)(n,r,t))}function lc(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function ic(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function sc(e,t){var n,r;function o(){var a=t.apply(this,arguments);return a!==r&&(n=(r=a)&&ic(e,a)),n}return o._value=t,o}function uc(e,t){var n,r;function o(){var a=t.apply(this,arguments);return a!==r&&(n=(r=a)&&lc(e,a)),n}return o._value=t,o}function cc(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=kn(e);return this.tween(n,(r.local?sc:uc)(r,t))}function dc(e,t){return function(){mr(this,e).delay=+t.apply(this,arguments)}}function fc(e,t){return t=+t,function(){mr(this,e).delay=t}}function pc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?dc:fc)(t,e)):Oe(this.node(),t).delay}function hc(e,t){return function(){qe(this,e).duration=+t.apply(this,arguments)}}function mc(e,t){return t=+t,function(){qe(this,e).duration=t}}function _c(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?hc:mc)(t,e)):Oe(this.node(),t).duration}function vc(e,t){if(typeof t!="function")throw new Error;return function(){qe(this,e).ease=t}}function gc(e){var t=this._id;return arguments.length?this.each(vc(t,e)):Oe(this.node(),t).ease}function yc(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;qe(this,e).ease=n}}function wc(e){if(typeof e!="function")throw new Error;return this.each(yc(this._id,e))}function bc(e){typeof e!="function"&&(e=Wo(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var a=t[o],l=a.length,s=r[o]=[],c,p=0;p<l;++p)(c=a[p])&&e.call(c,c.__data__,p,a)&&s.push(c);return new Xe(r,this._parents,this._name,this._id)}function xc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,a=Math.min(r,o),l=new Array(r),s=0;s<a;++s)for(var c=t[s],p=n[s],i=c.length,u=l[s]=new Array(i),d,h=0;h<i;++h)(d=c[h]||p[h])&&(u[h]=d);for(;s<r;++s)l[s]=t[s];return new Xe(l,this._parents,this._name,this._id)}function Sc(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function kc(e,t,n){var r,o,a=Sc(t)?mr:qe;return function(){var l=a(this,e),s=l.on;s!==r&&(o=(r=s).copy()).on(t,n),l.on=o}}function Cc(e,t){var n=this._id;return arguments.length<2?Oe(this.node(),n).on.on(e):this.each(kc(n,e,t))}function Ec(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function Rc(){return this.on("end.remove",Ec(this._id))}function Vc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=dr(e));for(var r=this._groups,o=r.length,a=new Array(o),l=0;l<o;++l)for(var s=r[l],c=s.length,p=a[l]=new Array(c),i,u,d=0;d<c;++d)(i=s[d])&&(u=e.call(i,i.__data__,d,s))&&("__data__"in i&&(u.__data__=i.__data__),p[d]=u,En(p[d],t,n,d,p,Oe(i,n)));return new Xe(a,this._parents,t,n)}function $c(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Ko(e));for(var r=this._groups,o=r.length,a=[],l=[],s=0;s<o;++s)for(var c=r[s],p=c.length,i,u=0;u<p;++u)if(i=c[u]){for(var d=e.call(i,i.__data__,u,c),h,w=Oe(i,n),D=0,_=d.length;D<_;++D)(h=d[D])&&En(h,t,n,D,d,w);a.push(d),l.push(i)}return new Xe(a,l,t,n)}var Pc=Bt.prototype.constructor;function Dc(){return new Pc(this._groups,this._parents)}function Ic(e,t){var n,r,o;return function(){var a=mt(this,e),l=(this.style.removeProperty(e),mt(this,e));return a===l?null:a===n&&l===r?o:o=t(n=a,r=l)}}function _a(e){return function(){this.style.removeProperty(e)}}function Mc(e,t,n){var r,o=n+"",a;return function(){var l=mt(this,e);return l===o?null:l===r?a:a=t(r=l,n)}}function Ac(e,t,n){var r,o,a;return function(){var l=mt(this,e),s=n(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),mt(this,e))),l===c?null:l===r&&c===o?a:(o=c,a=t(r=l,s))}}function Nc(e,t){var n,r,o,a="style."+t,l="end."+a,s;return function(){var c=qe(this,e),p=c.on,i=c.value[a]==null?s||(s=_a(t)):void 0;(p!==n||o!==i)&&(r=(n=p).copy()).on(l,o=i),c.on=r}}function Uc(e,t,n){var r=(e+="")=="transform"?Lu:ma;return t==null?this.styleTween(e,Ic(e,r)).on("end.style."+e,_a(e)):typeof t=="function"?this.styleTween(e,Ac(e,r,_r(this,"style."+e,t))).each(Nc(this._id,e)):this.styleTween(e,Mc(e,r,t),n).on("end.style."+e,null)}function Tc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function Oc(e,t,n){var r,o;function a(){var l=t.apply(this,arguments);return l!==o&&(r=(o=l)&&Tc(e,l,n)),r}return a._value=t,a}function Lc(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,Oc(e,t,n??""))}function Fc(e){return function(){this.textContent=e}}function zc(e){return function(){var t=e(this);this.textContent=t??""}}function qc(e){return this.tween("text",typeof e=="function"?zc(_r(this,"text",e)):Fc(e==null?"":e+""))}function Yc(e){return function(t){this.textContent=e.call(this,t)}}function Bc(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&Yc(o)),t}return r._value=e,r}function Hc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Bc(e))}function Xc(){for(var e=this._name,t=this._id,n=va(),r=this._groups,o=r.length,a=0;a<o;++a)for(var l=r[a],s=l.length,c,p=0;p<s;++p)if(c=l[p]){var i=Oe(c,t);En(c,e,n,p,l,{time:i.time+i.delay+i.duration,delay:0,duration:i.duration,ease:i.ease})}return new Xe(r,this._parents,e,n)}function Gc(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(a,l){var s={value:l},c={value:function(){--o===0&&a()}};n.each(function(){var p=qe(this,r),i=p.on;i!==e&&(t=(e=i).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),p.on=t}),o===0&&a()})}var Kc=0;function Xe(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function va(){return++Kc}var Be=Bt.prototype;Xe.prototype={constructor:Xe,select:Vc,selectAll:$c,selectChild:Be.selectChild,selectChildren:Be.selectChildren,filter:bc,merge:xc,selection:Dc,transition:Xc,call:Be.call,nodes:Be.nodes,node:Be.node,size:Be.size,empty:Be.empty,each:Be.each,on:Cc,attr:ac,attrTween:cc,style:Uc,styleTween:Lc,text:qc,textTween:Hc,remove:Rc,tween:Ju,delay:pc,duration:_c,ease:gc,easeVarying:wc,end:Gc,[Symbol.iterator]:Be[Symbol.iterator]};function Wc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Qc={time:null,delay:0,duration:250,ease:Wc};function jc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Jc(e){var t,n;e instanceof Xe?(t=e._id,e=e._name):(t=va(),(n=Qc).time=hr(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,a=0;a<o;++a)for(var l=r[a],s=l.length,c,p=0;p<s;++p)(c=l[p])&&En(c,e,t,p,l,n||jc(c,t));return new Xe(r,this._parents,e,t)}Bt.prototype.interrupt=Wu;Bt.prototype.transition=Jc;function Zc(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function ed(e,t){return fetch(e,t).then(Zc)}var pe=1e-6,ie=Math.PI,$e=ie/2,oo=ie/4,Ie=ie*2,Pe=180/ie,ke=ie/180,ye=Math.abs,ga=Math.atan,Lt=Math.atan2,xe=Math.cos,td=Math.exp,nd=Math.log,Se=Math.sin,rd=Math.sign||function(e){return e>0?1:e<0?-1:0},ot=Math.sqrt,od=Math.tan;function ad(e){return e>1?0:e<-1?ie:Math.acos(e)}function Ft(e){return e>1?$e:e<-1?-$e:Math.asin(e)}function Ae(){}function pn(e,t){e&&lo.hasOwnProperty(e.type)&&lo[e.type](e,t)}var ao={Feature:function(e,t){pn(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,o=n.length;++r<o;)pn(n[r].geometry,t)}},lo={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){Kn(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)Kn(n[r],t,0)},Polygon:function(e,t){io(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)io(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,o=n.length;++r<o;)pn(n[r],t)}};function Kn(e,t,n){var r=-1,o=e.length-n,a;for(t.lineStart();++r<o;)a=e[r],t.point(a[0],a[1],a[2]);t.lineEnd()}function io(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)Kn(e[n],t,1);t.polygonEnd()}function it(e,t){e&&ao.hasOwnProperty(e.type)?ao[e.type](e,t):pn(e,t)}function Wn(e){return[Lt(e[1],e[0]),Ft(e[2])]}function vt(e){var t=e[0],n=e[1],r=xe(n);return[r*xe(t),r*Se(t),Se(n)]}function Qt(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function hn(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function An(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function jt(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function Qn(e){var t=ot(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function jn(e,t){function n(r,o){return r=e(r,o),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,o){return r=t.invert(r,o),r&&e.invert(r[0],r[1])}),n}function Jn(e,t){return ye(e)>ie&&(e-=Math.round(e/Ie)*Ie),[e,t]}Jn.invert=Jn;function ya(e,t,n){return(e%=Ie)?t||n?jn(uo(e),co(t,n)):uo(e):t||n?co(t,n):Jn}function so(e){return function(t,n){return t+=e,ye(t)>ie&&(t-=Math.round(t/Ie)*Ie),[t,n]}}function uo(e){var t=so(e);return t.invert=so(-e),t}function co(e,t){var n=xe(e),r=Se(e),o=xe(t),a=Se(t);function l(s,c){var p=xe(c),i=xe(s)*p,u=Se(s)*p,d=Se(c),h=d*n+i*r;return[Lt(u*o-h*a,i*n-d*r),Ft(h*o+u*a)]}return l.invert=function(s,c){var p=xe(c),i=xe(s)*p,u=Se(s)*p,d=Se(c),h=d*o-u*a;return[Lt(u*o+d*a,i*n+h*r),Ft(h*n-i*r)]},l}function ld(e){e=ya(e[0]*ke,e[1]*ke,e.length>2?e[2]*ke:0);function t(n){return n=e(n[0]*ke,n[1]*ke),n[0]*=Pe,n[1]*=Pe,n}return t.invert=function(n){return n=e.invert(n[0]*ke,n[1]*ke),n[0]*=Pe,n[1]*=Pe,n},t}function id(e,t,n,r,o,a){if(n){var l=xe(t),s=Se(t),c=r*n;o==null?(o=t+r*Ie,a=t-c/2):(o=fo(l,o),a=fo(l,a),(r>0?o<a:o>a)&&(o+=r*Ie));for(var p,i=o;r>0?i>a:i<a;i-=c)p=Wn([l,-s*xe(i),-s*Se(i)]),e.point(p[0],p[1])}}function fo(e,t){t=vt(t),t[0]-=e,Qn(t);var n=ad(-t[1]);return((-t[2]<0?-n:n)+Ie-pe)%Ie}function wa(){var e=[],t;return{point:function(n,r,o){t.push([n,r,o])},lineStart:function(){e.push(t=[])},lineEnd:Ae,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function on(e,t){return ye(e[0]-t[0])<pe&&ye(e[1]-t[1])<pe}function Jt(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function ba(e,t,n,r,o){var a=[],l=[],s,c;if(e.forEach(function(w){if(!((D=w.length-1)<=0)){var D,_=w[0],C=w[D],k;if(on(_,C)){if(!_[2]&&!C[2]){for(o.lineStart(),s=0;s<D;++s)o.point((_=w[s])[0],_[1]);o.lineEnd();return}C[0]+=2*pe}a.push(k=new Jt(_,w,null,!0)),l.push(k.o=new Jt(_,null,k,!1)),a.push(k=new Jt(C,w,null,!1)),l.push(k.o=new Jt(C,null,k,!0))}}),!!a.length){for(l.sort(t),po(a),po(l),s=0,c=l.length;s<c;++s)l[s].e=n=!n;for(var p=a[0],i,u;;){for(var d=p,h=!0;d.v;)if((d=d.n)===p)return;i=d.z,o.lineStart();do{if(d.v=d.o.v=!0,d.e){if(h)for(s=0,c=i.length;s<c;++s)o.point((u=i[s])[0],u[1]);else r(d.x,d.n.x,1,o);d=d.n}else{if(h)for(i=d.p.z,s=i.length-1;s>=0;--s)o.point((u=i[s])[0],u[1]);else r(d.x,d.p.x,-1,o);d=d.p}d=d.o,i=d.z,h=!h}while(!d.v);o.lineEnd()}}}function po(e){if(t=e.length){for(var t,n=0,r=e[0],o;++n<t;)r.n=o=e[n],o.p=r,r=o;r.n=o=e[0],o.p=r}}function Nn(e){return ye(e[0])<=ie?e[0]:rd(e[0])*((ye(e[0])+ie)%Ie-ie)}function sd(e,t){var n=Nn(t),r=t[1],o=Se(r),a=[Se(n),-xe(n),0],l=0,s=0,c=new et;o===1?r=$e+pe:o===-1&&(r=-$e-pe);for(var p=0,i=e.length;p<i;++p)if(d=(u=e[p]).length)for(var u,d,h=u[d-1],w=Nn(h),D=h[1]/2+oo,_=Se(D),C=xe(D),k=0;k<d;++k,w=U,_=q,C=O,h=A){var A=u[k],U=Nn(A),H=A[1]/2+oo,q=Se(H),O=xe(H),j=U-w,ee=j>=0?1:-1,m=ee*j,b=m>ie,P=_*q;if(c.add(Lt(P*ee*Se(m),C*O+P*xe(m))),l+=b?j+ee*Ie:j,b^w>=n^U>=n){var V=hn(vt(h),vt(A));Qn(V);var I=hn(a,V);Qn(I);var x=(b^j>=0?-1:1)*Ft(I[2]);(r>x||r===x&&(V[0]||V[1]))&&(s+=b^j>=0?1:-1)}}return(l<-1e-6||l<pe&&c<-1e-12)^s&1}function xa(e,t,n,r){return function(o){var a=t(o),l=wa(),s=t(l),c=!1,p,i,u,d={point:h,lineStart:D,lineEnd:_,polygonStart:function(){d.point=C,d.lineStart=k,d.lineEnd=A,i=[],p=[]},polygonEnd:function(){d.point=h,d.lineStart=D,d.lineEnd=_,i=Ho(i);var U=sd(p,r);i.length?(c||(o.polygonStart(),c=!0),ba(i,cd,U,n,o)):U&&(c||(o.polygonStart(),c=!0),o.lineStart(),n(null,null,1,o),o.lineEnd()),c&&(o.polygonEnd(),c=!1),i=p=null},sphere:function(){o.polygonStart(),o.lineStart(),n(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function h(U,H){e(U,H)&&o.point(U,H)}function w(U,H){a.point(U,H)}function D(){d.point=w,a.lineStart()}function _(){d.point=h,a.lineEnd()}function C(U,H){u.push([U,H]),s.point(U,H)}function k(){s.lineStart(),u=[]}function A(){C(u[0][0],u[0][1]),s.lineEnd();var U=s.clean(),H=l.result(),q,O=H.length,j,ee,m;if(u.pop(),p.push(u),u=null,!!O){if(U&1){if(ee=H[0],(j=ee.length-1)>0){for(c||(o.polygonStart(),c=!0),o.lineStart(),q=0;q<j;++q)o.point((m=ee[q])[0],m[1]);o.lineEnd()}return}O>1&&U&2&&H.push(H.pop().concat(H.shift())),i.push(H.filter(ud))}}return d}}function ud(e){return e.length>1}function cd(e,t){return((e=e.x)[0]<0?e[1]-$e-pe:$e-e[1])-((t=t.x)[0]<0?t[1]-$e-pe:$e-t[1])}const ho=xa(function(){return!0},dd,pd,[-ie,-$e]);function dd(e){var t=NaN,n=NaN,r=NaN,o;return{lineStart:function(){e.lineStart(),o=1},point:function(a,l){var s=a>0?ie:-ie,c=ye(a-t);ye(c-ie)<pe?(e.point(t,n=(n+l)/2>0?$e:-$e),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),e.point(a,n),o=0):r!==s&&c>=ie&&(ye(t-r)<pe&&(t-=r*pe),ye(a-s)<pe&&(a-=s*pe),n=fd(t,n,a,l),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),o=0),e.point(t=a,n=l),r=s},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-o}}}function fd(e,t,n,r){var o,a,l=Se(e-n);return ye(l)>pe?ga((Se(t)*(a=xe(r))*Se(n)-Se(r)*(o=xe(t))*Se(e))/(o*a*l)):(t+r)/2}function pd(e,t,n,r){var o;if(e==null)o=n*$e,r.point(-ie,o),r.point(0,o),r.point(ie,o),r.point(ie,0),r.point(ie,-o),r.point(0,-o),r.point(-ie,-o),r.point(-ie,0),r.point(-ie,o);else if(ye(e[0]-t[0])>pe){var a=e[0]<t[0]?ie:-ie;o=n*a/2,r.point(-a,o),r.point(0,o),r.point(a,o)}else r.point(t[0],t[1])}function hd(e){var t=xe(e),n=2*ke,r=t>0,o=ye(t)>pe;function a(i,u,d,h){id(h,e,n,d,i,u)}function l(i,u){return xe(i)*xe(u)>t}function s(i){var u,d,h,w,D;return{lineStart:function(){w=h=!1,D=1},point:function(_,C){var k=[_,C],A,U=l(_,C),H=r?U?0:p(_,C):U?p(_+(_<0?ie:-ie),C):0;if(!u&&(w=h=U)&&i.lineStart(),U!==h&&(A=c(u,k),(!A||on(u,A)||on(k,A))&&(k[2]=1)),U!==h)D=0,U?(i.lineStart(),A=c(k,u),i.point(A[0],A[1])):(A=c(u,k),i.point(A[0],A[1],2),i.lineEnd()),u=A;else if(o&&u&&r^U){var q;!(H&d)&&(q=c(k,u,!0))&&(D=0,r?(i.lineStart(),i.point(q[0][0],q[0][1]),i.point(q[1][0],q[1][1]),i.lineEnd()):(i.point(q[1][0],q[1][1]),i.lineEnd(),i.lineStart(),i.point(q[0][0],q[0][1],3)))}U&&(!u||!on(u,k))&&i.point(k[0],k[1]),u=k,h=U,d=H},lineEnd:function(){h&&i.lineEnd(),u=null},clean:function(){return D|(w&&h)<<1}}}function c(i,u,d){var h=vt(i),w=vt(u),D=[1,0,0],_=hn(h,w),C=Qt(_,_),k=_[0],A=C-k*k;if(!A)return!d&&i;var U=t*C/A,H=-t*k/A,q=hn(D,_),O=jt(D,U),j=jt(_,H);An(O,j);var ee=q,m=Qt(O,ee),b=Qt(ee,ee),P=m*m-b*(Qt(O,O)-1);if(!(P<0)){var V=ot(P),I=jt(ee,(-m-V)/b);if(An(I,O),I=Wn(I),!d)return I;var x=i[0],B=u[0],E=i[1],y=u[1],R;B<x&&(R=x,x=B,B=R);var Y=B-x,z=ye(Y-ie)<pe,J=z||Y<pe;if(!z&&y<E&&(R=E,E=y,y=R),J?z?E+y>0^I[1]<(ye(I[0]-x)<pe?E:y):E<=I[1]&&I[1]<=y:Y>ie^(x<=I[0]&&I[0]<=B)){var se=jt(ee,(-m+V)/b);return An(se,O),[I,Wn(se)]}}}function p(i,u){var d=r?e:ie-e,h=0;return i<-d?h|=1:i>d&&(h|=2),u<-d?h|=4:u>d&&(h|=8),h}return xa(l,s,a,r?[0,-e]:[-ie,e-ie])}function md(e,t,n,r,o,a){var l=e[0],s=e[1],c=t[0],p=t[1],i=0,u=1,d=c-l,h=p-s,w;if(w=n-l,!(!d&&w>0)){if(w/=d,d<0){if(w<i)return;w<u&&(u=w)}else if(d>0){if(w>u)return;w>i&&(i=w)}if(w=o-l,!(!d&&w<0)){if(w/=d,d<0){if(w>u)return;w>i&&(i=w)}else if(d>0){if(w<i)return;w<u&&(u=w)}if(w=r-s,!(!h&&w>0)){if(w/=h,h<0){if(w<i)return;w<u&&(u=w)}else if(h>0){if(w>u)return;w>i&&(i=w)}if(w=a-s,!(!h&&w<0)){if(w/=h,h<0){if(w>u)return;w>i&&(i=w)}else if(h>0){if(w<i)return;w<u&&(u=w)}return i>0&&(e[0]=l+i*d,e[1]=s+i*h),u<1&&(t[0]=l+u*d,t[1]=s+u*h),!0}}}}}var Zt=1e9,en=-1e9;function _d(e,t,n,r){function o(p,i){return e<=p&&p<=n&&t<=i&&i<=r}function a(p,i,u,d){var h=0,w=0;if(p==null||(h=l(p,u))!==(w=l(i,u))||c(p,i)<0^u>0)do d.point(h===0||h===3?e:n,h>1?r:t);while((h=(h+u+4)%4)!==w);else d.point(i[0],i[1])}function l(p,i){return ye(p[0]-e)<pe?i>0?0:3:ye(p[0]-n)<pe?i>0?2:1:ye(p[1]-t)<pe?i>0?1:0:i>0?3:2}function s(p,i){return c(p.x,i.x)}function c(p,i){var u=l(p,1),d=l(i,1);return u!==d?u-d:u===0?i[1]-p[1]:u===1?p[0]-i[0]:u===2?p[1]-i[1]:i[0]-p[0]}return function(p){var i=p,u=wa(),d,h,w,D,_,C,k,A,U,H,q,O={point:j,lineStart:P,lineEnd:V,polygonStart:m,polygonEnd:b};function j(x,B){o(x,B)&&i.point(x,B)}function ee(){for(var x=0,B=0,E=h.length;B<E;++B)for(var y=h[B],R=1,Y=y.length,z=y[0],J,se,we=z[0],te=z[1];R<Y;++R)J=we,se=te,z=y[R],we=z[0],te=z[1],se<=r?te>r&&(we-J)*(r-se)>(te-se)*(e-J)&&++x:te<=r&&(we-J)*(r-se)<(te-se)*(e-J)&&--x;return x}function m(){i=u,d=[],h=[],q=!0}function b(){var x=ee(),B=q&&x,E=(d=Ho(d)).length;(B||E)&&(p.polygonStart(),B&&(p.lineStart(),a(null,null,1,p),p.lineEnd()),E&&ba(d,s,x,a,p),p.polygonEnd()),i=p,d=h=w=null}function P(){O.point=I,h&&h.push(w=[]),H=!0,U=!1,k=A=NaN}function V(){d&&(I(D,_),C&&U&&u.rejoin(),d.push(u.result())),O.point=j,U&&i.lineEnd()}function I(x,B){var E=o(x,B);if(h&&w.push([x,B]),H)D=x,_=B,C=E,H=!1,E&&(i.lineStart(),i.point(x,B));else if(E&&U)i.point(x,B);else{var y=[k=Math.max(en,Math.min(Zt,k)),A=Math.max(en,Math.min(Zt,A))],R=[x=Math.max(en,Math.min(Zt,x)),B=Math.max(en,Math.min(Zt,B))];md(y,R,e,t,n,r)?(U||(i.lineStart(),i.point(y[0],y[1])),i.point(R[0],R[1]),E||i.lineEnd(),q=!1):E&&(i.lineStart(),i.point(x,B),q=!1)}k=x,A=B,U=E}return O}}const Zn=e=>e;var Un=new et,er=new et,Sa,ka,tr,nr,He={point:Ae,lineStart:Ae,lineEnd:Ae,polygonStart:function(){He.lineStart=vd,He.lineEnd=yd},polygonEnd:function(){He.lineStart=He.lineEnd=He.point=Ae,Un.add(ye(er)),er=new et},result:function(){var e=Un/2;return Un=new et,e}};function vd(){He.point=gd}function gd(e,t){He.point=Ca,Sa=tr=e,ka=nr=t}function Ca(e,t){er.add(nr*e-tr*t),tr=e,nr=t}function yd(){Ca(Sa,ka)}var gt=1/0,mn=gt,zt=-gt,_n=zt,vn={point:wd,lineStart:Ae,lineEnd:Ae,polygonStart:Ae,polygonEnd:Ae,result:function(){var e=[[gt,mn],[zt,_n]];return zt=_n=-(mn=gt=1/0),e}};function wd(e,t){e<gt&&(gt=e),e>zt&&(zt=e),t<mn&&(mn=t),t>_n&&(_n=t)}var rr=0,or=0,Ct=0,gn=0,yn=0,st=0,ar=0,lr=0,Et=0,Ea,Ra,Le,Fe,Me={point:nt,lineStart:mo,lineEnd:_o,polygonStart:function(){Me.lineStart=Sd,Me.lineEnd=kd},polygonEnd:function(){Me.point=nt,Me.lineStart=mo,Me.lineEnd=_o},result:function(){var e=Et?[ar/Et,lr/Et]:st?[gn/st,yn/st]:Ct?[rr/Ct,or/Ct]:[NaN,NaN];return rr=or=Ct=gn=yn=st=ar=lr=Et=0,e}};function nt(e,t){rr+=e,or+=t,++Ct}function mo(){Me.point=bd}function bd(e,t){Me.point=xd,nt(Le=e,Fe=t)}function xd(e,t){var n=e-Le,r=t-Fe,o=ot(n*n+r*r);gn+=o*(Le+e)/2,yn+=o*(Fe+t)/2,st+=o,nt(Le=e,Fe=t)}function _o(){Me.point=nt}function Sd(){Me.point=Cd}function kd(){Va(Ea,Ra)}function Cd(e,t){Me.point=Va,nt(Ea=Le=e,Ra=Fe=t)}function Va(e,t){var n=e-Le,r=t-Fe,o=ot(n*n+r*r);gn+=o*(Le+e)/2,yn+=o*(Fe+t)/2,st+=o,o=Fe*e-Le*t,ar+=o*(Le+e),lr+=o*(Fe+t),Et+=o*3,nt(Le=e,Fe=t)}function $a(e){this._context=e}$a.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,Ie);break}}},result:Ae};var ir=new et,Tn,Pa,Da,Rt,Vt,qt={point:Ae,lineStart:function(){qt.point=Ed},lineEnd:function(){Tn&&Ia(Pa,Da),qt.point=Ae},polygonStart:function(){Tn=!0},polygonEnd:function(){Tn=null},result:function(){var e=+ir;return ir=new et,e}};function Ed(e,t){qt.point=Ia,Pa=Rt=e,Da=Vt=t}function Ia(e,t){Rt-=e,Vt-=t,ir.add(ot(Rt*Rt+Vt*Vt)),Rt=e,Vt=t}let vo,wn,go,yo;class wo{constructor(t){this._append=t==null?Ma:Rd(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==go||this._append!==wn){const r=this._radius,o=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,go=r,wn=this._append,yo=this._,this._=o}this._+=yo;break}}}result(){const t=this._;return this._="",t.length?t:null}}function Ma(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function Rd(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return Ma;if(t!==vo){const n=10**t;vo=t,wn=function(o){let a=1;this._+=o[0];for(const l=o.length;a<l;++a)this._+=Math.round(arguments[a]*n)/n+o[a]}}return wn}function bo(e,t){let n=3,r=4.5,o,a;function l(s){return s&&(typeof r=="function"&&a.pointRadius(+r.apply(this,arguments)),it(s,o(a))),a.result()}return l.area=function(s){return it(s,o(He)),He.result()},l.measure=function(s){return it(s,o(qt)),qt.result()},l.bounds=function(s){return it(s,o(vn)),vn.result()},l.centroid=function(s){return it(s,o(Me)),Me.result()},l.projection=function(s){return arguments.length?(o=s==null?(e=null,Zn):(e=s).stream,l):e},l.context=function(s){return arguments.length?(a=s==null?(t=null,new wo(n)):new $a(t=s),typeof r!="function"&&a.pointRadius(r),l):t},l.pointRadius=function(s){return arguments.length?(r=typeof s=="function"?s:(a.pointRadius(+s),+s),l):r},l.digits=function(s){if(!arguments.length)return n;if(s==null)n=null;else{const c=Math.floor(s);if(!(c>=0))throw new RangeError(`invalid digits: ${s}`);n=c}return t===null&&(a=new wo(n)),l},l.projection(e).digits(n).context(t)}function vr(e){return function(t){var n=new sr;for(var r in e)n[r]=e[r];return n.stream=t,n}}function sr(){}sr.prototype={constructor:sr,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function gr(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),it(n,e.stream(vn)),t(vn.result()),r!=null&&e.clipExtent(r),e}function Aa(e,t,n){return gr(e,function(r){var o=t[1][0]-t[0][0],a=t[1][1]-t[0][1],l=Math.min(o/(r[1][0]-r[0][0]),a/(r[1][1]-r[0][1])),s=+t[0][0]+(o-l*(r[1][0]+r[0][0]))/2,c=+t[0][1]+(a-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([s,c])},n)}function Vd(e,t,n){return Aa(e,[[0,0],t],n)}function $d(e,t,n){return gr(e,function(r){var o=+t,a=o/(r[1][0]-r[0][0]),l=(o-a*(r[1][0]+r[0][0]))/2,s=-a*r[0][1];e.scale(150*a).translate([l,s])},n)}function Pd(e,t,n){return gr(e,function(r){var o=+t,a=o/(r[1][1]-r[0][1]),l=-a*r[0][0],s=(o-a*(r[1][1]+r[0][1]))/2;e.scale(150*a).translate([l,s])},n)}var xo=16,Dd=xe(30*ke);function So(e,t){return+t?Md(e,t):Id(e)}function Id(e){return vr({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function Md(e,t){function n(r,o,a,l,s,c,p,i,u,d,h,w,D,_){var C=p-r,k=i-o,A=C*C+k*k;if(A>4*t&&D--){var U=l+d,H=s+h,q=c+w,O=ot(U*U+H*H+q*q),j=Ft(q/=O),ee=ye(ye(q)-1)<pe||ye(a-u)<pe?(a+u)/2:Lt(H,U),m=e(ee,j),b=m[0],P=m[1],V=b-r,I=P-o,x=k*V-C*I;(x*x/A>t||ye((C*V+k*I)/A-.5)>.3||l*d+s*h+c*w<Dd)&&(n(r,o,a,l,s,c,b,P,ee,U/=O,H/=O,q,D,_),_.point(b,P),n(b,P,ee,U,H,q,p,i,u,d,h,w,D,_))}}return function(r){var o,a,l,s,c,p,i,u,d,h,w,D,_={point:C,lineStart:k,lineEnd:U,polygonStart:function(){r.polygonStart(),_.lineStart=H},polygonEnd:function(){r.polygonEnd(),_.lineStart=k}};function C(j,ee){j=e(j,ee),r.point(j[0],j[1])}function k(){u=NaN,_.point=A,r.lineStart()}function A(j,ee){var m=vt([j,ee]),b=e(j,ee);n(u,d,i,h,w,D,u=b[0],d=b[1],i=j,h=m[0],w=m[1],D=m[2],xo,r),r.point(u,d)}function U(){_.point=C,r.lineEnd()}function H(){k(),_.point=q,_.lineEnd=O}function q(j,ee){A(o=j,ee),a=u,l=d,s=h,c=w,p=D,_.point=A}function O(){n(u,d,i,h,w,D,a,l,o,s,c,p,xo,r),_.lineEnd=U,U()}return _}}var Ad=vr({point:function(e,t){this.stream.point(e*ke,t*ke)}});function Nd(e){return vr({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function Ud(e,t,n,r,o){function a(l,s){return l*=r,s*=o,[t+e*l,n-e*s]}return a.invert=function(l,s){return[(l-t)/e*r,(n-s)/e*o]},a}function ko(e,t,n,r,o,a){if(!a)return Ud(e,t,n,r,o);var l=xe(a),s=Se(a),c=l*e,p=s*e,i=l/e,u=s/e,d=(s*n-l*t)/e,h=(s*t+l*n)/e;function w(D,_){return D*=r,_*=o,[c*D-p*_+t,n-p*D-c*_]}return w.invert=function(D,_){return[r*(i*D-u*_+d),o*(h-u*D-i*_)]},w}function Td(e){return Od(function(){return e})()}function Od(e){var t,n=150,r=480,o=250,a=0,l=0,s=0,c=0,p=0,i,u=0,d=1,h=1,w=null,D=ho,_=null,C,k,A,U=Zn,H=.5,q,O,j,ee,m;function b(x){return j(x[0]*ke,x[1]*ke)}function P(x){return x=j.invert(x[0],x[1]),x&&[x[0]*Pe,x[1]*Pe]}b.stream=function(x){return ee&&m===x?ee:ee=Ad(Nd(i)(D(q(U(m=x)))))},b.preclip=function(x){return arguments.length?(D=x,w=void 0,I()):D},b.postclip=function(x){return arguments.length?(U=x,_=C=k=A=null,I()):U},b.clipAngle=function(x){return arguments.length?(D=+x?hd(w=x*ke):(w=null,ho),I()):w*Pe},b.clipExtent=function(x){return arguments.length?(U=x==null?(_=C=k=A=null,Zn):_d(_=+x[0][0],C=+x[0][1],k=+x[1][0],A=+x[1][1]),I()):_==null?null:[[_,C],[k,A]]},b.scale=function(x){return arguments.length?(n=+x,V()):n},b.translate=function(x){return arguments.length?(r=+x[0],o=+x[1],V()):[r,o]},b.center=function(x){return arguments.length?(a=x[0]%360*ke,l=x[1]%360*ke,V()):[a*Pe,l*Pe]},b.rotate=function(x){return arguments.length?(s=x[0]%360*ke,c=x[1]%360*ke,p=x.length>2?x[2]%360*ke:0,V()):[s*Pe,c*Pe,p*Pe]},b.angle=function(x){return arguments.length?(u=x%360*ke,V()):u*Pe},b.reflectX=function(x){return arguments.length?(d=x?-1:1,V()):d<0},b.reflectY=function(x){return arguments.length?(h=x?-1:1,V()):h<0},b.precision=function(x){return arguments.length?(q=So(O,H=x*x),I()):ot(H)},b.fitExtent=function(x,B){return Aa(b,x,B)},b.fitSize=function(x,B){return Vd(b,x,B)},b.fitWidth=function(x,B){return $d(b,x,B)},b.fitHeight=function(x,B){return Pd(b,x,B)};function V(){var x=ko(n,0,0,d,h,u).apply(null,t(a,l)),B=ko(n,r-x[0],o-x[1],d,h,u);return i=ya(s,c,p),O=jn(t,B),j=jn(i,O),q=So(O,H),I()}function I(){return ee=m=null,b}return function(){return t=e.apply(this,arguments),b.invert=t.invert&&P,V()}}function yr(e,t){return[e,nd(od(($e+t)/2))]}yr.invert=function(e,t){return[e,2*ga(td(t))-$e]};function Ld(){return Fd(yr).scale(961/Ie)}function Fd(e){var t=Td(e),n=t.center,r=t.scale,o=t.translate,a=t.clipExtent,l=null,s,c,p;t.scale=function(u){return arguments.length?(r(u),i()):r()},t.translate=function(u){return arguments.length?(o(u),i()):o()},t.center=function(u){return arguments.length?(n(u),i()):n()},t.clipExtent=function(u){return arguments.length?(u==null?l=s=c=p=null:(l=+u[0][0],s=+u[0][1],c=+u[1][0],p=+u[1][1]),i()):l==null?null:[[l,s],[c,p]]};function i(){var u=ie*r(),d=t(ld(t.rotate()).invert([0,0]));return a(l==null?[[d[0]-u,d[1]-u],[d[0]+u,d[1]+u]]:e===yr?[[Math.max(d[0]-u,l),s],[Math.min(d[0]+u,c),p]]:[[l,Math.max(d[1]-u,s)],[c,Math.min(d[1]+u,p)]])}return i()}function $t(e,t,n){this.k=e,this.x=t,this.y=n}$t.prototype={constructor:$t,scale:function(e){return e===1?this:new $t(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new $t(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};$t.prototype;const zd={class:"clock-container"},qd={class:"holographic-clock",viewBox:"0 0 100 100"},Yd={class:"clock-markers"},Bd=["x1","y1","x2","y2"],Hd={class:"clock-hands"},Xd=["x2","y2"],Gd=["x2","y2"],Kd=["x2","y2"],Wd={class:"duty-table-container"},Qd={class:"duty-table"},jd={key:1,colspan:"2"},Jd=["id"],Zd=["id"],ef={__name:"VisualScreen",setup(e){const t=N(null),n=N(null),r=N(0),o=N(!1),a=new Map,l=N({x:0,y:0}),s=N(0),c=N(0),p=N(0),i=N(0);let u,d=null;const h=()=>{d=new EventSource("/DutySched/api/sched_manage.php?controlCode=sse_push"),d.addEventListener("duty_update",P=>{console.log(P.data);const V=JSON.parse(P.data);D.value=[{type:"局领导",a_dir:V.a_dir,b_dir:V.b_dir},{type:"指挥长",a_com:V.a_com,b_com:V.b_com},{type:"值班长",sub:V.sub},{type:"值班员",op:Array.isArray(V.op)?V.op.join("、"):V.op},{type:"技术室",tech:V.tech},{type:"内部值守",sec:Array.isArray(V.sec)?V.sec.join("、"):V.sec}]}),d.onerror=P=>{console.error("SSE连接错误:",P)}},w=N([{id:"china",url:"/public/geo/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"/public/geo/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"/public/geo/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"/public/geo/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),D=N([]),_=async()=>{try{const P=new FormData;P.append("controlCode","query1");const V=await ne.post("/DutySched/api/sched_manage.php",P);if(V.status===1){const I=V.data[0];D.value=[{type:"局领导",a_dir:I.a_dir,b_dir:I.b_dir},{type:"指挥长",a_com:I.a_com,b_com:I.b_com},{type:"值班长",sub:I.sub},{type:"值班员",op:Array.isArray(I.op)?I.op.join("、"):I.op},{type:"技术室",tech:I.tech},{type:"内部值守",sec:Array.isArray(I.sec)?I.sec.join("、"):I.sec}]}console.log("值班")}catch(P){console.error("获取值班数据失败:",P)}},C={longitude:106.43,latitude:30.55},k=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},A=()=>{const P=new Date;s.value=P.getHours()%12,c.value=P.getMinutes(),p.value=P.getSeconds(),i.value=P.getMilliseconds()},U=P=>{if(a.has(P.id))return;const V=k(),I=Xt(`#${P.id}-map`).attr("viewBox",`0 0 ${V.width} ${V.height}`).attr("preserveAspectRatio","xMidYMid meet");ed(P.url).then(x=>{const B=Ld().center(P.center).scale(Math.min(V.width,V.height)*P.scaleFactor).translate([V.width/2,V.height/2]);a.set(P.id,{projection:B,data:x}),I.selectAll(".boundary").data(x.features).enter().append("path").attr("class","boundary").attr("d",bo().projection(B)),H(P.id)}).catch(x=>console.error("地图加载失败:",x))},H=P=>{const{projection:V}=a.get(P)||{};if(!V)return;const[I,x]=V([C.longitude,C.latitude]);l.value={x:I,y:x}},q=()=>{const P=k();w.value.forEach(V=>{const{projection:I,data:x}=a.get(V.id)||{};!I||!x||(I.scale(Math.min(P.width,P.height)*V.scaleFactor).translate([P.width/2,P.height/2]),Xt(`#${V.id}-map`).attr("viewBox",`0 0 ${P.width} ${P.height}`).selectAll(".boundary").attr("d",bo().projection(I)),V.id===w.value[r.value].id&&H(V.id))})},O=P=>{if(o.value)return;o.value=!0;const V=w.value.length,I=(r.value+P+V)%V,x=w.value[r.value],B=w.value[I];Xt(`#${x.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{Xt(`#${B.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=I,o.value=!1,H(B.id)})})},j=()=>{const{width:V,height:I}=k();for(let x=0;x<100;x++){const B=document.createElement("div");B.className="particle";const E=Math.random()*V,y=Math.random()*I,R=Math.random()*10,Y=Math.random()*2+1;B.style.left=`${E}px`,B.style.top=`${y}px`,B.style.animationDelay=`${R}s`,B.style.width=`${Y}px`,B.style.height=`${Y}px`,n.value.appendChild(B)}};let ee;const m=()=>{ee=setInterval(()=>{O(1)},w.value[0].duration+1500)};Qe(()=>{_(),h(),A(),u=setInterval(A,50),w.value.forEach(U),j(),m(),document.addEventListener("fullscreenchange",b)}),Ga(()=>{d&&d.close()}),Ka(()=>{clearInterval(u),clearInterval(ee),a.clear(),document.removeEventListener("fullscreenchange",b)});const b=()=>{an(()=>{q(),n.value&&(n.value.innerHTML=""),j()})};return ut([()=>window.innerWidth,()=>window.innerHeight,()=>{var P;return(P=t.value)==null?void 0:P.clientWidth},()=>{var P;return(P=t.value)==null?void 0:P.clientHeight}],()=>{an(q)}),(P,V)=>(Q(),ae("div",{class:"holographic-container",ref_key:"container",ref:t},[V[4]||(V[4]=W("div",{class:"slogan-container"},[W("div",null,"对党忠诚  服务人民"),W("div",null,"执法公正  纪律严明")],-1)),V[5]||(V[5]=W("div",{class:"title-container"},[W("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),W("div",zd,[(Q(),ae("svg",qd,[V[0]||(V[0]=W("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),V[1]||(V[1]=W("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),W("g",Yd,[(Q(),ae(fe,null,ge(12,I=>W("line",{key:I,x1:50+38*Math.cos((I*30-90)*Math.PI/180),y1:50+38*Math.sin((I*30-90)*Math.PI/180),x2:50+42*Math.cos((I*30-90)*Math.PI/180),y2:50+42*Math.sin((I*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,Bd)),64))]),W("g",Hd,[W("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((s.value*30+c.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((s.value*30+c.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,Xd),W("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((c.value*6+p.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((c.value*6+p.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Gd),W("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((p.value*6+i.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((p.value*6+i.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,Kd)]),V[2]||(V[2]=W("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),W("div",Wd,[W("table",Qd,[V[3]||(V[3]=W("tr",null,[W("th",null,"值班岗位"),W("th",null,"A岗"),W("th",null,"B岗")],-1)),(Q(!0),ae(fe,null,ge(D.value,(I,x)=>(Q(),ae("tr",{key:x},[W("td",null,Ce(I.type),1),x<2?(Q(),ae(fe,{key:0},[W("td",null,Ce(x===0?I.a_dir:I.a_com),1),W("td",null,Ce(x===0?I.b_dir:I.b_com),1)],64)):(Q(),ae("td",jd,Ce(x===2?I.sub:x===3?I.op:x===4?I.tech:I.sec),1))]))),128))])]),V[6]||(V[6]=re("码 (存储加密后的哈希值)'; ")),W("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),V[7]||(V[7]=W("div",{class:"hologram-grid"},null,-1)),V[8]||(V[8]=W("div",{class:"scan-line-vertical"},null,-1)),V[9]||(V[9]=W("div",{class:"scan-line-horizontal"},null,-1)),V[10]||(V[10]=W("div",{class:"hologram-frame"},[W("div")],-1)),(Q(!0),ae(fe,null,ge(w.value,(I,x)=>(Q(),ae("div",{key:I.id,id:`${I.id}-container`,class:Wa(["map-container",{"map-visible":r.value===x}])},[(Q(),ae("svg",{id:`${I.id}-map`,class:"map-svg"},null,8,Zd)),r.value===x?(Q(),ae("div",{key:0,class:"location-marker",style:ur({left:l.value.x+"px",top:l.value.y+"px"})},null,4)):Pt("",!0)],10,Jd))),128))],512))}},tf={class:"app-management-container"},nf={class:"clearfix"},rf={__name:"AppManagement",setup(e){const t=N([]),n=N([]),r=N(0),o=N(!1);N(!1),N("");const a=N(null),l=Ue({id:null,name:"",url:"",isPublic:"",roles:[]}),s=Ue({name:[{required:!0,message:"请输入应用名称",trigger:"blur"}],url:[{required:!0,message:"请输入应用URL",trigger:"blur"}],isPublic:[{required:!0,message:"请选择可见范围",trigger:"change"}],roles:[{type:"array",required:!0,validator:(_,C,k)=>{C&&C.length>0?k():k(new Error("至少选择一个角色组"))},trigger:"change"}]}),c=_=>{try{return JSON.parse(_).map(k=>{const A=n.value.find(U=>String(U.value)===String(k));return A?A.label:k})}catch(C){return console.error("解析 roleList 失败:",C),[]}},p=async()=>{try{const _=new FormData;_.append("controlCode","query");const C=await ne.post("/api/application_manage.php",_);C.status===1&&(t.value=C.data.application,n.value=C.data.rolelist.map(k=>({value:k.id,label:k.roleName})),r.value=C.data.application.length)}catch(_){console.error("获取应用列表失败:",_),G.error("获取应用列表失败")}};Qe(()=>{p()});const i=_=>{switch(_){case"0":return"公开";case"1":return"非第三方人员";case"2":return"民警";case"3":return"授权用户";default:return"未知"}},u=()=>{h(),o.value=!0},d=_=>{if(h(),n.value.length===0){console.error("角色选项未加载完成"),G.error("角色数据加载中，请稍后再试");return}l.id=_.id,l.name=_.application_name,l.url=_.url,l.isPublic=_.public;try{const C=JSON.parse(_.roleList);l.roles=C.map(k=>String(k)),an(()=>{l.roles=[...l.roles]})}catch(C){console.error("解析角色列表失败:",C),l.roles=[]}o.value=!0},h=()=>{a.value&&a.value.resetFields(),l.id=null,l.name="",l.url="",l.isPublic="",l.roles=[]},w=_=>{ft.confirm(`确定要删除应用 "${_.application_name}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const C=new FormData;C.append("controlCode","del"),C.append("id",_.id);const k=await ne.post("/api/application_manage.php",C);k.status===1?(G.success("删除成功"),p()):G.error("删除失败: "+(k.message||"未知错误"))}catch(C){console.error("删除应用失败:",C),G.error("删除应用失败: "+C.message)}}).catch(()=>{G.info("已取消删除")})},D=async()=>{try{await a.value.validate()}catch(_){console.error("表单验证失败:",_);return}try{const _=new FormData;_.append("controlCode",l.id?"modify":"add"),_.append("id",l.id),_.append("application_name",l.name),_.append("url",l.url),_.append("public",l.isPublic),_.append("roleList",JSON.stringify(l.roles));const C=await ne.post("/api/application_manage.php",_);C.status===1?(G.success("更新成功"),o.value=!1,p()):G.error(C.message||"更新失败")}catch(_){console.error("更新应用失败:",_),G.error("更新应用失败: "+_.message)}};return(_,C)=>{const k=F("el-button"),A=F("el-table-column"),U=F("el-tag"),H=F("el-icon"),q=F("el-button-group"),O=F("el-table"),j=F("el-card"),ee=F("el-input"),m=F("el-form-item"),b=F("el-option"),P=F("el-select"),V=F("el-checkbox"),I=F("el-checkbox-group"),x=F("el-form"),B=F("el-dialog");return Q(),ae("div",tf,[f(j,{class:"box-card"},{header:v(()=>[W("div",nf,[f(k,{style:{float:"right"},round:"",type:"primary",onClick:u},{default:v(()=>C[6]||(C[6]=[re(" 添加应用 ")])),_:1,__:[6]})])]),default:v(()=>[f(O,{data:t.value,stripe:"",border:"",fit:"","highlight-current-row":"",onRowDblclick:d,style:{width:"100%"}},{default:v(()=>[f(A,{label:"序号",width:"80",align:"center"},{default:v(E=>[re(Ce(E.$index+1),1)]),_:1}),f(A,{prop:"application_name",label:"应用名称","min-width":"120"}),f(A,{prop:"url",label:"URL","min-width":"150"}),f(A,{prop:"public",label:"是否公开",width:"150",align:"center"},{default:v(E=>[re(Ce(i(E.row.public)),1)]),_:1}),f(A,{prop:"roles",label:"授权角色组","min-width":"180"},{default:v(E=>[(Q(!0),ae(fe,null,ge(c(E.row.roleList),y=>(Q(),me(U,{key:y,size:"small",type:"info"},{default:v(()=>[re(Ce(y),1)]),_:2},1024))),128))]),_:1}),f(A,{label:"操作",width:"160",align:"center"},{default:v(E=>[f(q,null,{default:v(()=>[f(k,{size:"mini",type:"warning",onClick:y=>d(E.row)},{default:v(()=>[f(H,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"]),f(k,{size:"mini",type:"danger",onClick:y=>w(E.row)},{default:v(()=>[f(H,null,{default:v(()=>[f(de(Yt))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1}),f(B,{modelValue:o.value,"onUpdate:modelValue":C[5]||(C[5]=E=>o.value=E),title:l.id?"编辑应用":"添加应用",width:"50%"},{footer:v(()=>[f(k,{onClick:C[4]||(C[4]=E=>o.value=!1)},{default:v(()=>C[7]||(C[7]=[re("取消")])),_:1,__:[7]}),f(k,{type:"primary",onClick:D},{default:v(()=>C[8]||(C[8]=[re("保存")])),_:1,__:[8]})]),default:v(()=>[f(x,{model:l,rules:s,ref_key:"formRef",ref:a,"label-width":"100px"},{default:v(()=>[f(m,{label:"应用名称",prop:"name"},{default:v(()=>[f(ee,{modelValue:l.name,"onUpdate:modelValue":C[0]||(C[0]=E=>l.name=E),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1}),f(m,{label:"URL",prop:"url"},{default:v(()=>[f(ee,{modelValue:l.url,"onUpdate:modelValue":C[1]||(C[1]=E=>l.url=E),placeholder:"请输入应用URL"},null,8,["modelValue"])]),_:1}),f(m,{label:"是否公开",prop:"isPublic"},{default:v(()=>[f(P,{modelValue:l.isPublic,"onUpdate:modelValue":C[2]||(C[2]=E=>l.isPublic=E),placeholder:"请选择是否公开"},{default:v(()=>[f(b,{value:"0",label:"公开"}),f(b,{value:"1",label:"非第三方人员"}),f(b,{value:"2",label:"民警"}),f(b,{value:"3",label:"授权用户"})]),_:1},8,["modelValue"])]),_:1}),f(m,{label:"授权角色组",prop:"roles"},{default:v(()=>[f(I,{modelValue:l.roles,"onUpdate:modelValue":C[3]||(C[3]=E=>l.roles=E)},{default:v(()=>[(Q(!0),ae(fe,null,ge(n.value,E=>(Q(),me(V,{key:E.value,label:E.value},{default:v(()=>[re(Ce(E.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}},of=rt(rf,[["__scopeId","data-v-2a67e87b"]]),af={class:"container"},lf={class:"left-section"},sf={class:"query-bar"},uf={class:"user-table"},cf={class:"pagination"},df={class:"right-section"},ff={class:"query-bar"},pf={class:"authorization-table"},Co=10,hf={__name:"PowerManagement",setup(e){const t=N([]);N("");const n=N(""),r=N(""),o=N(1),a=N(0),l=N([]),s=N([]),c=N([]),p=N([]),i=N([]),u=N(!1),d=N(!1),h=N("add"),w=N(null),D=Ue({unitId:null,unitIdPath:[]}),_=Ue({id:null,userId:null,userName:"",appId:null,roleId:null,unitIdPath:[],unitId:null});Ee(()=>_.appId?p.value.filter(E=>E.applicationId==_.appId):[]);const C={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"};Qe(()=>{k(),A(),U()});const k=async()=>{try{const E=new FormData;E.append("controlCode","query");const y=await ne.post("/api/application_manage.php",E);y.status===1&&(c.value=y.data.application)}catch(E){console.error("获取应用列表失败:",E),G.error("获取应用列表失败")}},A=async()=>{try{const E=await ne.post("api/get_unit_info.php");s.value=E.data?[E.data]:[]}catch(E){console.error("获取单位数据失败:",E),G.error("获取单位数据失败")}},U=async()=>{try{const E=new FormData;E.append("controlCode","query"),E.append("page",o.value),E.append("pagesize",Co),r.value&&E.append("search_keyword",r.value),D.unitId&&E.append("organization_unit",D.unitId);const y=await ne.post("/api/user_manage.php",E);y.status===1?(a.value=y.total,l.value=y.data.map(R=>{const Y=x(s.value,R.organization_unit);return{...R,unit_name:Y?Y.unit_name:"未知单位"}})):G.error("获取用户数据失败")}catch(E){console.error("查询用户失败:",E),G.error("查询用户失败")}},H=E=>{E&&E.length>0?D.unitId=E[E.length-1]:D.unitId=null,console.log("Selected Unit ID:",D.unitId),U()},q=E=>{o.value=E,U()},O=async()=>{if(!n.value){G.warning("请先选择应用");return}const E=new FormData;E.append("controlCode","getAppUser"),E.append("appId",n.value);try{const y=await ne.post("/api/user_Role_manage.php",E);console.log("响应",y),y.status===1?l.value=y.data||[]:G.error("查询授权信息失败")}catch(y){console.error("查询授权失败:",y),G.error("查询授权失败")}},j=async E=>{const y=new FormData;y.append("controlCode","query"),y.append("userId",E);try{const R=await ne.post("/api/user_Role_manage.php",y);R.status===1?i.value=R.data.map(Y=>{const z=c.value.find(we=>we.id==Y.appId),J=x(s.value,Y.unitId),se=p.value.find(we=>we.id==Y.roleId);return{id:Y.id,appName:z?z.application_name:"未知应用",unitName:J?J.unit_name:"未知单位",roleName:se?se.roleName:"未知角色",...Y}}):G.error("获取用户授权失败")}catch(R){console.error("获取用户授权失败:",R),G.error("获取用户授权失败")}},ee=E=>{if(h.value="add",_.id=null,_.userId=E.id,_.userName=E.name,_.appId=null,_.roleId=null,_.unitIdPath=[],_.unitId=null,E.organization_unit){const y=B(s.value,E.organization_unit);_.unitIdPath=y,_.unitId=E.organization_unit}u.value=!0},m=E=>{h.value="edit",_.id=E.id,_.userId=E.userId,_.userName=E.userName||"",_.appId=String(E.appId),b(),_.roleId=E.roleId;const y=B(s.value,E.unitId);_.unitIdPath=y,_.unitId=E.unitId,u.value=!0},b=async()=>{const E=_.appId;if(!E){t.value=[];return}try{const y=new FormData;y.append("controlCode","query"),y.append("id",E);const R=await ne.post("/api/application_manage.php",y);R.status===1?t.value=R.data.rolelist.map(Y=>({id:Y.id,roleName:Y.roleName})):(G.error("获取角色列表失败"),t.value=[])}catch(y){console.error("加载角色失败:",y),G.error("加载角色失败"),t.value=[]}},P=async()=>{if(_.unitIdPath&&_.unitIdPath.length>0)_.unitId=_.unitIdPath[_.unitIdPath.length-1];else{G.warning("请选择单位");return}if(!_.roleId){G.warning("请选择角色");return}try{const E=h.value==="add"?"add":"modify",y=new FormData;y.append("controlCode",E),y.append("userId",_.userId),y.append("appId",_.appId),y.append("unitId",_.unitId),y.append("roleId",_.roleId),h.value==="edit"&&y.append("id",_.id),(await ne.post("/api/user_Role_manage.php",y)).status===1?(G.success("授权保存成功"),u.value=!1,h.value==="add"?j(_.userId):O()):G.error("授权保存失败")}catch(E){console.error("保存授权失败:",E),G.error("保存授权失败")}},V=E=>{w.value=E,d.value=!0},I=async()=>{try{const E=new FormData;E.append("controlCode","del"),E.append("id",w.value),(await ne.post("/api/user_Role_manage.php",E)).status===1?(G.success("授权删除成功"),d.value=!1,j()):G.error("授权删除失败")}catch(E){console.error("删除授权失败:",E),G.error("删除授权失败")}},x=(E,y)=>{for(const R of E){if(R.id==y)return R;if(R.children){const Y=x(R.children,y);if(Y)return Y}}return null},B=(E,y,R=[])=>{for(const Y of E){const z=[...R,Y.id];if(Y.id==y)return z;if(Y.children&&Y.children.length>0){const J=B(Y.children,y,z);if(J)return J}}return null};return(E,y)=>{const R=F("el-cascader"),Y=F("el-input"),z=F("el-button"),J=F("el-table-column"),se=F("el-table"),we=F("el-pagination"),te=F("el-option"),ce=F("el-select"),$=F("el-form-item"),K=F("el-form"),X=F("el-dialog");return Q(),ae("div",af,[W("div",lf,[W("div",sf,[f(R,{modelValue:D.unitIdPath,"onUpdate:modelValue":y[0]||(y[0]=T=>D.unitIdPath=T),options:s.value,props:C,onChange:H,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择单位"},null,8,["modelValue","options"]),f(Y,{modelValue:r.value,"onUpdate:modelValue":y[1]||(y[1]=T=>r.value=T),placeholder:"输入姓名、警号或身份证号",class:"input-user"},null,8,["modelValue"]),f(z,{type:"primary",onClick:U},{default:v(()=>y[11]||(y[11]=[re("查询用户")])),_:1,__:[11]})]),W("div",uf,[y[14]||(y[14]=W("h4",null,"用户列表",-1)),f(se,{data:l.value,"empty-text":"No Data",class:"table"},{default:v(()=>[f(J,{prop:"unit_name",label:"单位"}),f(J,{prop:"name",label:"姓名"}),f(J,{prop:"police_number",label:"警号"}),f(J,{prop:"id_number",label:"身份证号"}),f(J,{prop:"personnel_type",label:"身份"}),f(J,{label:"操作",width:"180"},{default:v(({row:T})=>[f(z,{size:"small",onClick:L=>ee(T)},{default:v(()=>y[12]||(y[12]=[re("添加权限")])),_:2,__:[12]},1032,["onClick"]),f(z,{size:"small",onClick:L=>j(T.id),type:"success"},{default:v(()=>y[13]||(y[13]=[re("查看权限")])),_:2,__:[13]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),W("div",cf,[f(we,{layout:"prev, pager, next","current-page":o.value,"page-size":Co,total:a.value,onCurrentChange:q},null,8,["current-page","total"])])])]),W("div",df,[W("div",ff,[f(ce,{modelValue:n.value,"onUpdate:modelValue":y[2]||(y[2]=T=>n.value=T),placeholder:"选择应用",class:"select-app"},{default:v(()=>[f(te,{label:"选择应用",value:""}),(Q(!0),ae(fe,null,ge(c.value,T=>(Q(),me(te,{key:T.id,label:T.application_name,value:T.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),f(z,{type:"primary",onClick:O},{default:v(()=>y[15]||(y[15]=[re("查询授权人员")])),_:1,__:[15]})]),W("div",pf,[y[18]||(y[18]=W("h4",null,"授权信息",-1)),f(se,{data:i.value,"empty-text":"No Data",class:"table"},{default:v(()=>[f(J,{prop:"appName",label:"授权应用"}),f(J,{prop:"unitName",label:"授权单位"}),f(J,{prop:"roleName",label:"授权角色"}),f(J,{label:"操作",width:"150"},{default:v(({row:T})=>[f(z,{size:"small",onClick:L=>m(T)},{default:v(()=>y[16]||(y[16]=[re("修改")])),_:2,__:[16]},1032,["onClick"]),f(z,{size:"small",onClick:L=>V(T.id),type:"danger"},{default:v(()=>y[17]||(y[17]=[re("删除")])),_:2,__:[17]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])]),f(X,{modelValue:u.value,"onUpdate:modelValue":y[8]||(y[8]=T=>u.value=T),title:h.value==="add"?"添加授权":"修改授权"},{footer:v(()=>[f(z,{onClick:y[7]||(y[7]=T=>u.value=!1)},{default:v(()=>y[19]||(y[19]=[re("取消")])),_:1,__:[19]}),f(z,{type:"primary",onClick:P},{default:v(()=>y[20]||(y[20]=[re("确认")])),_:1,__:[20]})]),default:v(()=>[f(K,{model:_,"label-width":"120px"},{default:v(()=>[f($,{label:"用户姓名",prop:"userName"},{default:v(()=>[f(Y,{modelValue:_.userName,"onUpdate:modelValue":y[3]||(y[3]=T=>_.userName=T),disabled:""},null,8,["modelValue"])]),_:1}),f($,{label:"应用选择",prop:"appId",required:""},{default:v(()=>[f(ce,{modelValue:_.appId,"onUpdate:modelValue":y[4]||(y[4]=T=>_.appId=T),onChange:b,placeholder:"请选择应用"},{default:v(()=>[(Q(!0),ae(fe,null,ge(c.value,T=>(Q(),me(te,{key:T.id,label:T.application_name,value:T.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),f($,{label:"单位选择",prop:"unitId",required:""},{default:v(()=>[f(R,{modelValue:_.unitIdPath,"onUpdate:modelValue":y[5]||(y[5]=T=>_.unitIdPath=T),options:s.value,props:C,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择单位"},null,8,["modelValue","options"])]),_:1}),f($,{label:"角色选择",prop:"roleId",required:""},{default:v(()=>[f(ce,{modelValue:_.roleId,"onUpdate:modelValue":y[6]||(y[6]=T=>_.roleId=T),placeholder:"请选择角色"},{default:v(()=>[(Q(!0),ae(fe,null,ge(t.value,T=>(Q(),me(te,{key:T.id,label:T.roleName,value:T.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),f(X,{modelValue:d.value,"onUpdate:modelValue":y[10]||(y[10]=T=>d.value=T),title:"确认删除",width:"30%"},{footer:v(()=>[f(z,{onClick:y[9]||(y[9]=T=>d.value=!1)},{default:v(()=>y[21]||(y[21]=[re("取消")])),_:1,__:[21]}),f(z,{type:"danger",onClick:I},{default:v(()=>y[22]||(y[22]=[re("确认删除")])),_:1,__:[22]})]),default:v(()=>[y[23]||(y[23]=W("p",null,"确定要删除此授权信息吗？",-1))]),_:1,__:[23]},8,["modelValue"])])}}},mf=rt(hf,[["__scopeId","data-v-1ed7837f"]]),_f={class:"role-management-container"},vf={class:"toolbar flex justify-end items-center mb-4"},gf={__name:"RoleManagement",setup(e){const t=N([]),n=N(null),r=async()=>{try{const i=new FormData;i.append("controlCode","query");const u=await ne.post("/api/role_List_manage.php",i);u.status===1?t.value=u.data.map(d=>({id:d.id,name:d.roleName,desc:d.roleDesc})):G.error(u.message||"获取角色列表失败")}catch(i){console.error("获取角色列表失败:",i),G.error("获取角色列表失败")}};Qe(()=>{r()});const o=N(!1),a=Ue({id:null,name:"",desc:""}),l=()=>{n.value&&n.value.resetFields(),a.id=null,a.name="",a.desc="",o.value=!0},s=i=>{n.value&&n.value.resetFields(),a.id=i.id,a.name=i.name,a.desc=i.desc,o.value=!0},c=i=>{ft.confirm(`确定要删除角色 "${i.name}" 吗？`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const u=new FormData;u.append("controlCode","del"),u.append("id",i.id);const d=await ne.post("/api/role_List_manage.php",u);d.status===1?(G.success("删除成功"),r()):G.error(d.message||"删除失败")}catch(u){console.error("删除角色失败:",u),G.error("删除角色失败: "+u.message)}}).catch(()=>{G.info("已取消删除")})},p=async()=>{if(!a.name){G.error("角色名称不能为空");return}try{const i=new FormData;i.append("controlCode",a.id?"modify":"add"),i.append("roleName",a.name),i.append("roleDesc",a.desc),a.id&&i.append("id",a.id);const u=await ne.post("/api/role_List_manage.php",i);u.status===1?(G.success(a.id?"更新成功":"添加成功"),o.value=!1,r()):G.error(u.message||(a.id?"更新失败":"添加失败"))}catch(i){console.error("操作失败:",i),G.error("操作失败: "+i.message)}};return(i,u)=>{const d=F("el-icon"),h=F("el-button"),w=F("el-table-column"),D=F("el-button-group"),_=F("el-table"),C=F("el-input"),k=F("el-form-item"),A=F("el-form"),U=F("el-dialog");return Q(),ae("div",_f,[W("div",vf,[f(h,{type:"primary",onClick:l},{default:v(()=>[f(d,null,{default:v(()=>[f(de($o))]),_:1}),u[4]||(u[4]=re(" 添加角色 "))]),_:1,__:[4]})]),f(_,{data:t.value,style:{width:"100%"},border:""},{default:v(()=>[f(w,{label:"序号",width:"80",align:"center"},{default:v(H=>[re(Ce(H.$index+1),1)]),_:1}),f(w,{prop:"name",label:"角色名称"}),f(w,{prop:"desc",label:"角色描述"}),f(w,{label:"操作"},{default:v(H=>[f(D,null,{default:v(()=>[f(h,{size:"mini",type:"warning",onClick:q=>s(H.row)},{default:v(()=>[f(d,null,{default:v(()=>[f(de(bn))]),_:1})]),_:2},1032,["onClick"]),f(h,{size:"mini",type:"danger",onClick:q=>c(H.row)},{default:v(()=>[f(d,null,{default:v(()=>[f(de(Yt))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),f(U,{modelValue:o.value,"onUpdate:modelValue":u[3]||(u[3]=H=>o.value=H),width:"20%"},{default:v(()=>[f(A,{model:a,"label-width":"100px",ref_key:"formRef",ref:n},{default:v(()=>[f(k,{label:"角色名称",prop:"name"},{default:v(()=>[f(C,{modelValue:a.name,"onUpdate:modelValue":u[0]||(u[0]=H=>a.name=H)},null,8,["modelValue"])]),_:1}),f(k,{label:"角色描述",prop:"desc"},{default:v(()=>[f(C,{modelValue:a.desc,"onUpdate:modelValue":u[1]||(u[1]=H=>a.desc=H),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),footer:v(()=>[f(h,{onClick:u[2]||(u[2]=H=>o.value=!1)},{default:v(()=>u[5]||(u[5]=[re("取消")])),_:1,__:[5]}),f(h,{type:"primary",onClick:p},{default:v(()=>u[6]||(u[6]=[re("确定")])),_:1,__:[6]})]),_:1},8,["modelValue"])])}}},yf=rt(gf,[["__scopeId","data-v-2a4abc11"]]),wf={class:"duty-management"},bf={__name:"DutyManagement",setup(e){const t=N([]),n=N(!1),r=N([]),o=N(!1),a=N([]),l=N(!1),s=N([]),c=N(!1),p=N([]),i=N(!1),u=N([]),d=N(!1),h=N(1),w=N(10),D=N(0),_=Ue({time:"",aLeader:"",bLeader:"",aCommander:"",bCommander:"",dutyLeader:"",dutyStaff:[],techRoom:"",innerSecurity:[]}),C=N(!1),k=N(""),A=Ue([]),U=N({}),H=async y=>{if(!y){t.value=[];return}n.value=!0;try{const R=new FormData;R.append("controlCode","query"),R.append("search_keyword",y);const Y=await ne.post("/api/user_manage.php",R);Y.status===1?t.value=Y.data:t.value=[]}catch{t.value=[]}finally{n.value=!1}},q=async()=>{const y=new FormData;y.append("controlCode","query"),y.append("page",h.value),y.append("pagesize",w.value);try{const R=await ne.post("/DutySched/api/sched_manage.php",y);if(R.status===1){D.value=R.totalCount;const Y=new Map(R.userInfo.map(J=>[J.userId,J.userName])),z=R.data.map(J=>({id:J.id,schedDate:J.sched_date,aDir:Y.get(J.a_dir)||"未知用户",bDir:Y.get(J.b_dir)||"未知用户",aCom:Y.get(J.a_com)||"未知用户",bCom:Y.get(J.b_com)||"未知用户",sub:Y.get(J.sub)||"未知用户",op:J.op.map(se=>Y.get(se)||"未知用户").join("、"),tech:Y.get(J.tech)||"未知用户",sec:J.sec.map(se=>Y.get(se)||"未知用户").join("、")}));A.length=0,A.push(...z)}else G.error(R.message||"获取值班记录失败")}catch{G.error("网络请求失败")}},O=y=>{h.value=y,q()},j=y=>{w.value=y,q()},ee=async()=>{const y=new FormData;y.append("controlCode","getRole");const R=await ne.post("/DutySched/api/sched_manage.php",y);if(R.status===1){const Y={};R.data.forEach(z=>{Y[z.roleName]=z.id}),U.value=Y}},m=y=>{C.value=!0,k.value="新增值班记录",console.log("获取局领导角色ID:",U.value.局领导),P(U.value.局领导),V(U.value.指挥长),I(U.value.值班长),x(U.value.技术室),B(U.value.值班员)};Qe(()=>{q(),ee()});const b=async y=>{if(await ft.confirm("确定删除该值班记录？","提示",{type:"warning",confirmButtonText:"确定",cancelButtonText:"取消"})==="confirm")try{const Y=new FormData;Y.append("controlCode","del"),Y.append("id",y.id);const z=await ne.post("/DutySched/api/sched_manage.php",Y);if(z.status===1){const J=A.findIndex(se=>se.id===y.id);J!==-1&&(A.splice(J,1),G.success("删除成功"))}else G.error("删除失败："+z.msg)}catch{G.error("删除请求失败")}},P=async y=>{o.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",y);const Y=await ne.post("/DutySched/api/sched_manage.php",R);Y.status===1&&(r.value=Y.data.map(z=>({value:z.userId,label:`${z.name} (${z.unitName})`})),console.log("局领导用户列表:",r.value))}catch(R){console.error("获取角色用户失败:",R)}finally{o.value=!1}},V=async y=>{l.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",y);const Y=await ne.post("/DutySched/api/sched_manage.php",R);Y.status===1&&(a.value=Y.data.map(z=>({value:z.userId,label:`${z.name} (${z.unitName})`})))}catch(R){console.error("获取指挥长用户失败:",R)}finally{l.value=!1}},I=async y=>{c.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",y);const Y=await ne.post("/DutySched/api/sched_manage.php",R);s.value=Y.data.map(z=>({value:z.userId,label:`${z.name}(${z.unitName})`}))}catch(R){console.error("获取值班长列表失败:",R)}finally{c.value=!1}},x=async y=>{i.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",y);const Y=await ne.post("/DutySched/api/sched_manage.php",R);p.value=Y.data.map(z=>({value:z.userId,label:`${z.name}(${z.unitName})`}))}catch(R){console.error("获取技术室人员列表失败:",R)}finally{i.value=!1}},B=async y=>{d.value=!0;try{const R=new FormData;R.append("controlCode","getRoleUser"),R.append("roleId",y);const Y=await ne.post("/DutySched/api/sched_manage.php",R);u.value=Y.data.map(z=>({value:z.userId,label:`${z.name}(${z.unitName})`})),console.log("值班员",u.value)}catch(R){console.error("获取值班员列表失败:",R)}finally{d.value=!1}},E=async()=>{try{const y=new FormData;y.append("controlCode","add"),y.append("sched_date",_.time),y.append("a_dir",_.aLeader),y.append("b_dir",_.bLeader),y.append("a_com",_.aCommander),y.append("b_com",_.bCommander),y.append("sub",_.dutyLeader),console.log("值班员",_.dutyStaff),y.append("op",_.dutyStaff.join(",")),y.append("tech",_.techRoom),y.append("sec",_.innerSecurity.join(","));const R=await ne.post("/DutySched/api/sched_manage.php",y);R.status===1?(G.success("保存成功"),_.time="",_.aLeader="",_.bLeader="",_.aCommander="",_.bCommander="",_.dutyLeader="",_.dutyStaff=[],_.techRoom="",_.innerSecurity=[],h.value=1,C.value=!1,q()):G.error(R.message||"保存失败")}catch(y){console.error("保存失败:",y),G.error("保存失败，请稍后重试")}};return(y,R)=>{const Y=F("el-button"),z=F("el-table-column"),J=F("el-tooltip"),se=F("el-table"),we=F("el-pagination"),te=F("el-date-picker"),ce=F("el-form-item"),$=F("el-option"),K=F("el-select"),X=F("el-form"),T=F("el-dialog");return Q(),ae("div",wf,[f(Y,{type:"primary",onClick:R[0]||(R[0]=L=>m())},{default:v(()=>R[12]||(R[12]=[re("新增记录")])),_:1,__:[12]}),f(se,{data:A,style:{width:"100%"},border:""},{default:v(()=>[f(z,{prop:"schedDate",label:"值班日期",width:"120"}),f(z,{prop:"aDir",label:"A岗领导",width:"100"}),f(z,{prop:"bDir",label:"B岗领导",width:"100"}),f(z,{prop:"aCom",label:"A岗指挥长",width:"120"}),f(z,{prop:"bCom",label:"B岗指挥长",width:"120"}),f(z,{prop:"sub",label:"值班员",width:"100"}),f(z,{prop:"op",label:"操作人",width:"180"}),f(z,{prop:"tech",label:"技术室",width:"100"}),f(z,{prop:"sec",label:"内部安全值班"}),f(z,{label:"操作",width:"100"},{default:v(L=>[f(J,{content:"删除",placement:"top"},{default:v(()=>[f(Y,{type:"danger",onClick:_e=>b(L.row),icon:de(Yt),circle:""},null,8,["onClick","icon"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),f(we,{"current-page":h.value,"page-size":w.value,total:D.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:O,onSizeChange:j},null,8,["current-page","page-size","total"]),f(T,{modelValue:C.value,"onUpdate:modelValue":R[11]||(R[11]=L=>C.value=L),title:k.value,width:"60%"},{footer:v(()=>[f(Y,{onClick:R[10]||(R[10]=L=>C.value=!1)},{default:v(()=>R[13]||(R[13]=[re("取消")])),_:1,__:[13]}),f(Y,{type:"primary",onClick:E},{default:v(()=>R[14]||(R[14]=[re("保存")])),_:1,__:[14]})]),default:v(()=>[f(X,{model:_,"label-width":"120px"},{default:v(()=>[f(ce,{label:"时间"},{default:v(()=>[f(te,{modelValue:_.time,"onUpdate:modelValue":R[1]||(R[1]=L=>_.time=L),type:"date",placeholder:"选择时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),f(ce,{label:"局领导"},{default:v(()=>[f(K,{modelValue:_.aLeader,"onUpdate:modelValue":R[2]||(R[2]=L=>_.aLeader=L),placeholder:"请选择局领导",loading:o.value,options:r.value,filterable:"",clearable:""},{default:v(()=>[(Q(!0),ae(fe,null,ge(r.value,L=>(Q(),me($,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(ce,{label:"B岗局领导"},{default:v(()=>[f(K,{modelValue:_.bLeader,"onUpdate:modelValue":R[3]||(R[3]=L=>_.bLeader=L),placeholder:"请选择B岗局领导",loading:o.value,options:r.value,filterable:"",clearable:""},{default:v(()=>[(Q(!0),ae(fe,null,ge(r.value,L=>(Q(),me($,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(ce,{label:"A岗指挥长"},{default:v(()=>[f(K,{modelValue:_.aCommander,"onUpdate:modelValue":R[4]||(R[4]=L=>_.aCommander=L),placeholder:"请选择A岗指挥长",loading:l.value,options:a.value,filterable:"",clearable:""},{default:v(()=>[(Q(!0),ae(fe,null,ge(a.value,L=>(Q(),me($,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(ce,{label:"B岗指挥长"},{default:v(()=>[f(K,{modelValue:_.bCommander,"onUpdate:modelValue":R[5]||(R[5]=L=>_.bCommander=L),placeholder:"请选择B岗指挥长",loading:l.value,options:a.value,filterable:"",clearable:""},{default:v(()=>[(Q(!0),ae(fe,null,ge(a.value,L=>(Q(),me($,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(ce,{label:"值班长"},{default:v(()=>[f(K,{modelValue:_.dutyLeader,"onUpdate:modelValue":R[6]||(R[6]=L=>_.dutyLeader=L),placeholder:"请选择值班长",loading:c.value,options:s.value,filterable:"",clearable:""},{default:v(()=>[(Q(!0),ae(fe,null,ge(s.value,L=>(Q(),me($,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(ce,{label:"值班员"},{default:v(()=>[f(K,{modelValue:_.dutyStaff,"onUpdate:modelValue":R[7]||(R[7]=L=>_.dutyStaff=L),options:u.value,loading:d.value,multiple:"",filterable:"",clearable:"",ip:"",placeholder:"请选择值班员"},{default:v(()=>[(Q(!0),ae(fe,null,ge(u.value,L=>(Q(),me($,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","options","loading"])]),_:1}),f(ce,{label:"技术室"},{default:v(()=>[f(K,{modelValue:_.techRoom,"onUpdate:modelValue":R[8]||(R[8]=L=>_.techRoom=L),placeholder:"请选择技术员",loading:i.value,options:p.value,filterable:"",clearable:""},{default:v(()=>[(Q(!0),ae(fe,null,ge(p.value,L=>(Q(),me($,{key:L.value,label:L.label,value:L.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading","options"])]),_:1}),f(ce,{label:"内部安全值班"},{default:v(()=>[f(K,{modelValue:_.innerSecurity,"onUpdate:modelValue":R[9]||(R[9]=L=>_.innerSecurity=L),multiple:"",filterable:"",remote:"","remote-method":H,loading:n.value,placeholder:"请选择/搜索人员",style:{width:"300px"}},{default:v(()=>[(Q(!0),ae(fe,null,ge(t.value,L=>(Q(),me($,{key:L.id,label:L.name,value:L.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}},xf=rt(bf,[["__scopeId","data-v-110647f3"]]),Sf=[{path:"/unit-management",name:"UnitManagement",component:gi},{path:"/user-management",name:"UserManagement",component:Ui},{path:"/visual-screen",name:"VisualScreen",component:ef},{path:"/app-management",name:"AppManagement",component:of},{path:"/role-management",name:"RoleManagement",component:yf},{path:"/power-management",name:"PowerManagement",component:mf},{path:"/duty-management",name:"DutyManagement",component:xf}],kf=ti({history:Pl(),routes:Sf}),Rn=Qa(hi);Rn.use(ja);for(const[e,t]of Object.entries(Ja))Rn.component(e,t);Rn.use(kf);Rn.mount("#app");
