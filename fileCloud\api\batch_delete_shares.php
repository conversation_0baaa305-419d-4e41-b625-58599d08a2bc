<?php
/**
 * 文件网盘系统 - 批量删除分享API
 * 创建时间: 2025-06-26
 */


require_once '../functions.php';

header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    jsonResponse(['success' => false, 'message' => '请先登录'], 401);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => '无效的请求方法'], 405);
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => '无效的请求'], 400);
}

$shareIds = $_POST['share_ids'] ?? '';
$type = $_POST['type'] ?? '';
$currentUserId = $_SESSION['user_id'];

// 验证参数
if (empty($shareIds) || empty($type)) {
    jsonResponse(['success' => false, 'message' => '参数错误'], 400);
}

// 解析分享ID列表
$shareIdArray = array_filter(array_map('intval', explode(',', $shareIds)));
if (empty($shareIdArray)) {
    jsonResponse(['success' => false, 'message' => '无效的分享ID列表'], 400);
}

// 验证操作类型
if (!in_array($type, ['received', 'sent'])) {
    jsonResponse(['success' => false, 'message' => '无效的操作类型'], 400);
}

try {
    $pdo->beginTransaction();

    $deletedCount = 0;
    $errors = [];

    foreach ($shareIdArray as $shareId) {
        try {
            if ($type === 'received') {
                // 删除收到的分享 - 检查是否为接收者
                $checkStmt = $pdo->prepare("
                    SELECT share_id FROM filecloud_share
                    WHERE share_id = ? AND to_user_id = ? AND is_deleted = 0
                ");
                $checkStmt->execute([$shareId, $currentUserId]);

                if (!$checkStmt->fetch()) {
                    $errors[] = "分享记录 {$shareId} 不存在或无权限";
                    continue;
                }

                // 标记为已删除（软删除）
                $deleteStmt = $pdo->prepare("
                    UPDATE filecloud_share
                    SET is_deleted = 1
                    WHERE share_id = ?
                ");

            } else { // sent
                // 取消我的分享 - 检查是否为分享者
                $checkStmt = $pdo->prepare("
                    SELECT share_id FROM filecloud_share
                    WHERE share_id = ? AND from_user_id = ? AND is_deleted = 0
                ");
                $checkStmt->execute([$shareId, $currentUserId]);

                if (!$checkStmt->fetch()) {
                    $errors[] = "分享记录 {$shareId} 不存在或无权限";
                    continue;
                }

                // 直接删除分享记录
                $deleteStmt = $pdo->prepare("
                    DELETE FROM filecloud_share WHERE share_id = ?
                ");
            }

            if ($deleteStmt->execute([$shareId])) {
                $deletedCount++;
            } else {
                $errors[] = "删除分享记录 {$shareId} 失败";
            }

        } catch (Exception $e) {
            $errors[] = "处理分享记录 {$shareId} 时出错: " . $e->getMessage();
        }
    }

    if ($deletedCount > 0) {
        $pdo->commit();

        $actionText = $type === 'received' ? '删除' : '取消分享';
        $message = "成功{$actionText} {$deletedCount} 个文件";

        if (!empty($errors)) {
            $message .= "，但有 " . count($errors) . " 个操作失败";
        }

        jsonResponse([
            'success' => true,
            'message' => $message,
            'deleted_count' => $deletedCount,
            'errors' => $errors
        ]);
    } else {
        $pdo->rollback();
        $errorMessage = !empty($errors) ? implode('; ', $errors) : '没有文件被处理';
        jsonResponse(['success' => false, 'message' => $errorMessage], 400);
    }

} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }
    jsonResponse(['success' => false, 'message' => '操作失败: ' . $e->getMessage()], 500);
}
?>
