<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>人员信息查询测试</title>
</head>
<body>
    <h1>人员信息查询测试</h1>
    <button onclick="fetchData('identity')">查询人员身份</button>
    <button onclick="fetchData('status')">查询人员状态</button>
    <button onclick="fetchData('rank')">查询人员职级</button>
    <pre id="result"></pre>

    <script>
        function fetchData(type) {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'api/person_info_api.php?type=' + type, true);
            xhr.setRequestHeader('Accept', 'application/json');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        const resultDiv = document.getElementById('result');
                        resultDiv.textContent = JSON.stringify(data, null, 2);
                    } catch (error) {
                        const resultDiv = document.getElementById('result');
                        resultDiv.textContent = '解析 JSON 数据时出错: ' + error.message;
                    }
                }
            };
            xhr.send();
        }
    </script>
</body>
</html>
