<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SSE Demo</title>
</head>
<body>
<h1>Server-Sent Events Demo</h1>
<div id="messages"></div>

<script>

    if(!window.EventSource) {

        alert('该浏览器不支持SSE');
    }else{

        const eventSource = new EventSource("http://**************:8080/sseServer");

        // 连接状态跟踪
        eventSource.onopen = function() {
        console.log('SSE连接已建立');
        };


        //接收到 新的数据
        eventSource.onmessage = function(event) {
            const messagesDiv = document.getElementById("messages");
            const data = JSON.parse(event.data);
            
            let html = `<div>
                <p>日期: ${data.data[0].sched_date}</p>
                <p>局领导: A岗:${data.data[0].a_dir} B岗:${data.data[0].a_com}</p>
                <p>指挥长: A岗:${data.data[0].b_dir} B岗:${data.data[0].b_com}</p>
                <p>值班长: ${data.data[0].sub}</p>
                <p>值班员: ${data.data[0].op.join('、')}</p>
                <p>技术员: ${data.data[0].tech}</p>
                <p>安全员: ${data.data[0].sec.join('、')}</p>
            </div>`;
            
            messagesDiv.innerHTML = html;
        };

        // 打开事件
        eventSource.onopen = function (event) { console.log('连接成功'); };

        //关闭事件
        eventSource.onclose = function(event) {console.log('连接关闭');};

    }


</script>
</body>
</html>