<?php
require_once '../config.php';

// 检查session是否过期（30分钟无活动）
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
    session_unset();
    session_destroy();
    echo json_encode(['status' => 0, 'message' => '会话已过期']);
    exit;
}

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 0, 'message' => '未登录']);
    exit;
}

// 更新最后活动时间
$_SESSION['last_activity'] = time();

// 返回用户信息
echo json_encode([
    'status' => 1,
    'message' => '查询成功',
    'user' => [
        'id' => $_SESSION['user_id'],
        'name' => $_SESSION['username'],
        'unit_id' => $_SESSION['unit_id'],
        'is_admin' => $_SESSION['is_admin']
    ]
]);
?>
