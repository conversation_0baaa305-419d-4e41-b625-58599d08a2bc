import{s as Ni,d as bo,u as G,a as Ii,c as ve,p as Sn,r as B,w as rt,h as Eo,n as jt,i as it,b as Be,_ as Dt,o as dt,e as se,f as _e,g as Y,j as p,k as _,l as O,m as Di,F as He,q as le,t as Ti,v as ye,x as ne,y as Ge,z as Ye,A as Ui,B as So,C as _r,D as gr,E as Z,G as Oi,H as en,I as ir,J as ko,K as _n,L as gn,M as Co,N as Ro,O as St,P as zi,Q as Li,R as Fi,S as vr,T as Ot,U as yr,V as wr,W as qi,X as zt,Y as xr,Z as Me,$ as Yi,a0 as Bi,a1 as Hi,a2 as Xi}from"./index-CYGykDVR.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON> <PERSON>
  * @license MIT
  */const et=typeof document<"u";function Po(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ki(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Po(e.default)}const oe=Object.assign;function kn(e,t){const n={};for(const r in t){const o=t[r];n[r]=$e(o)?o.map(e):e(o)}return n}const bt=()=>{},$e=Array.isArray,$o=/#/g,Gi=/&/g,Wi=/\//g,Qi=/=/g,Zi=/\?/g,Vo=/\+/g,Ji=/%5B/g,ji=/%5D/g,Mo=/%5E/g,el=/%60/g,Ao=/%7B/g,tl=/%7C/g,No=/%7D/g,nl=/%20/g;function lr(e){return encodeURI(""+e).replace(tl,"|").replace(Ji,"[").replace(ji,"]")}function rl(e){return lr(e).replace(Ao,"{").replace(No,"}").replace(Mo,"^")}function In(e){return lr(e).replace(Vo,"%2B").replace(nl,"+").replace($o,"%23").replace(Gi,"%26").replace(el,"`").replace(Ao,"{").replace(No,"}").replace(Mo,"^")}function ol(e){return In(e).replace(Qi,"%3D")}function il(e){return lr(e).replace($o,"%23").replace(Zi,"%3F")}function ll(e){return e==null?"":il(e).replace(Wi,"%2F")}function kt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const al=/\/$/,sl=e=>e.replace(al,"");function Cn(e,t,n="/"){let r,o={},i="",l="";const s=t.indexOf("#");let c=t.indexOf("?");return s<c&&s>=0&&(c=-1),c>-1&&(r=t.slice(0,c),i=t.slice(c+1,s>-1?s:t.length),o=e(i)),s>-1&&(r=r||t.slice(0,s),l=t.slice(s,t.length)),r=dl(r??t,n),{fullPath:r+(i&&"?")+i+l,path:r,query:o,hash:kt(l)}}function ul(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function br(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function cl(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&lt(t.matched[r],n.matched[o])&&Io(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function lt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Io(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!fl(e[n],t[n]))return!1;return!0}function fl(e,t){return $e(e)?Er(e,t):$e(t)?Er(t,e):e===t}function Er(e,t){return $e(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function dl(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let i=n.length-1,l,s;for(l=0;l<r.length;l++)if(s=r[l],s!==".")if(s==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+r.slice(l).join("/")}const Le={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ct;(function(e){e.pop="pop",e.push="push"})(Ct||(Ct={}));var Et;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Et||(Et={}));function pl(e){if(!e)if(et){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),sl(e)}const hl=/^[^#]+#/;function ml(e,t){return e.replace(hl,"#")+t}function _l(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const vn=()=>({left:window.scrollX,top:window.scrollY});function gl(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=_l(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Sr(e,t){return(history.state?history.state.position-t:-1)+e}const Dn=new Map;function vl(e,t){Dn.set(e,t)}function yl(e){const t=Dn.get(e);return Dn.delete(e),t}let wl=()=>location.protocol+"//"+location.host;function Do(e,t){const{pathname:n,search:r,hash:o}=t,i=e.indexOf("#");if(i>-1){let s=o.includes(e.slice(i))?e.slice(i).length:1,c=o.slice(s);return c[0]!=="/"&&(c="/"+c),br(c,"")}return br(n,e)+r+o}function xl(e,t,n,r){let o=[],i=[],l=null;const s=({state:f})=>{const h=Do(e,location),v=n.value,C=t.value;let x=0;if(f){if(n.value=h,t.value=f,l&&l===v){l=null;return}x=C?f.position-C.position:0}else r(h);o.forEach(S=>{S(n.value,v,{delta:x,type:Ct.pop,direction:x?x>0?Et.forward:Et.back:Et.unknown})})};function c(){l=n.value}function d(f){o.push(f);const h=()=>{const v=o.indexOf(f);v>-1&&o.splice(v,1)};return i.push(h),h}function a(){const{history:f}=window;f.state&&f.replaceState(oe({},f.state,{scroll:vn()}),"")}function u(){for(const f of i)f();i=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:d,destroy:u}}function kr(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?vn():null}}function bl(e){const{history:t,location:n}=window,r={value:Do(e,n)},o={value:t.state};o.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,d,a){const u=e.indexOf("#"),f=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+c:wl()+e+c;try{t[a?"replaceState":"pushState"](d,"",f),o.value=d}catch(h){console.error(h),n[a?"replace":"assign"](f)}}function l(c,d){const a=oe({},t.state,kr(o.value.back,c,o.value.forward,!0),d,{position:o.value.position});i(c,a,!0),r.value=c}function s(c,d){const a=oe({},o.value,t.state,{forward:c,scroll:vn()});i(a.current,a,!0);const u=oe({},kr(r.value,c,null),{position:a.position+1},d);i(c,u,!1),r.value=c}return{location:r,state:o,push:s,replace:l}}function El(e){e=pl(e);const t=bl(e),n=xl(e,t.state,t.location,t.replace);function r(i,l=!0){l||n.pauseListeners(),history.go(i)}const o=oe({location:"",base:e,go:r,createHref:ml.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Sl(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),El(e)}function kl(e){return typeof e=="string"||e&&typeof e=="object"}function To(e){return typeof e=="string"||typeof e=="symbol"}const Uo=Symbol("");var Cr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Cr||(Cr={}));function at(e,t){return oe(new Error,{type:e,[Uo]:!0},t)}function Te(e,t){return e instanceof Error&&Uo in e&&(t==null||!!(e.type&t))}const Rr="[^/]+?",Cl={sensitive:!1,strict:!1,start:!0,end:!0},Rl=/[.+*?^${}()[\]/\\]/g;function Pl(e,t){const n=oe({},Cl,t),r=[];let o=n.start?"^":"";const i=[];for(const d of e){const a=d.length?[]:[90];n.strict&&!d.length&&(o+="/");for(let u=0;u<d.length;u++){const f=d[u];let h=40+(n.sensitive?.25:0);if(f.type===0)u||(o+="/"),o+=f.value.replace(Rl,"\\$&"),h+=40;else if(f.type===1){const{value:v,repeatable:C,optional:x,regexp:S}=f;i.push({name:v,repeatable:C,optional:x});const m=S||Rr;if(m!==Rr){h+=10;try{new RegExp(`(${m})`)}catch(V){throw new Error(`Invalid custom RegExp for param "${v}" (${m}): `+V.message)}}let R=C?`((?:${m})(?:/(?:${m}))*)`:`(${m})`;u||(R=x&&d.length<2?`(?:/${R})`:"/"+R),x&&(R+="?"),o+=R,h+=20,x&&(h+=-8),C&&(h+=-20),m===".*"&&(h+=-50)}a.push(h)}r.push(a)}if(n.strict&&n.end){const d=r.length-1;r[d][r[d].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,n.sensitive?"":"i");function s(d){const a=d.match(l),u={};if(!a)return null;for(let f=1;f<a.length;f++){const h=a[f]||"",v=i[f-1];u[v.name]=h&&v.repeatable?h.split("/"):h}return u}function c(d){let a="",u=!1;for(const f of e){(!u||!a.endsWith("/"))&&(a+="/"),u=!1;for(const h of f)if(h.type===0)a+=h.value;else if(h.type===1){const{value:v,repeatable:C,optional:x}=h,S=v in d?d[v]:"";if($e(S)&&!C)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const m=$e(S)?S.join("/"):S;if(!m)if(x)f.length<2&&(a.endsWith("/")?a=a.slice(0,-1):u=!0);else throw new Error(`Missing required param "${v}"`);a+=m}}return a||"/"}return{re:l,score:r,keys:i,parse:s,stringify:c}}function $l(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Oo(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const i=$l(r[n],o[n]);if(i)return i;n++}if(Math.abs(o.length-r.length)===1){if(Pr(r))return 1;if(Pr(o))return-1}return o.length-r.length}function Pr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Vl={type:0,value:""},Ml=/[a-zA-Z0-9_]/;function Al(e){if(!e)return[[]];if(e==="/")return[[Vl]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${n})/"${d}": ${h}`)}let n=0,r=n;const o=[];let i;function l(){i&&o.push(i),i=[]}let s=0,c,d="",a="";function u(){d&&(n===0?i.push({type:0,value:d}):n===1||n===2||n===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function f(){d+=c}for(;s<e.length;){if(c=e[s++],c==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:c==="/"?(d&&u(),l()):c===":"?(u(),n=1):f();break;case 4:f(),n=r;break;case 1:c==="("?n=2:Ml.test(c)?f():(u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&s--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),u(),l(),o}function Nl(e,t,n){const r=Pl(Al(e.path),n),o=oe(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Il(e,t){const n=[],r=new Map;t=Ar({strict:!1,end:!0,sensitive:!1},t);function o(u){return r.get(u)}function i(u,f,h){const v=!h,C=Vr(u);C.aliasOf=h&&h.record;const x=Ar(t,u),S=[C];if("alias"in u){const V=typeof u.alias=="string"?[u.alias]:u.alias;for(const I of V)S.push(Vr(oe({},C,{components:h?h.record.components:C.components,path:I,aliasOf:h?h.record:C})))}let m,R;for(const V of S){const{path:I}=V;if(f&&I[0]!=="/"){const D=f.record.path,M=D[D.length-1]==="/"?"":"/";V.path=f.record.path+(I&&M+I)}if(m=Nl(V,f,x),h?h.alias.push(m):(R=R||m,R!==m&&R.alias.push(m),v&&u.name&&!Mr(m)&&l(u.name)),zo(m)&&c(m),C.children){const D=C.children;for(let M=0;M<D.length;M++)i(D[M],m,h&&h.children[M])}h=h||m}return R?()=>{l(R)}:bt}function l(u){if(To(u)){const f=r.get(u);f&&(r.delete(u),n.splice(n.indexOf(f),1),f.children.forEach(l),f.alias.forEach(l))}else{const f=n.indexOf(u);f>-1&&(n.splice(f,1),u.record.name&&r.delete(u.record.name),u.children.forEach(l),u.alias.forEach(l))}}function s(){return n}function c(u){const f=Ul(u,n);n.splice(f,0,u),u.record.name&&!Mr(u)&&r.set(u.record.name,u)}function d(u,f){let h,v={},C,x;if("name"in u&&u.name){if(h=r.get(u.name),!h)throw at(1,{location:u});x=h.record.name,v=oe($r(f.params,h.keys.filter(R=>!R.optional).concat(h.parent?h.parent.keys.filter(R=>R.optional):[]).map(R=>R.name)),u.params&&$r(u.params,h.keys.map(R=>R.name))),C=h.stringify(v)}else if(u.path!=null)C=u.path,h=n.find(R=>R.re.test(C)),h&&(v=h.parse(C),x=h.record.name);else{if(h=f.name?r.get(f.name):n.find(R=>R.re.test(f.path)),!h)throw at(1,{location:u,currentLocation:f});x=h.record.name,v=oe({},f.params,u.params),C=h.stringify(v)}const S=[];let m=h;for(;m;)S.unshift(m.record),m=m.parent;return{name:x,path:C,params:v,matched:S,meta:Tl(S)}}e.forEach(u=>i(u));function a(){n.length=0,r.clear()}return{addRoute:i,resolve:d,removeRoute:l,clearRoutes:a,getRoutes:s,getRecordMatcher:o}}function $r(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Vr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Dl(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Dl(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Mr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Tl(e){return e.reduce((t,n)=>oe(t,n.meta),{})}function Ar(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Ul(e,t){let n=0,r=t.length;for(;n!==r;){const i=n+r>>1;Oo(e,t[i])<0?r=i:n=i+1}const o=Ol(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function Ol(e){let t=e;for(;t=t.parent;)if(zo(t)&&Oo(e,t)===0)return t}function zo({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function zl(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const i=r[o].replace(Vo," "),l=i.indexOf("="),s=kt(l<0?i:i.slice(0,l)),c=l<0?null:kt(i.slice(l+1));if(s in t){let d=t[s];$e(d)||(d=t[s]=[d]),d.push(c)}else t[s]=c}return t}function Nr(e){let t="";for(let n in e){const r=e[n];if(n=ol(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}($e(r)?r.map(i=>i&&In(i)):[r&&In(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function Ll(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=$e(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const Fl=Symbol(""),Ir=Symbol(""),yn=Symbol(""),Lo=Symbol(""),Tn=Symbol("");function pt(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Fe(e,t,n,r,o,i=l=>l()){const l=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((s,c)=>{const d=f=>{f===!1?c(at(4,{from:n,to:t})):f instanceof Error?c(f):kl(f)?c(at(2,{from:t,to:f})):(l&&r.enterCallbacks[o]===l&&typeof f=="function"&&l.push(f),s())},a=i(()=>e.call(r&&r.instances[o],t,n,d));let u=Promise.resolve(a);e.length<3&&(u=u.then(d)),u.catch(f=>c(f))})}function Rn(e,t,n,r,o=i=>i()){const i=[];for(const l of e)for(const s in l.components){let c=l.components[s];if(!(t!=="beforeRouteEnter"&&!l.instances[s]))if(Po(c)){const a=(c.__vccOpts||c)[t];a&&i.push(Fe(a,n,r,l,s,o))}else{let d=c();i.push(()=>d.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${s}" at "${l.path}"`);const u=Ki(a)?a.default:a;l.mods[s]=a,l.components[s]=u;const h=(u.__vccOpts||u)[t];return h&&Fe(h,n,r,l,s,o)()}))}}return i}function Dr(e){const t=it(yn),n=it(Lo),r=ve(()=>{const c=G(e.to);return t.resolve(c)}),o=ve(()=>{const{matched:c}=r.value,{length:d}=c,a=c[d-1],u=n.matched;if(!a||!u.length)return-1;const f=u.findIndex(lt.bind(null,a));if(f>-1)return f;const h=Tr(c[d-2]);return d>1&&Tr(a)===h&&u[u.length-1].path!==h?u.findIndex(lt.bind(null,c[d-2])):f}),i=ve(()=>o.value>-1&&Xl(n.params,r.value.params)),l=ve(()=>o.value>-1&&o.value===n.matched.length-1&&Io(n.params,r.value.params));function s(c={}){if(Hl(c)){const d=t[G(e.replace)?"replace":"push"](G(e.to)).catch(bt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:r,href:ve(()=>r.value.href),isActive:i,isExactActive:l,navigate:s}}function ql(e){return e.length===1?e[0]:e}const Yl=bo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Dr,setup(e,{slots:t}){const n=Be(Dr(e)),{options:r}=it(yn),o=ve(()=>({[Ur(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Ur(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&ql(t.default(n));return e.custom?i:Eo("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},i)}}}),Bl=Yl;function Hl(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Xl(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!$e(o)||o.length!==r.length||r.some((i,l)=>i!==o[l]))return!1}return!0}function Tr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ur=(e,t,n)=>e??t??n,Kl=bo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=it(Tn),o=ve(()=>e.route||r.value),i=it(Ir,0),l=ve(()=>{let d=G(i);const{matched:a}=o.value;let u;for(;(u=a[d])&&!u.components;)d++;return d}),s=ve(()=>o.value.matched[l.value]);Sn(Ir,ve(()=>l.value+1)),Sn(Fl,s),Sn(Tn,o);const c=B();return rt(()=>[c.value,s.value,e.name],([d,a,u],[f,h,v])=>{a&&(a.instances[u]=d,h&&h!==a&&d&&d===f&&(a.leaveGuards.size||(a.leaveGuards=h.leaveGuards),a.updateGuards.size||(a.updateGuards=h.updateGuards))),d&&a&&(!h||!lt(a,h)||!f)&&(a.enterCallbacks[u]||[]).forEach(C=>C(d))},{flush:"post"}),()=>{const d=o.value,a=e.name,u=s.value,f=u&&u.components[a];if(!f)return Or(n.default,{Component:f,route:d});const h=u.props[a],v=h?h===!0?d.params:typeof h=="function"?h(d):h:null,x=Eo(f,oe({},v,t,{onVnodeUnmounted:S=>{S.component.isUnmounted&&(u.instances[a]=null)},ref:c}));return Or(n.default,{Component:x,route:d})||x}}});function Or(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Gl=Kl;function Wl(e){const t=Il(e.routes,e),n=e.parseQuery||zl,r=e.stringifyQuery||Nr,o=e.history,i=pt(),l=pt(),s=pt(),c=Ni(Le);let d=Le;et&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=kn.bind(null,k=>""+k),u=kn.bind(null,ll),f=kn.bind(null,kt);function h(k,z){let T,H;return To(k)?(T=t.getRecordMatcher(k),H=z):H=k,t.addRoute(H,T)}function v(k){const z=t.getRecordMatcher(k);z&&t.removeRoute(z)}function C(){return t.getRoutes().map(k=>k.record)}function x(k){return!!t.getRecordMatcher(k)}function S(k,z){if(z=oe({},z||c.value),typeof k=="string"){const Q=Cn(n,k,z.path),W=t.resolve({path:Q.path},z),be=o.createHref(Q.fullPath);return oe(Q,W,{params:f(W.params),hash:kt(Q.hash),redirectedFrom:void 0,href:be})}let T;if(k.path!=null)T=oe({},k,{path:Cn(n,k.path,z.path).path});else{const Q=oe({},k.params);for(const W in Q)Q[W]==null&&delete Q[W];T=oe({},k,{params:u(Q)}),z.params=u(z.params)}const H=t.resolve(T,z),ie=k.hash||"";H.params=a(f(H.params));const fe=ul(r,oe({},k,{hash:rl(ie),path:H.path})),q=o.createHref(fe);return oe({fullPath:fe,hash:ie,query:r===Nr?Ll(k.query):k.query||{}},H,{redirectedFrom:void 0,href:q})}function m(k){return typeof k=="string"?Cn(n,k,c.value.path):oe({},k)}function R(k,z){if(d!==k)return at(8,{from:z,to:k})}function V(k){return M(k)}function I(k){return V(oe(m(k),{replace:!0}))}function D(k){const z=k.matched[k.matched.length-1];if(z&&z.redirect){const{redirect:T}=z;let H=typeof T=="function"?T(k):T;return typeof H=="string"&&(H=H.includes("?")||H.includes("#")?H=m(H):{path:H},H.params={}),oe({query:k.query,hash:k.hash,params:H.path!=null?{}:k.params},H)}}function M(k,z){const T=d=S(k),H=c.value,ie=k.state,fe=k.force,q=k.replace===!0,Q=D(T);if(Q)return M(oe(m(Q),{state:typeof Q=="object"?oe({},ie,Q.state):ie,force:fe,replace:q}),z||T);const W=T;W.redirectedFrom=z;let be;return!fe&&cl(r,H,T)&&(be=at(16,{to:W,from:H}),re(H,H,!0,!1)),(be?Promise.resolve(be):w(W,H)).catch(ge=>Te(ge)?Te(ge,2)?ge:me(ge):ee(ge,W,H)).then(ge=>{if(ge){if(Te(ge,2))return M(oe({replace:q},m(ge.to),{state:typeof ge.to=="object"?oe({},ie,ge.to.state):ie,force:fe}),z||W)}else ge=N(W,H,!0,q,ie);return g(W,H,ge),ge})}function A(k,z){const T=R(k,z);return T?Promise.reject(T):Promise.resolve()}function $(k){const z=J.values().next().value;return z&&typeof z.runWithContext=="function"?z.runWithContext(k):k()}function w(k,z){let T;const[H,ie,fe]=Ql(k,z);T=Rn(H.reverse(),"beforeRouteLeave",k,z);for(const Q of H)Q.leaveGuards.forEach(W=>{T.push(Fe(W,k,z))});const q=A.bind(null,k,z);return T.push(q),j(T).then(()=>{T=[];for(const Q of i.list())T.push(Fe(Q,k,z));return T.push(q),j(T)}).then(()=>{T=Rn(ie,"beforeRouteUpdate",k,z);for(const Q of ie)Q.updateGuards.forEach(W=>{T.push(Fe(W,k,z))});return T.push(q),j(T)}).then(()=>{T=[];for(const Q of fe)if(Q.beforeEnter)if($e(Q.beforeEnter))for(const W of Q.beforeEnter)T.push(Fe(W,k,z));else T.push(Fe(Q.beforeEnter,k,z));return T.push(q),j(T)}).then(()=>(k.matched.forEach(Q=>Q.enterCallbacks={}),T=Rn(fe,"beforeRouteEnter",k,z,$),T.push(q),j(T))).then(()=>{T=[];for(const Q of l.list())T.push(Fe(Q,k,z));return T.push(q),j(T)}).catch(Q=>Te(Q,8)?Q:Promise.reject(Q))}function g(k,z,T){s.list().forEach(H=>$(()=>H(k,z,T)))}function N(k,z,T,H,ie){const fe=R(k,z);if(fe)return fe;const q=z===Le,Q=et?history.state:{};T&&(H||q?o.replace(k.fullPath,oe({scroll:q&&Q&&Q.scroll},ie)):o.push(k.fullPath,ie)),c.value=k,re(k,z,T,q),me()}let L;function F(){L||(L=o.listen((k,z,T)=>{if(!U.listening)return;const H=S(k),ie=D(H);if(ie){M(oe(ie,{replace:!0,force:!0}),H).catch(bt);return}d=H;const fe=c.value;et&&vl(Sr(fe.fullPath,T.delta),vn()),w(H,fe).catch(q=>Te(q,12)?q:Te(q,2)?(M(oe(m(q.to),{force:!0}),H).then(Q=>{Te(Q,20)&&!T.delta&&T.type===Ct.pop&&o.go(-1,!1)}).catch(bt),Promise.reject()):(T.delta&&o.go(-T.delta,!1),ee(q,H,fe))).then(q=>{q=q||N(H,fe,!1),q&&(T.delta&&!Te(q,8)?o.go(-T.delta,!1):T.type===Ct.pop&&Te(q,20)&&o.go(-1,!1)),g(H,fe,q)}).catch(bt)}))}let E=pt(),X=pt(),K;function ee(k,z,T){me(k);const H=X.list();return H.length?H.forEach(ie=>ie(k,z,T)):console.error(k),Promise.reject(k)}function ce(){return K&&c.value!==Le?Promise.resolve():new Promise((k,z)=>{E.add([k,z])})}function me(k){return K||(K=!k,F(),E.list().forEach(([z,T])=>k?T(k):z()),E.reset()),k}function re(k,z,T,H){const{scrollBehavior:ie}=e;if(!et||!ie)return Promise.resolve();const fe=!T&&yl(Sr(k.fullPath,0))||(H||!T)&&history.state&&history.state.scroll||null;return jt().then(()=>ie(k,z,fe)).then(q=>q&&gl(q)).catch(q=>ee(q,k,z))}const y=k=>o.go(k);let b;const J=new Set,U={currentRoute:c,listening:!0,addRoute:h,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:x,getRoutes:C,resolve:S,options:e,push:V,replace:I,go:y,back:()=>y(-1),forward:()=>y(1),beforeEach:i.add,beforeResolve:l.add,afterEach:s.add,onError:X.add,isReady:ce,install(k){const z=this;k.component("RouterLink",Bl),k.component("RouterView",Gl),k.config.globalProperties.$router=z,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>G(c)}),et&&!b&&c.value===Le&&(b=!0,V(o.location).catch(ie=>{}));const T={};for(const ie in Le)Object.defineProperty(T,ie,{get:()=>c.value[ie],enumerable:!0});k.provide(yn,z),k.provide(Lo,Ii(T)),k.provide(Tn,c);const H=k.unmount;J.add(k),k.unmount=function(){J.delete(k),J.size<1&&(d=Le,L&&L(),L=null,c.value=Le,b=!1,K=!1),H()}}};function j(k){return k.reduce((z,T)=>z.then(()=>$(T)),Promise.resolve())}return U}function Ql(e,t){const n=[],r=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let l=0;l<i;l++){const s=t.matched[l];s&&(e.matched.find(d=>lt(d,s))?r.push(s):n.push(s));const c=e.matched[l];c&&(t.matched.find(d=>lt(d,c))||o.push(c))}return[n,r,o]}function Zl(){return it(yn)}const Jl={class:"app-container"},jl={class:"grid-container"},ea={class:"header"},ta={class:"header-left"},na={class:"user-info"},ra={class:"el-dropdown-link"},oa={class:"user-name"},ia={class:"sidebar"},la={class:"content",ref:"contentRef"},aa={__name:"App",setup(e){const t=Zl(),n=B(""),r=B([]),o=B(!1),i=B(""),l=B(null);dt(async()=>{await s(),await c(),t.push("/unit-management")});const s=async()=>{const D=await se.post("/api/get_user_info.php");n.value=D.user.name},c=async()=>{const D=await se.post("/api/get_user_app.php");r.value=D.data},d=D=>{console.log("点击的菜单对应的路由是:",D),t.push(D)},a=()=>{o.value=!o.value},u=B(!1),f=B({oldPassword:"",newPassword:"",confirmPassword:""}),h=B(null),v=B(!1),C=B(!1),x=()=>{v.value=!v.value},S=()=>{C.value=!C.value},m=async D=>{D==="logout"?(await se.get("/api/logout.php"),window.location.href="login.html"):D==="changePassword"&&(u.value=!0)},R=async()=>{h.value&&await h.value.validate(D=>{if(D){if(f.value.newPassword!==f.value.confirmPassword){Z.error("两次输入的密码不一致");return}const M=new FormData;M.append("old_password",f.value.oldPassword),M.append("new_password",f.value.newPassword),se.post("/api/change_password.php",M).then(()=>{Z.success("密码修改成功，请重新登录"),u.value=!1,f.value={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout(()=>{window.location.href="login.html"},3e3)}).catch(()=>{Z.error("密码修改失败")})}})},V=Be({oldPassword:[{required:!0,message:"请输入旧密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认新密码",trigger:"blur"},{validator:(D,M,A)=>{M!==f.value.newPassword?A(new Error("两次输入的密码不一致")):A()},trigger:"blur"}]}),I=()=>{const D=l.value;D&&(document.fullscreenElement?document.exitFullscreen():D.requestFullscreen().catch(M=>{console.error("全屏失败:",M),Z.error("全屏功能不支持")}))};return(D,M)=>{const A=O("el-button"),$=O("el-icon"),w=O("el-avatar"),g=O("el-dropdown-item"),N=O("el-dropdown-menu"),L=O("el-dropdown"),F=O("el-menu-item"),E=O("el-menu"),X=O("router-view"),K=O("el-input"),ee=O("el-form-item"),ce=O("el-form"),me=O("el-dialog");return le(),_e(He,null,[Y("div",Jl,[Y("div",jl,[Y("div",ea,[Y("div",ta,[p(A,{onClick:a,type:"text",class:"menu-btn"},{default:_(()=>M[5]||(M[5]=[Y("i",{class:"el-icon-menu"},null,-1)])),_:1,__:[5]}),M[6]||(M[6]=Y("img",{src:Di,alt:"Logo",class:"header-logo"},null,-1)),M[7]||(M[7]=Y("span",{class:"logo"},"CloudPivot",-1))])]),Y("div",na,[p(L,{onCommand:m},{dropdown:_(()=>[p(N,null,{default:_(()=>[p(g,{command:"profile"},{default:_(()=>M[8]||(M[8]=[ne("个人信息")])),_:1,__:[8]}),p(g,{command:"changePassword"},{default:_(()=>M[9]||(M[9]=[ne("修改密码")])),_:1,__:[9]}),p(g,{command:"logout"},{default:_(()=>M[10]||(M[10]=[ne("退出登录")])),_:1,__:[10]})]),_:1})]),default:_(()=>[Y("span",ra,[p(w,{size:"small"},{default:_(()=>[p($,null,{default:_(()=>[p(G(Ti),{style:{color:"#409EFF"}})]),_:1})]),_:1}),Y("span",oa,ye(n.value),1)])]),_:1})]),Y("div",ia,[p(E,{"default-active":i.value,class:"el-menu-vertical",mode:"vertical",collapse:o.value,onOpen:D.handleOpen,onClose:D.handleClose},{default:_(()=>[(le(!0),_e(He,null,Ge(r.value,re=>(le(),Ye(F,{key:re.id,index:re.id.toString(),router:re.url,onClick:y=>d(re.url)},{title:_(()=>[Y("span",null,ye(re.application_name),1)]),_:2},1032,["index","router","onClick"]))),128))]),_:1},8,["default-active","collapse","onOpen","onClose"])]),Y("div",la,[p(A,{onClick:I,type:"text",class:"fullscreen-btn"},{default:_(()=>[p($,null,{default:_(()=>[p(G(Ui))]),_:1})]),_:1}),Y("div",{class:"fullscreen-target",ref_key:"fullscreenTargetRef",ref:l},[p(X,{ref:"routerViewRef"},null,512)],512)],512)])]),p(me,{modelValue:u.value,"onUpdate:modelValue":M[4]||(M[4]=re=>u.value=re),width:"400px"},{default:_(()=>[p(ce,{model:f.value,ref_key:"passwordFormRef",ref:h,rules:V,"label-width":"120px",onSubmit:So(R,["prevent"])},{default:_(()=>[p(ee,{label:"旧密码",prop:"oldPassword",required:""},{default:_(()=>[p(K,{modelValue:f.value.oldPassword,"onUpdate:modelValue":M[0]||(M[0]=re=>f.value.oldPassword=re),type:"password",placeholder:"请输入旧密码"},null,8,["modelValue"])]),_:1}),p(ee,{label:"新密码",prop:"newPassword",required:""},{default:_(()=>[p(K,{modelValue:f.value.newPassword,"onUpdate:modelValue":M[1]||(M[1]=re=>f.value.newPassword=re),type:v.value?"text":"password",placeholder:"请输入新密码"},{suffix:_(()=>[p(A,{icon:v.value?G(_r):G(gr),onClick:x,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),p(ee,{label:"确认新密码",prop:"confirmPassword",required:""},{default:_(()=>[p(K,{modelValue:f.value.confirmPassword,"onUpdate:modelValue":M[2]||(M[2]=re=>f.value.confirmPassword=re),type:C.value?"text":"password",placeholder:"请输入确认新密码"},{suffix:_(()=>[p(A,{icon:C.value?G(_r):G(gr),onClick:S,circle:"",size:"small"},null,8,["icon"])]),_:1},8,["modelValue","type"])]),_:1}),p(ee,null,{default:_(()=>[p(A,{type:"primary","native-type":"submit"},{default:_(()=>M[11]||(M[11]=[ne("确定")])),_:1,__:[11]}),p(A,{onClick:M[3]||(M[3]=re=>u.value=!1)},{default:_(()=>M[12]||(M[12]=[ne("取消")])),_:1,__:[12]})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}},sa=Dt(aa,[["__scopeId","data-v-1e64b65d"]]),ua={class:"unit-management"},ca={style:{"text-align":"right",margin:"10px"}},fa={__name:"UnitManagement",setup(e){const t=B(!1),n={expandTrigger:"hover",checkStrictly:!0,value:"id",label:"unit_name",children:"children"},r=w=>{if(w&&w.length>0){const g=w[w.length-1],N=i(g);N&&(o.parentId=g,u.value=N.children||[])}else o.parentId=null,u.value=[]},o=Be({id:null,name:"",parentId:null,parentIdPath:[],code:"",sort:0});rt(()=>o.parentId,(w,g)=>{if(console.log("parentId 发生变化，旧值: ",g,"新值: ",w),w){const N=i(w);N&&(u.value=N.children||[])}else u.value=[]},{immediate:!1});const i=(w,g=l.value)=>{for(let N=0;N<g.length;N++){if(g[N].id===w)return g[N];if(g[N].children){const L=i(w,g[N].children);if(L)return L}}return null},l=B([]),s=B(new Set),c=ve(()=>{const w=[],g=(N,L=0,F=null)=>{N.forEach(E=>{w.push({...E,level:L,parentId:F,expanded:s.value.has(E.id)}),E.children&&g(E.children,L+1,E.id)})};return g(l.value),w}),d=ve(()=>{const w=[],g=N=>{if(N.level===0)return!0;let L=c.value.find(F=>F.id===N.parentId);for(;L;){if(!L.expanded)return!1;L=c.value.find(F=>F.id===L.parentId)}return!0};return c.value.forEach(N=>{g(N)&&w.push(N)}),w}),a=B(!1),u=B([]),f=B(null);dt(async()=>{await m()});const h=B(!1),v=w=>{const g=[];function N(L,F){for(const E of L){const X=[...F,E.id];if(E.id===w)return g.push(...X),!0;if(E.children&&E.children.length>0&&N(E.children,X))return!0}return!1}return N(l.value,[]),g},C=w=>{if(f.value&&f.value.resetFields(),t.value=!1,o.id=null,o.name="",o.code="",o.parentId=null,o.parentIdPath=[],o.sort_order=0,h.value=!!w,w){const g=i(w);g&&(o.parentId=w,console.log("parentId位置一:",w),o.parentIdPath=v(w),u.value=g.children||[])}else o.parentId=null,o.parentIdPath=[],u.value=[];a.value=!0,console.log("dialogVisible 已设为 true")},x=w=>{f.value&&f.value.resetFields(),t.value=!0,o.id=w.id;let g={currentUnit:w.unit_name,selectedSortOrder:null,selectedUnit:null};o.name=w.unit_name,o.code=w.code,o.parentId=w.parent_id,o.parentIdPath=v(w.parent_id);const N=i(w.parent_id);if(N){u.value=(N.children||[]).filter(E=>E.id!==w.id);const L=N.children||[],F=L.findIndex(E=>E.id===w.id);if(F>0){const E=L[F-1];o.sort_order=E.sort_order,g.selectedUnit=E.unit_name}else o.sort_order=0,g.selectedUnit="最前";g.selectedSortOrder=o.sort_order}else{u.value=l.value.filter(F=>F.id!==w.id);const L=l.value.findIndex(F=>F.id===w.id);if(L>0){const F=l.value[L-1];o.sort_order=F.sort_order,g.selectedUnit=F.unit_name}else o.sort_order=0,g.selectedUnit="最前";g.selectedSortOrder=o.sort_order}console.log("排序选项:",u.value),console.log("自动选择结果:",g),a.value=!0},S=()=>{f.value.validate(async w=>{if(w)try{const g=new FormData;o.id?(g.append("action","edit"),g.append("id",o.id)):g.append("action","add"),g.append("sort_order",o.sort_order+1),g.append("unit_name",o.name),g.append("code",o.code),g.append("parent_id",o.parentId||null);const N=await se.post("api/unit_manage.php",g,{headers:{"Content-Type":"multipart/form-data"}});N.status===1?(await m(),a.value=!1,Z.success(o.id?"编辑成功":"新增成功")):Z.error(N.message)}catch(g){console.error("保存单位失败:",g),Z.error("保存单位失败，请稍后重试")}})},m=async()=>{const w=await se.post("api/get_unit_info.php");l.value=[w.data],console.log("获取单位数据成功:",l.value);const g=N=>{N.forEach(L=>{L.children&&L.children.length>0&&(s.value.add(L.id),g(L.children))})};g(l.value)},R=w=>{St.confirm(`确定要删除 ${w.unit_name} 吗？`,"Warning",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{const g=new FormData;g.append("action","del"),g.append("id",w.id),await se.post("api/unit_manage.php",g),Z.success("删除成功"),await m()})},V=w=>{const g=new Set(s.value);g.has(w.id)?g.delete(w.id):g.add(w.id),s.value=g},I=w=>((w.parentId?i(w.parentId):{children:l.value}).children||[]).findIndex(F=>F.id===w.id)===0,D=w=>{const N=(w.parentId?i(w.parentId):{children:l.value}).children||[];return N.findIndex(F=>F.id===w.id)===N.length-1},M=async w=>{const g=new FormData;g.append("action","edit"),g.append("id",w.id),g.append("sort_order",w.sort_order-1),g.append("unit_name",w.unit_name),g.append("code",w.code),g.append("parent_id",w.parentId),await se.post("api/unit_manage.php",g),await m()},A=async w=>{const g=new FormData;g.append("action","edit"),g.append("id",w.id),g.append("sort_order",w.sort_order+1),g.append("unit_name",w.unit_name),g.append("code",w.code),g.append("parent_id",w.parentId),await se.post("api/unit_manage.php",g),await m()},$=ve(()=>u.value.filter(w=>w.id!==o.id));return(w,g)=>{const N=O("el-button"),L=O("el-col"),F=O("el-row"),E=O("el-table-column"),X=O("el-icon"),K=O("el-button-group"),ee=O("el-table"),ce=O("el-input"),me=O("el-form-item"),re=O("el-cascader"),y=O("el-option"),b=O("el-select"),J=O("el-form");return le(),_e("div",ua,[p(F,null,{default:_(()=>[p(L,{span:24},{default:_(()=>[Y("div",ca,[p(N,{type:"primary",round:"",onClick:g[0]||(g[0]=U=>C(null))},{default:_(()=>g[7]||(g[7]=[ne("添加单位")])),_:1,__:[7]})])]),_:1})]),_:1}),p(ee,{data:d.value,style:{width:"100%",height:"calc(100vh - 200px)","overflow-y":"auto"},border:""},{default:_(()=>[p(E,{label:"操作",width:"60",align:"center"},{default:_(U=>[U.row.children&&U.row.children.length?(le(),Ye(N,{key:0,size:"mini",type:"text",onClick:j=>V(U.row)},{default:_(()=>[ne(ye(U.row.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):en("",!0)]),_:1}),p(E,{prop:"unit_name",label:"单位名称"},{default:_(U=>[Y("span",{style:ir({paddingLeft:`${U.row.level*20}px`})},ye(U.row.unit_name),5)]),_:1}),p(E,{prop:"code",label:"组织机构代码",width:"250"}),p(E,{label:"操作",width:"350"},{default:_(U=>[p(K,null,{default:_(()=>[p(N,{size:"mini",type:"primary",onClick:j=>C(U.row.id)},{default:_(()=>[p(X,null,{default:_(()=>[p(G(ko))]),_:1})]),_:2},1032,["onClick"]),p(N,{size:"mini",type:"warning",onClick:j=>x(U.row)},{default:_(()=>[p(X,null,{default:_(()=>[p(G(_n))]),_:1})]),_:2},1032,["onClick"]),p(N,{size:"mini",type:"danger",onClick:j=>R(U.row)},{default:_(()=>[p(X,null,{default:_(()=>[p(G(gn))]),_:1})]),_:2},1032,["onClick"]),p(N,{size:"mini",type:"info",onClick:j=>M(U.row),disabled:I(U.row)},{default:_(()=>[p(X,null,{default:_(()=>[p(G(Co))]),_:1})]),_:2},1032,["onClick","disabled"]),p(N,{size:"mini",type:"info",onClick:j=>A(U.row),disabled:D(U.row)},{default:_(()=>[p(X,null,{default:_(()=>[p(G(Ro))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),p(G(Oi),{modelValue:a.value,"onUpdate:modelValue":g[6]||(g[6]=U=>a.value=U),title:"",width:"450px","close-on-click-modal":!1},{footer:_(()=>[p(N,{onClick:g[5]||(g[5]=U=>a.value=!1)},{default:_(()=>g[8]||(g[8]=[ne("取消")])),_:1,__:[8]}),p(N,{type:"primary",onClick:S},{default:_(()=>g[9]||(g[9]=[ne("确定")])),_:1,__:[9]})]),default:_(()=>[p(J,{model:o,ref_key:"formRef",ref:f,"label-width":"130px"},{default:_(()=>[p(me,{label:"单位名称",prop:"name",required:""},{default:_(()=>[p(ce,{modelValue:o.name,"onUpdate:modelValue":g[1]||(g[1]=U=>o.name=U),required:""},null,8,["modelValue"])]),_:1}),p(me,{label:"组织机构代码",prop:"code"},{default:_(()=>[p(ce,{modelValue:o.code,"onUpdate:modelValue":g[2]||(g[2]=U=>o.code=U)},null,8,["modelValue"])]),_:1}),p(me,{label:"上级单位",prop:"parentId"},{default:_(()=>[p(re,{modelValue:o.parentIdPath,"onUpdate:modelValue":g[3]||(g[3]=U=>o.parentIdPath=U),options:l.value,props:n,onChange:r,style:{width:"100%"},"show-all-levels":!1,placeholder:"请选择上级单位"},null,8,["modelValue","options"])]),_:1}),p(me,{label:"排序",prop:"sort",required:""},{default:_(()=>[p(b,{modelValue:o.sort_order,"onUpdate:modelValue":g[4]||(g[4]=U=>o.sort_order=U),placeholder:"请选择排序位置"},{default:_(()=>[p(y,{label:"置于 最前",value:0}),(le(!0),_e(He,null,Ge($.value,U=>(le(),Ye(y,{key:U.id,label:`置于 ${U.unit_name} 之后`,value:U.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},da={class:"user-management-container"},pa={class:"left-panel"},ha=["onClick"],ma={class:"right-panel"},_a={class:"action-buttons",style:{display:"flex","align-items":"center"}},ga={class:"left-buttons",style:{display:"flex","align-items":"center",gap:"8px"}},va={class:"right-switches",style:{"margin-left":"auto",display:"flex","align-items":"center",gap:"10px"}},ya={class:"form-row"},wa={class:"form-row"},xa={class:"form-row"},ba={class:"form-row"},Ea={class:"form-row"},Sa={class:"form-row"},ka={class:"form-row"},Ca={class:"form-row"},Ra={class:"dialog-footer"},Pa={__name:"UserManagement",setup(e){const t=B(null);B("");const n=B([]),r=ve(()=>{if(!t.value)return"";const y=c.value.find(b=>b.id===t.value);return console.log("当前选中的部门ID:",t.value),console.log("当前选中的部门:",y.unit_name),y?y.unit_name:""}),o=B(1),i=B(10),l=B(0),s=B([]),c=B([]),d=B(""),a=B([]),u=B([]),f=B([]),h=B([]),v=B(!1);B(!1);const C=B(!1),x=B(null),S=ve(()=>d.value?c.value.filter(y=>y.show&&y.unit_name.toLowerCase().includes(d.value.toLowerCase())):c.value.filter(y=>y.show)),m=Be({name:"",id_number:"",phone:"",archive_birthdate:null,gender:null,short_code:"",alt_phone_1:"",alt_phone_2:"",landline:"",organization_unit:null,work_unit:null,employment_date:null,political_status:"",party_join_date:null,personnel_type:"",police_number:"",is_assisting_officer:null,employment_status:"",job_rank:"",current_rank_date:null,position:"",current_position_date:null,sorted_order:null,desc:""}),R={name:[{required:!0,message:"",trigger:"blur"}],id_number:[{required:!1,message:"",trigger:"blur"},{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"请输入正确的身份证号",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号码",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],archive_birthdate:[{required:!0,message:"请选择档案出生时间",trigger:"change"}]},V=B(null);dt(async()=>{try{const y=await se.post("api/get_unit_info.php");s.value=[y.data],D(y.data),a.value=s.value,u.value=s.value,console.log("获取部门数据成功:",s.value),await I()}catch(y){console.error("获取数据失败:",y),Z.error("获取数据失败，请稍后重试")}});const I=async()=>{console.log("开始获取用户数据...");try{const y=new FormData;y.append("controlCode","query"),y.append("page",o.value),y.append("pagesize",i.value),t.value&&y.append("organization_unit",t.value),$.value?y.append("isShowInPersonnel","1"):y.append("isShowInPersonnel","0"),A.value?y.append("isShowOutPersonnel","1"):y.append("isShowOutPersonnel","0");const b=await se.post("api/user_manage.php",y,{headers:{"Content-Type":"multipart/form-data"}});f.value=b.data,console.log("获取用户数据成功:",f.value),l.value=b.total}catch(y){console.error("获取用户数据失败:",y),Z.error("获取用户数据失败，请稍后重试")}},D=(y,b=0,J=null)=>{const U={...y,level:b,expanded:y.children&&y.children.length>0,parent:J,indent:b*20,show:!0};c.value.push(U),y.children&&y.children.length>0&&y.children.forEach(j=>{D(j,b+1,U)})};console.log("扁平化后的部门列表:",c.value);const M=y=>{y.expanded=!y.expanded;const b=c.value.indexOf(y)+1;let J=y.level+1;for(let U=b;U<c.value.length;U++){const j=c.value[U];if(j.level<=y.level)break;j.level===J?j.show=y.expanded:j.level>J&&(j.show=y.expanded&&c.value[U-1].show)}},A=B(!0),$=B(!0),w=()=>{v.value=!0,V.value&&V.value.resetFields()},g=async()=>{if(h.value.length===0){Z.warning("请先选择要删除的用户");return}try{await St.confirm(`确定要删除选中的 ${h.value.length} 个用户吗？删除后数据不可恢复`,"批量删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const y=new FormData;y.append("controlCode","del");const b=h.value.map(J=>J.id);y.append("id",b.join(",")),await se.post("api/user_manage.php",y,{headers:{"Content-Type":"multipart/form-data"}}),Z.success(`成功删除 ${h.value.length} 个用户`),await I(),h.value=[]}catch(y){y!=="cancel"&&Z.error(`删除失败：${y.message||"未知错误"}`)}},N=y=>{C.value=!0,x.value=y.id,v.value=!0,m.name=y.name,m.id_number=y.id_number,m.phone=y.phone,m.archive_birthdate=y.archive_birthdate,m.gender=y.gender,m.short_code=y.short_code,m.alt_phone_1=y.alt_phone_1,m.alt_phone_2=y.alt_phone_2,m.landline=y.landline,m.organization_unit=y.organization_unit,m.work_unit=y.work_unit,m.employment_date=y.employment_date,m.political_status=y.political_status,m.party_join_date=y.party_join_date,m.personnel_type=y.personnel_type,m.police_number=y.police_number,m.is_assisting_officer=y.is_assisting_officer,m.employment_status=y.employment_status,m.job_rank=y.job_rank,m.current_rank_date=y.current_rank_date,m.position=y.position,m.current_position_date=y.current_position_date,m.sorted_order=y.sorted_order,m.desc=y.desc},L=y=>{console.log("查看用户详情:",y)},F=async y=>{try{await St.confirm(`确定要删除用户 ${y.name} 吗？删除后数据不可恢复`,"删除用户确认",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"});const b=new FormData;b.append("controlCode","del"),b.append("id",y.id),await se.post("api/user_manage.php",b,{headers:{"Content-Type":"multipart/form-data"}}),Z.success("用户删除成功"),await I()}catch(b){b!=="cancel"&&Z.error(`删除失败：${b.message||"未知错误"}`)}},E=y=>{h.value=y},X=async y=>{console.log("点击的部门名称:",y),t.value=y.id,await I()},K=async()=>{try{await V.value.validate();const y=new FormData;C.value?(y.append("controlCode","update"),y.append("user_id",x.value)):y.append("controlCode","add"),y.append("name",m.name),y.append("id_number",m.id_number||""),y.append("phone",m.phone),y.append("archive_birthdate",m.archive_birthdate||""),y.append("gender",m.gender||""),y.append("short_code",m.short_code||""),y.append("alt_phone_1",m.alt_phone_1||""),y.append("alt_phone_2",m.alt_phone_2||""),y.append("landline",m.landline||"");const b=Array.isArray(m.organization_unit)?m.organization_unit[m.organization_unit.length-1]:m.organization_unit;y.append("organization_unit",b||"");const J=Array.isArray(m.work_unit)?m.work_unit[m.work_unit.length-1]:m.work_unit;y.append("work_unit",J||b),y.append("employment_date",m.employment_date||""),y.append("political_status",m.political_status||""),y.append("party_join_date",m.party_join_date||""),y.append("personnel_type",m.personnel_type||""),y.append("police_number",m.police_number||""),y.append("assisting_officer",m.is_assisting_officer||""),y.append("employment_status",m.employment_status||""),y.append("job_rank",m.job_rank||""),y.append("current_rank_date",m.current_rank_date||""),y.append("position",m.position||""),y.append("current_position_date",m.current_position_date||""),y.append("sort_order",m.sorted_order+1),y.append("desc",m.desc||"");const U=await se.post("api/user_manage.php",y,{headers:{"Content-Type":"multipart/form-data"}}),j=C.value?"编辑":"添加";Z.success(`用户${j}成功`),v.value=!1,C.value=!1,x.value=null,await I(),V.value.resetFields()}catch(y){Z.error("提交失败："+(y.message||"未知错误"))}},ee=ve(()=>{const y=new Map;return c.value.forEach(b=>{y.set(b.id,b.unit_name)}),y}),ce=y=>(console.log("单位ID:",y),console.log("组织映射:",ee.value),console.log("单位名称:",ee.value.get(y)),ee.value.get(y)||"未知单位"),me=async y=>{try{const b=new FormData;b.append("controlCode","modify");for(const U in y)U!=="sort_order"&&b.append(U,y[U]);const J=y.sort_order-1;b.append("sort_order",J),await se.post("api/user_manage.php",b),await I(),Z.success("上移成功")}catch{Z.error("上移失败，请稍后重试")}},re=async y=>{try{const b=new FormData;b.append("action","adjust_sort"),b.append("user_id",y.id),b.append("direction","down"),await se.post("api/user_manage.php",b),await I(),Z.success("下移成功")}catch{Z.error("下移失败，请稍后重试")}};return rt($,async y=>{o.value=1,await I()}),rt(A,async y=>{o.value=1,await I()}),rt(()=>m.organization_unit,async y=>{if(!y){n.value=[];return}try{const b=Array.isArray(y)?y[y.length-1]:y,J=new FormData;J.append("controlCode","getSortOptions"),J.append("organization_unit",b);const U=await se.post("/api/user_manage.php",J,{headers:{"Content-Type":"multipart/form-data"}});n.value=U.data,console.log("获取排序选项成功:",n.value)}catch(b){console.error("获取排序选项失败:",b),Z.error("获取排序选项失败，请稍后重试"),n.value=[]}}),(y,b)=>{const J=O("el-input"),U=O("el-button"),j=O("el-table-column"),k=O("el-table"),z=O("el-tag"),T=O("el-switch"),H=O("el-icon"),ie=O("el-button-group"),fe=O("el-pagination"),q=O("el-form-item"),Q=O("el-date-picker"),W=O("el-option"),be=O("el-select"),ge=O("el-cascader"),Mi=O("el-form"),Ai=O("el-dialog");return le(),_e("div",da,[Y("div",pa,[p(J,{modelValue:d.value,"onUpdate:modelValue":b[0]||(b[0]=P=>d.value=P),placeholder:"搜索单位",class:"department-search"},null,8,["modelValue"]),p(k,{data:S.value,border:"",class:"department-table"},{default:_(()=>[p(j,{label:"操作",width:"55"},{default:_(({row:P})=>[P.children&&P.children.length>0?(le(),Ye(U,{key:0,type:"text",size:"small",onClick:So(je=>M(P),["stop"])},{default:_(()=>[ne(ye(P.expanded?"-":"+"),1)]),_:2},1032,["onClick"])):en("",!0)]),_:1}),p(j,{prop:"unit_name",label:"单位名称"},{default:_(({row:P})=>[Y("span",{class:"indent",style:ir({width:`${P.indent}px`})},null,4),Y("span",{onClick:je=>X(P),style:{cursor:"pointer"}},ye(P.unit_name),9,ha)]),_:1})]),_:1},8,["data"])]),Y("div",ma,[Y("div",_a,[Y("div",ga,[p(U,{type:"primary",onClick:w},{default:_(()=>b[31]||(b[31]=[ne("新增")])),_:1,__:[31]}),p(U,{type:"danger",onClick:g},{default:_(()=>b[32]||(b[32]=[ne("删除")])),_:1,__:[32]})]),Y("div",va,[r.value?(le(),Ye(z,{key:0,type:"info",style:{"margin-left":"10px"}},{default:_(()=>[ne(" 当前单位："+ye(r.value),1)]),_:1})):en("",!0),p(T,{modelValue:A.value,"onUpdate:modelValue":b[1]||(b[1]=P=>A.value=P),"inline-prompt":"","active-text":"显示抽出人员","inactive-text":"不显示抽出人员",style:{"margin-left":"10px"}},null,8,["modelValue"]),p(T,{modelValue:$.value,"onUpdate:modelValue":b[2]||(b[2]=P=>$.value=P),"active-text":"显示抽入人员","inactive-text":"显示抽入人员","inline-prompt":"",style:{"margin-left":"10px"}},null,8,["modelValue"])])]),p(k,{data:f.value,border:"",class:"user-table",onSelectionChange:E},{default:_(()=>[p(j,{type:"selection",width:"55"}),p(j,{prop:"name",label:"姓名",width:"70"}),p(j,{prop:"id_number",label:"身份证号",width:"100"}),p(j,{prop:"phone",label:"手机号码",width:"115"}),p(j,{label:"性别",width:"55"},{default:_(P=>[ne(ye(P.row.gender===1?"男":P.row.gender===2?"女":"未知"),1)]),_:1}),p(j,{label:"编制单位"},{default:_(P=>[ne(ye(ce(P.row.organization_unit)),1)]),_:1}),p(j,{label:"工作单位"},{default:_(P=>[ne(ye(ce(P.row.work_unit)),1)]),_:1}),p(j,{prop:"personnel_type",label:"人员身份"}),p(j,{prop:"employment_status",label:"人员状态"}),p(j,{prop:"desc",label:"备注"}),p(j,{label:"操作",width:"260",align:"center"},{default:_(({row:P})=>[p(ie,null,{default:_(()=>[p(U,{type:"warning",size:"mini",onClick:je=>N(P)},{default:_(()=>[p(H,null,{default:_(()=>[p(G(_n))]),_:1})]),_:2},1032,["onClick"]),p(U,{type:"success",size:"mini",onClick:je=>L(P)},{default:_(()=>[p(H,null,{default:_(()=>[p(G(zi))]),_:1})]),_:2},1032,["onClick"]),p(U,{type:"danger",size:"mini",onClick:je=>F(P)},{default:_(()=>[p(H,null,{default:_(()=>[p(G(gn))]),_:1})]),_:2},1032,["onClick"]),p(U,{size:"mini",type:"info",onClick:je=>me(P),disabled:P.sort_order<=1},{default:_(()=>[p(H,null,{default:_(()=>[p(G(Co))]),_:1})]),_:2},1032,["onClick","disabled"]),p(U,{size:"mini",type:"info",onClick:je=>re(P),disabled:P.sort_order>=P.sortMax},{default:_(()=>[p(H,null,{default:_(()=>[p(G(Ro))]),_:1})]),_:2},1032,["onClick","disabled"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),p(fe,{"current-page":o.value,"page-size":i.value,total:l.value,layout:"prev, pager, next, jumper, sizes",onCurrentChange:b[3]||(b[3]=P=>{o.value=P,I()}),onSizeChange:b[4]||(b[4]=P=>{i.value=P,o.value=1,I()})},null,8,["current-page","page-size","total"])]),p(Ai,{modelValue:v.value,"onUpdate:modelValue":b[30]||(b[30]=P=>v.value=P),title:"",width:"1000px"},{footer:_(()=>[Y("span",Ra,[p(U,{onClick:b[29]||(b[29]=P=>v.value=!1)},{default:_(()=>b[34]||(b[34]=[ne("取消")])),_:1,__:[34]}),p(U,{type:"primary",onClick:K},{default:_(()=>b[35]||(b[35]=[ne("确定")])),_:1,__:[35]})])]),default:_(()=>[p(Mi,{model:m,rules:R,ref_key:"newUserFormRef",ref:V,"label-width":"120px",inline:!1,class:"user-form"},{default:_(()=>[Y("div",ya,[p(q,{label:"姓名",prop:"name",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.name,"onUpdate:modelValue":b[5]||(b[5]=P=>m.name=P)},null,8,["modelValue"])]),_:1}),p(q,{label:"身份证号",prop:"id_number",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.id_number,"onUpdate:modelValue":b[6]||(b[6]=P=>m.id_number=P),maxlength:"18"},null,8,["modelValue"])]),_:1}),p(q,{label:"手机号码",prop:"phone",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.phone,"onUpdate:modelValue":b[7]||(b[7]=P=>m.phone=P),maxlength:"11"},null,8,["modelValue"])]),_:1})]),Y("div",wa,[p(q,{label:"档案出生日期",prop:"archive_birthdate",style:{flex:"1"}},{default:_(()=>[p(Q,{modelValue:m.archive_birthdate,"onUpdate:modelValue":b[8]||(b[8]=P=>m.archive_birthdate=P),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(q,{label:"性别",prop:"gender",style:{flex:"1"}},{default:_(()=>[p(be,{modelValue:m.gender,"onUpdate:modelValue":b[9]||(b[9]=P=>m.gender=P)},{default:_(()=>[p(W,{label:"未知",value:"0"}),p(W,{label:"男",value:"1"}),p(W,{label:"女",value:"2"})]),_:1},8,["modelValue"])]),_:1}),p(q,{label:"备注",prop:"desc",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.desc,"onUpdate:modelValue":b[10]||(b[10]=P=>m.desc=P)},null,8,["modelValue"])]),_:1})]),Y("div",xa,[p(q,{label:"短号",prop:"short_code",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.short_code,"onUpdate:modelValue":b[11]||(b[11]=P=>m.short_code=P)},null,8,["modelValue"]),b[33]||(b[33]=Y("div",{class:""},null,-1))]),_:1,__:[33]}),p(q,{label:"手机号码2",prop:"alt_phone_1",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.alt_phone_1,"onUpdate:modelValue":b[12]||(b[12]=P=>m.alt_phone_1=P)},null,8,["modelValue"])]),_:1}),p(q,{label:"手机号码3",prop:"alt_phone_2",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.alt_phone_2,"onUpdate:modelValue":b[13]||(b[13]=P=>m.alt_phone_2=P)},null,8,["modelValue"])]),_:1})]),Y("div",ba,[p(q,{label:"座机",prop:"landline",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.landline,"onUpdate:modelValue":b[14]||(b[14]=P=>m.landline=P)},null,8,["modelValue"])]),_:1}),p(q,{label:"编制单位",prop:"organization_unit",required:""},{default:_(()=>[p(ge,{modelValue:m.organization_unit,"onUpdate:modelValue":b[15]||(b[15]=P=>m.organization_unit=P),options:a.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择编制单位"},null,8,["modelValue","options"])]),_:1}),p(q,{label:"工作单位",prop:"work_unit"},{default:_(()=>[p(ge,{modelValue:m.work_unit,"onUpdate:modelValue":b[16]||(b[16]=P=>m.work_unit=P),options:u.value,"show-all-levels":!1,props:{expandTrigger:"hover",value:"id",label:"unit_name",children:"children",checkStrictly:!0},placeholder:"请选择工作单位"},null,8,["modelValue","options"])]),_:1})]),Y("div",Ea,[p(q,{label:"参工日期",prop:"employment_date",style:{flex:"1"}},{default:_(()=>[p(Q,{modelValue:m.employment_date,"onUpdate:modelValue":b[17]||(b[17]=P=>m.employment_date=P),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(q,{label:"政治面貌",prop:"political_status",style:{flex:"1"}},{default:_(()=>[p(be,{modelValue:m.political_status,"onUpdate:modelValue":b[18]||(b[18]=P=>m.political_status=P),placeholder:"请选择政治面貌"},{default:_(()=>[p(W,{label:"中共党员",value:"中共党员"}),p(W,{label:"中共预备党员",value:"中共预备党员"}),p(W,{label:"共青团员",value:"共青团员"}),p(W,{label:"民主党派",value:"民主党派"}),p(W,{label:"无党派人士",value:"无党派人士"}),p(W,{label:"群众",value:"群众"})]),_:1},8,["modelValue"])]),_:1}),p(q,{label:"加入组织日期",prop:"party_join_date",style:{flex:"1"}},{default:_(()=>[p(Q,{modelValue:m.party_join_date,"onUpdate:modelValue":b[19]||(b[19]=P=>m.party_join_date=P),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),Y("div",Sa,[p(q,{label:"人员身份",prop:"personnel_type",style:{flex:"1"}},{default:_(()=>[p(be,{modelValue:m.personnel_type,"onUpdate:modelValue":b[20]||(b[20]=P=>m.personnel_type=P)},{default:_(()=>[p(W,{label:"民警",value:"民警"}),p(W,{label:"职工",value:"职工"}),p(W,{label:"辅警",value:"辅警"}),p(W,{label:"机关工勤",value:"机关工勤"}),p(W,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),p(q,{label:"警号/辅警号",prop:"police_number",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.police_number,"onUpdate:modelValue":b[21]||(b[21]=P=>m.police_number=P)},null,8,["modelValue"])]),_:1}),p(q,{label:"带辅民警",prop:"is_assisting_officer",style:{flex:"1"}},{default:_(()=>[p(be,{modelValue:m.is_assisting_officer,"onUpdate:modelValue":b[22]||(b[22]=P=>m.is_assisting_officer=P)},{default:_(()=>[p(W,{label:"否",value:"0"}),p(W,{label:"是",value:"1"})]),_:1},8,["modelValue"])]),_:1})]),Y("div",ka,[p(q,{label:"人员状态",prop:"employment_status",style:{flex:"1"}},{default:_(()=>[p(be,{modelValue:m.employment_status,"onUpdate:modelValue":b[23]||(b[23]=P=>m.employment_status=P)},{default:_(()=>[p(W,{label:"在职",value:"在职"}),p(W,{label:"调离",value:"调离"}),p(W,{label:"退休",value:"退休"}),p(W,{label:"开除",value:"开除"}),p(W,{label:"借调出局",value:"借调出局"})]),_:1},8,["modelValue"])]),_:1}),p(q,{label:"职级",prop:"job_rank",style:{flex:"1"}},{default:_(()=>[p(be,{modelValue:m.job_rank,"onUpdate:modelValue":b[24]||(b[24]=P=>m.job_rank=P)},{default:_(()=>[p(W,{label:"初级",value:"初级"}),p(W,{label:"中级",value:"中级"}),p(W,{label:"高级",value:"高级"})]),_:1},8,["modelValue"])]),_:1}),p(q,{label:"任现职级日期",prop:"current_rank_date",style:{flex:"1"}},{default:_(()=>[p(Q,{modelValue:m.current_rank_date,"onUpdate:modelValue":b[25]||(b[25]=P=>m.current_rank_date=P),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1})]),Y("div",Ca,[p(q,{label:"职务",prop:"position",style:{flex:"1"}},{default:_(()=>[p(J,{modelValue:m.position,"onUpdate:modelValue":b[26]||(b[26]=P=>m.position=P)},null,8,["modelValue"])]),_:1}),p(q,{label:"任现职务日期",prop:"current_position_date",style:{flex:"1"}},{default:_(()=>[p(Q,{modelValue:m.current_position_date,"onUpdate:modelValue":b[27]||(b[27]=P=>m.current_position_date=P),type:"date","value-format":"YYYY-MM-DD",fomat:"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(q,{label:"人员排序",prop:"sorted_order",style:{flex:"1"}},{default:_(()=>[p(be,{modelValue:m.sorted_order,"onUpdate:modelValue":b[28]||(b[28]=P=>m.sorted_order=P),placeholder:"请选择人员排序"},{default:_(()=>[p(W,{label:"置于 最前",value:"0"}),(le(!0),_e(He,null,Ge(n.value,P=>(le(),Ye(W,{key:P.id,label:`置于 ${P.name} 之后`,value:P.sort_order},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},$a=Dt(Pa,[["__scopeId","data-v-50d1cf9c"]]);class We{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const n=this._partials;let r=0;for(let o=0;o<this._n&&o<32;o++){const i=n[o],l=t+i,s=Math.abs(t)<Math.abs(i)?t-(l-i):i-(l-t);s&&(n[r++]=s),t=l}return n[r]=t,this._n=r+1,this}valueOf(){const t=this._partials;let n=this._n,r,o,i,l=0;if(n>0){for(l=t[--n];n>0&&(r=l,o=t[--n],l=r+o,i=o-(l-r),!i););n>0&&(i<0&&t[n-1]<0||i>0&&t[n-1]>0)&&(o=i*2,r=l+o,o==r-l&&(l=r))}return l}}function*Va(e){for(const t of e)yield*t}function Fo(e){return Array.from(Va(e))}var Ma={value:()=>{}};function qo(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new Wt(n)}function Wt(e){this._=e}function Aa(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}Wt.prototype=qo.prototype={constructor:Wt,on:function(e,t){var n=this._,r=Aa(e+"",n),o,i=-1,l=r.length;if(arguments.length<2){for(;++i<l;)if((o=(e=r[i]).type)&&(o=Na(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++i<l;)if(o=(e=r[i]).type)n[o]=zr(n[o],e.name,t);else if(t==null)for(o in n)n[o]=zr(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Wt(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,i;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=this._[e],r=0,o=i.length;r<o;++r)i[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};function Na(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function zr(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=Ma,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var Un="http://www.w3.org/1999/xhtml";const Lr={svg:"http://www.w3.org/2000/svg",xhtml:Un,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function wn(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Lr.hasOwnProperty(t)?{space:Lr[t],local:e}:e}function Ia(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Un&&t.documentElement.namespaceURI===Un?t.createElement(e):t.createElementNS(n,e)}}function Da(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Yo(e){var t=wn(e);return(t.local?Da:Ia)(t)}function Ta(){}function ar(e){return e==null?Ta:function(){return this.querySelector(e)}}function Ua(e){typeof e!="function"&&(e=ar(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],l=i.length,s=r[o]=new Array(l),c,d,a=0;a<l;++a)(c=i[a])&&(d=e.call(c,c.__data__,a,i))&&("__data__"in c&&(d.__data__=c.__data__),s[a]=d);return new Se(r,this._parents)}function Oa(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function za(){return[]}function Bo(e){return e==null?za:function(){return this.querySelectorAll(e)}}function La(e){return function(){return Oa(e.apply(this,arguments))}}function Fa(e){typeof e=="function"?e=La(e):e=Bo(e);for(var t=this._groups,n=t.length,r=[],o=[],i=0;i<n;++i)for(var l=t[i],s=l.length,c,d=0;d<s;++d)(c=l[d])&&(r.push(e.call(c,c.__data__,d,l)),o.push(c));return new Se(r,o)}function Ho(e){return function(){return this.matches(e)}}function Xo(e){return function(t){return t.matches(e)}}var qa=Array.prototype.find;function Ya(e){return function(){return qa.call(this.children,e)}}function Ba(){return this.firstElementChild}function Ha(e){return this.select(e==null?Ba:Ya(typeof e=="function"?e:Xo(e)))}var Xa=Array.prototype.filter;function Ka(){return Array.from(this.children)}function Ga(e){return function(){return Xa.call(this.children,e)}}function Wa(e){return this.selectAll(e==null?Ka:Ga(typeof e=="function"?e:Xo(e)))}function Qa(e){typeof e!="function"&&(e=Ho(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],l=i.length,s=r[o]=[],c,d=0;d<l;++d)(c=i[d])&&e.call(c,c.__data__,d,i)&&s.push(c);return new Se(r,this._parents)}function Ko(e){return new Array(e.length)}function Za(){return new Se(this._enter||this._groups.map(Ko),this._parents)}function tn(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}tn.prototype={constructor:tn,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Ja(e){return function(){return e}}function ja(e,t,n,r,o,i){for(var l=0,s,c=t.length,d=i.length;l<d;++l)(s=t[l])?(s.__data__=i[l],r[l]=s):n[l]=new tn(e,i[l]);for(;l<c;++l)(s=t[l])&&(o[l]=s)}function es(e,t,n,r,o,i,l){var s,c,d=new Map,a=t.length,u=i.length,f=new Array(a),h;for(s=0;s<a;++s)(c=t[s])&&(f[s]=h=l.call(c,c.__data__,s,t)+"",d.has(h)?o[s]=c:d.set(h,c));for(s=0;s<u;++s)h=l.call(e,i[s],s,i)+"",(c=d.get(h))?(r[s]=c,c.__data__=i[s],d.delete(h)):n[s]=new tn(e,i[s]);for(s=0;s<a;++s)(c=t[s])&&d.get(f[s])===c&&(o[s]=c)}function ts(e){return e.__data__}function ns(e,t){if(!arguments.length)return Array.from(this,ts);var n=t?es:ja,r=this._parents,o=this._groups;typeof e!="function"&&(e=Ja(e));for(var i=o.length,l=new Array(i),s=new Array(i),c=new Array(i),d=0;d<i;++d){var a=r[d],u=o[d],f=u.length,h=rs(e.call(a,a&&a.__data__,d,r)),v=h.length,C=s[d]=new Array(v),x=l[d]=new Array(v),S=c[d]=new Array(f);n(a,u,C,x,S,h,t);for(var m=0,R=0,V,I;m<v;++m)if(V=C[m]){for(m>=R&&(R=m+1);!(I=x[R])&&++R<v;);V._next=I||null}}return l=new Se(l,r),l._enter=s,l._exit=c,l}function rs(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function os(){return new Se(this._exit||this._groups.map(Ko),this._parents)}function is(e,t,n){var r=this.enter(),o=this,i=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?i.remove():n(i),r&&o?r.merge(o).order():o}function ls(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,l=Math.min(o,i),s=new Array(o),c=0;c<l;++c)for(var d=n[c],a=r[c],u=d.length,f=s[c]=new Array(u),h,v=0;v<u;++v)(h=d[v]||a[v])&&(f[v]=h);for(;c<o;++c)s[c]=n[c];return new Se(s,this._parents)}function as(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,i=r[o],l;--o>=0;)(l=r[o])&&(i&&l.compareDocumentPosition(i)^4&&i.parentNode.insertBefore(l,i),i=l);return this}function ss(e){e||(e=us);function t(u,f){return u&&f?e(u.__data__,f.__data__):!u-!f}for(var n=this._groups,r=n.length,o=new Array(r),i=0;i<r;++i){for(var l=n[i],s=l.length,c=o[i]=new Array(s),d,a=0;a<s;++a)(d=l[a])&&(c[a]=d);c.sort(t)}return new Se(o,this._parents).order()}function us(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function cs(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function fs(){return Array.from(this)}function ds(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var l=r[o];if(l)return l}return null}function ps(){let e=0;for(const t of this)++e;return e}function hs(){return!this.node()}function ms(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],i=0,l=o.length,s;i<l;++i)(s=o[i])&&e.call(s,s.__data__,i,o);return this}function _s(e){return function(){this.removeAttribute(e)}}function gs(e){return function(){this.removeAttributeNS(e.space,e.local)}}function vs(e,t){return function(){this.setAttribute(e,t)}}function ys(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function ws(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function xs(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function bs(e,t){var n=wn(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?gs:_s:typeof t=="function"?n.local?xs:ws:n.local?ys:vs)(n,t))}function Go(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function Es(e){return function(){this.style.removeProperty(e)}}function Ss(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ks(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function Cs(e,t,n){return arguments.length>1?this.each((t==null?Es:typeof t=="function"?ks:Ss)(e,t,n??"")):st(this.node(),e)}function st(e,t){return e.style.getPropertyValue(t)||Go(e).getComputedStyle(e,null).getPropertyValue(t)}function Rs(e){return function(){delete this[e]}}function Ps(e,t){return function(){this[e]=t}}function $s(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function Vs(e,t){return arguments.length>1?this.each((t==null?Rs:typeof t=="function"?$s:Ps)(e,t)):this.node()[e]}function Wo(e){return e.trim().split(/^|\s+/)}function sr(e){return e.classList||new Qo(e)}function Qo(e){this._node=e,this._names=Wo(e.getAttribute("class")||"")}Qo.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Zo(e,t){for(var n=sr(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function Jo(e,t){for(var n=sr(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function Ms(e){return function(){Zo(this,e)}}function As(e){return function(){Jo(this,e)}}function Ns(e,t){return function(){(t.apply(this,arguments)?Zo:Jo)(this,e)}}function Is(e,t){var n=Wo(e+"");if(arguments.length<2){for(var r=sr(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?Ns:t?Ms:As)(n,t))}function Ds(){this.textContent=""}function Ts(e){return function(){this.textContent=e}}function Us(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Os(e){return arguments.length?this.each(e==null?Ds:(typeof e=="function"?Us:Ts)(e)):this.node().textContent}function zs(){this.innerHTML=""}function Ls(e){return function(){this.innerHTML=e}}function Fs(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function qs(e){return arguments.length?this.each(e==null?zs:(typeof e=="function"?Fs:Ls)(e)):this.node().innerHTML}function Ys(){this.nextSibling&&this.parentNode.appendChild(this)}function Bs(){return this.each(Ys)}function Hs(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Xs(){return this.each(Hs)}function Ks(e){var t=typeof e=="function"?e:Yo(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Gs(){return null}function Ws(e,t){var n=typeof e=="function"?e:Yo(e),r=t==null?Gs:typeof t=="function"?t:ar(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function Qs(){var e=this.parentNode;e&&e.removeChild(this)}function Zs(){return this.each(Qs)}function Js(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function js(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function eu(e){return this.select(e?js:Js)}function tu(e){return arguments.length?this.property("__data__",e):this.node().__data__}function nu(e){return function(t){e.call(this,t,this.__data__)}}function ru(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function ou(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,i;n<o;++n)i=t[n],(!e.type||i.type===e.type)&&i.name===e.name?this.removeEventListener(i.type,i.listener,i.options):t[++r]=i;++r?t.length=r:delete this.__on}}}function iu(e,t,n){return function(){var r=this.__on,o,i=nu(t);if(r){for(var l=0,s=r.length;l<s;++l)if((o=r[l]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),o.value=t;return}}this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function lu(e,t,n){var r=ru(e+""),o,i=r.length,l;if(arguments.length<2){var s=this.node().__on;if(s){for(var c=0,d=s.length,a;c<d;++c)for(o=0,a=s[c];o<i;++o)if((l=r[o]).type===a.type&&l.name===a.name)return a.value}return}for(s=t?iu:ou,o=0;o<i;++o)this.each(s(r[o],t,n));return this}function jo(e,t,n){var r=Go(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function au(e,t){return function(){return jo(this,e,t)}}function su(e,t){return function(){return jo(this,e,t.apply(this,arguments))}}function uu(e,t){return this.each((typeof t=="function"?su:au)(e,t))}function*cu(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length,l;o<i;++o)(l=r[o])&&(yield l)}var ei=[null];function Se(e,t){this._groups=e,this._parents=t}function Tt(){return new Se([[document.documentElement]],ei)}function fu(){return this}Se.prototype=Tt.prototype={constructor:Se,select:Ua,selectAll:Fa,selectChild:Ha,selectChildren:Wa,filter:Qa,data:ns,enter:Za,exit:os,join:is,merge:ls,selection:fu,order:as,sort:ss,call:cs,nodes:fs,node:ds,size:ps,empty:hs,each:ms,attr:bs,style:Cs,property:Vs,classed:Is,text:Os,html:qs,raise:Bs,lower:Xs,append:Ks,insert:Ws,remove:Zs,clone:eu,datum:tu,on:lu,dispatch:uu,[Symbol.iterator]:cu};function Lt(e){return typeof e=="string"?new Se([[document.querySelector(e)]],[document.documentElement]):new Se([[e]],ei)}function ur(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function ti(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Ut(){}var Rt=.7,nn=1/Rt,ot="\\s*([+-]?\\d+)\\s*",Pt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Ie="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",du=/^#([0-9a-f]{3,8})$/,pu=new RegExp(`^rgb\\(${ot},${ot},${ot}\\)$`),hu=new RegExp(`^rgb\\(${Ie},${Ie},${Ie}\\)$`),mu=new RegExp(`^rgba\\(${ot},${ot},${ot},${Pt}\\)$`),_u=new RegExp(`^rgba\\(${Ie},${Ie},${Ie},${Pt}\\)$`),gu=new RegExp(`^hsl\\(${Pt},${Ie},${Ie}\\)$`),vu=new RegExp(`^hsla\\(${Pt},${Ie},${Ie},${Pt}\\)$`),Fr={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};ur(Ut,$t,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:qr,formatHex:qr,formatHex8:yu,formatHsl:wu,formatRgb:Yr,toString:Yr});function qr(){return this.rgb().formatHex()}function yu(){return this.rgb().formatHex8()}function wu(){return ni(this).formatHsl()}function Yr(){return this.rgb().formatRgb()}function $t(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=du.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?Br(t):n===3?new we(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Ft(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Ft(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=pu.exec(e))?new we(t[1],t[2],t[3],1):(t=hu.exec(e))?new we(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=mu.exec(e))?Ft(t[1],t[2],t[3],t[4]):(t=_u.exec(e))?Ft(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=gu.exec(e))?Kr(t[1],t[2]/100,t[3]/100,1):(t=vu.exec(e))?Kr(t[1],t[2]/100,t[3]/100,t[4]):Fr.hasOwnProperty(e)?Br(Fr[e]):e==="transparent"?new we(NaN,NaN,NaN,0):null}function Br(e){return new we(e>>16&255,e>>8&255,e&255,1)}function Ft(e,t,n,r){return r<=0&&(e=t=n=NaN),new we(e,t,n,r)}function xu(e){return e instanceof Ut||(e=$t(e)),e?(e=e.rgb(),new we(e.r,e.g,e.b,e.opacity)):new we}function On(e,t,n,r){return arguments.length===1?xu(e):new we(e,t,n,r??1)}function we(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}ur(we,On,ti(Ut,{brighter(e){return e=e==null?nn:Math.pow(nn,e),new we(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Rt:Math.pow(Rt,e),new we(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new we(Ke(this.r),Ke(this.g),Ke(this.b),rn(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Hr,formatHex:Hr,formatHex8:bu,formatRgb:Xr,toString:Xr}));function Hr(){return`#${Xe(this.r)}${Xe(this.g)}${Xe(this.b)}`}function bu(){return`#${Xe(this.r)}${Xe(this.g)}${Xe(this.b)}${Xe((isNaN(this.opacity)?1:this.opacity)*255)}`}function Xr(){const e=rn(this.opacity);return`${e===1?"rgb(":"rgba("}${Ke(this.r)}, ${Ke(this.g)}, ${Ke(this.b)}${e===1?")":`, ${e})`}`}function rn(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Ke(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Xe(e){return e=Ke(e),(e<16?"0":"")+e.toString(16)}function Kr(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Pe(e,t,n,r)}function ni(e){if(e instanceof Pe)return new Pe(e.h,e.s,e.l,e.opacity);if(e instanceof Ut||(e=$t(e)),!e)return new Pe;if(e instanceof Pe)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),l=NaN,s=i-o,c=(i+o)/2;return s?(t===i?l=(n-r)/s+(n<r)*6:n===i?l=(r-t)/s+2:l=(t-n)/s+4,s/=c<.5?i+o:2-i-o,l*=60):s=c>0&&c<1?0:l,new Pe(l,s,c,e.opacity)}function Eu(e,t,n,r){return arguments.length===1?ni(e):new Pe(e,t,n,r??1)}function Pe(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}ur(Pe,Eu,ti(Ut,{brighter(e){return e=e==null?nn:Math.pow(nn,e),new Pe(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Rt:Math.pow(Rt,e),new Pe(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new we(Pn(e>=240?e-240:e+120,o,r),Pn(e,o,r),Pn(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new Pe(Gr(this.h),qt(this.s),qt(this.l),rn(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=rn(this.opacity);return`${e===1?"hsl(":"hsla("}${Gr(this.h)}, ${qt(this.s)*100}%, ${qt(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Gr(e){return e=(e||0)%360,e<0?e+360:e}function qt(e){return Math.max(0,Math.min(1,e||0))}function Pn(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const ri=e=>()=>e;function Su(e,t){return function(n){return e+n*t}}function ku(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function Cu(e){return(e=+e)==1?oi:function(t,n){return n-t?ku(t,n,e):ri(isNaN(t)?n:t)}}function oi(e,t){var n=t-e;return n?Su(e,n):ri(isNaN(e)?t:e)}const Wr=function e(t){var n=Cu(t);function r(o,i){var l=n((o=On(o)).r,(i=On(i)).r),s=n(o.g,i.g),c=n(o.b,i.b),d=oi(o.opacity,i.opacity);return function(a){return o.r=l(a),o.g=s(a),o.b=c(a),o.opacity=d(a),o+""}}return r.gamma=e,r}(1);function qe(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var zn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,$n=new RegExp(zn.source,"g");function Ru(e){return function(){return e}}function Pu(e){return function(t){return e(t)+""}}function $u(e,t){var n=zn.lastIndex=$n.lastIndex=0,r,o,i,l=-1,s=[],c=[];for(e=e+"",t=t+"";(r=zn.exec(e))&&(o=$n.exec(t));)(i=o.index)>n&&(i=t.slice(n,i),s[l]?s[l]+=i:s[++l]=i),(r=r[0])===(o=o[0])?s[l]?s[l]+=o:s[++l]=o:(s[++l]=null,c.push({i:l,x:qe(r,o)})),n=$n.lastIndex;return n<t.length&&(i=t.slice(n),s[l]?s[l]+=i:s[++l]=i),s.length<2?c[0]?Pu(c[0].x):Ru(t):(t=c.length,function(d){for(var a=0,u;a<t;++a)s[(u=c[a]).i]=u.x(d);return s.join("")})}var Qr=180/Math.PI,Ln={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function ii(e,t,n,r,o,i){var l,s,c;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,c/=s),e*r<t*n&&(e=-e,t=-t,c=-c,l=-l),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*Qr,skewX:Math.atan(c)*Qr,scaleX:l,scaleY:s}}var Yt;function Vu(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Ln:ii(t.a,t.b,t.c,t.d,t.e,t.f)}function Mu(e){return e==null||(Yt||(Yt=document.createElementNS("http://www.w3.org/2000/svg","g")),Yt.setAttribute("transform",e),!(e=Yt.transform.baseVal.consolidate()))?Ln:(e=e.matrix,ii(e.a,e.b,e.c,e.d,e.e,e.f))}function li(e,t,n,r){function o(d){return d.length?d.pop()+" ":""}function i(d,a,u,f,h,v){if(d!==u||a!==f){var C=h.push("translate(",null,t,null,n);v.push({i:C-4,x:qe(d,u)},{i:C-2,x:qe(a,f)})}else(u||f)&&h.push("translate("+u+t+f+n)}function l(d,a,u,f){d!==a?(d-a>180?a+=360:a-d>180&&(d+=360),f.push({i:u.push(o(u)+"rotate(",null,r)-2,x:qe(d,a)})):a&&u.push(o(u)+"rotate("+a+r)}function s(d,a,u,f){d!==a?f.push({i:u.push(o(u)+"skewX(",null,r)-2,x:qe(d,a)}):a&&u.push(o(u)+"skewX("+a+r)}function c(d,a,u,f,h,v){if(d!==u||a!==f){var C=h.push(o(h)+"scale(",null,",",null,")");v.push({i:C-4,x:qe(d,u)},{i:C-2,x:qe(a,f)})}else(u!==1||f!==1)&&h.push(o(h)+"scale("+u+","+f+")")}return function(d,a){var u=[],f=[];return d=e(d),a=e(a),i(d.translateX,d.translateY,a.translateX,a.translateY,u,f),l(d.rotate,a.rotate,u,f),s(d.skewX,a.skewX,u,f),c(d.scaleX,d.scaleY,a.scaleX,a.scaleY,u,f),d=a=null,function(h){for(var v=-1,C=f.length,x;++v<C;)u[(x=f[v]).i]=x.x(h);return u.join("")}}}var Au=li(Vu,"px, ","px)","deg)"),Nu=li(Mu,", ",")",")"),ut=0,mt=0,ht=0,ai=1e3,on,_t,ln=0,Qe=0,xn=0,Vt=typeof performance=="object"&&performance.now?performance:Date,si=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function cr(){return Qe||(si(Iu),Qe=Vt.now()+xn)}function Iu(){Qe=0}function an(){this._call=this._time=this._next=null}an.prototype=ui.prototype={constructor:an,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?cr():+n)+(t==null?0:+t),!this._next&&_t!==this&&(_t?_t._next=this:on=this,_t=this),this._call=e,this._time=n,Fn()},stop:function(){this._call&&(this._call=null,this._time=1/0,Fn())}};function ui(e,t,n){var r=new an;return r.restart(e,t,n),r}function Du(){cr(),++ut;for(var e=on,t;e;)(t=Qe-e._time)>=0&&e._call.call(void 0,t),e=e._next;--ut}function Zr(){Qe=(ln=Vt.now())+xn,ut=mt=0;try{Du()}finally{ut=0,Uu(),Qe=0}}function Tu(){var e=Vt.now(),t=e-ln;t>ai&&(xn-=t,ln=e)}function Uu(){for(var e,t=on,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:on=n);_t=e,Fn(r)}function Fn(e){if(!ut){mt&&(mt=clearTimeout(mt));var t=e-Qe;t>24?(e<1/0&&(mt=setTimeout(Zr,e-Vt.now()-xn)),ht&&(ht=clearInterval(ht))):(ht||(ln=Vt.now(),ht=setInterval(Tu,ai)),ut=1,si(Zr))}}function Jr(e,t,n){var r=new an;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var Ou=qo("start","end","cancel","interrupt"),zu=[],ci=0,jr=1,qn=2,Qt=3,eo=4,Yn=5,Zt=6;function bn(e,t,n,r,o,i){var l=e.__transition;if(!l)e.__transition={};else if(n in l)return;Lu(e,n,{name:t,index:r,group:o,on:Ou,tween:zu,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:ci})}function fr(e,t){var n=Ve(e,t);if(n.state>ci)throw new Error("too late; already scheduled");return n}function De(e,t){var n=Ve(e,t);if(n.state>Qt)throw new Error("too late; already running");return n}function Ve(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Lu(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=ui(i,0,n.time);function i(d){n.state=jr,n.timer.restart(l,n.delay,n.time),n.delay<=d&&l(d-n.delay)}function l(d){var a,u,f,h;if(n.state!==jr)return c();for(a in r)if(h=r[a],h.name===n.name){if(h.state===Qt)return Jr(l);h.state===eo?(h.state=Zt,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[a]):+a<t&&(h.state=Zt,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[a])}if(Jr(function(){n.state===Qt&&(n.state=eo,n.timer.restart(s,n.delay,n.time),s(d))}),n.state=qn,n.on.call("start",e,e.__data__,n.index,n.group),n.state===qn){for(n.state=Qt,o=new Array(f=n.tween.length),a=0,u=-1;a<f;++a)(h=n.tween[a].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(d){for(var a=d<n.duration?n.ease.call(null,d/n.duration):(n.timer.restart(c),n.state=Yn,1),u=-1,f=o.length;++u<f;)o[u].call(e,a);n.state===Yn&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){n.state=Zt,n.timer.stop(),delete r[t];for(var d in r)return;delete e.__transition}}function Fu(e,t){var n=e.__transition,r,o,i=!0,l;if(n){t=t==null?null:t+"";for(l in n){if((r=n[l]).name!==t){i=!1;continue}o=r.state>qn&&r.state<Yn,r.state=Zt,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[l]}i&&delete e.__transition}}function qu(e){return this.each(function(){Fu(this,e)})}function Yu(e,t){var n,r;return function(){var o=De(this,e),i=o.tween;if(i!==n){r=n=i;for(var l=0,s=r.length;l<s;++l)if(r[l].name===t){r=r.slice(),r.splice(l,1);break}}o.tween=r}}function Bu(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var i=De(this,e),l=i.tween;if(l!==r){o=(r=l).slice();for(var s={name:t,value:n},c=0,d=o.length;c<d;++c)if(o[c].name===t){o[c]=s;break}c===d&&o.push(s)}i.tween=o}}function Hu(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Ve(this.node(),n).tween,o=0,i=r.length,l;o<i;++o)if((l=r[o]).name===e)return l.value;return null}return this.each((t==null?Yu:Bu)(n,e,t))}function dr(e,t,n){var r=e._id;return e.each(function(){var o=De(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return Ve(o,r).value[t]}}function fi(e,t){var n;return(typeof t=="number"?qe:t instanceof $t?Wr:(n=$t(t))?(t=n,Wr):$u)(e,t)}function Xu(e){return function(){this.removeAttribute(e)}}function Ku(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Gu(e,t,n){var r,o=n+"",i;return function(){var l=this.getAttribute(e);return l===o?null:l===r?i:i=t(r=l,n)}}function Wu(e,t,n){var r,o=n+"",i;return function(){var l=this.getAttributeNS(e.space,e.local);return l===o?null:l===r?i:i=t(r=l,n)}}function Qu(e,t,n){var r,o,i;return function(){var l,s=n(this),c;return s==null?void this.removeAttribute(e):(l=this.getAttribute(e),c=s+"",l===c?null:l===r&&c===o?i:(o=c,i=t(r=l,s)))}}function Zu(e,t,n){var r,o,i;return function(){var l,s=n(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(l=this.getAttributeNS(e.space,e.local),c=s+"",l===c?null:l===r&&c===o?i:(o=c,i=t(r=l,s)))}}function Ju(e,t){var n=wn(e),r=n==="transform"?Nu:fi;return this.attrTween(e,typeof t=="function"?(n.local?Zu:Qu)(n,r,dr(this,"attr."+e,t)):t==null?(n.local?Ku:Xu)(n):(n.local?Wu:Gu)(n,r,t))}function ju(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function ec(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function tc(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&ec(e,i)),n}return o._value=t,o}function nc(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&ju(e,i)),n}return o._value=t,o}function rc(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=wn(e);return this.tween(n,(r.local?tc:nc)(r,t))}function oc(e,t){return function(){fr(this,e).delay=+t.apply(this,arguments)}}function ic(e,t){return t=+t,function(){fr(this,e).delay=t}}function lc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?oc:ic)(t,e)):Ve(this.node(),t).delay}function ac(e,t){return function(){De(this,e).duration=+t.apply(this,arguments)}}function sc(e,t){return t=+t,function(){De(this,e).duration=t}}function uc(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?ac:sc)(t,e)):Ve(this.node(),t).duration}function cc(e,t){if(typeof t!="function")throw new Error;return function(){De(this,e).ease=t}}function fc(e){var t=this._id;return arguments.length?this.each(cc(t,e)):Ve(this.node(),t).ease}function dc(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;De(this,e).ease=n}}function pc(e){if(typeof e!="function")throw new Error;return this.each(dc(this._id,e))}function hc(e){typeof e!="function"&&(e=Ho(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],l=i.length,s=r[o]=[],c,d=0;d<l;++d)(c=i[d])&&e.call(c,c.__data__,d,i)&&s.push(c);return new ze(r,this._parents,this._name,this._id)}function mc(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),l=new Array(r),s=0;s<i;++s)for(var c=t[s],d=n[s],a=c.length,u=l[s]=new Array(a),f,h=0;h<a;++h)(f=c[h]||d[h])&&(u[h]=f);for(;s<r;++s)l[s]=t[s];return new ze(l,this._parents,this._name,this._id)}function _c(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function gc(e,t,n){var r,o,i=_c(t)?fr:De;return function(){var l=i(this,e),s=l.on;s!==r&&(o=(r=s).copy()).on(t,n),l.on=o}}function vc(e,t){var n=this._id;return arguments.length<2?Ve(this.node(),n).on.on(e):this.each(gc(n,e,t))}function yc(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function wc(){return this.on("end.remove",yc(this._id))}function xc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=ar(e));for(var r=this._groups,o=r.length,i=new Array(o),l=0;l<o;++l)for(var s=r[l],c=s.length,d=i[l]=new Array(c),a,u,f=0;f<c;++f)(a=s[f])&&(u=e.call(a,a.__data__,f,s))&&("__data__"in a&&(u.__data__=a.__data__),d[f]=u,bn(d[f],t,n,f,d,Ve(a,n)));return new ze(i,this._parents,t,n)}function bc(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Bo(e));for(var r=this._groups,o=r.length,i=[],l=[],s=0;s<o;++s)for(var c=r[s],d=c.length,a,u=0;u<d;++u)if(a=c[u]){for(var f=e.call(a,a.__data__,u,c),h,v=Ve(a,n),C=0,x=f.length;C<x;++C)(h=f[C])&&bn(h,t,n,C,f,v);i.push(f),l.push(a)}return new ze(i,l,t,n)}var Ec=Tt.prototype.constructor;function Sc(){return new Ec(this._groups,this._parents)}function kc(e,t){var n,r,o;return function(){var i=st(this,e),l=(this.style.removeProperty(e),st(this,e));return i===l?null:i===n&&l===r?o:o=t(n=i,r=l)}}function di(e){return function(){this.style.removeProperty(e)}}function Cc(e,t,n){var r,o=n+"",i;return function(){var l=st(this,e);return l===o?null:l===r?i:i=t(r=l,n)}}function Rc(e,t,n){var r,o,i;return function(){var l=st(this,e),s=n(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),st(this,e))),l===c?null:l===r&&c===o?i:(o=c,i=t(r=l,s))}}function Pc(e,t){var n,r,o,i="style."+t,l="end."+i,s;return function(){var c=De(this,e),d=c.on,a=c.value[i]==null?s||(s=di(t)):void 0;(d!==n||o!==a)&&(r=(n=d).copy()).on(l,o=a),c.on=r}}function $c(e,t,n){var r=(e+="")=="transform"?Au:fi;return t==null?this.styleTween(e,kc(e,r)).on("end.style."+e,di(e)):typeof t=="function"?this.styleTween(e,Rc(e,r,dr(this,"style."+e,t))).each(Pc(this._id,e)):this.styleTween(e,Cc(e,r,t),n).on("end.style."+e,null)}function Vc(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function Mc(e,t,n){var r,o;function i(){var l=t.apply(this,arguments);return l!==o&&(r=(o=l)&&Vc(e,l,n)),r}return i._value=t,i}function Ac(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,Mc(e,t,n??""))}function Nc(e){return function(){this.textContent=e}}function Ic(e){return function(){var t=e(this);this.textContent=t??""}}function Dc(e){return this.tween("text",typeof e=="function"?Ic(dr(this,"text",e)):Nc(e==null?"":e+""))}function Tc(e){return function(t){this.textContent=e.call(this,t)}}function Uc(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&Tc(o)),t}return r._value=e,r}function Oc(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,Uc(e))}function zc(){for(var e=this._name,t=this._id,n=pi(),r=this._groups,o=r.length,i=0;i<o;++i)for(var l=r[i],s=l.length,c,d=0;d<s;++d)if(c=l[d]){var a=Ve(c,t);bn(c,e,n,d,l,{time:a.time+a.delay+a.duration,delay:0,duration:a.duration,ease:a.ease})}return new ze(r,this._parents,e,n)}function Lc(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,l){var s={value:l},c={value:function(){--o===0&&i()}};n.each(function(){var d=De(this,r),a=d.on;a!==e&&(t=(e=a).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),d.on=t}),o===0&&i()})}var Fc=0;function ze(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function pi(){return++Fc}var Ue=Tt.prototype;ze.prototype={constructor:ze,select:xc,selectAll:bc,selectChild:Ue.selectChild,selectChildren:Ue.selectChildren,filter:hc,merge:mc,selection:Sc,transition:zc,call:Ue.call,nodes:Ue.nodes,node:Ue.node,size:Ue.size,empty:Ue.empty,each:Ue.each,on:vc,attr:Ju,attrTween:rc,style:$c,styleTween:Ac,text:Dc,textTween:Oc,remove:wc,tween:Hu,delay:lc,duration:uc,ease:fc,easeVarying:pc,end:Lc,[Symbol.iterator]:Ue[Symbol.iterator]};function qc(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var Yc={time:null,delay:0,duration:250,ease:qc};function Bc(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Hc(e){var t,n;e instanceof ze?(t=e._id,e=e._name):(t=pi(),(n=Yc).time=cr(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var l=r[i],s=l.length,c,d=0;d<s;++d)(c=l[d])&&bn(c,e,t,d,l,n||Bc(c,t));return new ze(r,this._parents,e,t)}Tt.prototype.interrupt=qu;Tt.prototype.transition=Hc;function Xc(e){if(!e.ok)throw new Error(e.status+" "+e.statusText);if(!(e.status===204||e.status===205))return e.json()}function Kc(e,t){return fetch(e,t).then(Xc)}var ae=1e-6,te=Math.PI,xe=te/2,to=te/4,ke=te*2,Ee=180/te,he=te/180,ue=Math.abs,hi=Math.atan,Mt=Math.atan2,de=Math.cos,Gc=Math.exp,Wc=Math.log,pe=Math.sin,Qc=Math.sign||function(e){return e>0?1:e<0?-1:0},Je=Math.sqrt,Zc=Math.tan;function Jc(e){return e>1?0:e<-1?te:Math.acos(e)}function At(e){return e>1?xe:e<-1?-xe:Math.asin(e)}function Re(){}function sn(e,t){e&&ro.hasOwnProperty(e.type)&&ro[e.type](e,t)}var no={Feature:function(e,t){sn(e.geometry,t)},FeatureCollection:function(e,t){for(var n=e.features,r=-1,o=n.length;++r<o;)sn(n[r].geometry,t)}},ro={Sphere:function(e,t){t.sphere()},Point:function(e,t){e=e.coordinates,t.point(e[0],e[1],e[2])},MultiPoint:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)e=n[r],t.point(e[0],e[1],e[2])},LineString:function(e,t){Bn(e.coordinates,t,0)},MultiLineString:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)Bn(n[r],t,0)},Polygon:function(e,t){oo(e.coordinates,t)},MultiPolygon:function(e,t){for(var n=e.coordinates,r=-1,o=n.length;++r<o;)oo(n[r],t)},GeometryCollection:function(e,t){for(var n=e.geometries,r=-1,o=n.length;++r<o;)sn(n[r],t)}};function Bn(e,t,n){var r=-1,o=e.length-n,i;for(t.lineStart();++r<o;)i=e[r],t.point(i[0],i[1],i[2]);t.lineEnd()}function oo(e,t){var n=-1,r=e.length;for(t.polygonStart();++n<r;)Bn(e[n],t,1);t.polygonEnd()}function tt(e,t){e&&no.hasOwnProperty(e.type)?no[e.type](e,t):sn(e,t)}function Hn(e){return[Mt(e[1],e[0]),At(e[2])]}function ct(e){var t=e[0],n=e[1],r=de(n);return[r*de(t),r*pe(t),pe(n)]}function Bt(e,t){return e[0]*t[0]+e[1]*t[1]+e[2]*t[2]}function un(e,t){return[e[1]*t[2]-e[2]*t[1],e[2]*t[0]-e[0]*t[2],e[0]*t[1]-e[1]*t[0]]}function Vn(e,t){e[0]+=t[0],e[1]+=t[1],e[2]+=t[2]}function Ht(e,t){return[e[0]*t,e[1]*t,e[2]*t]}function Xn(e){var t=Je(e[0]*e[0]+e[1]*e[1]+e[2]*e[2]);e[0]/=t,e[1]/=t,e[2]/=t}function Kn(e,t){function n(r,o){return r=e(r,o),t(r[0],r[1])}return e.invert&&t.invert&&(n.invert=function(r,o){return r=t.invert(r,o),r&&e.invert(r[0],r[1])}),n}function Gn(e,t){return ue(e)>te&&(e-=Math.round(e/ke)*ke),[e,t]}Gn.invert=Gn;function mi(e,t,n){return(e%=ke)?t||n?Kn(lo(e),ao(t,n)):lo(e):t||n?ao(t,n):Gn}function io(e){return function(t,n){return t+=e,ue(t)>te&&(t-=Math.round(t/ke)*ke),[t,n]}}function lo(e){var t=io(e);return t.invert=io(-e),t}function ao(e,t){var n=de(e),r=pe(e),o=de(t),i=pe(t);function l(s,c){var d=de(c),a=de(s)*d,u=pe(s)*d,f=pe(c),h=f*n+a*r;return[Mt(u*o-h*i,a*n-f*r),At(h*o+u*i)]}return l.invert=function(s,c){var d=de(c),a=de(s)*d,u=pe(s)*d,f=pe(c),h=f*o-u*i;return[Mt(u*o+f*i,a*n+h*r),At(h*n-a*r)]},l}function jc(e){e=mi(e[0]*he,e[1]*he,e.length>2?e[2]*he:0);function t(n){return n=e(n[0]*he,n[1]*he),n[0]*=Ee,n[1]*=Ee,n}return t.invert=function(n){return n=e.invert(n[0]*he,n[1]*he),n[0]*=Ee,n[1]*=Ee,n},t}function ef(e,t,n,r,o,i){if(n){var l=de(t),s=pe(t),c=r*n;o==null?(o=t+r*ke,i=t-c/2):(o=so(l,o),i=so(l,i),(r>0?o<i:o>i)&&(o+=r*ke));for(var d,a=o;r>0?a>i:a<i;a-=c)d=Hn([l,-s*de(a),-s*pe(a)]),e.point(d[0],d[1])}}function so(e,t){t=ct(t),t[0]-=e,Xn(t);var n=Jc(-t[1]);return((-t[2]<0?-n:n)+ke-ae)%ke}function _i(){var e=[],t;return{point:function(n,r,o){t.push([n,r,o])},lineStart:function(){e.push(t=[])},lineEnd:Re,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function Jt(e,t){return ue(e[0]-t[0])<ae&&ue(e[1]-t[1])<ae}function Xt(e,t,n,r){this.x=e,this.z=t,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function gi(e,t,n,r,o){var i=[],l=[],s,c;if(e.forEach(function(v){if(!((C=v.length-1)<=0)){var C,x=v[0],S=v[C],m;if(Jt(x,S)){if(!x[2]&&!S[2]){for(o.lineStart(),s=0;s<C;++s)o.point((x=v[s])[0],x[1]);o.lineEnd();return}S[0]+=2*ae}i.push(m=new Xt(x,v,null,!0)),l.push(m.o=new Xt(x,null,m,!1)),i.push(m=new Xt(S,v,null,!1)),l.push(m.o=new Xt(S,null,m,!0))}}),!!i.length){for(l.sort(t),uo(i),uo(l),s=0,c=l.length;s<c;++s)l[s].e=n=!n;for(var d=i[0],a,u;;){for(var f=d,h=!0;f.v;)if((f=f.n)===d)return;a=f.z,o.lineStart();do{if(f.v=f.o.v=!0,f.e){if(h)for(s=0,c=a.length;s<c;++s)o.point((u=a[s])[0],u[1]);else r(f.x,f.n.x,1,o);f=f.n}else{if(h)for(a=f.p.z,s=a.length-1;s>=0;--s)o.point((u=a[s])[0],u[1]);else r(f.x,f.p.x,-1,o);f=f.p}f=f.o,a=f.z,h=!h}while(!f.v);o.lineEnd()}}}function uo(e){if(t=e.length){for(var t,n=0,r=e[0],o;++n<t;)r.n=o=e[n],o.p=r,r=o;r.n=o=e[0],o.p=r}}function Mn(e){return ue(e[0])<=te?e[0]:Qc(e[0])*((ue(e[0])+te)%ke-te)}function tf(e,t){var n=Mn(t),r=t[1],o=pe(r),i=[pe(n),-de(n),0],l=0,s=0,c=new We;o===1?r=xe+ae:o===-1&&(r=-xe-ae);for(var d=0,a=e.length;d<a;++d)if(f=(u=e[d]).length)for(var u,f,h=u[f-1],v=Mn(h),C=h[1]/2+to,x=pe(C),S=de(C),m=0;m<f;++m,v=V,x=D,S=M,h=R){var R=u[m],V=Mn(R),I=R[1]/2+to,D=pe(I),M=de(I),A=V-v,$=A>=0?1:-1,w=$*A,g=w>te,N=x*D;if(c.add(Mt(N*$*pe(w),S*M+N*de(w))),l+=g?A+$*ke:A,g^v>=n^V>=n){var L=un(ct(h),ct(R));Xn(L);var F=un(i,L);Xn(F);var E=(g^A>=0?-1:1)*At(F[2]);(r>E||r===E&&(L[0]||L[1]))&&(s+=g^A>=0?1:-1)}}return(l<-1e-6||l<ae&&c<-1e-12)^s&1}function vi(e,t,n,r){return function(o){var i=t(o),l=_i(),s=t(l),c=!1,d,a,u,f={point:h,lineStart:C,lineEnd:x,polygonStart:function(){f.point=S,f.lineStart=m,f.lineEnd=R,a=[],d=[]},polygonEnd:function(){f.point=h,f.lineStart=C,f.lineEnd=x,a=Fo(a);var V=tf(d,r);a.length?(c||(o.polygonStart(),c=!0),gi(a,rf,V,n,o)):V&&(c||(o.polygonStart(),c=!0),o.lineStart(),n(null,null,1,o),o.lineEnd()),c&&(o.polygonEnd(),c=!1),a=d=null},sphere:function(){o.polygonStart(),o.lineStart(),n(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function h(V,I){e(V,I)&&o.point(V,I)}function v(V,I){i.point(V,I)}function C(){f.point=v,i.lineStart()}function x(){f.point=h,i.lineEnd()}function S(V,I){u.push([V,I]),s.point(V,I)}function m(){s.lineStart(),u=[]}function R(){S(u[0][0],u[0][1]),s.lineEnd();var V=s.clean(),I=l.result(),D,M=I.length,A,$,w;if(u.pop(),d.push(u),u=null,!!M){if(V&1){if($=I[0],(A=$.length-1)>0){for(c||(o.polygonStart(),c=!0),o.lineStart(),D=0;D<A;++D)o.point((w=$[D])[0],w[1]);o.lineEnd()}return}M>1&&V&2&&I.push(I.pop().concat(I.shift())),a.push(I.filter(nf))}}return f}}function nf(e){return e.length>1}function rf(e,t){return((e=e.x)[0]<0?e[1]-xe-ae:xe-e[1])-((t=t.x)[0]<0?t[1]-xe-ae:xe-t[1])}const co=vi(function(){return!0},of,af,[-te,-xe]);function of(e){var t=NaN,n=NaN,r=NaN,o;return{lineStart:function(){e.lineStart(),o=1},point:function(i,l){var s=i>0?te:-te,c=ue(i-t);ue(c-te)<ae?(e.point(t,n=(n+l)/2>0?xe:-xe),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),e.point(i,n),o=0):r!==s&&c>=te&&(ue(t-r)<ae&&(t-=r*ae),ue(i-s)<ae&&(i-=s*ae),n=lf(t,n,i,l),e.point(r,n),e.lineEnd(),e.lineStart(),e.point(s,n),o=0),e.point(t=i,n=l),r=s},lineEnd:function(){e.lineEnd(),t=n=NaN},clean:function(){return 2-o}}}function lf(e,t,n,r){var o,i,l=pe(e-n);return ue(l)>ae?hi((pe(t)*(i=de(r))*pe(n)-pe(r)*(o=de(t))*pe(e))/(o*i*l)):(t+r)/2}function af(e,t,n,r){var o;if(e==null)o=n*xe,r.point(-te,o),r.point(0,o),r.point(te,o),r.point(te,0),r.point(te,-o),r.point(0,-o),r.point(-te,-o),r.point(-te,0),r.point(-te,o);else if(ue(e[0]-t[0])>ae){var i=e[0]<t[0]?te:-te;o=n*i/2,r.point(-i,o),r.point(0,o),r.point(i,o)}else r.point(t[0],t[1])}function sf(e){var t=de(e),n=2*he,r=t>0,o=ue(t)>ae;function i(a,u,f,h){ef(h,e,n,f,a,u)}function l(a,u){return de(a)*de(u)>t}function s(a){var u,f,h,v,C;return{lineStart:function(){v=h=!1,C=1},point:function(x,S){var m=[x,S],R,V=l(x,S),I=r?V?0:d(x,S):V?d(x+(x<0?te:-te),S):0;if(!u&&(v=h=V)&&a.lineStart(),V!==h&&(R=c(u,m),(!R||Jt(u,R)||Jt(m,R))&&(m[2]=1)),V!==h)C=0,V?(a.lineStart(),R=c(m,u),a.point(R[0],R[1])):(R=c(u,m),a.point(R[0],R[1],2),a.lineEnd()),u=R;else if(o&&u&&r^V){var D;!(I&f)&&(D=c(m,u,!0))&&(C=0,r?(a.lineStart(),a.point(D[0][0],D[0][1]),a.point(D[1][0],D[1][1]),a.lineEnd()):(a.point(D[1][0],D[1][1]),a.lineEnd(),a.lineStart(),a.point(D[0][0],D[0][1],3)))}V&&(!u||!Jt(u,m))&&a.point(m[0],m[1]),u=m,h=V,f=I},lineEnd:function(){h&&a.lineEnd(),u=null},clean:function(){return C|(v&&h)<<1}}}function c(a,u,f){var h=ct(a),v=ct(u),C=[1,0,0],x=un(h,v),S=Bt(x,x),m=x[0],R=S-m*m;if(!R)return!f&&a;var V=t*S/R,I=-t*m/R,D=un(C,x),M=Ht(C,V),A=Ht(x,I);Vn(M,A);var $=D,w=Bt(M,$),g=Bt($,$),N=w*w-g*(Bt(M,M)-1);if(!(N<0)){var L=Je(N),F=Ht($,(-w-L)/g);if(Vn(F,M),F=Hn(F),!f)return F;var E=a[0],X=u[0],K=a[1],ee=u[1],ce;X<E&&(ce=E,E=X,X=ce);var me=X-E,re=ue(me-te)<ae,y=re||me<ae;if(!re&&ee<K&&(ce=K,K=ee,ee=ce),y?re?K+ee>0^F[1]<(ue(F[0]-E)<ae?K:ee):K<=F[1]&&F[1]<=ee:me>te^(E<=F[0]&&F[0]<=X)){var b=Ht($,(-w+L)/g);return Vn(b,M),[F,Hn(b)]}}}function d(a,u){var f=r?e:te-e,h=0;return a<-f?h|=1:a>f&&(h|=2),u<-f?h|=4:u>f&&(h|=8),h}return vi(l,s,i,r?[0,-e]:[-te,e-te])}function uf(e,t,n,r,o,i){var l=e[0],s=e[1],c=t[0],d=t[1],a=0,u=1,f=c-l,h=d-s,v;if(v=n-l,!(!f&&v>0)){if(v/=f,f<0){if(v<a)return;v<u&&(u=v)}else if(f>0){if(v>u)return;v>a&&(a=v)}if(v=o-l,!(!f&&v<0)){if(v/=f,f<0){if(v>u)return;v>a&&(a=v)}else if(f>0){if(v<a)return;v<u&&(u=v)}if(v=r-s,!(!h&&v>0)){if(v/=h,h<0){if(v<a)return;v<u&&(u=v)}else if(h>0){if(v>u)return;v>a&&(a=v)}if(v=i-s,!(!h&&v<0)){if(v/=h,h<0){if(v>u)return;v>a&&(a=v)}else if(h>0){if(v<a)return;v<u&&(u=v)}return a>0&&(e[0]=l+a*f,e[1]=s+a*h),u<1&&(t[0]=l+u*f,t[1]=s+u*h),!0}}}}}var Kt=1e9,Gt=-1e9;function cf(e,t,n,r){function o(d,a){return e<=d&&d<=n&&t<=a&&a<=r}function i(d,a,u,f){var h=0,v=0;if(d==null||(h=l(d,u))!==(v=l(a,u))||c(d,a)<0^u>0)do f.point(h===0||h===3?e:n,h>1?r:t);while((h=(h+u+4)%4)!==v);else f.point(a[0],a[1])}function l(d,a){return ue(d[0]-e)<ae?a>0?0:3:ue(d[0]-n)<ae?a>0?2:1:ue(d[1]-t)<ae?a>0?1:0:a>0?3:2}function s(d,a){return c(d.x,a.x)}function c(d,a){var u=l(d,1),f=l(a,1);return u!==f?u-f:u===0?a[1]-d[1]:u===1?d[0]-a[0]:u===2?d[1]-a[1]:a[0]-d[0]}return function(d){var a=d,u=_i(),f,h,v,C,x,S,m,R,V,I,D,M={point:A,lineStart:N,lineEnd:L,polygonStart:w,polygonEnd:g};function A(E,X){o(E,X)&&a.point(E,X)}function $(){for(var E=0,X=0,K=h.length;X<K;++X)for(var ee=h[X],ce=1,me=ee.length,re=ee[0],y,b,J=re[0],U=re[1];ce<me;++ce)y=J,b=U,re=ee[ce],J=re[0],U=re[1],b<=r?U>r&&(J-y)*(r-b)>(U-b)*(e-y)&&++E:U<=r&&(J-y)*(r-b)<(U-b)*(e-y)&&--E;return E}function w(){a=u,f=[],h=[],D=!0}function g(){var E=$(),X=D&&E,K=(f=Fo(f)).length;(X||K)&&(d.polygonStart(),X&&(d.lineStart(),i(null,null,1,d),d.lineEnd()),K&&gi(f,s,E,i,d),d.polygonEnd()),a=d,f=h=v=null}function N(){M.point=F,h&&h.push(v=[]),I=!0,V=!1,m=R=NaN}function L(){f&&(F(C,x),S&&V&&u.rejoin(),f.push(u.result())),M.point=A,V&&a.lineEnd()}function F(E,X){var K=o(E,X);if(h&&v.push([E,X]),I)C=E,x=X,S=K,I=!1,K&&(a.lineStart(),a.point(E,X));else if(K&&V)a.point(E,X);else{var ee=[m=Math.max(Gt,Math.min(Kt,m)),R=Math.max(Gt,Math.min(Kt,R))],ce=[E=Math.max(Gt,Math.min(Kt,E)),X=Math.max(Gt,Math.min(Kt,X))];uf(ee,ce,e,t,n,r)?(V||(a.lineStart(),a.point(ee[0],ee[1])),a.point(ce[0],ce[1]),K||a.lineEnd(),D=!1):K&&(a.lineStart(),a.point(E,X),D=!1)}m=E,R=X,V=K}return M}}const Wn=e=>e;var An=new We,Qn=new We,yi,wi,Zn,Jn,Oe={point:Re,lineStart:Re,lineEnd:Re,polygonStart:function(){Oe.lineStart=ff,Oe.lineEnd=pf},polygonEnd:function(){Oe.lineStart=Oe.lineEnd=Oe.point=Re,An.add(ue(Qn)),Qn=new We},result:function(){var e=An/2;return An=new We,e}};function ff(){Oe.point=df}function df(e,t){Oe.point=xi,yi=Zn=e,wi=Jn=t}function xi(e,t){Qn.add(Jn*e-Zn*t),Zn=e,Jn=t}function pf(){xi(yi,wi)}var ft=1/0,cn=ft,Nt=-ft,fn=Nt,dn={point:hf,lineStart:Re,lineEnd:Re,polygonStart:Re,polygonEnd:Re,result:function(){var e=[[ft,cn],[Nt,fn]];return Nt=fn=-(cn=ft=1/0),e}};function hf(e,t){e<ft&&(ft=e),e>Nt&&(Nt=e),t<cn&&(cn=t),t>fn&&(fn=t)}var jn=0,er=0,gt=0,pn=0,hn=0,nt=0,tr=0,nr=0,vt=0,bi,Ei,Ae,Ne,Ce={point:Ze,lineStart:fo,lineEnd:po,polygonStart:function(){Ce.lineStart=gf,Ce.lineEnd=vf},polygonEnd:function(){Ce.point=Ze,Ce.lineStart=fo,Ce.lineEnd=po},result:function(){var e=vt?[tr/vt,nr/vt]:nt?[pn/nt,hn/nt]:gt?[jn/gt,er/gt]:[NaN,NaN];return jn=er=gt=pn=hn=nt=tr=nr=vt=0,e}};function Ze(e,t){jn+=e,er+=t,++gt}function fo(){Ce.point=mf}function mf(e,t){Ce.point=_f,Ze(Ae=e,Ne=t)}function _f(e,t){var n=e-Ae,r=t-Ne,o=Je(n*n+r*r);pn+=o*(Ae+e)/2,hn+=o*(Ne+t)/2,nt+=o,Ze(Ae=e,Ne=t)}function po(){Ce.point=Ze}function gf(){Ce.point=yf}function vf(){Si(bi,Ei)}function yf(e,t){Ce.point=Si,Ze(bi=Ae=e,Ei=Ne=t)}function Si(e,t){var n=e-Ae,r=t-Ne,o=Je(n*n+r*r);pn+=o*(Ae+e)/2,hn+=o*(Ne+t)/2,nt+=o,o=Ne*e-Ae*t,tr+=o*(Ae+e),nr+=o*(Ne+t),vt+=o*3,Ze(Ae=e,Ne=t)}function ki(e){this._context=e}ki.prototype={_radius:4.5,pointRadius:function(e){return this._radius=e,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){this._line===0&&this._context.closePath(),this._point=NaN},point:function(e,t){switch(this._point){case 0:{this._context.moveTo(e,t),this._point=1;break}case 1:{this._context.lineTo(e,t);break}default:{this._context.moveTo(e+this._radius,t),this._context.arc(e,t,this._radius,0,ke);break}}},result:Re};var rr=new We,Nn,Ci,Ri,yt,wt,It={point:Re,lineStart:function(){It.point=wf},lineEnd:function(){Nn&&Pi(Ci,Ri),It.point=Re},polygonStart:function(){Nn=!0},polygonEnd:function(){Nn=null},result:function(){var e=+rr;return rr=new We,e}};function wf(e,t){It.point=Pi,Ci=yt=e,Ri=wt=t}function Pi(e,t){yt-=e,wt-=t,rr.add(Je(yt*yt+wt*wt)),yt=e,wt=t}let ho,mn,mo,_o;class go{constructor(t){this._append=t==null?$i:xf(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){this._line===0&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:{this._append`M${t},${n}`,this._point=1;break}case 1:{this._append`L${t},${n}`;break}default:{if(this._append`M${t},${n}`,this._radius!==mo||this._append!==mn){const r=this._radius,o=this._;this._="",this._append`m0,${r}a${r},${r} 0 1,1 0,${-2*r}a${r},${r} 0 1,1 0,${2*r}z`,mo=r,mn=this._append,_o=this._,this._=o}this._+=_o;break}}}result(){const t=this._;return this._="",t.length?t:null}}function $i(e){let t=1;this._+=e[0];for(const n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function xf(e){const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);if(t>15)return $i;if(t!==ho){const n=10**t;ho=t,mn=function(o){let i=1;this._+=o[0];for(const l=o.length;i<l;++i)this._+=Math.round(arguments[i]*n)/n+o[i]}}return mn}function vo(e,t){let n=3,r=4.5,o,i;function l(s){return s&&(typeof r=="function"&&i.pointRadius(+r.apply(this,arguments)),tt(s,o(i))),i.result()}return l.area=function(s){return tt(s,o(Oe)),Oe.result()},l.measure=function(s){return tt(s,o(It)),It.result()},l.bounds=function(s){return tt(s,o(dn)),dn.result()},l.centroid=function(s){return tt(s,o(Ce)),Ce.result()},l.projection=function(s){return arguments.length?(o=s==null?(e=null,Wn):(e=s).stream,l):e},l.context=function(s){return arguments.length?(i=s==null?(t=null,new go(n)):new ki(t=s),typeof r!="function"&&i.pointRadius(r),l):t},l.pointRadius=function(s){return arguments.length?(r=typeof s=="function"?s:(i.pointRadius(+s),+s),l):r},l.digits=function(s){if(!arguments.length)return n;if(s==null)n=null;else{const c=Math.floor(s);if(!(c>=0))throw new RangeError(`invalid digits: ${s}`);n=c}return t===null&&(i=new go(n)),l},l.projection(e).digits(n).context(t)}function pr(e){return function(t){var n=new or;for(var r in e)n[r]=e[r];return n.stream=t,n}}function or(){}or.prototype={constructor:or,point:function(e,t){this.stream.point(e,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};function hr(e,t,n){var r=e.clipExtent&&e.clipExtent();return e.scale(150).translate([0,0]),r!=null&&e.clipExtent(null),tt(n,e.stream(dn)),t(dn.result()),r!=null&&e.clipExtent(r),e}function Vi(e,t,n){return hr(e,function(r){var o=t[1][0]-t[0][0],i=t[1][1]-t[0][1],l=Math.min(o/(r[1][0]-r[0][0]),i/(r[1][1]-r[0][1])),s=+t[0][0]+(o-l*(r[1][0]+r[0][0]))/2,c=+t[0][1]+(i-l*(r[1][1]+r[0][1]))/2;e.scale(150*l).translate([s,c])},n)}function bf(e,t,n){return Vi(e,[[0,0],t],n)}function Ef(e,t,n){return hr(e,function(r){var o=+t,i=o/(r[1][0]-r[0][0]),l=(o-i*(r[1][0]+r[0][0]))/2,s=-i*r[0][1];e.scale(150*i).translate([l,s])},n)}function Sf(e,t,n){return hr(e,function(r){var o=+t,i=o/(r[1][1]-r[0][1]),l=-i*r[0][0],s=(o-i*(r[1][1]+r[0][1]))/2;e.scale(150*i).translate([l,s])},n)}var yo=16,kf=de(30*he);function wo(e,t){return+t?Rf(e,t):Cf(e)}function Cf(e){return pr({point:function(t,n){t=e(t,n),this.stream.point(t[0],t[1])}})}function Rf(e,t){function n(r,o,i,l,s,c,d,a,u,f,h,v,C,x){var S=d-r,m=a-o,R=S*S+m*m;if(R>4*t&&C--){var V=l+f,I=s+h,D=c+v,M=Je(V*V+I*I+D*D),A=At(D/=M),$=ue(ue(D)-1)<ae||ue(i-u)<ae?(i+u)/2:Mt(I,V),w=e($,A),g=w[0],N=w[1],L=g-r,F=N-o,E=m*L-S*F;(E*E/R>t||ue((S*L+m*F)/R-.5)>.3||l*f+s*h+c*v<kf)&&(n(r,o,i,l,s,c,g,N,$,V/=M,I/=M,D,C,x),x.point(g,N),n(g,N,$,V,I,D,d,a,u,f,h,v,C,x))}}return function(r){var o,i,l,s,c,d,a,u,f,h,v,C,x={point:S,lineStart:m,lineEnd:V,polygonStart:function(){r.polygonStart(),x.lineStart=I},polygonEnd:function(){r.polygonEnd(),x.lineStart=m}};function S(A,$){A=e(A,$),r.point(A[0],A[1])}function m(){u=NaN,x.point=R,r.lineStart()}function R(A,$){var w=ct([A,$]),g=e(A,$);n(u,f,a,h,v,C,u=g[0],f=g[1],a=A,h=w[0],v=w[1],C=w[2],yo,r),r.point(u,f)}function V(){x.point=S,r.lineEnd()}function I(){m(),x.point=D,x.lineEnd=M}function D(A,$){R(o=A,$),i=u,l=f,s=h,c=v,d=C,x.point=R}function M(){n(u,f,a,h,v,C,i,l,o,s,c,d,yo,r),x.lineEnd=V,V()}return x}}var Pf=pr({point:function(e,t){this.stream.point(e*he,t*he)}});function $f(e){return pr({point:function(t,n){var r=e(t,n);return this.stream.point(r[0],r[1])}})}function Vf(e,t,n,r,o){function i(l,s){return l*=r,s*=o,[t+e*l,n-e*s]}return i.invert=function(l,s){return[(l-t)/e*r,(n-s)/e*o]},i}function xo(e,t,n,r,o,i){if(!i)return Vf(e,t,n,r,o);var l=de(i),s=pe(i),c=l*e,d=s*e,a=l/e,u=s/e,f=(s*n-l*t)/e,h=(s*t+l*n)/e;function v(C,x){return C*=r,x*=o,[c*C-d*x+t,n-d*C-c*x]}return v.invert=function(C,x){return[r*(a*C-u*x+f),o*(h-u*C-a*x)]},v}function Mf(e){return Af(function(){return e})()}function Af(e){var t,n=150,r=480,o=250,i=0,l=0,s=0,c=0,d=0,a,u=0,f=1,h=1,v=null,C=co,x=null,S,m,R,V=Wn,I=.5,D,M,A,$,w;function g(E){return A(E[0]*he,E[1]*he)}function N(E){return E=A.invert(E[0],E[1]),E&&[E[0]*Ee,E[1]*Ee]}g.stream=function(E){return $&&w===E?$:$=Pf($f(a)(C(D(V(w=E)))))},g.preclip=function(E){return arguments.length?(C=E,v=void 0,F()):C},g.postclip=function(E){return arguments.length?(V=E,x=S=m=R=null,F()):V},g.clipAngle=function(E){return arguments.length?(C=+E?sf(v=E*he):(v=null,co),F()):v*Ee},g.clipExtent=function(E){return arguments.length?(V=E==null?(x=S=m=R=null,Wn):cf(x=+E[0][0],S=+E[0][1],m=+E[1][0],R=+E[1][1]),F()):x==null?null:[[x,S],[m,R]]},g.scale=function(E){return arguments.length?(n=+E,L()):n},g.translate=function(E){return arguments.length?(r=+E[0],o=+E[1],L()):[r,o]},g.center=function(E){return arguments.length?(i=E[0]%360*he,l=E[1]%360*he,L()):[i*Ee,l*Ee]},g.rotate=function(E){return arguments.length?(s=E[0]%360*he,c=E[1]%360*he,d=E.length>2?E[2]%360*he:0,L()):[s*Ee,c*Ee,d*Ee]},g.angle=function(E){return arguments.length?(u=E%360*he,L()):u*Ee},g.reflectX=function(E){return arguments.length?(f=E?-1:1,L()):f<0},g.reflectY=function(E){return arguments.length?(h=E?-1:1,L()):h<0},g.precision=function(E){return arguments.length?(D=wo(M,I=E*E),F()):Je(I)},g.fitExtent=function(E,X){return Vi(g,E,X)},g.fitSize=function(E,X){return bf(g,E,X)},g.fitWidth=function(E,X){return Ef(g,E,X)},g.fitHeight=function(E,X){return Sf(g,E,X)};function L(){var E=xo(n,0,0,f,h,u).apply(null,t(i,l)),X=xo(n,r-E[0],o-E[1],f,h,u);return a=mi(s,c,d),M=Kn(t,X),A=Kn(a,M),D=wo(M,I),F()}function F(){return $=w=null,g}return function(){return t=e.apply(this,arguments),g.invert=t.invert&&N,L()}}function mr(e,t){return[e,Wc(Zc((xe+t)/2))]}mr.invert=function(e,t){return[e,2*hi(Gc(t))-xe]};function Nf(){return If(mr).scale(961/ke)}function If(e){var t=Mf(e),n=t.center,r=t.scale,o=t.translate,i=t.clipExtent,l=null,s,c,d;t.scale=function(u){return arguments.length?(r(u),a()):r()},t.translate=function(u){return arguments.length?(o(u),a()):o()},t.center=function(u){return arguments.length?(n(u),a()):n()},t.clipExtent=function(u){return arguments.length?(u==null?l=s=c=d=null:(l=+u[0][0],s=+u[0][1],c=+u[1][0],d=+u[1][1]),a()):l==null?null:[[l,s],[c,d]]};function a(){var u=te*r(),f=t(jc(t.rotate()).invert([0,0]));return i(l==null?[[f[0]-u,f[1]-u],[f[0]+u,f[1]+u]]:e===mr?[[Math.max(f[0]-u,l),s],[Math.min(f[0]+u,c),d]]:[[l,Math.max(f[1]-u,s)],[c,Math.min(f[1]+u,d)]])}return a()}function xt(e,t,n){this.k=e,this.x=t,this.y=n}xt.prototype={constructor:xt,scale:function(e){return e===1?this:new xt(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new xt(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};xt.prototype;const Df={class:"clock-container"},Tf={class:"holographic-clock",viewBox:"0 0 100 100"},Uf={class:"clock-markers"},Of=["x1","y1","x2","y2"],zf={class:"clock-hands"},Lf=["x2","y2"],Ff=["x2","y2"],qf=["x2","y2"],Yf=["id"],Bf=["id"],Hf={__name:"VisualScreen",setup(e){const t=B(null),n=B(null),r=B(0),o=B(!1),i=new Map,l=B({x:0,y:0}),s=B(0),c=B(0),d=B(0),a=B(0);let u;const f=B([{id:"china",url:"https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json",center:[104,35],scaleFactor:.75,duration:5e3},{id:"sichuan",url:"https://geo.datav.aliyun.com/areas_v3/bound/510000_full.json",center:[103,30],scaleFactor:3,duration:5e3},{id:"guangan",url:"https://geo.datav.aliyun.com/areas_v3/bound/511600_full.json",center:[106.6,30.5],scaleFactor:30,duration:5e3},{id:"yuechi",url:"https://geo.datav.aliyun.com/areas_v3/bound/511621.json",center:[106.4,30.5],scaleFactor:50,duration:5e3}]),h={longitude:106.43,latitude:30.55},v=()=>t.value?{width:t.value.clientWidth,height:t.value.clientHeight}:{width:window.innerWidth,height:window.innerHeight},C=()=>{const A=new Date;s.value=A.getHours()%12,c.value=A.getMinutes(),d.value=A.getSeconds(),a.value=A.getMilliseconds()},x=A=>{if(i.has(A.id))return;const $=v(),w=Lt(`#${A.id}-map`).attr("viewBox",`0 0 ${$.width} ${$.height}`).attr("preserveAspectRatio","xMidYMid meet");Kc(A.url).then(g=>{const N=Nf().center(A.center).scale(Math.min($.width,$.height)*A.scaleFactor).translate([$.width/2,$.height/2]);i.set(A.id,{projection:N,data:g}),w.selectAll(".boundary").data(g.features).enter().append("path").attr("class","boundary").attr("d",vo().projection(N)),S(A.id)}).catch(g=>console.error("地图加载失败:",g))},S=A=>{const{projection:$}=i.get(A)||{};if(!$)return;const[w,g]=$([h.longitude,h.latitude]);l.value={x:w,y:g}},m=()=>{const A=v();f.value.forEach($=>{const{projection:w,data:g}=i.get($.id)||{};!w||!g||(w.scale(Math.min(A.width,A.height)*$.scaleFactor).translate([A.width/2,A.height/2]),Lt(`#${$.id}-map`).attr("viewBox",`0 0 ${A.width} ${A.height}`).selectAll(".boundary").attr("d",vo().projection(w)),$.id===f.value[r.value].id&&S($.id))})},R=A=>{if(o.value)return;o.value=!0;const $=f.value.length,w=(r.value+A+$)%$,g=f.value[r.value],N=f.value[w];Lt(`#${g.id}-container`).transition().duration(1500).style("opacity",0).on("end",()=>{Lt(`#${N.id}-container`).style("opacity",0).classed("map-visible",!0).transition().duration(1500).style("opacity",1).on("end",()=>{r.value=w,o.value=!1,S(N.id)})})},V=()=>{const{width:$,height:w}=v();for(let g=0;g<100;g++){const N=document.createElement("div");N.className="particle";const L=Math.random()*$,F=Math.random()*w,E=Math.random()*10,X=Math.random()*2+1;N.style.left=`${L}px`,N.style.top=`${F}px`,N.style.animationDelay=`${E}s`,N.style.width=`${X}px`,N.style.height=`${X}px`,n.value.appendChild(N)}};let I;const D=()=>{I=setInterval(()=>{R(1)},f.value[0].duration+1500)};dt(()=>{C(),u=setInterval(C,50),f.value.forEach(x),V(),D(),document.addEventListener("fullscreenchange",M)}),Li(()=>{clearInterval(u),clearInterval(I),i.clear(),document.removeEventListener("fullscreenchange",M)});const M=()=>{jt(()=>{m(),n.value&&(n.value.innerHTML=""),V()})};return rt([()=>window.innerWidth,()=>window.innerHeight,()=>{var A;return(A=t.value)==null?void 0:A.clientWidth},()=>{var A;return(A=t.value)==null?void 0:A.clientHeight}],()=>{jt(m)}),(A,$)=>(le(),_e("div",{class:"holographic-container",ref_key:"container",ref:t},[$[3]||($[3]=Y("div",{class:"slogan-container"},[Y("div",null,"对党忠诚  服务人民"),Y("div",null,"执法公正  纪律严明")],-1)),$[4]||($[4]=Y("div",{class:"title-container"},[Y("div",{class:"holographic-title"},"岳池县公安局情报指挥中心")],-1)),Y("div",Df,[(le(),_e("svg",Tf,[$[0]||($[0]=Y("circle",{cx:"50",cy:"50",r:"45",fill:"rgba(0,0,0,0)",stroke:"#00e5ff","stroke-width":"0.5"},null,-1)),$[1]||($[1]=Y("circle",{cx:"50",cy:"50",r:"42",fill:"rgba(5,15,44,0.5)"},null,-1)),Y("g",Uf,[(le(),_e(He,null,Ge(12,w=>Y("line",{key:w,x1:50+38*Math.cos((w*30-90)*Math.PI/180),y1:50+38*Math.sin((w*30-90)*Math.PI/180),x2:50+42*Math.cos((w*30-90)*Math.PI/180),y2:50+42*Math.sin((w*30-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1.5"},null,8,Of)),64))]),Y("g",zf,[Y("line",{class:"hour-hand",x1:50,y1:50,x2:50+20*Math.cos((s.value*30+c.value*.5-90)*Math.PI/180),y2:50+20*Math.sin((s.value*30+c.value*.5-90)*Math.PI/180),stroke:"#9e4edd","stroke-width":"3","stroke-linecap":"round"},null,8,Lf),Y("line",{class:"minute-hand",x1:50,y1:50,x2:50+30*Math.cos((c.value*6+d.value*.1-90)*Math.PI/180),y2:50+30*Math.sin((c.value*6+d.value*.1-90)*Math.PI/180),stroke:"#ff4d4d","stroke-width":"2","stroke-linecap":"round"},null,8,Ff),Y("line",{class:"second-hand",x1:50,y1:50,x2:50+38*Math.cos((d.value*6+a.value*.006-90)*Math.PI/180),y2:50+38*Math.sin((d.value*6+a.value*.006-90)*Math.PI/180),stroke:"#00e5ff","stroke-width":"1","stroke-linecap":"round"},null,8,qf)]),$[2]||($[2]=Y("circle",{cx:"50",cy:"50",r:"2",fill:"#fff"},null,-1))]))]),Y("div",{ref_key:"particlesContainer",ref:n,class:"particles-container"},null,512),$[5]||($[5]=Y("div",{class:"hologram-grid"},null,-1)),$[6]||($[6]=Y("div",{class:"scan-line-vertical"},null,-1)),$[7]||($[7]=Y("div",{class:"scan-line-horizontal"},null,-1)),$[8]||($[8]=Y("div",{class:"hologram-frame"},[Y("div")],-1)),(le(!0),_e(He,null,Ge(f.value,(w,g)=>(le(),_e("div",{key:w.id,id:`${w.id}-container`,class:Fi(["map-container",{"map-visible":r.value===g}])},[(le(),_e("svg",{id:`${w.id}-map`,class:"map-svg"},null,8,Bf)),r.value===g?(le(),_e("div",{key:0,class:"location-marker",style:ir({left:l.value.x+"px",top:l.value.y+"px"})},null,4)):en("",!0)],10,Yf))),128))],512))}},Xf={class:"app-management-container"},Kf={class:"clearfix"},Gf={__name:"AppManagement",setup(e){const t=B([]),n=B([]),r=B(0),o=B(!1);B(!1),B("");const i=B(null),l=Be({id:null,name:"",url:"",isPublic:"",roles:[]}),s=Be({name:[{required:!0,message:"请输入应用名称",trigger:"blur"}],url:[{required:!0,message:"请输入应用URL",trigger:"blur"}],isPublic:[{required:!0,message:"请选择可见范围",trigger:"change"}],roles:[{type:"array",required:!0,validator:(x,S,m)=>{S&&S.length>0?m():m(new Error("至少选择一个角色组"))},trigger:"change"}]}),c=x=>{try{return JSON.parse(x).map(m=>{const R=n.value.find(V=>String(V.value)===String(m));return R?R.label:m})}catch(S){return console.error("解析 roleList 失败:",S),[]}},d=async()=>{try{const x=new FormData;x.append("controlCode","query");const S=await se.post("/api/application_manage.php",x);S.status===1&&(t.value=S.data.application,n.value=S.data.rolelist.map(m=>({value:m.id,label:m.roleName})),r.value=S.data.application.length)}catch(x){console.error("获取应用列表失败:",x),Z.error("获取应用列表失败")}};dt(()=>{d()});const a=x=>{switch(x){case"0":return"公开";case"1":return"非第三方人员";case"2":return"民警";case"3":return"授权用户";default:return"未知"}},u=()=>{h(),o.value=!0},f=x=>{if(h(),n.value.length===0){console.error("角色选项未加载完成"),Z.error("角色数据加载中，请稍后再试");return}l.id=x.id,l.name=x.application_name,l.url=x.url,l.isPublic=x.public;try{const S=JSON.parse(x.roleList);l.roles=S.map(m=>String(m)),jt(()=>{l.roles=[...l.roles]})}catch(S){console.error("解析角色列表失败:",S),l.roles=[]}o.value=!0},h=()=>{i.value&&i.value.resetFields(),l.id=null,l.name="",l.url="",l.isPublic="",l.roles=[]},v=x=>{St.confirm(`确定要删除应用 "${x.application_name}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const S=new FormData;S.append("controlCode","del"),S.append("id",x.id);const m=await se.post("/api/application_manage.php",S);m.status===1?(Z.success("删除成功"),d()):Z.error("删除失败: "+(m.message||"未知错误"))}catch(S){console.error("删除应用失败:",S),Z.error("删除应用失败: "+S.message)}}).catch(()=>{Z.info("已取消删除")})},C=async()=>{try{await i.value.validate()}catch(x){console.error("表单验证失败:",x);return}try{const x=new FormData;x.append("controlCode",l.id?"modify":"add"),x.append("id",l.id),x.append("application_name",l.name),x.append("url",l.url),x.append("public",l.isPublic),x.append("roleList",JSON.stringify(l.roles));const S=await se.post("/api/application_manage.php",x);S.status===1?(Z.success("更新成功"),o.value=!1,d()):Z.error(S.message||"更新失败")}catch(x){console.error("更新应用失败:",x),Z.error("更新应用失败: "+x.message)}};return(x,S)=>{const m=O("el-button"),R=O("el-table-column"),V=O("el-tag"),I=O("el-icon"),D=O("el-button-group"),M=O("el-table"),A=O("el-card"),$=O("el-input"),w=O("el-form-item"),g=O("el-option"),N=O("el-select"),L=O("el-checkbox"),F=O("el-checkbox-group"),E=O("el-form"),X=O("el-dialog");return le(),_e("div",Xf,[p(A,{class:"box-card"},{header:_(()=>[Y("div",Kf,[p(m,{style:{float:"right"},round:"",type:"primary",onClick:u},{default:_(()=>S[6]||(S[6]=[ne(" 添加应用 ")])),_:1,__:[6]})])]),default:_(()=>[p(M,{data:t.value,stripe:"",border:"",fit:"","highlight-current-row":"",onRowDblclick:f,style:{width:"100%"}},{default:_(()=>[p(R,{label:"序号",width:"80",align:"center"},{default:_(K=>[ne(ye(K.$index+1),1)]),_:1}),p(R,{prop:"application_name",label:"应用名称","min-width":"120"}),p(R,{prop:"url",label:"URL","min-width":"150"}),p(R,{prop:"public",label:"是否公开",width:"150",align:"center"},{default:_(K=>[ne(ye(a(K.row.public)),1)]),_:1}),p(R,{prop:"roles",label:"授权角色组","min-width":"180"},{default:_(K=>[(le(!0),_e(He,null,Ge(c(K.row.roleList),ee=>(le(),Ye(V,{key:ee,size:"small",type:"info"},{default:_(()=>[ne(ye(ee),1)]),_:2},1024))),128))]),_:1}),p(R,{label:"操作",width:"160",align:"center"},{default:_(K=>[p(D,null,{default:_(()=>[p(m,{size:"mini",type:"warning",onClick:ee=>f(K.row)},{default:_(()=>[p(I,null,{default:_(()=>[p(G(_n))]),_:1})]),_:2},1032,["onClick"]),p(m,{size:"mini",type:"danger",onClick:ee=>v(K.row)},{default:_(()=>[p(I,null,{default:_(()=>[p(G(gn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])]),_:1}),p(X,{modelValue:o.value,"onUpdate:modelValue":S[5]||(S[5]=K=>o.value=K),title:l.id?"编辑应用":"添加应用",width:"50%"},{footer:_(()=>[p(m,{onClick:S[4]||(S[4]=K=>o.value=!1)},{default:_(()=>S[7]||(S[7]=[ne("取消")])),_:1,__:[7]}),p(m,{type:"primary",onClick:C},{default:_(()=>S[8]||(S[8]=[ne("保存")])),_:1,__:[8]})]),default:_(()=>[p(E,{model:l,rules:s,ref_key:"formRef",ref:i,"label-width":"100px"},{default:_(()=>[p(w,{label:"应用名称",prop:"name"},{default:_(()=>[p($,{modelValue:l.name,"onUpdate:modelValue":S[0]||(S[0]=K=>l.name=K),placeholder:"请输入应用名称"},null,8,["modelValue"])]),_:1}),p(w,{label:"URL",prop:"url"},{default:_(()=>[p($,{modelValue:l.url,"onUpdate:modelValue":S[1]||(S[1]=K=>l.url=K),placeholder:"请输入应用URL"},null,8,["modelValue"])]),_:1}),p(w,{label:"是否公开",prop:"isPublic"},{default:_(()=>[p(N,{modelValue:l.isPublic,"onUpdate:modelValue":S[2]||(S[2]=K=>l.isPublic=K),placeholder:"请选择是否公开"},{default:_(()=>[p(g,{value:"0",label:"公开"}),p(g,{value:"1",label:"非第三方人员"}),p(g,{value:"2",label:"民警"}),p(g,{value:"3",label:"授权用户"})]),_:1},8,["modelValue"])]),_:1}),p(w,{label:"授权角色组",prop:"roles"},{default:_(()=>[p(F,{modelValue:l.roles,"onUpdate:modelValue":S[3]||(S[3]=K=>l.roles=K)},{default:_(()=>[(le(!0),_e(He,null,Ge(n.value,K=>(le(),Ye(L,{key:K.value,label:K.value},{default:_(()=>[ne(ye(K.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}}},Wf=Dt(Gf,[["__scopeId","data-v-2a67e87b"]]),Qf={class:"user-auth-management"},Zf={__name:"PowerManagement",setup(e){const t=Be({unit:"all",userKeyword:"",selectedApp:"",userList:[],authList:[],currentPage:1,pageSize:10,total:0}),n=()=>{console.log("查询用户：",t.unit,t.userKeyword),t.userList=[],t.total=0},r=()=>{console.log("查询授权：",t.selectedApp),t.authList=[]},o=i=>{t.currentPage=i,n()};return(i,l)=>(le(),_e("div",Qf,[p(G(vr),{gutter:20,class:"query-section"},{default:_(()=>[p(G(Ot),{span:12},{default:_(()=>[p(G(yr),{modelValue:i.unit,"onUpdate:modelValue":l[0]||(l[0]=s=>i.unit=s),placeholder:"所有单位",style:{width:"180px","margin-right":"10px"}},{default:_(()=>[p(G(wr),{label:"所有单位",value:"all"})]),_:1},8,["modelValue"]),p(G(qi),{modelValue:i.userKeyword,"onUpdate:modelValue":l[1]||(l[1]=s=>i.userKeyword=s),placeholder:"输入姓名、警号或身份证号",style:{width:"300px","margin-right":"10px"}},null,8,["modelValue"]),p(G(zt),{type:"primary",onClick:n},{default:_(()=>l[3]||(l[3]=[ne("查询用户")])),_:1,__:[3]})]),_:1}),p(G(Ot),{span:12,style:{"text-align":"right"}},{default:_(()=>[p(G(yr),{modelValue:i.selectedApp,"onUpdate:modelValue":l[2]||(l[2]=s=>i.selectedApp=s),placeholder:"选择应用",style:{width:"180px","margin-right":"10px"}},{default:_(()=>[p(G(wr),{label:"选择应用",value:""})]),_:1},8,["modelValue"]),p(G(zt),{type:"primary",onClick:r},{default:_(()=>l[4]||(l[4]=[ne("查询授权人员")])),_:1,__:[4]})]),_:1})]),_:1}),p(G(vr),{gutter:20,class:"table-section"},{default:_(()=>[p(G(Ot),{span:12},{default:_(()=>[l[6]||(l[6]=Y("div",{class:"table-title"},"用户列表",-1)),p(G(xr),{data:i.userList,border:"",style:{width:"100%"}},{default:_(()=>[p(G(Me),{prop:"unit",label:"单位"}),p(G(Me),{prop:"name",label:"姓名"}),p(G(Me),{prop:"policeNo",label:"警号"}),p(G(Me),{prop:"idCard",label:"身份证号"}),p(G(Me),{prop:"identity",label:"身份"}),p(G(Me),{label:"操作"},{default:_(s=>[p(G(zt),{size:"mini",type:"text"},{default:_(()=>l[5]||(l[5]=[ne("操作")])),_:1,__:[5]})]),_:1})]),_:1},8,["data"]),p(G(Yi),{onCurrentChange:o,"current-page":i.currentPage,"page-size":i.pageSize,layout:"prev, pager, next",total:i.total,style:{"margin-top":"10px","text-align":"center"}},null,8,["current-page","page-size","total"])]),_:1,__:[6]}),p(G(Ot),{span:12},{default:_(()=>[l[8]||(l[8]=Y("div",{class:"table-title"},"授权信息",-1)),p(G(xr),{data:i.authList,border:"",style:{width:"100%"}},{default:_(()=>[p(G(Me),{prop:"app",label:"授权应用"}),p(G(Me),{prop:"authUnit",label:"授权单位"}),p(G(Me),{prop:"role",label:"授权角色"}),p(G(Me),{label:"操作"},{default:_(s=>[p(G(zt),{size:"mini",type:"text"},{default:_(()=>l[7]||(l[7]=[ne("操作")])),_:1,__:[7]})]),_:1})]),_:1},8,["data"])]),_:1,__:[8]})]),_:1})]))}},Jf=Dt(Zf,[["__scopeId","data-v-7ce52fb3"]]),jf={class:"role-management-container"},ed={class:"toolbar flex justify-end items-center mb-4"},td={__name:"RoleManagement",setup(e){const t=B([]),n=B(null),r=async()=>{try{const a=new FormData;a.append("controlCode","query");const u=await se.post("/api/role_List_manage.php",a);u.status===1?t.value=u.data.map(f=>({id:f.id,name:f.roleName,desc:f.roleDesc})):Z.error(u.message||"获取角色列表失败")}catch(a){console.error("获取角色列表失败:",a),Z.error("获取角色列表失败")}};dt(()=>{r()});const o=B(!1),i=Be({id:null,name:"",desc:""}),l=()=>{n.value&&n.value.resetFields(),i.id=null,i.name="",i.desc="",o.value=!0},s=a=>{n.value&&n.value.resetFields(),i.id=a.id,i.name=a.name,i.desc=a.desc,o.value=!0},c=a=>{St.confirm(`确定要删除角色 "${a.name}" 吗？`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const u=new FormData;u.append("controlCode","del"),u.append("id",a.id);const f=await se.post("/api/role_List_manage.php",u);f.status===1?(Z.success("删除成功"),r()):Z.error(f.message||"删除失败")}catch(u){console.error("删除角色失败:",u),Z.error("删除角色失败: "+u.message)}}).catch(()=>{Z.info("已取消删除")})},d=async()=>{if(!i.name){Z.error("角色名称不能为空");return}try{const a=new FormData;a.append("controlCode",i.id?"modify":"add"),a.append("roleName",i.name),a.append("roleDesc",i.desc),i.id&&a.append("id",i.id);const u=await se.post("/api/role_List_manage.php",a);u.status===1?(Z.success(i.id?"更新成功":"添加成功"),o.value=!1,r()):Z.error(u.message||(i.id?"更新失败":"添加失败"))}catch(a){console.error("操作失败:",a),Z.error("操作失败: "+a.message)}};return(a,u)=>{const f=O("el-icon"),h=O("el-button"),v=O("el-table-column"),C=O("el-button-group"),x=O("el-table"),S=O("el-input"),m=O("el-form-item"),R=O("el-form"),V=O("el-dialog");return le(),_e("div",jf,[Y("div",ed,[p(h,{type:"primary",onClick:l},{default:_(()=>[p(f,null,{default:_(()=>[p(G(ko))]),_:1}),u[4]||(u[4]=ne(" 添加角色 "))]),_:1,__:[4]})]),p(x,{data:t.value,style:{width:"100%"},border:""},{default:_(()=>[p(v,{label:"序号",width:"80",align:"center"},{default:_(I=>[ne(ye(I.$index+1),1)]),_:1}),p(v,{prop:"name",label:"角色名称"}),p(v,{prop:"desc",label:"角色描述"}),p(v,{label:"操作"},{default:_(I=>[p(C,null,{default:_(()=>[p(h,{size:"mini",type:"warning",onClick:D=>s(I.row)},{default:_(()=>[p(f,null,{default:_(()=>[p(G(_n))]),_:1})]),_:2},1032,["onClick"]),p(h,{size:"mini",type:"danger",onClick:D=>c(I.row)},{default:_(()=>[p(f,null,{default:_(()=>[p(G(gn))]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"]),p(V,{modelValue:o.value,"onUpdate:modelValue":u[3]||(u[3]=I=>o.value=I),width:"20%"},{default:_(()=>[p(R,{model:i,"label-width":"100px",ref_key:"formRef",ref:n},{default:_(()=>[p(m,{label:"角色名称",prop:"name"},{default:_(()=>[p(S,{modelValue:i.name,"onUpdate:modelValue":u[0]||(u[0]=I=>i.name=I)},null,8,["modelValue"])]),_:1}),p(m,{label:"角色描述",prop:"desc"},{default:_(()=>[p(S,{modelValue:i.desc,"onUpdate:modelValue":u[1]||(u[1]=I=>i.desc=I),placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),footer:_(()=>[p(h,{onClick:u[2]||(u[2]=I=>o.value=!1)},{default:_(()=>u[5]||(u[5]=[ne("取消")])),_:1,__:[5]}),p(h,{type:"primary",onClick:d},{default:_(()=>u[6]||(u[6]=[ne("确定")])),_:1,__:[6]})]),_:1},8,["modelValue"])])}}},nd=Dt(td,[["__scopeId","data-v-2a4abc11"]]),rd=[{path:"/unit-management",name:"UnitManagement",component:fa},{path:"/user-management",name:"UserManagement",component:$a},{path:"/visual-screen",name:"VisualScreen",component:Hf},{path:"/app-management",name:"AppManagement",component:Wf},{path:"/role-management",name:"RoleManagement",component:nd},{path:"/power-management",name:"PowerManagement",component:Jf}],od=Wl({history:Sl(),routes:rd}),En=Bi(sa);En.use(Hi);for(const[e,t]of Object.entries(Xi))En.component(e,t);En.use(od);En.mount("#app");
