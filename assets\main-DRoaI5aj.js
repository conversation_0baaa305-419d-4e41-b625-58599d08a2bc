import{_ as E,c as d,r,o as s,w as n,a as _,b as e,d as f,F as g,e as u,u as m,n as y,t as c,f as V,i as $,E as I}from"./index-DKTW-tst.js";import{_ as N}from"./logo-LbKsNqzq.js";/* empty css              */const P={class:"category-container"},S={class:"app-category"},j={class:"card-grid"},z=["onClick"],D={class:"app-info"},L={class:"app-category"},O={class:"card-grid"},T=["onClick"],W={class:"app-info"},q={class:"app-category"},G={class:"card-grid"},H=["onClick"],J={class:"app-info"},K={__name:"Front",setup(v){const l=[{id:1,name:"通讯录",path:"/contacts",desc:"组织架构与人员信息管理",color:"linear-gradient(135deg, #409eff 0%, #66b1ff 100%)",category:"public"},{id:2,name:"单位管理",path:"/unit",desc:"组织机构与部门管理",color:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)",category:"system"},{id:3,name:"用户管理",path:"/users",desc:"系统用户账户管理",color:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)",category:"system"},{id:4,name:"角色管理",path:"/roles",desc:"角色权限配置",color:"linear-gradient(135deg, #fa8c16 0%, #ff7a45 100%)",category:"system"},{id:5,name:"应用管理",path:"/applications",desc:"子系统接入管理",color:"linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)",category:"system"},{id:6,name:"授权管理",path:"/auth",desc:"权限分配与审计",color:"linear-gradient(135deg, #faad14 0%, #ffc53d 100%)",category:"system"},{id:7,name:"值班管理",path:"/duty",desc:"值班计划与排班管理",color:"linear-gradient(135deg, #eb2f96 0%, #ff79c6 100%)",category:"system"},{id:8,name:"数据大屏",path:"/dashboard",desc:"数据可视化分析",color:"linear-gradient(135deg, #2f54eb 0%, #5c70ff 100%)",category:"private"},{id:9,name:"文件快递柜",path:"fileCloud/",desc:"文件上传与下载",color:"linear-gradient(135deg, #fa541c 0%, #ff8968 100%)",category:"public"},{id:10,name:"资产管理",path:"/contacts",desc:"岳池县公安局资产管理",color:"linear-gradient(135deg, #fa541c 0%, #ff8968 100%)",category:"public"}],k=l.filter(o=>o.category==="public"),w=l.filter(o=>o.category==="private"),C=l.filter(o=>o.category==="system"),i=o=>{o&&o.startsWith("fileCloud/")?window.location.href=o:window.location.href="back.html"};return(o,a)=>{const x=r("el-header"),p=r("el-card"),A=r("el-main"),B=r("el-footer"),F=r("el-container");return s(),d(F,{class:"portal-container"},{default:n(()=>[_(x,{class:"portal-header"},{default:n(()=>a[0]||(a[0]=[e("div",{class:"header-content"},[e("img",{src:N,class:"logo"}),e("h1",null,"云枢应用平台")],-1)])),_:1,__:[0]}),_(A,null,{default:n(()=>[e("div",P,[e("div",S,[a[1]||(a[1]=e("h2",{class:"category-title"},"公用应用",-1)),e("div",j,[(s(!0),f(g,null,u(m(k),t=>(s(),d(p,{key:t.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:n(()=>[e("div",{class:"card-content",onClick:b=>i(t.path)},[e("div",{class:"app-icon",style:y({background:t.color})},null,4),e("div",D,[e("h3",null,c(t.name),1),e("p",null,c(t.desc),1)])],8,z)]),_:2},1024))),128))])]),e("div",L,[a[2]||(a[2]=e("h2",{class:"category-title"},"专用应用",-1)),e("div",O,[(s(!0),f(g,null,u(m(w),t=>(s(),d(p,{key:t.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:n(()=>[e("div",{class:"card-content",onClick:b=>i(t.path)},[e("div",{class:"app-icon",style:y({background:t.color})},null,4),e("div",W,[e("h3",null,c(t.name),1),e("p",null,c(t.desc),1)])],8,T)]),_:2},1024))),128))])]),e("div",q,[a[3]||(a[3]=e("h2",{class:"category-title"},"系统应用",-1)),e("div",G,[(s(!0),f(g,null,u(m(C),t=>(s(),d(p,{key:t.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:n(()=>[e("div",{class:"card-content",onClick:b=>i(t.path)},[e("div",{class:"app-icon",style:y({background:t.color})},null,4),e("div",J,[e("h3",null,c(t.name),1),e("p",null,c(t.desc),1)])],8,H)]),_:2},1024))),128))])])])]),_:1}),_(B,{class:"portal-footer"},{default:n(()=>a[4]||(a[4]=[e("p",null,"Powered by ：科技通信中队",-1)])),_:1,__:[4]})]),_:1})}}},M=E(K,[["__scopeId","data-v-ae037a29"]]),h=V(M);h.use($);for(const[v,l]of Object.entries(I))h.component(v,l);h.mount("#app");
