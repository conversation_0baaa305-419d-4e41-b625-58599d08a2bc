<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限验证失败 - CloudPivot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
            background: linear-gradient(135deg, #409eff, #66b1ff);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 背景装饰元素 */
        .bg-decoration {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .bg-decoration:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .bg-decoration:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }

        .bg-decoration:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        .bg-decoration:nth-child(4) {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 10%;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* 主容器 */
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 500px;
            width: 90%;
            position: relative;
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 图标 */
        .error-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 标题 */
        .error-title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
        }

        /* 消息 */
        .error-message {
            font-size: 16px;
            color: #606266;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        /* 倒计时容器 */
        .countdown-container {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid #dee2e6;
        }

        .countdown-text {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .countdown-timer {
            font-size: 32px;
            font-weight: bold;
            color: #409eff;
            font-family: 'Courier New', monospace;
            text-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
        }

        /* 按钮 */
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #409eff, #66b1ff);
            color: white;
            box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(64, 158, 255, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.8);
            color: #606266;
            border: 1px solid #dcdfe6;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .error-container {
                padding: 30px 20px;
                margin: 20px;
            }
            
            .error-title {
                font-size: 20px;
            }
            
            .error-message {
                font-size: 14px;
            }
            
            .countdown-timer {
                font-size: 28px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- 背景装饰元素 -->
    <div class="bg-decoration"></div>
    <div class="bg-decoration"></div>
    <div class="bg-decoration"></div>
    <div class="bg-decoration"></div>

    <div class="error-container">
        <div class="error-icon">
            🔒
        </div>
        
        <h1 class="error-title" id="errorTitle">权限验证失败</h1>
        
        <p class="error-message" id="errorMessage">
            您当前的账户权限不足以使用该应用，请联系系统管理员申请相应权限。
        </p>
        
        <div class="countdown-container">
            <div class="countdown-text">页面将在以下时间后自动跳转到首页：</div>
            <div class="countdown-timer" id="countdown">5</div>
        </div>
        
        <div class="action-buttons">
            <a href="index.html" class="btn btn-primary">
                🏠 立即返回首页
            </a>
            <button onclick="window.history.back()" class="btn btn-secondary">
                ↩️ 返回上一页
            </button>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 设置错误信息
        function setErrorInfo() {
            const type = getUrlParameter('type') || 'permission';
            const message = getUrlParameter('message');

            const titleElement = document.getElementById('errorTitle');
            const messageElement = document.getElementById('errorMessage');
            const countdownTextElement = document.querySelector('.countdown-text');
            const primaryButton = document.querySelector('.btn-primary');

            if (type === 'login') {
                titleElement.textContent = '访问被拒绝';
                messageElement.textContent = message || '您尚未登录系统，请先完成身份验证。';
                document.querySelector('.error-icon').textContent = '🚫';
                countdownTextElement.textContent = '页面将在以下时间后自动跳转到登录页：';
                primaryButton.href = 'login.html';
                primaryButton.innerHTML = '🔑 立即前往登录';
            } else {
                titleElement.textContent = '权限验证失败';
                messageElement.textContent = message || '您当前的账户权限不足以访问该系统，请联系系统管理员申请相应权限。';
                countdownTextElement.textContent = '页面将在以下时间后自动跳转到首页：';
                primaryButton.href = 'index.html';
                primaryButton.innerHTML = '🏠 立即返回首页';
            }
        }

        // 倒计时功能
        function startCountdown() {
            let seconds = 5;
            const countdownElement = document.getElementById('countdown');
            const type = getUrlParameter('type') || 'permission';

            const timer = setInterval(() => {
                countdownElement.textContent = seconds;
                seconds--;

                if (seconds < 0) {
                    clearInterval(timer);
                    // 根据错误类型跳转到不同页面
                    if (type === 'login') {
                        window.location.href = 'login.html';
                    } else {
                        window.location.href = 'index.html';
                    }
                }
            }, 1000);
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            setErrorInfo();
            startCountdown();
        });
    </script>
</body>
</html>
