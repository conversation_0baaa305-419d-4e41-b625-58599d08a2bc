import{_ as F,c as d,r,o as s,w as c,a as g,b as e,d as f,F as m,e as _,u,n as y,t as n,E as I,f as N,i as P,g as V}from"./index-D8fAg56F.js";import{_ as $}from"./logo-LbKsNqzq.js";/* empty css              */const L={class:"category-container"},S={class:"app-category"},j={class:"card-grid"},z=["onClick"],D={class:"app-info"},O={class:"app-category"},T={class:"card-grid"},q=["onClick"],G={class:"app-info"},H={class:"app-category"},J={class:"card-grid"},K=["onClick"],M={class:"app-info"},Q={__name:"Front",setup(v){const l=[{name:"通讯录",path:"/undefined",desc:"通讯录管理",color:"grey",category:"public"},{name:"资产管理",path:"/undefined",desc:"岳池县公安局资产管理",color:"grey",category:"public"},{name:"人员调动模拟",path:"/undefined",desc:"模拟部门人员调动",color:"grey",category:"public"},{name:"文件快递柜",path:"fileCloud",desc:"文件上传与下载",color:"linear-gradient(135deg, #fa541c 0%, #ff8968 100%)",category:"private"},{name:"学生信息查询系统",path:"schInfo",desc:"学生信息查询",color:"linear-gradient(135deg, #2f54eb 0%, #5c70ff 100%)",category:"private"},{name:"数据大屏",path:"/back.html",desc:"岳池县110数据大屏预览",color:"linear-gradient(135deg, #722ed1 0%, #9254de 100%)",category:"private"},{name:"值班管理",path:"/back.html",desc:"岳池县公安局内部值班管理",color:"linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%)",category:"private"},{name:"重点人员管理",path:"/back.html",desc:"重点人员基本信息，动向管理",color:"linear-gradient(135deg, #eb2f96 0%, #f759ab 100%)",category:"private"},{name:"重大事件管理",path:"/back.html",desc:"重点事件基本信息，处置情况管理",color:"linear-gradient(135deg, #faad14 0%, #ffc53d 100%)",category:"private"},{name:"单位管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #52c41a 0%, #73d13d 100%)",category:"system"},{name:"用户管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #1890ff 0%, #40a9ff 100%)",category:"system"},{name:"授权管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #f5222d 0%, #ff4d4f 100%)",category:"system"},{name:"应用管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #a0d911 0%, #b7eb8f 100%)",category:"system"},{name:"角色管理",path:"/back.html",desc:"",color:"linear-gradient(135deg, #fa8c16 0%, #ffb347 100%)",category:"system"}],k=l.filter(o=>o.category==="public"),C=l.filter(o=>o.category==="private"),w=l.filter(o=>o.category==="system"),i=o=>{if(o==="/undefined"){I({title:"提示",message:"应用开发中！",type:"info",duration:3e3});return}else window.location.href=o};return(o,a)=>{const x=r("el-header"),p=r("el-card"),A=r("el-main"),B=r("el-footer"),E=r("el-container");return s(),d(E,{class:"portal-container"},{default:c(()=>[g(x,{class:"header"},{default:c(()=>a[0]||(a[0]=[e("div",{class:"header-left"},[e("img",{src:$,alt:"Logo",class:"header-logo"}),e("span",{class:"logo"},"CloudPivot")],-1)])),_:1,__:[0]}),g(A,null,{default:c(()=>[e("div",L,[e("div",S,[a[1]||(a[1]=e("h2",{class:"category-title"},"公用应用",-1)),e("div",j,[(s(!0),f(m,null,_(u(k),t=>(s(),d(p,{key:t.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:c(()=>[e("div",{class:"card-content",onClick:b=>i(t.path)},[e("div",{class:"app-icon",style:y({background:t.color})},null,4),e("div",D,[e("h3",null,n(t.name),1),e("p",null,n(t.desc),1)])],8,z)]),_:2},1024))),128))])]),e("div",O,[a[2]||(a[2]=e("h2",{class:"category-title"},"专用应用",-1)),e("div",T,[(s(!0),f(m,null,_(u(C),t=>(s(),d(p,{key:t.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:c(()=>[e("div",{class:"card-content",onClick:b=>i(t.path)},[e("div",{class:"app-icon",style:y({background:t.color})},null,4),e("div",G,[e("h3",null,n(t.name),1),e("p",null,n(t.desc),1)])],8,q)]),_:2},1024))),128))])]),e("div",H,[a[3]||(a[3]=e("h2",{class:"category-title"},"系统应用",-1)),e("div",J,[(s(!0),f(m,null,_(u(w),t=>(s(),d(p,{key:t.id,class:"app-card","body-style":{padding:"0px"},shadow:"hover"},{default:c(()=>[e("div",{class:"card-content",onClick:b=>i(t.path)},[e("div",{class:"app-icon",style:y({background:t.color})},null,4),e("div",M,[e("h3",null,n(t.name),1),e("p",null,n(t.desc),1)])],8,K)]),_:2},1024))),128))])])])]),_:1}),g(B,{class:"portal-footer"},{default:c(()=>a[4]||(a[4]=[e("p",null,"Powered by ：科技通信中队",-1)])),_:1,__:[4]})]),_:1})}}},R=F(Q,[["__scopeId","data-v-a24d36ab"]]),h=N(R);h.use(P);for(const[v,l]of Object.entries(V))h.component(v,l);h.mount("#app");
