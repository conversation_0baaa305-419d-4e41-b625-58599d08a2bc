-- 数据库更新脚本：添加uploadIp字段
-- 创建时间: 2025-07-03
-- 用途: 为filecloud_info表添加uploadIp字段以记录上传者IP地址

USE application;

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = 'application' 
         AND TABLE_NAME = 'filecloud_info' 
         AND COLUMN_NAME = 'uploadIp') = 0,
        'ALTER TABLE filecloud_info ADD COLUMN uploadIp VARCHAR(45) NULL COMMENT "上传者IP地址" AFTER file_path;',
        'SELECT "字段uploadIp已存在，跳过添加" as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为uploadIp字段添加索引（可选，用于查询优化）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = 'application' 
         AND TABLE_NAME = 'filecloud_info' 
         AND INDEX_NAME = 'idx_upload_ip') = 0,
        'ALTER TABLE filecloud_info ADD INDEX idx_upload_ip (uploadIp);',
        'SELECT "索引idx_upload_ip已存在，跳过添加" as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示更新后的表结构
DESCRIBE filecloud_info;

-- 显示表的索引信息
SHOW INDEX FROM filecloud_info;

SELECT 'uploadIp字段添加完成！' as status;
