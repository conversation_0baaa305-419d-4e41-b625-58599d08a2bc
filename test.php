<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户导入测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; text-align: center; }
        .upload-area { 
            border: 2px dashed #ccc; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0;
            border-radius: 5px;
        }
        #file-info { margin-top: 10px; font-size: 14px; color: #666; }
        button { 
            background: #4CAF50; 
            color: white; 
            border: none; 
            padding: 10px 15px; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 16px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #cccccc; }
        #result { 
            margin-top: 20px; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success { color: #4CAF50; }
        .error { color: #d9534f; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>用户导入测试</h1>
    
    <div class="upload-area">
        <input type="file" id="csvFile" accept=".csv">
        <p id="file-info">未选择文件</p>
        <button id="uploadBtn" disabled>导入用户</button>
    </div>
    
    <div id="result">
        <p>请上传CSV文件进行导入测试</p>
    </div>
    
    <h3>CSV文件格式示例</h3>
    <table>
        <thead>
            <tr>
                <th>name</th>
                <th>phone</th>
                <th>gender</th>
                <th>organization_unit</th>
                <th>personnel_type</th>
                <th>employment_status</th>
                <th>id_number</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>张三</td>
                <td>13800138000</td>
                <td>1</td>
                <td>31</td>
                <td>正式员工</td>
                <td>在职</td>
                <td>110101199001011234</td>
            </tr>
            <tr>
                <td>李四</td>
                <td>13800138001</td>
                <td>2</td>
                <td>32</td>
                <td>合同工</td>
                <td>在职</td>
                <td>110101199002021235</td>
            </tr>
        </tbody>
    </table>

    <script>
        const fileInput = document.getElementById('csvFile');
        const fileInfo = document.getElementById('file-info');
        const uploadBtn = document.getElementById('uploadBtn');
        const resultDiv = document.getElementById('result');
        
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                fileInfo.textContent = `已选择: ${this.files[0].name}`;
                uploadBtn.disabled = false;
            } else {
                fileInfo.textContent = '未选择文件';
                uploadBtn.disabled = true;
            }
        });
        
        uploadBtn.addEventListener('click', function() {
            const file = fileInput.files[0];
            if (!file) return;
            
            const formData = new FormData();
            formData.append('action', 'import');
            formData.append('csv_file', file);
            
            resultDiv.innerHTML = '<p>正在导入，请稍候...</p>';
            uploadBtn.disabled = true;
            
            fetch('api/user_manage.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    let html = `<p class="success">导入完成！成功 ${data.summary.success_count} 条，失败 ${data.summary.failed_count} 条</p>`;
                    
                    if (data.data.failed.length > 0) {
                        html += '<h3>失败记录</h3><table><tr><th>行号</th><th>原因</th><th>数据</th></tr>';
                        data.data.failed.forEach(item => {
                            html += `<tr>
                                <td>${item.row}</td>
                                <td>${item.reason}</td>
                                <td><pre>${JSON.stringify(item.data, null, 2)}</pre></td>
                            </tr>`;
                        });
                        html += '</table>';
                    }
                    
                    if (data.data.success.length > 0) {
                        html += '<h3>成功记录</h3><table><tr><th>行号</th><th>操作</th><th>用户ID</th><th>数据</th></tr>';
                        data.data.success.forEach(item => {
                            html += `<tr>
                                <td>${item.row}</td>
                                <td>${item.action}</td>
                                <td>${item.user_id}</td>
                                <td><pre>${JSON.stringify(item.data, null, 2)}</pre></td>
                            </tr>`;
                        });
                        html += '</table>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<p class="error">导入失败: ${data.message}</p>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<p class="error">请求失败: ${error.message}</p>`;
            })
            .finally(() => {
                uploadBtn.disabled = false;
            });
        });
    </script>
</body>
</html>