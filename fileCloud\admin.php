<?php
/**
 * 文件网盘系统 - 管理员后台
 * 创建时间: 2025-06-23
 */

require_once 'functions.php';

// 检查用户是否已登录且为管理员
if (!isLoggedIn()) {
    redirect('login.php');
}

if (!isAdmin()) {
    die('访问被拒绝：您没有管理员权限');
}

$currentUsername = $_SESSION['username'] ?? '管理员';

// 获取系统统计信息
try {
    // 总文件数和大小
    $stmt = $pdo->query("
        SELECT COUNT(*) as total_files, COALESCE(SUM(file_size), 0) as total_size 
        FROM filecloud_info WHERE is_deleted = 0
    ");
    $fileStats = $stmt->fetch();
    
    // 总用户数（如果有用户表）
    $totalUsers = 0;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM 3_user");
    $totalUsers = $stmt->fetchColumn();
    } catch (Exception $e) {
        // 用户表可能不存在
    }
    
    // 今日上传文件数
    $stmt = $pdo->query("
        SELECT COUNT(*) FROM filecloud_info 
        WHERE DATE(upload_time) = CURDATE() AND is_deleted = 0
    ");
    $todayUploads = $stmt->fetchColumn();
    
    // 总下载次数
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(download_count), 0) FROM filecloud_info WHERE is_deleted = 0
    ");
    $totalDownloads = $stmt->fetchColumn();
    
    // 最近上传的文件
    $stmt = $pdo->query("
        SELECT f.*, u.name as username 
        FROM filecloud_info f 
        LEFT JOIN 3_user u ON f.user_id = u.id 
        WHERE f.is_deleted = 0 
        ORDER BY f.upload_time DESC 
        LIMIT 10
    ");
    $recentFiles = $stmt->fetchAll();
    
    // 存储使用情况（按日期）
    $stmt = $pdo->query("
        SELECT DATE(upload_time) as date, COUNT(*) as file_count, SUM(file_size) as size
        FROM filecloud_info 
        WHERE is_deleted = 0 AND upload_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(upload_time)
        ORDER BY date DESC
    ");
    $storageUsage = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = '获取统计信息失败：' . $e->getMessage();
}

// 获取系统配置
$configs = [];
try {
    $stmt = $pdo->query("SELECT * FROM filecloud_config ORDER BY config_key");
    $configRows = $stmt->fetchAll();
    foreach ($configRows as $row) {
        $configs[$row['config_key']] = $row;
    }
} catch (PDOException $e) {
    $configError = '获取配置失败';
}

// 处理配置更新
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_config') {
    if (validateCsrfToken($_POST['csrf_token'] ?? '')) {
        try {
            foreach ($_POST['config'] as $key => $value) {
                if (isset($configs[$key])) {
                    // 处理文件大小配置（数值*单位=字节）
                    if ($key === 'max_file_size_value' && isset($_POST['config']['max_file_size_unit'])) {
                        $sizeInBytes = (int)$value * (int)$_POST['config']['max_file_size_unit'];
                        setConfig('max_file_size', $sizeInBytes, $configs['max_file_size']['config_desc']);
                        continue; // 跳过后续处理，避免重复保存
                    } else if ($key === 'max_file_size_unit') {
                         continue; // 跳过单位字段的处理
                     }
                     
                     setConfig($key, $value, $configs[$key]['config_desc']);
                 }
                }
            $message = ['type' => 'success', 'text' => '配置更新成功'];
            
            // 重新获取配置
            $stmt = $pdo->query("SELECT * FROM filecloud_config ORDER BY config_key");
            $configRows = $stmt->fetchAll();
            $configs = [];
            foreach ($configRows as $row) {
                $configs[$row['config_key']] = $row;
            }
        } catch (Exception $e) {
            $message = ['type' => 'error', 'text' => '配置更新失败：' . $e->getMessage()];
        }
    } else {
        $message = ['type' => 'error', 'text' => '无效的请求'];
    }
}

$csrfToken = generateCsrfToken();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= h(SITE_TITLE) ?> - 系统管理</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <script src="assets/js/echarts.min.js"></script>
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="bi bi-cloud-upload me-2"></i><?= h(SITE_TITLE) ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-files me-1"></i>我的文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="upload.php">
                            <i class="bi bi-cloud-upload me-1"></i>上传文件
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="shared.php">
                            <i class="bi bi-share me-1"></i>与我共享
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="download.php">
                            <i class="bi bi-download me-1"></i>分享码下载
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-gear me-1"></i>系统管理
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item active" href="admin.php">系统概览</a></li>
                            <li><a class="dropdown-item" href="admin_files.php">文件管理</a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i><?= h($currentUsername) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid my-4">
        <!-- 消息提示 -->
        <?php if (isset($message)): ?>
        <div class="alert alert-<?= $message['type'] === 'success' ? 'success' : 'danger' ?> alert-dismissible fade show">
            <?= h($message['text']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?= h($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">总文件数</h6>
                                <h3 class="mb-0"><?= number_format($fileStats['total_files'] ?? 0) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-files"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">存储空间</h6>
                                <h3 class="mb-0"><?= formatFileSize($fileStats['total_size'] ?? 0) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-hdd"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">今日上传</h6>
                                <h3 class="mb-0"><?= number_format($todayUploads ?? 0) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-calendar-today"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="card-title mb-0">总下载量</h6>
                                <h3 class="mb-0"><?= number_format($totalDownloads ?? 0) ?></h3>
                            </div>
                            <div class="fs-1 opacity-75">
                                <i class="bi bi-download"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧内容 -->
            <div class="col-lg-8">
                <!-- 存储使用趋势图 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>最近30天存储使用趋势
                        </h5>
                    </div>
                    <div class="card-body" style="height: 300px;">
                        <div id="storageChart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>

                <!-- 最近上传文件 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>最近上传的文件
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (!empty($recentFiles)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>上传者</th>
                                        <th>上传时间</th>
                                        <th>下载量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentFiles as $file): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-file-earmark me-2 text-primary"></i>
                                                <span class="text-truncate" style="max-width: 200px;" title="<?= h($file['original_name']) ?>">
                                                    <?= h($file['original_name']) ?>
                                                </span>
                                            </div>
                                        </td>
                                        <td><?= formatFileSize($file['file_size']) ?></td>
                                        <td><?= h($file['username'] ?? '未知用户') ?></td>
                                        <td><?= date('m-d H:i', strtotime($file['upload_time'])) ?></td>
                                        <td>
                                            <span class="badge bg-info"><?= number_format($file['download_count']) ?></span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2">暂无上传记录</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="col-lg-4">
                <!-- 系统配置 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear me-2"></i>系统配置
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($configError)): ?>
                        <div class="alert alert-warning"><?= h($configError) ?></div>
                        <?php else: ?>
                        <form method="post">
                            <input type="hidden" name="action" value="save_config">
                            <input type="hidden" name="csrf_token" value="<?= h($csrfToken) ?>">
                            
                            <div class="mb-3">
                                <label for="maxFileSize" class="form-label">最大文件大小</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="maxFileSize" 
                                           name="config[max_file_size_value]" 
                                           value="<?= h($configs['max_file_size']['config_value'] ?? '') ?>">
                                    <select class="form-select" id="maxFileSizeUnit" name="config[max_file_size_unit]">
                                        <option value="1048576">MB</option>
                                        <option value="1073741824">GB</option>
                                    </select>
                                </div>
                                <div class="form-text">当前：<?= formatFileSize($configs['max_file_size']['config_value'] ?? 0) ?></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="allowedTypes" class="form-label">允许的文件类型</label>
                                <textarea class="form-control" id="allowedTypes" rows="3"
                                          name="config[allowed_file_types]"><?= h($configs['allowed_file_types']['config_value'] ?? '') ?></textarea>
                                <div class="form-text">用英文逗号分隔，如：jpg,png,pdf</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="expireDays" class="form-label">默认过期天数</label>
                                <input type="number" class="form-control" id="expireDays"
                                       name="config[share_code_expire_days]" 
                                       value="<?= h($configs['share_code_expire_days']['config_value'] ?? '') ?>">
                                <div class="form-text">0表示永不过期</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="siteTitle" class="form-label">网站标题</label>
                                <input type="text" class="form-control" id="siteTitle"
                                       name="config[site_title]" 
                                       value="<?= h($configs['site_title']['config_value'] ?? '') ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="siteDesc" class="form-label">网站描述</label>
                                <textarea class="form-control" id="siteDesc" rows="2"
                                          name="config[site_description]"><?= h($configs['site_description']['config_value'] ?? '') ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-check-circle me-1"></i>保存配置
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 系统信息 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>系统信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="info-item">
                            <strong>PHP版本：</strong> <?= phpversion() ?>
                        </div>
                        <div class="info-item">
                            <strong>服务器时间：</strong> <?= date('Y-m-d H:i:s') ?>
                        </div>
                        <div class="info-item">
                            <strong>上传目录：</strong> <?= UPLOAD_PATH ?>
                        </div>
                        <div class="info-item">
                            <strong>系统版本：</strong> v1.0.0
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/echarts.min.js"></script>
    <script>
        // 存储使用趋势图
        const storageData = <?= json_encode($storageUsage) ?>;
        
        if (storageData.length > 0) {
            const chartDom = document.getElementById('storageChart');
            const myChart = echarts.init(chartDom);
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: ['文件数量', '存储大小 (MB)']
                },
                grid: {
                     left: '5%',
                     right: '5%',
                     bottom: '10%',
                     top: '15%',
                     containLabel: true
                 },
                xAxis: [{
                    type: 'category',
                    boundaryGap: false,
                    data: storageData.map(item => {
                        const date = new Date(item.date);
                        return (date.getMonth() + 1) + '-' + date.getDate();
                    }).reverse()
                }],
                yAxis: [{
                    type: 'value',
                    name: '文件数量',
                    position: 'left'
                }, {
                    type: 'value',
                    name: '存储大小 (MB)',
                    position: 'right'
                }],
                series: [{
                    name: '文件数量',
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        width: 0
                    },
                    showSymbol: false,
                    areaStyle: {
                        opacity: 0.8,
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(13, 110, 253, 0.8)'
                            }, {
                                offset: 1,
                                color: 'rgba(13, 110, 253, 0.1)'
                            }
                        ])
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: storageData.map(item => item.file_count).reverse()
                }, {
                    name: '存储大小 (MB)',
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        width: 0
                    },
                    showSymbol: false,
                    areaStyle: {
                        opacity: 0.8,
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(25, 135, 84, 0.8)'
                            }, {
                                offset: 1,
                                color: 'rgba(25, 135, 84, 0.1)'
                            }
                        ])
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    yAxisIndex: 1,
                    data: storageData.map(item => (item.size / 1024 / 1024).toFixed(2)).reverse()
                }]
            };
            myChart.setOption(option);
        } else {
            document.getElementById('storageChart').parentElement.innerHTML = 
                '<div class="text-center py-4"><i class="bi bi-graph-up text-muted" style="font-size: 2rem;"></i><p class="text-muted mt-2">暂无数据</p></div>';
        }

        // 实时更新时间
        function updateTime() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                              String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                              String(now.getDate()).padStart(2, '0') + ' ' +
                              String(now.getHours()).padStart(2, '0') + ':' + 
                              String(now.getMinutes()).padStart(2, '0') + ':' + 
                              String(now.getSeconds()).padStart(2, '0');
            
            const timeElement = document.querySelector('.info-item:nth-child(2)');
            if (timeElement) {
                timeElement.innerHTML = '<strong>服务器时间：</strong> ' + timeString;
            }
        }

        // 每秒更新时间
        setInterval(updateTime, 1000);

        // 文件大小输入框格式化
        const maxFileSizeInput = document.getElementById('maxFileSize');
        if (maxFileSizeInput) {
            maxFileSizeInput.addEventListener('input', function() {
                const value = parseInt(this.value);
                if (!isNaN(value)) {
                    const formText = this.parentElement.querySelector('.form-text');
                    formText.textContent = '当前：' + formatFileSize(value);
                }
            });
        }

        function formatFileSize(bytes) {
            if (bytes >= 1073741824) {
                return (bytes / 1073741824).toFixed(2) + ' GB';
            } else if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(2) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(2) + ' KB';
            } else {
                return bytes + ' B';
            }
        }
    </script>
</body>
</html>
