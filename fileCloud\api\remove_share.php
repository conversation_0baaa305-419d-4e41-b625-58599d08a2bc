<?php
/**
 * 文件网盘系统 - 取消分享API
 * 创建时间: 2025-06-23
 * 更新时间: 2025-07-15
 */

require_once '../functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    redirect('../login.php');
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('无效的请求方法');
}

// 验证CSRF令牌
if (!validateCsrfToken($_POST['csrf_token'] ?? '')) {
    die('无效的请求');
}

$shareId = (int)($_POST['share_id'] ?? 0);
$currentUserId = $_SESSION['user_id'];

if ($shareId <= 0) {
    die('参数错误');
}

try {
    // 检查分享记录是否存在且属于当前用户
    $stmt = $pdo->prepare("
        SELECT share_id FROM filecloud_share 
        WHERE share_id = ? AND from_user_id = ?
    ");
    $stmt->execute([$shareId, $currentUserId]);
    
    if (!$stmt->fetch()) {
        die('分享记录不存在或无权限');
    }
    
    // 直接删除分享记录
    $stmt = $pdo->prepare("
        DELETE FROM filecloud_share WHERE share_id = ?
    ");
    $stmt->execute([$shareId]);
    
    // 重定向回分享页面并显示成功消息
    redirect('../shared.php?refresh=1');
    
} catch (PDOException $e) {
    die('取消分享失败：' . $e->getMessage());
}
?>