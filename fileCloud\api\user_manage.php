<?php
/**
 * 文件网盘系统 - 用户管理API
 * 创建时间: 2025-07-03
 */

require_once '../functions.php';

header('Content-Type: application/json; charset=utf-8');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['status' => 0, 'message' => '无效的请求方法'], 405);
}

$controlCode = $_POST['controlCode'] ?? '';
$searchKeyword = $_POST['search_keyword'] ?? '';

// 处理查询用户请求
if ($controlCode === 'query') {
    if (empty($searchKeyword)) {
        jsonResponse(['status' => 0, 'message' => '搜索关键词不能为空', 'data' => []]);
    }
    
    try {
        // 调用getUserInfo函数进行用户搜索
        $users = getUserInfo($searchKeyword);
        
        if (empty($users)) {
            jsonResponse(['status' => 0, 'message' => '未找到匹配的用户', 'data' => []]);
        }
        
        jsonResponse(['status' => 1, 'message' => '查询成功', 'data' => $users]);
        
    } catch (Exception $e) {
        error_log('用户搜索API错误: ' . $e->getMessage());
        jsonResponse(['status' => 0, 'message' => '查询失败，请稍后重试', 'data' => []], 500);
    }
} else {
    jsonResponse(['status' => 0, 'message' => '无效的操作代码'], 400);
}
?>
