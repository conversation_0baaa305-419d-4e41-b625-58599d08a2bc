/**
 * 文件网盘系统 - 主要JavaScript功能
 * 创建时间: 2025-06-23
 */

// 全局变量
const FileCloud = {
    // 配置选项
    config: {
        toastDuration: 3000,
        animationDuration: 300,
        maxFileSize: 52428800, // 50MB
        allowedTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar']
    },
    
    // 初始化
    init: function() {
        this.bindEvents();
        this.initTooltips();
        this.initClipboard();
        this.checkPageFeatures();
    },
    
    // 绑定事件
    bindEvents: function() {
        // 全局键盘快捷键
        document.addEventListener('keydown', this.handleKeyboard.bind(this));
        
        // 表格行点击事件
        document.querySelectorAll('.table-hover tbody tr').forEach(row => {
            row.addEventListener('click', this.handleTableRowClick.bind(this));
        });
        
        // 文件大小格式化
        document.querySelectorAll('[data-file-size]').forEach(element => {
            const bytes = parseInt(element.dataset.fileSize);
            element.textContent = this.formatFileSize(bytes);
        });
        
        // 时间格式化
        document.querySelectorAll('[data-time]').forEach(element => {
            const timestamp = element.dataset.time;
            element.textContent = this.formatTime(timestamp);
        });
    },
    
    // 初始化工具提示
    initTooltips: function() {
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    },
    
    // 初始化剪贴板功能
    initClipboard: function() {
        // 检查剪贴板API支持
        if (!navigator.clipboard) {
            console.warn('剪贴板API不支持');
            return;
        }
        
        // 为所有具有复制功能的元素添加点击事件
        document.querySelectorAll('[data-copy]').forEach(element => {
            element.addEventListener('click', this.handleCopyClick.bind(this));
        });
    },
    
    // 检查页面特性
    checkPageFeatures: function() {
        // 检查拖拽上传支持
        if (this.isDragSupported()) {
            document.body.classList.add('drag-supported');
        }
        
        // 检查文件API支持
        if (this.isFileAPISupported()) {
            document.body.classList.add('file-api-supported');
        }
        
        // 检查本地存储支持
        if (this.isLocalStorageSupported()) {
            document.body.classList.add('local-storage-supported');
            this.loadUserPreferences();
        }
    },
    
    // 键盘事件处理
    handleKeyboard: function(e) {
        // Ctrl/Cmd + K: 快速搜索
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.showQuickSearch();
        }
        
        // ESC: 关闭模态框
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                bootstrap.Modal.getInstance(openModal).hide();
            }
        }
        
        // F5: 刷新页面（阻止默认行为，使用AJAX刷新）
        if (e.key === 'F5') {
            e.preventDefault();
            this.refreshPage();
        }
    },
    
    // 表格行点击处理
    handleTableRowClick: function(e) {
        // 如果点击的是按钮或链接，不执行行点击事件
        if (e.target.closest('button, a, .btn')) {
            return;
        }
        
        const row = e.currentTarget;
        const fileId = row.dataset.fileId;
        
        if (fileId) {
            this.showFileDetails(fileId);
        }
    },
    
    // 复制点击处理
    handleCopyClick: function(e) {
        const element = e.currentTarget;
        const textToCopy = element.dataset.copy || element.textContent;
        
        this.copyToClipboard(textToCopy)
            .then(() => {
                this.showToast('已复制到剪贴板', 'success');
                this.animateElement(element, 'pulse');
            })
            .catch(() => {
                this.showToast('复制失败', 'error');
            });
    },
    
    // 显示文件详情
    showFileDetails: function(fileId) {
        // 这里可以显示文件详情模态框
        console.log('显示文件详情:', fileId);
    },
    
    // 快速搜索
    showQuickSearch: function() {
        // 创建搜索模态框
        const searchModal = this.createSearchModal();
        document.body.appendChild(searchModal);
        new bootstrap.Modal(searchModal).show();
    },
    
    // 创建搜索模态框
    createSearchModal: function() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">快速搜索</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="text" class="form-control" placeholder="搜索文件名..." id="quickSearchInput">
                        <div id="searchResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        `;
        
        // 绑定搜索事件
        const searchInput = modal.querySelector('#quickSearchInput');
        searchInput.addEventListener('input', this.debounce(this.performSearch.bind(this), 300));
        
        // 模态框关闭时删除元素
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
        
        return modal;
    },
    
    // 执行搜索
    performSearch: function(e) {
        const query = e.target.value.trim();
        const resultsContainer = document.getElementById('searchResults');
        
        if (query.length < 2) {
            resultsContainer.innerHTML = '';
            return;
        }
        
        // 模拟搜索（实际应用中应该调用API）
        const mockResults = [
            { name: '文档.pdf', size: '1.2MB', time: '2小时前' },
            { name: '图片.jpg', size: '856KB', time: '1天前' }
        ].filter(item => item.name.toLowerCase().includes(query.toLowerCase()));
        
        if (mockResults.length === 0) {
            resultsContainer.innerHTML = '<p class="text-muted">未找到相关文件</p>';
            return;
        }
        
        resultsContainer.innerHTML = mockResults.map(item => `
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div>
                    <i class="bi bi-file-earmark me-2"></i>
                    <strong>${item.name}</strong>
                    <small class="text-muted ms-2">${item.size}</small>
                </div>
                <small class="text-muted">${item.time}</small>
            </div>
        `).join('');
    },
    
    // 刷新页面
    refreshPage: function() {
        this.showToast('正在刷新...', 'info');
        setTimeout(() => {
            window.location.reload();
        }, 500);
    },
    
    // 复制到剪贴板
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text);
        } else {
            // 备用方法
            return new Promise((resolve, reject) => {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                try {
                    document.execCommand('copy');
                    resolve();
                } catch (err) {
                    reject(err);
                } finally {
                    document.body.removeChild(textarea);
                }
            });
        }
    },
    
    // 显示提示消息
    showToast: function(message, type = 'info', duration = null) {
        duration = duration || this.config.toastDuration;
        
        const toast = document.createElement('div');
        toast.className = `alert alert-${this.getAlertClass(type)} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // 自动消失
        setTimeout(() => {
            if (toast.parentNode) {
                bootstrap.Alert.getOrCreateInstance(toast).close();
            }
        }, duration);
        
        return toast;
    },
    
    // 获取警告类名
    getAlertClass: function(type) {
        const typeMap = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        return typeMap[type] || 'info';
    },
    
    // 动画元素
    animateElement: function(element, animation) {
        element.classList.add(`animate-${animation}`);
        setTimeout(() => {
            element.classList.remove(`animate-${animation}`);
        }, this.config.animationDuration);
    },
    
    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 格式化时间
    formatTime: function(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        // 小于1分钟
        if (diff < 60000) {
            return '刚刚';
        }
        
        // 小于1小时
        if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟前';
        }
        
        // 小于1天
        if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时前';
        }
        
        // 小于1周
        if (diff < 604800000) {
            return Math.floor(diff / 86400000) + '天前';
        }
        
        // 格式化为日期
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 检查拖拽支持
    isDragSupported: function() {
        const div = document.createElement('div');
        return (('draggable' in div) || ('ondragstart' in div && 'ondrop' in div));
    },
    
    // 检查文件API支持
    isFileAPISupported: function() {
        return window.File && window.FileReader && window.FileList && window.Blob;
    },
    
    // 检查本地存储支持
    isLocalStorageSupported: function() {
        try {
            const test = 'test';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    },
    
    // 加载用户偏好
    loadUserPreferences: function() {
        try {
            const prefs = JSON.parse(localStorage.getItem('filecloud_prefs') || '{}');
            
            // 应用主题
            if (prefs.theme) {
                document.body.classList.add(`theme-${prefs.theme}`);
            }
            
            // 应用其他设置
            if (prefs.language) {
                document.documentElement.lang = prefs.language;
            }
        } catch (e) {
            console.warn('加载用户偏好失败:', e);
        }
    },
    
    // 保存用户偏好
    saveUserPreferences: function(prefs) {
        try {
            const current = JSON.parse(localStorage.getItem('filecloud_prefs') || '{}');
            const updated = { ...current, ...prefs };
            localStorage.setItem('filecloud_prefs', JSON.stringify(updated));
        } catch (e) {
            console.warn('保存用户偏好失败:', e);
        }
    },
    
    // AJAX请求包装器
    ajax: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const config = { ...defaults, ...options };
        
        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX请求失败:', error);
                this.showToast('网络请求失败', 'error');
                throw error;
            });
    },
    
    // 显示加载状态
    showLoading: function(element, text = '加载中...') {
        const originalContent = element.innerHTML;
        element.innerHTML = `
            <span class="loading me-2"></span>
            ${text}
        `;
        element.disabled = true;
        
        return function hideLoading() {
            element.innerHTML = originalContent;
            element.disabled = false;
        };
    },
    
    // 确认对话框
    confirm: function(message, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">确认操作</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmBtn">确认</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const modalInstance = new bootstrap.Modal(modal);
        
        modal.querySelector('#confirmBtn').addEventListener('click', () => {
            modalInstance.hide();
            if (onConfirm) onConfirm();
        });
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
            if (onCancel) onCancel();
        });
        
        modalInstance.show();
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    FileCloud.init();
});

// 全局函数（向后兼容）
function showToast(message, type = 'info') {
    return FileCloud.showToast(message, type);
}

function copyToClipboard(text) {
    return FileCloud.copyToClipboard(text);
}

function formatFileSize(bytes) {
    return FileCloud.formatFileSize(bytes);
}

// 导出到全局作用域
window.FileCloud = FileCloud;
