#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV到MySQL导入文件生成器
功能：读取CSV文件并生成分割的MySQL导入SQL文件
作者：AI Assistant
日期：2025-07-07
"""

import csv
import os
import sys
from typing import List, <PERSON><PERSON>





def generate_create_table_sql(columns: List[str]) -> str:
    """
    生成CREATE TABLE语句
    
    Args:
        columns: 列名列表
        
    Returns:
        CREATE TABLE SQL语句
    """
    # 创建表结构，所有列都定义为TEXT类型
    sql = "-- 创建schInfo表\n"
    sql += "DROP TABLE IF EXISTS `schInfo`;\n"
    sql += "CREATE TABLE `schInfo` (\n"
    sql += "  `id` INT AUTO_INCREMENT PRIMARY KEY,\n"
    
    for column in columns:
        # 清理列名，移除特殊字符
        clean_column = column.strip().replace(' ', '_').replace('-', '_')
        sql += f"  `{clean_column}` TEXT,\n"
    
    sql = sql.rstrip(',\n') + "\n"
    sql += ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n"
    
    return sql


def calculate_file_size(content: str) -> int:
    """
    计算字符串的字节大小（UTF-8编码）
    
    Args:
        content: 字符串内容
        
    Returns:
        字节大小
    """
    return len(content.encode('utf-8'))


def read_csv_file(file_path: str) -> Tuple[List[str], List[List[str]]]:
    """
    读取CSV文件并返回列名和数据
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        (列名列表, 数据行列表)
        
    Raises:
        FileNotFoundError: 文件不存在
        Exception: 其他读取错误
    """
    print(f"正在读取CSV文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"CSV文件不存在: {file_path}")
    
    # 检查文件是否可读
    if not os.access(file_path, os.R_OK):
        raise PermissionError(f"无法读取CSV文件: {file_path}")
    
    columns = []
    data_rows = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', newline='') as csvfile:
            # 自动检测CSV方言
            sample = csvfile.read(1024)
            csvfile.seek(0)
            sniffer = csv.Sniffer()
            dialect = sniffer.sniff(sample)
            
            reader = csv.reader(csvfile, dialect)
            
            # 读取第一行作为列名
            columns = next(reader, [])
            if not columns:
                raise ValueError("CSV文件为空或没有列名")
            
            print(f"检测到 {len(columns)} 个列: {', '.join(columns[:5])}{'...' if len(columns) > 5 else ''}")
            
            # 读取数据行
            row_count = 0
            for row in reader:
                # 确保行的长度与列数一致，不足的用空字符串填充
                while len(row) < len(columns):
                    row.append('')
                
                data_rows.append(row[:len(columns)])  # 截取多余的列
                row_count += 1
                
                if row_count % 1000 == 0:
                    print(f"已读取 {row_count} 行数据...")
            
            print(f"CSV文件读取完成，共 {len(data_rows)} 行数据")
            
    except UnicodeDecodeError:
        print("UTF-8解码失败，尝试使用GBK编码...")
        try:
            with open(file_path, 'r', encoding='gbk', newline='') as csvfile:
                reader = csv.reader(csvfile)
                columns = next(reader, [])
                data_rows = list(reader)
                print(f"使用GBK编码成功读取，共 {len(data_rows)} 行数据")
        except Exception as e:
            raise Exception(f"无法读取CSV文件，尝试了UTF-8和GBK编码: {str(e)}")
    
    except Exception as e:
        raise Exception(f"读取CSV文件时发生错误: {str(e)}")
    
    return columns, data_rows


def escape_mysql_value(value: str) -> str:
    """
    转义MySQL值

    Args:
        value: 原始值

    Returns:
        转义后的值
    """
    if value is None or value.strip() == '':
        return 'NULL'

    # 转义特殊字符
    value = str(value)
    value = value.replace('\\', '\\\\')  # 反斜杠
    value = value.replace("'", "\\'")    # 单引号
    value = value.replace('"', '\\"')    # 双引号
    value = value.replace('\n', '\\n')   # 换行符
    value = value.replace('\r', '\\r')   # 回车符
    value = value.replace('\t', '\\t')   # 制表符

    return f"'{value}'"


def generate_insert_statements(columns: List[str], data_rows: List[List[str]]) -> List[str]:
    """
    生成INSERT语句列表

    Args:
        columns: 列名列表
        data_rows: 数据行列表

    Returns:
        INSERT语句列表
    """
    print("正在生成INSERT语句...")

    insert_statements = []
    clean_columns = [col.strip().replace(' ', '_').replace('-', '_') for col in columns]
    columns_str = ', '.join([f"`{col}`" for col in clean_columns])

    for i, row in enumerate(data_rows):
        # 处理每个值，进行转义
        escaped_values = []
        for value in row:
            escaped_values.append(escape_mysql_value(value))

        values_str = ', '.join(escaped_values)
        insert_sql = f"INSERT INTO `schInfo` ({columns_str}) VALUES ({values_str});"
        insert_statements.append(insert_sql)

        if (i + 1) % 1000 == 0:
            print(f"已生成 {i + 1} 条INSERT语句...")

    print(f"INSERT语句生成完成，共 {len(insert_statements)} 条")
    return insert_statements


def split_and_write_sql_files_evenly(create_table_sql: str, insert_statements: List[str],
                                    num_files: int = 2) -> None:
    """
    将SQL语句平均分割并写入多个文件

    Args:
        create_table_sql: CREATE TABLE语句
        insert_statements: INSERT语句列表
        num_files: 要生成的文件数量
    """
    total_statements = len(insert_statements)
    statements_per_file = total_statements // num_files
    remainder = total_statements % num_files

    print(f"正在平均分割SQL文件为 {num_files} 个文件...")
    print(f"总共 {total_statements} 条INSERT语句")
    print(f"每个文件大约 {statements_per_file} 条语句")
    print(f"余数: {remainder}")

    start_index = 0
    total_processed = 0

    for file_num in range(1, num_files + 1):
        # 计算当前文件应包含的语句数量
        current_file_statements = statements_per_file
        if file_num <= remainder:  # 前几个文件多分配一条语句来处理余数
            current_file_statements += 1

        end_index = start_index + current_file_statements

        print(f"文件 {file_num}: 索引范围 {start_index} 到 {end_index-1} (共 {current_file_statements} 条)")

        # 构建文件内容
        if file_num == 1:
            # 第一个文件包含CREATE TABLE语句
            current_file_content = create_table_sql
        else:
            current_file_content = ""

        # 添加INSERT语句
        statements_added = 0
        for i in range(start_index, min(end_index, total_statements)):
            current_file_content += insert_statements[i] + '\n'
            statements_added += 1

        total_processed += statements_added

        # 写入文件
        filename = f"schInfo_part{file_num}.sql"
        write_sql_file(filename, current_file_content)

        # 计算文件大小
        file_size_mb = calculate_file_size(current_file_content) / 1024 / 1024

        print(f"已生成 {filename}，包含 {statements_added} 条INSERT语句，"
              f"大小: {file_size_mb:.2f}MB")

        start_index = end_index

        if start_index >= total_statements:
            break

    print(f"SQL文件分割完成，共生成 {num_files} 个文件")
    print(f"总共处理了 {total_processed} 条INSERT语句")
    if total_processed != total_statements:
        print(f"警告: 处理的语句数量 ({total_processed}) 与总数 ({total_statements}) 不匹配！")


def split_and_write_sql_files(create_table_sql: str, insert_statements: List[str],
                             max_size_mb: int = 50) -> None:
    """
    将SQL语句分割并写入多个文件（按大小分割）

    Args:
        create_table_sql: CREATE TABLE语句
        insert_statements: INSERT语句列表
        max_size_mb: 每个文件的最大大小（MB）
    """
    max_size_bytes = max_size_mb * 1024 * 1024  # 转换为字节

    print(f"正在分割SQL文件，每个文件最大 {max_size_mb}MB...")

    # 第一个文件包含CREATE TABLE语句
    file_count = 1
    current_file_content = create_table_sql
    current_size = calculate_file_size(current_file_content)

    statements_in_current_file = 0

    for i, statement in enumerate(insert_statements):
        statement_with_newline = statement + '\n'
        statement_size = calculate_file_size(statement_with_newline)

        # 检查添加这条语句是否会超过大小限制
        if current_size + statement_size > max_size_bytes and statements_in_current_file > 0:
            # 写入当前文件
            filename = f"schInfo_part{file_count}.sql"
            write_sql_file(filename, current_file_content)
            print(f"已生成 {filename}，包含 {statements_in_current_file} 条INSERT语句，"
                  f"大小: {current_size / 1024 / 1024:.2f}MB")

            # 开始新文件
            file_count += 1
            current_file_content = ""
            current_size = 0
            statements_in_current_file = 0

        current_file_content += statement_with_newline
        current_size += statement_size
        statements_in_current_file += 1

        if (i + 1) % 1000 == 0:
            print(f"已处理 {i + 1}/{len(insert_statements)} 条INSERT语句...")

    # 写入最后一个文件
    if current_file_content.strip():
        filename = f"schInfo_part{file_count}.sql"
        write_sql_file(filename, current_file_content)
        print(f"已生成 {filename}，包含 {statements_in_current_file} 条INSERT语句，"
              f"大小: {current_size / 1024 / 1024:.2f}MB")

    print(f"SQL文件分割完成，共生成 {file_count} 个文件")


def write_sql_file(filename: str, content: str) -> None:
    """
    写入SQL文件
    
    Args:
        filename: 文件名
        content: 文件内容
    """
    try:
        # 确保目标目录存在
        output_dir = r"d:\\Users\\Administrator\\Desktop\\项目"
        os.makedirs(output_dir, exist_ok=True)
        
        # 拼接完整路径
        full_path = os.path.join(output_dir, filename)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
    except Exception as e:
        raise Exception(f"写入文件 {filename} 时发生错误: {str(e)}")


def check_duplicate_ids(data_rows: List[List[str]]) -> None:
    """
    检查数据中是否有重复的ID

    Args:
        data_rows: 数据行列表
    """
    print("正在检查重复ID...")
    id_counts = {}
    duplicate_ids = []

    for row in data_rows:
        if row and len(row) > 0:
            id_value = row[0].strip()  # 假设第一列是ID
            if id_value:
                if id_value in id_counts:
                    id_counts[id_value] += 1
                    if id_counts[id_value] == 2:  # 第一次发现重复
                        duplicate_ids.append(id_value)
                else:
                    id_counts[id_value] = 1

    if duplicate_ids:
        print(f"发现 {len(duplicate_ids)} 个重复ID:")
        for dup_id in duplicate_ids[:10]:  # 只显示前10个
            print(f"  ID {dup_id} 出现 {id_counts[dup_id]} 次")
        if len(duplicate_ids) > 10:
            print(f"  ... 还有 {len(duplicate_ids) - 10} 个重复ID")

        total_duplicates = sum(count - 1 for count in id_counts.values() if count > 1)
        print(f"总重复行数: {total_duplicates}")
        print(f"去重后预期行数: {len(data_rows) - total_duplicates}")
    else:
        print("未发现重复ID")


def split_into_small_batches(create_table_sql: str, insert_statements: List[str],
                           batch_size: int = 1000) -> None:
    """
    将SQL语句分割成小批次文件

    Args:
        create_table_sql: CREATE TABLE语句
        insert_statements: INSERT语句列表
        batch_size: 每个批次的语句数量
    """
    total_statements = len(insert_statements)
    num_batches = (total_statements + batch_size - 1) // batch_size  # 向上取整

    print(f"正在分割SQL文件为 {num_batches} 个小批次文件...")
    print(f"每个批次 {batch_size} 条语句")

    for batch_num in range(num_batches):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, total_statements)

        # 构建文件内容
        if batch_num == 0:
            # 第一个文件包含CREATE TABLE语句
            current_file_content = create_table_sql
        else:
            current_file_content = ""

        # 添加INSERT语句
        for i in range(start_idx, end_idx):
            current_file_content += insert_statements[i] + '\n'

        # 写入文件
        filename = f"schInfo_batch_{batch_num + 1:03d}.sql"
        write_sql_file(filename, current_file_content)

        # 计算文件大小
        file_size_mb = calculate_file_size(current_file_content) / 1024 / 1024
        actual_statements = end_idx - start_idx

        print(f"已生成 {filename}，包含 {actual_statements} 条INSERT语句，"
              f"大小: {file_size_mb:.2f}MB")

    print(f"小批次文件分割完成，共生成 {num_batches} 个文件")


def main():
    """主函数"""
    # CSV文件路径
    csv_file_path = r"F:\文档\岳池学生信息表.csv"

    try:
        print("=== CSV到MySQL导入文件生成器 ===")
        print(f"目标CSV文件: {csv_file_path}")
        print(f"当前工作目录: {os.getcwd()}")
        print()

        # 询问用户选择分割方式
        print("请选择分割方式:")
        print("1. 平均分成2个文件")
        print("2. 分成小批次文件（每批1000条，便于排查问题）")
        choice = input("请输入选择 (1 或 2): ").strip()

        # 读取CSV文件
        columns, data_rows = read_csv_file(csv_file_path)

        if not data_rows:
            print("警告: CSV文件中没有数据行")
            return

        # 检查重复ID
        check_duplicate_ids(data_rows)

        # 生成CREATE TABLE语句
        create_table_sql = generate_create_table_sql(columns)

        # 生成INSERT语句
        insert_statements = generate_insert_statements(columns, data_rows)

        # 根据用户选择进行分割
        if choice == "2":
            split_into_small_batches(create_table_sql, insert_statements, batch_size=1000)
        else:
            # 默认平均分割成2个文件
            split_and_write_sql_files_evenly(create_table_sql, insert_statements, num_files=2)

        print("\n=== 处理完成 ===")
        print("生成的文件:")
        output_dir = r"d:\\Users\\Administrator\\Desktop\\项目"

        # 列出所有生成的SQL文件
        for filename in os.listdir(output_dir):
            if filename.startswith("schInfo_") and filename.endswith(".sql"):
                full_path = os.path.join(output_dir, filename)
                size_mb = os.path.getsize(full_path) / 1024 / 1024
                print(f"  {filename} ({size_mb:.2f}MB)")

    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
