# 匿名上传功能说明

## 功能概述

匿名上传功能允许用户无需登录即可上传文件并分享给指定用户。这个功能特别适用于外部用户向内部用户分享文件的场景。

## 主要特性

### 1. 用户选择功能
- **智能搜索**：支持按用户姓名或组织单位名称搜索
- **实时搜索**：输入关键词即时显示匹配结果
- **多选支持**：可以选择多个用户进行分享
- **全选功能**：支持一键全选/取消全选搜索结果
- **用户标签**：已选用户以标签形式显示，可单独移除

### 2. 文件上传功能
- **拖拽上传**：支持拖拽文件到上传区域
- **点击选择**：传统的文件选择方式
- **实时验证**：上传前验证文件类型和大小
- **进度显示**：实时显示上传进度
- **错误处理**：完善的错误提示和处理机制

### 3. 自动分享功能
- **即时分享**：文件上传成功后自动分享给选定用户
- **分享确认**：显示分享成功的用户数量
- **分享码生成**：生成唯一分享码供下载使用

## 技术实现

### 1. 前端技术
- **Bootstrap 5**：响应式UI框架
- **原生JavaScript**：用户交互和AJAX请求
- **CSS3动画**：拖拽效果和过渡动画
- **步骤指示器**：清晰的操作流程指引

### 2. 后端技术
- **PHP**：服务器端逻辑处理
- **PDO**：数据库操作和事务管理
- **文件验证**：类型和大小限制
- **安全措施**：CSRF防护和输入验证

### 3. 数据库设计
- **匿名用户标识**：使用user_id=0标识匿名上传
- **分享记录**：自动创建分享记录到filecloud_share表
- **事务处理**：确保文件上传和分享记录的一致性

## 使用流程

### 第一步：选择用户
1. 在搜索框中输入用户姓名或组织单位名称
2. 从搜索结果中选择要分享的用户（支持多选）
3. 确认已选用户列表
4. 点击"继续上传文件"按钮

### 第二步：上传文件
1. 拖拽文件到上传区域或点击选择文件
2. 确认文件信息（名称、大小、类型）
3. 设置分享码有效期
4. 点击"开始上传并分享"按钮

### 第三步：完成分享
1. 等待文件上传完成
2. 获取生成的分享码
3. 复制分享码或继续上传其他文件

## 安全特性

### 1. 文件验证
- **类型白名单**：只允许预定义的文件类型
- **大小限制**：防止过大文件占用服务器空间
- **扩展名检查**：验证文件扩展名的合法性

### 2. 输入验证
- **CSRF防护**：防止跨站请求伪造攻击
- **参数验证**：严格验证所有输入参数
- **SQL注入防护**：使用预处理语句防止SQL注入

### 3. 错误处理
- **异常捕获**：完善的异常处理机制
- **事务回滚**：失败时自动清理已上传的文件
- **错误日志**：记录详细的错误信息供调试

## 配置说明

### 1. 文件限制配置
```php
// config.php中的相关配置
define('MAX_FILE_SIZE', 524288000); // 最大文件大小（50MB）
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar']);
```

### 2. 分享码配置
```php
define('SHARE_CODE_LENGTH', 6); // 分享码长度
define('DEFAULT_EXPIRE_DAYS', 30); // 默认过期天数
```

## API接口

### 用户搜索API
- **路径**：`api/user_manage.php`
- **方法**：POST
- **参数**：
  - `controlCode`: 'query'
  - `search_keyword`: 搜索关键词
- **返回**：JSON格式的用户列表

### 文件上传API
- **路径**：`anonymous_upload.php`
- **方法**：POST
- **参数**：
  - `file`: 上传的文件
  - `selected_users`: 选中的用户ID列表（逗号分隔）
  - `expire_days`: 过期天数
  - `csrf_token`: CSRF令牌

## 浏览器兼容性

- **现代浏览器**：Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **移动端**：iOS Safari 12+, Chrome Mobile 60+
- **功能支持**：
  - 拖拽上传：需要HTML5 Drag and Drop API支持
  - 文件API：需要File API支持
  - 进度显示：需要XMLHttpRequest Level 2支持

## 故障排除

### 常见问题
1. **搜索不到用户**：检查数据库连接和用户表数据
2. **文件上传失败**：检查文件大小和类型限制
3. **分享失败**：检查数据库权限和表结构
4. **页面样式异常**：检查CSS文件路径和Bootstrap版本

### 调试方法
1. 查看浏览器控制台错误信息
2. 检查服务器错误日志
3. 验证数据库连接和表结构
4. 确认文件权限设置

## 扩展功能建议

1. **批量上传**：支持同时上传多个文件
2. **上传历史**：记录匿名上传的历史记录
3. **邮件通知**：上传成功后自动发送邮件通知
4. **二维码分享**：生成分享码的二维码
5. **密码保护**：为分享码添加密码保护
6. **下载统计**：统计文件下载次数和时间
